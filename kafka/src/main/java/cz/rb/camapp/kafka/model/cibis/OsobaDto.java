package cz.rb.camapp.kafka.model.cibis;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

@Data
public class OsobaDto {
    @NotNull
    private Long bic;
    @NotNull(groups = {ValidationGroups.Create.class})
    @Size(min = 9, max = 10, groups = {ValidationGroups.Create.class})
    private String id;
    @NotNull(groups = {ValidationGroups.Create.class})
    @Size(min = 1, max = 1)
    private String typId;
    @NotNull(groups = {ValidationGroups.Create.class})
    private String datumNarozeni;
    @Size(max = 24, groups = {ValidationGroups.Create.class})
    @NotNull(groups = {ValidationGroups.Create.class})
    private String jmeno;
    @Size(max = 25, groups = {ValidationGroups.Create.class})
    @NotNull(groups = {ValidationGroups.Create.class})
    private String prijmeni;
    @NotNull(groups = {ValidationGroups.Create.class})
    @Valid
    private AdresaDto trvalaAdresa;
    @NotNull(groups = {ValidationGroups.Create.class})
    private Long pohlavi;
    @NotNull(groups = {ValidationGroups.Create.class})
    @Size(min = 2, max = 2, groups = {ValidationGroups.Cancel.class})
    private String statniPrislusnost;
    @NotNull(groups = {ValidationGroups.Create.class})
    private Long rodinnyStav;
    @NotNull(groups = {ValidationGroups.Create.class})
    private Long specialniStatusKlienta;
    private String rodnePrijmeni;
    @NotNull(groups = {ValidationGroups.Create.class})
    private String mistoNarozeni;
    @NotNull(groups = {ValidationGroups.Create.class})
    @Size(min = 2, max = 2, groups = {ValidationGroups.Create.class})
    private String zemeNarozeni;
    @NotNull(groups = {ValidationGroups.Create.class})
    private Boolean politickyExponovanaOsoba;
    @NotNull(groups = {ValidationGroups.Create.class})
    private Long nejvyssiDosazeneVzdelani;
    @NotNull(groups = {ValidationGroups.Create.class})
    private Long zpusobBydleni;
    @NotNull(groups = {ValidationGroups.Create.class})
    private Long interniDruhPobytu;
    @NotNull(groups = {ValidationGroups.Create.class})
    private Boolean novePodepsanaSPJ;
    private String datumPodpisuSPJ;
    private Long verzeSPJ;
    @NotNull(groups = {ValidationGroups.Create.class})
    private Boolean danovyRezident;
    private String partyId;
    @NotNull(groups = {ValidationGroups.Create.class})
    @Size(min = 1,max = 2)
    @Valid
    private List<OsobniDokladDto> osobniDoklady;
    @NotNull(groups = {ValidationGroups.Create.class})
    @Valid
    private List<KomunikacniKanalDto> komunikacniKanaly;
    @NotNull(groups = {ValidationGroups.Create.class})
    @Valid
    private PobytProMFDto pobytProMF;
    @NotNull(groups = {ValidationGroups.Create.class})
    @Valid
    private RoleOsobyDto udajeRole;
}
