package cz.rb.camapp.kafka.model.cibis;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.*;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class MessageBodyRstsBslCancel {
    @NotNull
    private Long zdrojovyKanal;
    @NotNull
    @Valid
    private ObchodniZastupceDto obchZastupceSepsani;
    @NotNull
    @Valid
    private Osoba2Dto klient;
    @NotNull
    @Valid
    private Sporeni2Dto sporeni;
    @NotNull
    @Valid
    private Uver2Dto uver;
    @NotNull
    @Valid
    private RiskKontrolyDto riskKontroly;
    @NotNull
    private Long duvodStoZam;
    @NotNull
    private String datumStoZam;
    @NotNull
    private Boolean provedenDotazDoUverovehoRegistru;
}
