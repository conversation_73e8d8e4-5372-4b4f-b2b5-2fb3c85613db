package cz.rb.camapp.kafka.model.cibis;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class AplikacniScoring2Dto implements AplikacniScoringCommon {
    private Long vek;
    private Long rodinnyStav;
    private Long nejvyssiDosazeneVzdelani;
    private Long pracovniZarazeni1;
    private Long pracovniZarazeni2;
    private Long pracovniZarazeni3;
    private Long oborCinnosti1;
    private Long oborCinnosti2;
    private Long oborCinnosti3;
    private BigDecimal cistyMesicniPrijem;
    private Long pocetOsobVDomacnosti;
    private Long kraj;
    private Boolean cizinec;
    private BigDecimal sumaSaldVkladuAkt;
    private BigDecimal saldoVkladuMax;
    private Long delkaSporeniAkt;
    private Long delkaSporeniMax;
    private BigDecimal uverovaAngazovanostVRsts;
    private Long dobaVBance;
    private Long celkovyPocetKontraktu;
    private Long aktualniPocetKontraktu;
    private String variantaPu;
    private BigDecimal vyseUveru;
    private Boolean interneRefinancovani;
    private Boolean refinancovaniMimoRsts;
    private Long ucel1;
    private Long ucel2;
    private Long ucel3;
    private Boolean uverBezUvedeniManzelky;
    private BigDecimal ltv;
    private BigDecimal dsti;
    private BigDecimal dti;
    private BigDecimal splatkaAktualnihoUveruStredniSazba;
    private BigDecimal akontaceSporeni;
    private BigDecimal cbSkore;
    private Long cbKod;
    private BigDecimal solusDluh;
    private Long pocetDuveryhodnost;
    private Long nejvyssiUpominka;
    private Long pohlavi;
    private Long refDelkaVzam1;
    private Long refDelkaVzam2;
    private Long refDelkaVzam3;
    private Long druhBydleni;
    private String rozhodneDatumProUrceniParametruVypoctu;
    private Long refPrijemTyp1;
    private Long refPrijemTyp2;
    private Long refPrijemTyp3;
    private String datumVypoctu;
    private BigDecimal vysledneAplikacniSkore;
    private String verzeFunkce;
}
