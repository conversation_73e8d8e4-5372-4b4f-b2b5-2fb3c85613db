package cz.rb.camapp.kafka.model.cibis;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class Urg2Dto implements UrgCommon {
    private Long stupenURGRWA;
    private BigDecimal stupenURGoldRWA;
    private BigDecimal skoreURGRWA;
    private Long stupenURGUW;
    private BigDecimal skoreURGUW;
    private BigDecimal pouziteAplikacniSkore;
    private BigDecimal pouziteCBSkore;
    private String rozhodneDatumProUrceniParametruVypoctu;
}
