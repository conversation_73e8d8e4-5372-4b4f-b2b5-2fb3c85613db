package cz.rb.camapp.kafka.model.cibis;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class Prijem2Dto {
    @NotNull
    private Long poradi;
    @NotNull
    private Long typPrijmu;
    private BigDecimal uznatelnaCastkaPrijmu;
    private String menaPrijmu;
    private PrijemZamADap2Dto udajePrijmuZamADap;
    private FirmaPrijmu2Dto udajefirmyPrijmu;

}
