package cz.rb.camapp.kafka;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class KafkaProducer {

    @Autowired
    private KafkaTemplate<String, org.apache.avro.generic.GenericData.Record> kafkaTemplate;

    public void sendMessage(org.apache.avro.generic.GenericData.Record message) {

        var future =
                kafkaTemplate.send(KafkaTopic.PUSH_BAI_PENNY_PAYMENT_UPLOAD.name(), message);

        future.whenCompleteAsync((result, ex) ->
        {
            if (result != null)
            {
                log.info("Sent message=[{}] with offset=[{}]{}", message, result.getRecordMetadata().offset(), result.getProducerRecord());
            }
            if (ex != null)
            {
                log.error("Unable to send message=[{}] due to : {}", message, ex.getMessage(), ex);
            }
        });
    }
}
