package cz.rb.camapp.kafka.model.cibis;

import com.google.gson.annotations.SerializedName;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class RstsBslCancelMessageDto {
    public static final String SERIALIZED_NAME_HEADER = "header";

    @SerializedName("header")
    @NotNull
    @Valid
    private MessageHeaderRstsBsl header;
    public static final String SERIALIZED_NAME_BODY = "body";

    @SerializedName("body")
    @NotNull
    @Valid
    private MessageBodyRstsBslCancel body;
}
