package cz.rb.camapp.kafka;

import cz.equa.camapp.lovs.LovBankCzCode;
import cz.equa.camapp.process.Message;
import cz.equa.camapp.process.MessageCorrelationException;
import cz.equa.camapp.process.ProcessManager;
import cz.equa.camapp.service.paymentorderserviceinternal.model.PaymentDTO;
import cz.equa.camapp.utils.AccountUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.nio.ByteBuffer;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CountDownLatch;

@Component
@Slf4j
public class KafkaMessageListener {

    @Autowired
    private ProcessManager processManager;

    //fields for unit tests only
    protected CountDownLatch latch = new CountDownLatch(1);
    protected org.apache.avro.generic.GenericData.Record payload = null;

    @KafkaListener(
            topics = "PUSH_BAI_PENNY_PAYMENT_UPLOAD",
            groupId = "camunda-consumer-rb",
            containerFactory = "kafkaListenerContainerFactory",
            autoStartup = "${kafka.enabled:false}")
    public void greetingListener(Object objectMessage) {
        log.info("Received objectMessage: {}", objectMessage);
        ConsumerRecord recordMessage = (ConsumerRecord) objectMessage;
        log.info("Received recordMessage: {}", recordMessage);
        log.info("Received recordMessage value: {}", recordMessage.value());
        org.apache.avro.generic.GenericData.Record notificationMessageRecord = (org.apache.avro.generic.GenericData.Record) recordMessage.value();
        log.info("Received recordMessage null element: {}", notificationMessageRecord.get(0));

        final PaymentDTO payment = new PaymentDTO();

        payment.setDebtorAccNum(AccountUtils.combine(contertToString(notificationMessageRecord.get("debitnumberpart1")),
                contertToString(notificationMessageRecord.get("debitnumberpart2"))));
        payment.setDebtorBankCode(new LovBankCzCode(contertToString(notificationMessageRecord.get("debitbankcode"))));
        payment.setDebtorAccName(contertToString(notificationMessageRecord.get("debitname")));
        payment.setAmount(contertToBigDecimal(notificationMessageRecord.get("amount")));
        payment.setVariableSymbol(contertToString(notificationMessageRecord.get("debitvarsymbol")));

        log.info("Received message: {}", payment);
        notifyProcessFromKafka(payment);

        //for testing purposes
        this.payload = notificationMessageRecord;
        latch.countDown();
    }

    private BigDecimal contertToBigDecimal(Object objectValue) {
        if (objectValue == null) {
            return null;
        } else {
            return new BigDecimal(new BigInteger(((ByteBuffer) objectValue).array()), 3);
        }
    }

    private String contertToString(Object objectValue) {
        if (objectValue == null) {
            return null;
        } else {
            return objectValue.toString();
        }
    }

    private void notifyProcessFromKafka(final PaymentDTO payment) {
        try {
            final Map<String, Object> processVariables = new HashMap<>();
            processVariables.put("pennyTransaction", payment);
            correlateMessage(payment.getVariableSymbol(), Message.PERSON_VERIFIED, processVariables);
        } catch (MessageCorrelationException ex) {
            log.error("Could not notify PennyPayment with variable symbol {}", payment.getVariableSymbol(), ex);
        }
    }

    private void correlateMessage(final String busApplId,
                                  final Message message,
                                  final Map<String, Object> addedProcessVariables) throws MessageCorrelationException {
        final Map<String, Object> correlation = new HashMap<>();
        correlation.put("busApplId", busApplId);
        processManager.notify(message, correlation, addedProcessVariables);
    }
}
