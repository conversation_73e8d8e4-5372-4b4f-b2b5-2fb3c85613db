package cz.rb.tif.cus_00050_contact.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;

import java.io.Serializable;

@Deprecated
// 11.3.2023 - it's bacause of old processes, could be deleted in 6 months
@JsonIgnoreProperties(ignoreUnknown = true)
@XmlAccessorType(XmlAccessType.FIELD)
public class EmailToInsertDTO implements Serializable {
    public static final String SBL_EMAIL_SLOT = "EMAIL";

    private String address;
    private String addressType;
    private Boolean isPreferred = false;

    public EmailToInsertDTO() {
    }

    public EmailToInsertDTO(String address, String addressType, Boolean isPreferred) {
        this.address = address;
        this.addressType = addressType;
        this.isPreferred = isPreferred;
    }

    @JsonIgnore
    public String getContactType() {
        return "EMAIL_ADDRESS";
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getAddressType() {
        return addressType;
    }

    public Boolean getIsPreferred() {
        return isPreferred;
    }

    public void setPreferred(Boolean preffered) {
        isPreferred = preffered;
    }

    public void setAddressType(String addressType) {
        this.addressType = addressType;
    }
}
