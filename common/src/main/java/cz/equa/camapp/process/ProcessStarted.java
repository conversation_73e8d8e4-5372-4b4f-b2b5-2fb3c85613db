package cz.equa.camapp.process;

import org.camunda.bpm.engine.runtime.ProcessInstance;

public class ProcessStarted {
    
    private ProcessStartResult processStartResult;        
    private ProcessInstance processInstance;
    
    private ProcessStarted(ProcessStartResult processStartResult, ProcessInstance processInstance) {
        this.processStartResult = processStartResult;
        this.processInstance = processInstance;
    }
    
    private ProcessStarted(ProcessStartResult processStartResult) {
        this.processStartResult = processStartResult;
    }
    
    private ProcessStarted() {
    }
    
    public static ProcessStarted OK(ProcessInstance processInstance) {
        return new ProcessStarted(ProcessStartResult.OK, processInstance);
    }
    
    public static ProcessStarted OK() {
        return new ProcessStarted(ProcessStartResult.OK);
    }
    
    public static ProcessStarted NOK() {
        return new ProcessStarted(ProcessStartResult.NOK);
    }
    
    public static ProcessStarted PROCESS_EXISTS() {
        return new ProcessStarted(ProcessStartResult.PROCESS_EXISTS);
    }

    public boolean isNOK() {
        return processStartResult.isNOK();
    }
    
    public boolean isOK() {
        return processStartResult.isOK();
    }
    
    public ProcessStartResult getProcessStartResult() {
        return processStartResult;
    }

    public ProcessInstance getProcessInstance() {
        return processInstance;
    }
}
