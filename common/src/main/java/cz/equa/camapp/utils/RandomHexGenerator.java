package cz.equa.camapp.utils;

import java.security.SecureRandom;

public class RandomHexGenerator {
    private static final SecureRandom random = new SecureRandom();
    private static final String HEX_CHARS = "0123456789abcdef";

    public static String randomHex(int length) {
        StringBuilder hexString = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            int index = random.nextInt(HEX_CHARS.length());
            hexString.append(HEX_CHARS.charAt(index));
        }
        return hexString.toString();
    }
}
