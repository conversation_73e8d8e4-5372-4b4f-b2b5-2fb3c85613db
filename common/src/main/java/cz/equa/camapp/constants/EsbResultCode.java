package cz.equa.camapp.constants;

/**
 * Esb result codes /OK/WARN/ERROR/FATAL
 *
 * <AUTHOR>
 */
public enum EsbResultCode {
	OK(0),        // bez chyby
	WARNING(4),    // varování, business chyba, v procesu lze pokračovat,
	ERROR(8),    //chyba, business chyba, v procesu nelze pokračovat,
	FATAL(255);  // kritická chyba, technická chyba, nezdokumentované sel<PERSON>ání, v procesu nelze pokračovat

	private int code;

	private EsbResultCode(int code) {
		this.code = code;
	}

	public int getCode() {
		return code;
	}

	public static EsbResultCode valueOf(int code) {
		for (EsbResultCode resultCode : values()) {
			if (resultCode.getCode() == code) {
				return resultCode;
			}
		}
		return null;

	}
}
