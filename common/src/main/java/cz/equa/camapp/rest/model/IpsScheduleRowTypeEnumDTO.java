package cz.equa.camapp.rest.model;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@AllArgsConstructor
@XmlAccessorType(XmlAccessType.FIELD)
@Getter
public enum IpsScheduleRowTypeEnumDTO {
    _ERP_N_V_RU("Čerpání úvěru"),

    PLATBY_NA_STAVEBN_SPO_EN_("Platby na stavební spoření"),

    VKLAD("Vklad"),

    SPL_TKA_PU("Splátka PU"),

    SPL_TKA_PU_A_VKLADY("Splátka PU a Vklady"),

    SPL_TKA_SU("Splátka SU"),

    CELKEM("<PERSON>lkem");

    private String value;


    public static IpsScheduleRowTypeEnumDTO fromValue(String value) {
        if (value == null) {
            return null;
        }

        for (IpsScheduleRowTypeEnumDTO type : values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }

        throw new IllegalArgumentException("Unkown type: " + value);
    }
}
