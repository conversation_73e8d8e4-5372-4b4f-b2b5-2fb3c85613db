package cz.equa.camapp.rest.model;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@XmlAccessorType(XmlAccessType.FIELD)
@Data
public class CalculateBuildingLoanIPSScheduleRowDTO implements Serializable {

    private String periodID;
    private IpsScheduleRowTypeEnumDTO ipsScheduleRowType;
    private BigDecimal finaAmt;
    private BigDecimal finalIR;
    private BigDecimal finaInsAmt;
    private BigDecimal repaidInterest;
    private BigDecimal otherCosts;
    private BigDecimal repaidPrincipal;
    private BigDecimal otherCostsExluded;
    private BigDecimal savingsIncome;
    private BigDecimal remainingPrincipal;
    private BigDecimal remainingInterest;
    private BigDecimal savings;
}
