package cz.equa.camapp.service.applicationservice.model;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;

import java.io.Serializable;
import java.math.BigInteger;

@XmlAccessorType(XmlAccessType.FIELD)
public class SignChannelDTO implements Serializable {
	private BigInteger applVariantSignCnlKey;

	private Long applVariantKey;

	private String signCnlId;
	private String signPosId;
	private Boolean applVariantSignCnlDelFlag;

	public SignChannelDTO() {
        //needed for deserialization
	}

	public BigInteger getApplVariantSignCnlKey() {
		return applVariantSignCnlKey;
	}

	public void setApplVariantSignCnlKey(BigInteger applVariantSignCnlKey) {
		this.applVariantSignCnlKey = applVariantSignCnlKey;
	}

	public String getSignCnlId() {
		return signCnlId;
	}

	public void setSignCnlId(String signCnlId) {
		this.signCnlId = signCnlId;
	}

	public Long getApplVariantKey() {
		return applVariantKey;
	}

	public void setApplVariantKey(Long applVariantKey) {
		this.applVariantKey = applVariantKey;
	}

	public Boolean getApplVariantSignCnlDelFlag() {
		return applVariantSignCnlDelFlag;
	}

	public void setApplVariantSignCnlDelFlag(Boolean applVariantSignCnlDelFlag) {
		this.applVariantSignCnlDelFlag = applVariantSignCnlDelFlag;
	}

    public String getSignPosId() {
        return signPosId;
    }

    public void setSignPosId(String signPosId) {
        this.signPosId = signPosId;
    }
}
