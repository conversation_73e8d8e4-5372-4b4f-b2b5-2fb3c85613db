package cz.equa.camapp.service.applicationservice.model;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;

import java.io.Serializable;
import java.math.BigDecimal;

@XmlAccessorType(XmlAccessType.FIELD)
public class MortApplVariantInsDTO implements Serializable {
    private Long insurApplVrntInsurKey;
    private String insurTpId;
    private BigDecimal insurAsesmntBase;
    private BigDecimal insurPremAmt;
    private BigDecimal insurPremAmtPct;
    private Boolean insurSelFlag;
    private Boolean insurDispoFlag;
    private Boolean insurDstiFlag;
    private Boolean insurRpsnFlag;

    public MortApplVariantInsDTO() {
        // needed for deserialization
    }
    public Long getInsurApplVrntInsurKey() {
        return insurApplVrntInsurKey;
    }

    public void setInsurApplVrntInsurKey(Long insurApplVrntInsurKey) {
        this.insurApplVrntInsurKey = insurApplVrntInsurKey;
    }

    public String getInsurTpId() {
        return insurTpId;
    }

    public void setInsurTpId(String insurTpId) {
        this.insurTpId = insurTpId;
    }

    public BigDecimal getInsurAsesmntBase() {
        return insurAsesmntBase;
    }

    public void setInsurAsesmntBase(BigDecimal insurAsesmntBase) {
        this.insurAsesmntBase = insurAsesmntBase;
    }

    public BigDecimal getInsurPremAmt() {
        return insurPremAmt;
    }

    public void setInsurPremAmt(BigDecimal insurPremAmt) {
        this.insurPremAmt = insurPremAmt;
    }

    public BigDecimal getInsurPremAmtPct() {
        return insurPremAmtPct;
    }

    public void setInsurPremAmtPct(BigDecimal insurPremAmtPct) {
        this.insurPremAmtPct = insurPremAmtPct;
    }

    public Boolean getInsurSelFlag() {
        return insurSelFlag;
    }

    public void setInsurSelFlag(Boolean insurSelFlag) {
        this.insurSelFlag = insurSelFlag;
    }

    public Boolean getInsurDispoFlag() {
        return insurDispoFlag;
    }

    public void setInsurDispoFlag(Boolean insurDispoFlag) {
        this.insurDispoFlag = insurDispoFlag;
    }

    public Boolean getInsurDstiFlag() {
        return insurDstiFlag;
    }

    public void setInsurDstiFlag(Boolean insurDstiFlag) {
        this.insurDstiFlag = insurDstiFlag;
    }

    public Boolean getInsurRpsnFlag() {
        return insurRpsnFlag;
    }

    public void setInsurRpsnFlag(Boolean insurRpsnFlag) {
        this.insurRpsnFlag = insurRpsnFlag;
    }
}
