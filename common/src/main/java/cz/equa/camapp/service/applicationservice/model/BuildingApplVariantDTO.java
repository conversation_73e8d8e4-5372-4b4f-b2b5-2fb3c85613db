package cz.equa.camapp.service.applicationservice.model;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Data
@ToString
public class BuildingApplVariantDTO implements Serializable {
    private Long applVariantKey;
    private String applVariantTpId;
    private Boolean applVariantSignCnlFlag;
    private String busProdTpId;
    private String busProdSubTpId;
    private BigDecimal finaAmt;
    private BigDecimal instlAmt;
    private Long instlCnt;
    private LocalDate maturityDate;
    private BigDecimal intrsRx;
    private BigDecimal rpsn;
    private String camCode;
    private BigDecimal minAvlblAmt;
    private BigDecimal minInstlAmt;
    private Long minInstlCnt;
    private BigDecimal maxAvlblAmt;
    private BigDecimal maxInstlAmt;
    private Long maxInstlCnt;
    private BigDecimal upsellAmt;
    private BigDecimal maxUpsellAmt;
    private String ccyId;
    private BigDecimal totRpmtAmt;
    private BigDecimal refiSavedAmt;
    private String accNumPrefix;
    private String accNum;
    private String accBankCode;
    private BigDecimal actSumInstlAmt;
    private BigDecimal consLoanInstlAmt;
    private BigDecimal dayIntrsAmt;
    private Boolean intrsDscntFlag;
    private BigDecimal intnOblgtnAmt;
    private BigDecimal extnOblgtnAmt;
    private LocalDate repreDrawDate;
    private LocalDate repreMaturityDate;
    private BigDecimal repreInt;
    private BigDecimal repreInt3M;
    private BigDecimal costs3M;
    private Boolean delFlag;
    private String declaredPurpose;
    private BigDecimal maxExtOblgtnAmt;
    private BigDecimal minIrForFinalization;
    private BigDecimal origScOfrIntrsRx;
    private BigDecimal payCpcyForFinal;
    private BigDecimal maxAvlblTopup;
    private String prodIntrsCodeId;
    private List<BuildingApplVariantPurposeDTO> loanPurposes = new ArrayList<>();
    private List<BuildingLoanCategoryDTO> loanCategories = new ArrayList<>();
    private List<VariantParameterDTO> variantParameters = new ArrayList<>();
    private List<CtSignChannelDTO> signChannels = new ArrayList<>();
    private List<BuildingApplVariantInsDTO> insurances = new ArrayList<>();
    private List<BuildingApplVariantSurDTO> surcharges = new ArrayList<>();
    private String fixPeriod;
    private String ecoInvestTpId;
    private BigDecimal ecoInvestShare;
    private Boolean closeRelCoFin;

    public BuildingApplVariantDTO() {
        // needed for deserialization
    }
}
