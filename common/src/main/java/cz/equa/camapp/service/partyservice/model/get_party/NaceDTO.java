package cz.equa.camapp.service.partyservice.model.get_party;

import cz.equa.camapp.lovs.LovNace;
import cz.equa.camapp.lovs.LovXMLAdapter;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import java.io.Serializable;

@XmlAccessorType(XmlAccessType.FIELD)
public class NaceDTO implements Serializable {

	@XmlJavaTypeAdapter(LovXMLAdapter.class)
	private LovNace naceCodeId;
	private boolean naceMain;
	private String naceSrcId;
	private boolean naceDeleted;

	public LovNace getNaceCodeId() {
		return naceCodeId;
	}

	public void setNaceCodeId(LovNace naceCodeId) {
		this.naceCodeId = naceCodeId;
	}

	public boolean isNaceMain() {
		return naceMain;
	}

	public void setNaceMain(boolean naceMain) {
		this.naceMain = naceMain;
	}

	public String getNaceSrcId() {
		return naceSrcId;
	}

	public void setNaceSrcId(String naceSrcId) {
		this.naceSrcId = naceSrcId;
	}

	public boolean isNaceDeleted() {
		return naceDeleted;
	}

	public void setNaceDeleted(boolean naceDeleted) {
		this.naceDeleted = naceDeleted;
	}
}
