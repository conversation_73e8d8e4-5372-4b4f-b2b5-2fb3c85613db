package cz.equa.camapp.service.applicationservice.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Schema(name = "ChannelChangeDTO", title = "ChannelChangeDTO", description = "Payload for event <code>onbStatusChange</code>.")
@Data
public class ChannelChangeDTO implements Serializable {
    @Schema(name = "channel", description = "channel", requiredMode = Schema.RequiredMode.REQUIRED, example = "ADB")
    private String channel;

    @Schema(name = "posId", description = "posId", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "18000000")
    private String posId;
}
