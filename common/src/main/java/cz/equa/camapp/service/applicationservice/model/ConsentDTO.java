package cz.equa.camapp.service.applicationservice.model;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.OffsetDateTime;

@XmlAccessorType(XmlAccessType.FIELD)
@Data
@NoArgsConstructor
public class ConsentDTO {
    private String consentTpId;
    private String consentSubTpId;
    private OffsetDateTime timestamp;
    private String status;
}
