package cz.equa.camapp.service.partyservice.model.get_party;

import cz.equa.camapp.soap.xjb.adapter.DateAdapter;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import java.io.Serializable;
import java.math.BigInteger;
import java.time.LocalDate;

@XmlAccessorType(XmlAccessType.FIELD)
public class AddressContactDTO implements Serializable {

	private String addrSrcId;
	private String addrPurpId;
	private String addrCntryId;
	private String addrStreetName;
	private String addrStreetNum;
	private String addrCity;
	private String postalCode;
	private String addresee;
    
    @XmlJavaTypeAdapter(DateAdapter.class)
	private LocalDate addrValidDate;
    
	private BigInteger addrVerifiedFlag;
	private Boolean addrDeleted;
	private String detailInformation;

	public String getAddrSrcId() {
		return addrSrcId;
	}

	public void setAddrSrcId(String addrSrcId) {
		this.addrSrcId = addrSrcId;
	}

	public String getAddrPurpId() {
		return addrPurpId;
	}

	public void setAddrPurpId(String addrPurpId) {
		this.addrPurpId = addrPurpId;
	}

	public String getAddrCntryId() {
		return addrCntryId;
	}

	public void setAddrCntryId(String addrCntryId) {
		this.addrCntryId = addrCntryId;
	}

	public String getAddrStreetName() {
		return addrStreetName;
	}

	public void setAddrStreetName(String addrStreetName) {
		this.addrStreetName = addrStreetName;
	}

	public String getAddrStreetNum() {
		return addrStreetNum;
	}

	public void setAddrStreetNum(String addrStreetNum) {
		this.addrStreetNum = addrStreetNum;
	}

	public String getAddrCity() {
		return addrCity;
	}

	public void setAddrCity(String addrCity) {
		this.addrCity = addrCity;
	}

	public String getPostalCode() {
		return postalCode;
	}

	public void setPostalCode(String postalCode) {
		this.postalCode = postalCode;
	}

	public String getAddresee() {
		return addresee;
	}

	public void setAddresee(String addresee) {
		this.addresee = addresee;
	}

	public LocalDate getAddrValidDate() {
		return addrValidDate;
	}

	public void setAddrValidDate(LocalDate addrValidDate) {
		this.addrValidDate = addrValidDate;
	}

	public BigInteger getAddrVerifiedFlag() {
		return addrVerifiedFlag;
	}

	public void setAddrVerifiedFlag(BigInteger addrVerifiedFlag) {
		this.addrVerifiedFlag = addrVerifiedFlag;
	}

	public Boolean getAddrDeleted() {
		return addrDeleted;
	}

	public void setAddrDeleted(Boolean addrDeleted) {
		this.addrDeleted = addrDeleted;
	}

	public String getDetailInformation() {
		return detailInformation;
	}

	public void setDetailInformation(String detailInformation) {
		this.detailInformation = detailInformation;
	}
}
