package cz.equa.camapp.service.applicationservice.model;

import cz.equa.camapp.soap.xjb.adapter.OffsetDateTimeAdapter;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.OffsetDateTime;

@XmlAccessorType(XmlAccessType.FIELD)
public class PersonDetSetDTO implements Serializable {
    private Long applKey;
    private Integer mntdChildCnt;
    private Integer mntdPersonCnt;
    private BigDecimal netInc;
    private BigDecimal netIncMin;
    private BigDecimal netIncEstm;
    private BigDecimal netIncVerif;
    private BigDecimal netIncFinal;
    private BigDecimal mortExpSum;
    private BigDecimal loanExpSum;
    private BigDecimal odExpSum;
    private BigDecimal othExpSum;
    @XmlJavaTypeAdapter(OffsetDateTimeAdapter.class)
    private OffsetDateTime crntAddrValidDate;
    private String creatorApplId;

    public PersonDetSetDTO() {
        // needed for deserialization
    }
    public Long getApplKey() {
        return applKey;
    }

    public void setApplKey(Long applKey) {
        this.applKey = applKey;
    }

    public Integer getMntdChildCnt() {
        return mntdChildCnt;
    }

    public void setMntdChildCnt(Integer mntdChildCnt) {
        this.mntdChildCnt = mntdChildCnt;
    }

    public Integer getMntdPersonCnt() {
        return mntdPersonCnt;
    }

    public void setMntdPersonCnt(Integer mntdPersonCnt) {
        this.mntdPersonCnt = mntdPersonCnt;
    }

    public BigDecimal getNetInc() {
        return netInc;
    }

    public void setNetInc(BigDecimal netInc) {
        this.netInc = netInc;
    }

    public BigDecimal getNetIncMin() {
        return netIncMin;
    }

    public void setNetIncMin(BigDecimal netIncMin) {
        this.netIncMin = netIncMin;
    }

    public BigDecimal getNetIncEstm() {
        return netIncEstm;
    }

    public void setNetIncEstm(BigDecimal netIncEstm) {
        this.netIncEstm = netIncEstm;
    }

    public BigDecimal getNetIncVerif() {
        return netIncVerif;
    }

    public void setNetIncVerif(BigDecimal netIncVerif) {
        this.netIncVerif = netIncVerif;
    }

    public BigDecimal getNetIncFinal() {
        return netIncFinal;
    }

    public void setNetIncFinal(BigDecimal netIncFinal) {
        this.netIncFinal = netIncFinal;
    }

    public BigDecimal getMortExpSum() {
        return mortExpSum;
    }

    public void setMortExpSum(BigDecimal mortExpSum) {
        this.mortExpSum = mortExpSum;
    }

    public BigDecimal getLoanExpSum() {
        return loanExpSum;
    }

    public void setLoanExpSum(BigDecimal loanExpSum) {
        this.loanExpSum = loanExpSum;
    }

    public BigDecimal getOdExpSum() {
        return odExpSum;
    }

    public void setOdExpSum(BigDecimal odExpSum) {
        this.odExpSum = odExpSum;
    }

    public BigDecimal getOthExpSum() {
        return othExpSum;
    }

    public void setOthExpSum(BigDecimal othExpSum) {
        this.othExpSum = othExpSum;
    }

    public OffsetDateTime getCrntAddrValidDate() {
        return crntAddrValidDate;
    }

    public void setCrntAddrValidDate(OffsetDateTime crntAddrValidDate) {
        this.crntAddrValidDate = crntAddrValidDate;
    }

    public String getCreatorApplId() {
        return creatorApplId;
    }

    public void setCreatorApplId(String creatorApplId) {
        this.creatorApplId = creatorApplId;
    }
}
