package cz.equa.camapp.service.applicationservice.model;

import cz.equa.camapp.lovs.LovBusProdSubTp;
import cz.equa.camapp.lovs.LovBusProdTp;
import cz.equa.camapp.lovs.LovInvsPurpAprchTp;
import cz.equa.camapp.lovs.LovXMLAdapter;
import cz.equa.camapp.soap.xjb.adapter.DateAdapter;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDate;

@XmlAccessorType(XmlAccessType.FIELD)
public class InvestmentDTO implements Serializable {
    
	private String invProdKey;
	
    @XmlJavaTypeAdapter(LovXMLAdapter.class)
	private LovBusProdTp invProdTp;
	
    @XmlJavaTypeAdapter(LovXMLAdapter.class)
	private LovBusProdSubTp invProdSubTp;
	
    @XmlJavaTypeAdapter(LovXMLAdapter.class)
	private LovInvsPurpAprchTp approach;
	
    private BigDecimal approachBond;
	private BigDecimal approachStock;
	private BigDecimal oneshotAmount;
	private BigDecimal regularAmount;
	private BigDecimal estValue;
	private BigDecimal estRevenue;
    
    @XmlJavaTypeAdapter(DateAdapter.class)
	private LocalDate estMaturityDate;
	
    private BigDecimal microCharge;
	private BigInteger drawDuration;
	private BigInteger cardPayment3MAvg;
	private Boolean callMeFlag;

	public String getInvProdKey() {
		return invProdKey;
	}

	public void setInvProdKey(String invProdKey) {
		this.invProdKey = invProdKey;
	}

	public LovBusProdTp getInvProdTp() {
		return invProdTp;
	}

	public void setInvProdTp(LovBusProdTp invProdTp) {
		this.invProdTp = invProdTp;
	}

	public LovBusProdSubTp getInvProdSubTp() {
		return invProdSubTp;
	}

	public void setInvProdSubTp(LovBusProdSubTp invProdSubTp) {
		this.invProdSubTp = invProdSubTp;
	}

	public LovInvsPurpAprchTp getApproach() {
		return approach;
	}

	public void setApproach(LovInvsPurpAprchTp approach) {
		this.approach = approach;
	}

	public BigDecimal getApproachBond() {
		return approachBond;
	}

	public void setApproachBond(BigDecimal approachBond) {
		this.approachBond = approachBond;
	}

	public BigDecimal getApproachStock() {
		return approachStock;
	}

	public void setApproachStock(BigDecimal approachStock) {
		this.approachStock = approachStock;
	}

	public BigDecimal getOneshotAmount() {
		return oneshotAmount;
	}

	public void setOneshotAmount(BigDecimal oneshotAmount) {
		this.oneshotAmount = oneshotAmount;
	}

	public BigDecimal getRegularAmount() {
		return regularAmount;
	}

	public void setRegularAmount(BigDecimal regularAmount) {
		this.regularAmount = regularAmount;
	}

	public BigDecimal getEstValue() {
		return estValue;
	}

	public void setEstValue(BigDecimal estValue) {
		this.estValue = estValue;
	}

	public BigDecimal getEstRevenue() {
		return estRevenue;
	}

	public void setEstRevenue(BigDecimal estRevenue) {
		this.estRevenue = estRevenue;
	}

	public LocalDate getEstMaturityDate() {
		return estMaturityDate;
	}

	public void setEstMaturityDate(LocalDate estMaturityDate) {
		this.estMaturityDate = estMaturityDate;
	}

	public BigDecimal getMicroCharge() {
		return microCharge;
	}

	public void setMicroCharge(BigDecimal microCharge) {
		this.microCharge = microCharge;
	}

	public BigInteger getDrawDuration() {
		return drawDuration;
	}

	public void setDrawDuration(BigInteger drawDuration) {
		this.drawDuration = drawDuration;
	}

	public BigInteger getCardPayment3MAvg() {
		return cardPayment3MAvg;
	}

	public void setCardPayment3MAvg(BigInteger cardPayment3MAvg) {
		this.cardPayment3MAvg = cardPayment3MAvg;
	}

	public Boolean getCallMeFlag() {
		return callMeFlag;
	}

	public void setCallMeFlag(Boolean callMeFlag) {
		this.callMeFlag = callMeFlag;
	}
}
