package cz.equa.camapp.service.applicationservice.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RolloverMortgageServicingDTO implements Serializable {
    public static final String JSON_PROPERTY_ROLLOV_OFR_ID = "rollovOfrId";
    private String rollovOfrId;

    public static final String JSON_PROPERTY_ROLLOV_OFR_INTRS_ID = "rollovOfrIntrsId";
    private String rollovOfrIntrsId;

    public static final String JSON_PROPERTY_ROLLOV_OFR_CRE_DATE = "rollovOfrCreDate";
    private LocalDate rollovOfrCreDate;

    public static final String JSON_PROPERTY_ROLLOV_OFR_TAKE_DATE = "rollovOfrTakeDate";
    private LocalDate rollovOfrTakeDate;

    public static final String JSON_PROPERTY_ROLLOV_OFR_VALID_TO = "rollovOfrValidTo";
    private LocalDate rollovOfrValidTo;

    public static final String JSON_PROPERTY_ROLLOV_RX_TP_ID = "rollovRxTpId";
    private String rollovRxTpId;

    public static final String JSON_PROPERTY_ROLLOV_RX_FIX_PER_TP_ID = "rollovRxFixPerTpId";
    private String rollovRxFixPerTpId;

    public static final String JSON_PROPERTY_ROLLOV_SURCHRG_RX = "rollovSurchrgRx";
    private BigDecimal rollovSurchrgRx;

    public static final String JSON_PROPERTY_ROLLOV_TOT_INTRS_RX = "rollovTotIntrsRx";
    private BigDecimal rollovTotIntrsRx;

    public static final String JSON_PROPERTY_ROLLOV_INSTL_AMT = "rollovInstlAmt";
    private BigDecimal rollovInstlAmt;

    public static final String JSON_PROPERTY_ROLLOV_FIRST_TOUCH_POINT_BNKR = "rollovFirstTouchPointBnkr";
    private String rollovFirstTouchPointBnkr;

    public static final String JSON_PROPERTY_ROLLOV_SEL_FLAG = "rollovSelFlag";
    private Boolean rollovSelFlag;
}
