package cz.equa.camapp.service.applicationservice.model;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;

@Schema(name = "IncomeVerificationStatementProcessingResultDTO", title = "IncomeVerificationStatementProcessingResultDTO", description = "Payload for event <code>clIncomeVerification_STATEMENT_PROCESSING_DONE</code>.")
public class IncomeVerificationStatementProcessingResultDTO implements Serializable {

    @Schema(name = "result", description = "result of verification", requiredMode = Schema.RequiredMode.REQUIRED, example = "statements_AUTO")
    private String result;

    public String getResult() {
        return result;
    }

    public IncomeVerificationStatementProcessingResultDTO setResult(String result) {
        this.result = result;
        return this;
    }
}
