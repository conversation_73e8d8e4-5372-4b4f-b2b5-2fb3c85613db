package cz.equa.camapp.service.product_service;

import cz.equa.camapp.lovs.LovAddtSrvcPvdrTp;
import cz.equa.camapp.lovs.LovAddtSrvcTermntnRsnTp;
import cz.equa.camapp.lovs.LovXMLAdapter;
import cz.equa.camapp.service.partyservice.model.get_party.SrcIdDTO;
import cz.equa.camapp.soap.xjb.adapter.DateTimeAdapter;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import java.io.Serializable;
import java.time.ZonedDateTime;

@XmlAccessorType(XmlAccessType.FIELD)
public class AbstractInsuranceDTO extends AbstractProductDTO implements Serializable {
    
    private SrcIdDTO relatedProdSrcId;
    private Long relatedProdApplId;
    private String relatedAccountNumber;
    
    @XmlJavaTypeAdapter(DateTimeAdapter.class)
    private ZonedDateTime validFrom;
    
    @XmlJavaTypeAdapter(LovXMLAdapter.class)
    private LovAddtSrvcPvdrTp provider;
    
    @XmlJavaTypeAdapter(LovXMLAdapter.class)
    private LovAddtSrvcTermntnRsnTp terminationReason;
    
    @XmlJavaTypeAdapter(DateTimeAdapter.class)
    private ZonedDateTime terminationDate;
    
    private SrcIdDTO applSrcId;

    public SrcIdDTO getRelatedProdSrcId() {
        return relatedProdSrcId;
    }

    public void setRelatedProdSrcId(SrcIdDTO relatedProdSrcId) {
        this.relatedProdSrcId = relatedProdSrcId;
    }

    public Long getRelatedProdApplId() {
        return relatedProdApplId;
    }

    public void setRelatedProdApplId(Long relatedProdApplId) {
        this.relatedProdApplId = relatedProdApplId;
    }

    public String getRelatedAccountNumber() {
        return relatedAccountNumber;
    }

    public void setRelatedAccountNumber(String relatedAccountNumber) {
        this.relatedAccountNumber = relatedAccountNumber;
    }

    public ZonedDateTime getValidFrom() {
        return validFrom;
    }

    public void setValidFrom(ZonedDateTime validFrom) {
        this.validFrom = validFrom;
    }

    public LovAddtSrvcPvdrTp getProvider() {
        return provider;
    }

    public void setProvider(LovAddtSrvcPvdrTp provider) {
        this.provider = provider;
    }

    public LovAddtSrvcTermntnRsnTp getTerminationReason() {
        return terminationReason;
    }

    public void setTerminationReason(LovAddtSrvcTermntnRsnTp terminationReason) {
        this.terminationReason = terminationReason;
    }

    public ZonedDateTime getTerminationDate() {
        return terminationDate;
    }

    public void setTerminationDate(ZonedDateTime terminationDate) {
        this.terminationDate = terminationDate;
    }

    public SrcIdDTO getApplSrcId() {
        return applSrcId;
    }

    public void setApplSrcId(SrcIdDTO applSrcId) {
        this.applSrcId = applSrcId;
    }

    @Override
    public String toString() {
        return "AbstractInsuranceDTO{" +
                "relatedProdSrcId=" + relatedProdSrcId +
                ", relatedProdApplId=" + relatedProdApplId +
                ", relatedAccountNumber='" + relatedAccountNumber + '\'' +
                ", validFrom=" + validFrom +
                ", provider=" + provider +
                ", terminationReason=" + terminationReason +
                ", terminationDate=" + terminationDate +
                ", applSrcId=" + applSrcId +
                ", prodSrcId=" + prodSrcId +
                ", status=" + status +
                ", contrBusId='" + contrBusId + '\'' +
                ", prodClass=" + prodClass +
                ", prodTp=" + prodTp +
                ", busProdSubTp=" + busProdSubTp +
                ", firstTouchPoint='" + firstTouchPoint + '\'' +
                ", fullfilmentCnl='" + fullfilmentCnl + '\'' +
                ", createDate=" + createDate +
                ", closeDate=" + closeDate +
                ", descr='" + descr + '\'' +
                '}';
    }
}
