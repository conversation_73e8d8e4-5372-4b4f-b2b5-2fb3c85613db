package cz.equa.camapp.service.applicationservice.model;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;

import java.io.Serializable;
import java.util.List;

@XmlAccessorType(XmlAccessType.FIELD)
public class ApplVarianParametersDTO implements Serializable {
    
    private List<VariantParameterDTO> allVariantParameters;

    public ApplVarianParametersDTO() {
        // needed for deserialization
    }
    
    public ApplVarianParametersDTO(List<VariantParameterDTO> allVariantParameters) {
        this.allVariantParameters = allVariantParameters;
    }

    public void setAllVariantParameters(List<VariantParameterDTO> allVariantParameters) {
        this.allVariantParameters = allVariantParameters;
    }
    
    public List<VariantParameterDTO> getAllVariantParameters() {
        return allVariantParameters;
    }
}
