package cz.equa.camapp.service.applicationservice.model;

import cz.equa.camapp.soap.xjb.adapter.OffsetDateTimeAdapter;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import java.io.Serializable;
import java.math.BigInteger;
import java.time.OffsetDateTime;
import java.util.List;

@XmlAccessorType(XmlAccessType.FIELD)
public class PersonIncomeVerificationDTO implements Serializable {
    
    private BigInteger instPtIncVerifKey;
    private String verifSource;
    private String personIncomeVerificationResultId;
    private String providerCode;
    
    @XmlJavaTypeAdapter(OffsetDateTimeAdapter.class)
    private OffsetDateTime verifTime;
    
    private String advisorId;
    private Boolean refreshAccounts;
    private List<AccountDTO> accounts;

    private String psdUserId;

    public BigInteger getInstPtIncVerifKey() {
        return instPtIncVerifKey;
    }

    public void setInstPtIncVerifKey(BigInteger instPtIncVerifKey) {
        this.instPtIncVerifKey = instPtIncVerifKey;
    }

    public String getVerifSource() {
        return verifSource;
    }

    public void setVerifSource(String verifSource) {
        this.verifSource = verifSource;
    }

    public String getPersonIncomeVerificationResultId() {
        return personIncomeVerificationResultId;
    }

    public void setPersonIncomeVerificationResultId(String personIncomeVerificationResultId) {
        this.personIncomeVerificationResultId = personIncomeVerificationResultId;
    }

    public String getProviderCode() {
        return providerCode;
    }

    public void setProviderCode(String providerCode) {
        this.providerCode = providerCode;
    }

    public OffsetDateTime getVerifTime() {
        return verifTime;
    }

    public void setVerifTime(OffsetDateTime verifTime) {
        this.verifTime = verifTime;
    }

    public String getAdvisorId() {
        return advisorId;
    }

    public void setAdvisorId(String advisorId) {
        this.advisorId = advisorId;
    }

    public Boolean getRefreshAccounts() {
        return refreshAccounts;
    }

    public void setRefreshAccounts(Boolean refreshAccounts) {
        this.refreshAccounts = refreshAccounts;
    }

    public List<AccountDTO> getAccounts() {
        return accounts;
    }

    public void setAccounts(List<AccountDTO> accounts) {
        this.accounts = accounts;
    }

    public String getPsdUserId() {
        return psdUserId;
    }

    public void setPsdUserId(String psdUserId) {
        this.psdUserId = psdUserId;
    }
}
