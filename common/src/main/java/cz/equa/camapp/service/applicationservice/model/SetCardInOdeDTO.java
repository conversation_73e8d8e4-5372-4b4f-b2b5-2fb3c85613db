package cz.equa.camapp.service.applicationservice.model;

import cz.equa.camapp.soap.xjb.adapter.OffsetDateTimeAdapter;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;

@XmlAccessorType(XmlAccessType.FIELD)
public class SetCardInOdeDTO implements Serializable {
	private Long prodInstKey;
	private String prodInstSrcId;
	private String prodInstSrcSysId;
	private String busProdTpId;
	private String busProdSubTpId;
	private String mprodId;
	private String contrNum;
	private BigDecimal lmtAmt;
	private LocalDate signDate;
	private LocalDate startDate;
	private LocalDate termntnDate;
	private Boolean delFlag;
	private String accSrcId;
	private String accSrcSysId;
	private String accNumPrefix;
	private String accNum;
	private Long plasticCardInstKey;
	private String plasticCardInstSrcId;
	private String viewableCardNum;
	private String cardName;
	private String cardTpId;
	private String cardStatId;
        
        @XmlJavaTypeAdapter(OffsetDateTimeAdapter.class)
	private OffsetDateTime actDate;
        
        @XmlJavaTypeAdapter(OffsetDateTimeAdapter.class)
	private OffsetDateTime expiresEnd;
        
	private String rpcProdCode;
	private String issuer;
	private String issuerBrn;
	private String hashCardNum;
	private List<CardOwnerDTO> cardOwners = new ArrayList<>();

	public List<CardOwnerDTO> getCardOwners() {
		return cardOwners;
	}

	public void setCardOwners(List<CardOwnerDTO> cardOwners) {
		this.cardOwners = cardOwners;
	}

	public Long getProdInstKey() {
		return prodInstKey;
	}

	public void setProdInstKey(Long prodInstKey) {
		this.prodInstKey = prodInstKey;
	}

	public String getProdInstSrcId() {
		return prodInstSrcId;
	}

	public void setProdInstSrcId(String prodInstSrcId) {
		this.prodInstSrcId = prodInstSrcId;
	}

	public String getProdInstSrcSysId() {
		return prodInstSrcSysId;
	}

	public void setProdInstSrcSysId(String prodInstSrcSysId) {
		this.prodInstSrcSysId = prodInstSrcSysId;
	}

	public String getBusProdTpId() {
		return busProdTpId;
	}

	public void setBusProdTpId(String busProdTpId) {
		this.busProdTpId = busProdTpId;
	}

	public String getBusProdSubTpId() {
		return busProdSubTpId;
	}

	public void setBusProdSubTpId(String busProdSubTpId) {
		this.busProdSubTpId = busProdSubTpId;
	}

	public String getMprodId() {
		return mprodId;
	}

	public void setMprodId(String mprodId) {
		this.mprodId = mprodId;
	}

	public String getContrNum() {
		return contrNum;
	}

	public void setContrNum(String contrNum) {
		this.contrNum = contrNum;
	}

	public BigDecimal getLmtAmt() {
		return lmtAmt;
	}

	public void setLmtAmt(BigDecimal lmtAmt) {
		this.lmtAmt = lmtAmt;
	}

	public LocalDate getSignDate() {
		return signDate;
	}

	public void setSignDate(LocalDate signDate) {
		this.signDate = signDate;
	}

	public LocalDate getStartDate() {
		return startDate;
	}

	public void setStartDate(LocalDate startDate) {
		this.startDate = startDate;
	}

	public LocalDate getTermntnDate() {
		return termntnDate;
	}

	public void setTermntnDate(LocalDate termntnDate) {
		this.termntnDate = termntnDate;
	}

	public Boolean getDelFlag() {
		return delFlag;
	}

	public void setDelFlag(Boolean delFlag) {
		this.delFlag = delFlag;
	}

	public String getAccSrcId() {
		return accSrcId;
	}

	public void setAccSrcId(String accSrcId) {
		this.accSrcId = accSrcId;
	}

	public String getAccSrcSysId() {
		return accSrcSysId;
	}

	public void setAccSrcSysId(String accSrcSysId) {
		this.accSrcSysId = accSrcSysId;
	}

	public String getAccNumPrefix() {
		return accNumPrefix;
	}

	public void setAccNumPrefix(String accNumPrefix) {
		this.accNumPrefix = accNumPrefix;
	}

	public String getAccNum() {
		return accNum;
	}

	public void setAccNum(String accNum) {
		this.accNum = accNum;
	}

	public Long getPlasticCardInstKey() {
		return plasticCardInstKey;
	}

	public void setPlasticCardInstKey(Long plasticCardInstKey) {
		this.plasticCardInstKey = plasticCardInstKey;
	}

	public String getPlasticCardInstSrcId() {
		return plasticCardInstSrcId;
	}

	public void setPlasticCardInstSrcId(String plasticCardInstSrcId) {
		this.plasticCardInstSrcId = plasticCardInstSrcId;
	}

	public String getViewableCardNum() {
		return viewableCardNum;
	}

	public void setViewableCardNum(String viewableCardNum) {
		this.viewableCardNum = viewableCardNum;
	}

	public String getCardName() {
		return cardName;
	}

	public void setCardName(String cardName) {
		this.cardName = cardName;
	}

	public String getCardTpId() {
		return cardTpId;
	}

	public void setCardTpId(String cardTpId) {
		this.cardTpId = cardTpId;
	}

	public String getCardStatId() {
		return cardStatId;
	}

	public void setCardStatId(String cardStatId) {
		this.cardStatId = cardStatId;
	}

	public OffsetDateTime getActDate() {
		return actDate;
	}

	public void setActDate(OffsetDateTime actDate) {
		this.actDate = actDate;
	}

	public OffsetDateTime getExpiresEnd() {
		return expiresEnd;
	}

	public void setExpiresEnd(OffsetDateTime expiresEnd) {
		this.expiresEnd = expiresEnd;
	}

	public String getRpcProdCode() {
		return rpcProdCode;
	}

	public void setRpcProdCode(String rpcProdCode) {
		this.rpcProdCode = rpcProdCode;
	}

	public String getIssuer() {
		return issuer;
	}

	public void setIssuer(String issuer) {
		this.issuer = issuer;
	}

	public String getIssuerBrn() {
		return issuerBrn;
	}

	public void setIssuerBrn(String issuerBrn) {
		this.issuerBrn = issuerBrn;
	}

	public String getHashCardNum() {
		return hashCardNum;
	}

	public void setHashCardNum(String hashCardNum) {
		this.hashCardNum = hashCardNum;
	}
}
