package cz.equa.camapp.service.applicationservice.model;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;

import java.io.Serializable;

@XmlAccessorType(XmlAccessType.FIELD)
public class ApplIdKeyIdDTO implements Serializable {
    private Long applKey;
    private String busApplId;

    public ApplIdKeyIdDTO() {
        // needed for deserialization
    }
    public Long getApplKey() {
        return applKey;
    }

    public void setApplKey(Long applKey) {
        this.applKey = applKey;
    }

    public String getBusApplId() {
        return busApplId;
    }

    public void setBusApplId(String busApplId) {
        this.busApplId = busApplId;
    }
}
