package cz.equa.camapp.service.partyservice.model.get_party;

import cz.equa.camapp.lovs.*;
import cz.equa.camapp.soap.xjb.adapter.DateAdapter;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import java.io.Serializable;
import java.time.LocalDate;

@XmlAccessorType(XmlAccessType.FIELD)
public class IndividualPartyFieldsDTO implements Serializable {

	private String firstName;
	private String lastName;
	private String middleName;
	private String birthName;
	private String rcNumber;
	@XmlJavaTypeAdapter(LovXMLAdapter.class)
	private LovStayTp stayType;
	@XmlJavaTypeAdapter(LovXMLAdapter.class)
	private LovGender gender;
	@XmlJavaTypeAdapter(LovXMLAdapter.class)
	private LovMarStat maritalState;
	@XmlJavaTypeAdapter(LovXMLAdapter.class)
	private LovHousingStat housingState;
	@XmlJavaTypeAdapter(LovXMLAdapter.class)
	private LovEduStat educationState;
	@XmlJavaTypeAdapter(LovXMLAdapter.class)
	private LovCntry birthCountry;
	private String birthPlace;
    
    @XmlJavaTypeAdapter(DateAdapter.class)
	private LocalDate birthDate;
    
    @XmlJavaTypeAdapter(DateAdapter.class)
	private LocalDate deathDate;
    
	private String titleBefore;
	private String titleAfter;
	private String salutation;
	@XmlJavaTypeAdapter(LovXMLAdapter.class)
	private LovSocStat occupation;
	@XmlJavaTypeAdapter(LovXMLAdapter.class)
	private LovIncTp incomeType;
	private Boolean signed101;
	private Boolean signedMarketing;
	private Boolean brkiNrkiSolusHistory;

	public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public String getMiddleName() {
		return middleName;
	}

	public void setMiddleName(String middleName) {
		this.middleName = middleName;
	}

	public String getBirthName() {
		return birthName;
	}

	public void setBirthName(String birthName) {
		this.birthName = birthName;
	}

	public String getRcNumber() {
		return rcNumber;
	}

	public void setRcNumber(String rcNumber) {
		this.rcNumber = rcNumber;
	}

	public LovStayTp getStayType() {
		return stayType;
	}

	public void setStayType(LovStayTp stayType) {
		this.stayType = stayType;
	}

	public LovGender getGender() {
		return gender;
	}

	public void setGender(LovGender gender) {
		this.gender = gender;
	}

	public LovMarStat getMaritalState() {
		return maritalState;
	}

	public void setMaritalState(LovMarStat maritalState) {
		this.maritalState = maritalState;
	}

	public LovHousingStat getHousingState() {
		return housingState;
	}

	public void setHousingState(LovHousingStat housingState) {
		this.housingState = housingState;
	}

	public LovEduStat getEducationState() {
		return educationState;
	}

	public void setEducationState(LovEduStat educationState) {
		this.educationState = educationState;
	}

	public LovCntry getBirthCountry() {
		return birthCountry;
	}

	public void setBirthCountry(LovCntry birthCountry) {
		this.birthCountry = birthCountry;
	}

	public String getBirthPlace() {
		return birthPlace;
	}

	public void setBirthPlace(String birthPlace) {
		this.birthPlace = birthPlace;
	}

	public LocalDate getBirthDate() {
		return birthDate;
	}

	public void setBirthDate(LocalDate birthDate) {
		this.birthDate = birthDate;
	}

	public LocalDate getDeathDate() {
		return deathDate;
	}

	public void setDeathDate(LocalDate deathDate) {
		this.deathDate = deathDate;
	}

	public String getTitleBefore() {
		return titleBefore;
	}

	public void setTitleBefore(String titleBefore) {
		this.titleBefore = titleBefore;
	}

	public String getTitleAfter() {
		return titleAfter;
	}

	public void setTitleAfter(String titleAfter) {
		this.titleAfter = titleAfter;
	}

	public String getSalutation() {
		return salutation;
	}

	public void setSalutation(String salutation) {
		this.salutation = salutation;
	}

	public LovSocStat getOccupation() {
		return occupation;
	}

	public void setOccupation(LovSocStat occupation) {
		this.occupation = occupation;
	}

	public LovIncTp getIncomeType() {
		return incomeType;
	}

	public void setIncomeType(LovIncTp incomeType) {
		this.incomeType = incomeType;
	}

	public Boolean getSigned101() {
		return signed101;
	}

	public void setSigned101(Boolean signed101) {
		this.signed101 = signed101;
	}

	public Boolean getSignedMarketing() {
		return signedMarketing;
	}

	public void setSignedMarketing(Boolean signedMarketing) {
		this.signedMarketing = signedMarketing;
	}

	public Boolean getBrkiNrkiSolusHistory() {
		return brkiNrkiSolusHistory;
	}

	public void setBrkiNrkiSolusHistory(Boolean brkiNrkiSolusHistory) {
		this.brkiNrkiSolusHistory = brkiNrkiSolusHistory;
	}
}
