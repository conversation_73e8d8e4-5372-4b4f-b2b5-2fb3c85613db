package cz.equa.camapp.service.applicationservice.model;

import cz.equa.camapp.soap.xjb.adapter.DateAdapter;
import cz.equa.camapp.soap.xjb.adapter.OffsetDateTimeAdapter;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.List;

@XmlAccessorType(XmlAccessType.FIELD)
public class GetMortApplDTO implements Serializable {
    private ApplIdKeyIdDTO applId;
    private String applTpId;
    private String applStatId;
    @XmlJavaTypeAdapter(OffsetDateTimeAdapter.class)
    private OffsetDateTime applDate;
    private String posId;
    private String firstTouchPoint;
    private String hash;
    private String applReason;
    private String preApprOfrId;
    private String opportunityId;
    private String ccyId;
    private String distCnlId;
    private String advisorId;
    private String fulfillmentCnlId;
    private String wrkPlaceId;
    private String busProdSubTp;
    private String browserInfo;
    private String activationId;
    private Boolean telcoQueryAllowedFlag;
    private Boolean authMobSearchFlag;
    private String firstTouchPointOwnr;
    private String firstTouchPointBnkr;
    private String promoCode;
    private String applDsnKey;
    private String applComplPosId;
    private String contrNum;
    @XmlJavaTypeAdapter(DateAdapter.class)
    private LocalDate contrSignDate;
    private String contrSignPosId;
    private String contrSignAdvisorId;
    private String contrSignAdvisorName;
    private String lastChangeAdvisorId;
    private String rejectRsn;
    private String rejectRsnTp;
    private String fiOperCode;
    private String registryResult;
    private String finalRiskClass;
    private String busProdTp;
    @XmlJavaTypeAdapter(OffsetDateTimeAdapter.class)
    private OffsetDateTime applDateTo;
    private Long prntApplKey;
    private String prntApplBusApplId;
    private List<GetMortApplPersDTO> persons = null;
    private List<GetMortApplChildDTO> chieldAppls = null;
    private List<HouseholdDTO> households = null;
    private List<MortApplVariantDTO> applVariants = null;
    private List<ApplMetadataEntryDTO> applMetadata = null;
    private List<PtIncomeMortDTO> incomes = null;

    public GetMortApplDTO() {
        // needed for deserialization
    }
    public ApplIdKeyIdDTO getApplId() {
        return applId;
    }

    public void setApplId(ApplIdKeyIdDTO applId) {
        this.applId = applId;
    }

    public String getApplTpId() {
        return applTpId;
    }

    public void setApplTpId(String applTpId) {
        this.applTpId = applTpId;
    }

    public String getApplStatId() {
        return applStatId;
    }

    public void setApplStatId(String applStatId) {
        this.applStatId = applStatId;
    }

    public OffsetDateTime getApplDate() {
        return applDate;
    }

    public void setApplDate(OffsetDateTime applDate) {
        this.applDate = applDate;
    }

    public String getPosId() {
        return posId;
    }

    public void setPosId(String posId) {
        this.posId = posId;
    }

    public String getFirstTouchPoint() {
        return firstTouchPoint;
    }

    public void setFirstTouchPoint(String firstTouchPoint) {
        this.firstTouchPoint = firstTouchPoint;
    }

    public String getApplReason() {
        return applReason;
    }

    public void setApplReason(String applReason) {
        this.applReason = applReason;
    }

    public String getHash() {
        return hash;
    }

    public void setHash(String hash) {
        this.hash = hash;
    }

    public String getPreApprOfrId() {
        return preApprOfrId;
    }

    public void setPreApprOfrId(String preApprOfrId) {
        this.preApprOfrId = preApprOfrId;
    }

    public String getOpportunityId() {
        return opportunityId;
    }

    public void setOpportunityId(String opportunityId) {
        this.opportunityId = opportunityId;
    }

    public String getCcyId() {
        return ccyId;
    }

    public void setCcyId(String ccyId) {
        this.ccyId = ccyId;
    }

    public String getDistCnlId() {
        return distCnlId;
    }

    public void setDistCnlId(String distCnlId) {
        this.distCnlId = distCnlId;
    }

    public String getAdvisorId() {
        return advisorId;
    }

    public void setAdvisorId(String advisorId) {
        this.advisorId = advisorId;
    }

    public String getFulfillmentCnlId() {
        return fulfillmentCnlId;
    }

    public void setFulfillmentCnlId(String fulfillmentCnlId) {
        this.fulfillmentCnlId = fulfillmentCnlId;
    }

    public String getWrkPlaceId() {
        return wrkPlaceId;
    }

    public void setWrkPlaceId(String wrkPlaceId) {
        this.wrkPlaceId = wrkPlaceId;
    }

    public String getBusProdSubTp() {
        return busProdSubTp;
    }

    public void setBusProdSubTp(String busProdSubTp) {
        this.busProdSubTp = busProdSubTp;
    }

    public String getBrowserInfo() {
        return browserInfo;
    }

    public void setBrowserInfo(String browserInfo) {
        this.browserInfo = browserInfo;
    }

    public String getActivationId() {
        return activationId;
    }

    public void setActivationId(String activationId) {
        this.activationId = activationId;
    }

    public Boolean getTelcoQueryAllowedFlag() {
        return telcoQueryAllowedFlag;
    }

    public void setTelcoQueryAllowedFlag(Boolean telcoQueryAllowedFlag) {
        this.telcoQueryAllowedFlag = telcoQueryAllowedFlag;
    }

    public Boolean getAuthMobSearchFlag() {
        return authMobSearchFlag;
    }

    public void setAuthMobSearchFlag(Boolean authMobSearchFlag) {
        this.authMobSearchFlag = authMobSearchFlag;
    }

    public String getFirstTouchPointOwnr() {
        return firstTouchPointOwnr;
    }

    public void setFirstTouchPointOwnr(String firstTouchPointOwnr) {
        this.firstTouchPointOwnr = firstTouchPointOwnr;
    }

    public String getFirstTouchPointBnkr() {
        return firstTouchPointBnkr;
    }

    public GetMortApplDTO setFirstTouchPointBnkr(String firstTouchPointBnkr) {
        this.firstTouchPointBnkr = firstTouchPointBnkr;
        return this;
    }

    public String getPromoCode() {
        return promoCode;
    }

    public void setPromoCode(String promoCode) {
        this.promoCode = promoCode;
    }

    public String getApplDsnKey() {
        return applDsnKey;
    }

    public void setApplDsnKey(String applDsnKey) {
        this.applDsnKey = applDsnKey;
    }

    public String getApplComplPosId() {
        return applComplPosId;
    }

    public void setApplComplPosId(String applComplPosId) {
        this.applComplPosId = applComplPosId;
    }

    public String getContrNum() {
        return contrNum;
    }

    public void setContrNum(String contrNum) {
        this.contrNum = contrNum;
    }

    public LocalDate getContrSignDate() {
        return contrSignDate;
    }

    public void setContrSignDate(LocalDate contrSignDate) {
        this.contrSignDate = contrSignDate;
    }

    public String getContrSignPosId() {
        return contrSignPosId;
    }

    public void setContrSignPosId(String contrSignPosId) {
        this.contrSignPosId = contrSignPosId;
    }

    public String getContrSignAdvisorId() {
        return contrSignAdvisorId;
    }

    public void setContrSignAdvisorId(String contrSignAdvisorId) {
        this.contrSignAdvisorId = contrSignAdvisorId;
    }

    public String getContrSignAdvisorName() {
        return contrSignAdvisorName;
    }

    public void setContrSignAdvisorName(String contrSignAdvisorName) {
        this.contrSignAdvisorName = contrSignAdvisorName;
    }

    public String getLastChangeAdvisorId() {
        return lastChangeAdvisorId;
    }

    public void setLastChangeAdvisorId(String lastChangeAdvisorId) {
        this.lastChangeAdvisorId = lastChangeAdvisorId;
    }

    public String getRejectRsn() {
        return rejectRsn;
    }

    public void setRejectRsn(String rejectRsn) {
        this.rejectRsn = rejectRsn;
    }

    public String getRejectRsnTp() {
        return rejectRsnTp;
    }

    public void setRejectRsnTp(String rejectRsnTp) {
        this.rejectRsnTp = rejectRsnTp;
    }

    public String getFiOperCode() {
        return fiOperCode;
    }

    public void setFiOperCode(String fiOperCode) {
        this.fiOperCode = fiOperCode;
    }

    public String getRegistryResult() {
        return registryResult;
    }

    public void setRegistryResult(String registryResult) {
        this.registryResult = registryResult;
    }

    public String getFinalRiskClass() {
        return finalRiskClass;
    }

    public void setFinalRiskClass(String finalRiskClass) {
        this.finalRiskClass = finalRiskClass;
    }

    public String getBusProdTp() {
        return busProdTp;
    }

    public void setBusProdTp(String busProdTp) {
        this.busProdTp = busProdTp;
    }

    public OffsetDateTime getApplDateTo() {
        return applDateTo;
    }

    public void setApplDateTo(OffsetDateTime applDateTo) {
        this.applDateTo = applDateTo;
    }

    public Long getPrntApplKey() {
        return prntApplKey;
    }

    public void setPrntApplKey(Long prntApplKey) {
        this.prntApplKey = prntApplKey;
    }

    public String getPrntApplBusApplId() {
        return prntApplBusApplId;
    }

    public void setPrntApplBusApplId(String prntApplBusApplId) {
        this.prntApplBusApplId = prntApplBusApplId;
    }

    public List<GetMortApplPersDTO> getPersons() {
        return persons;
    }

    public void setPersons(List<GetMortApplPersDTO> persons) {
        this.persons = persons;
    }

    public List<GetMortApplChildDTO> getChieldAppls() {
        return chieldAppls;
    }

    public void setChieldAppls(List<GetMortApplChildDTO> chieldAppls) {
        this.chieldAppls = chieldAppls;
    }

    public List<HouseholdDTO> getHouseholds() {
        return households;
    }

    public void setHouseholds(List<HouseholdDTO> households) {
        this.households = households;
    }

    public List<MortApplVariantDTO> getApplVariants() {
        return applVariants;
    }

    public void setApplVariants(List<MortApplVariantDTO> applVariants) {
        this.applVariants = applVariants;
    }

    public List<ApplMetadataEntryDTO> getApplMetadata() {
        return applMetadata;
    }

    public void setApplMetadata(List<ApplMetadataEntryDTO> applMetadata) {
        this.applMetadata = applMetadata;
    }

    public List<PtIncomeMortDTO> getIncomes() {
        return incomes;
    }

    public void setIncomes(List<PtIncomeMortDTO> incomes) {
        this.incomes = incomes;
    }

    public MortApplVariantDTO getApplVariant(String applVariantTpId) {
        if (applVariants == null) {
            return null;
        }

        for (MortApplVariantDTO variant : applVariants) {
            if (applVariantTpId.equals(variant.getApplVariantTpId())) {
                return variant;
            }
        }
        return null;
    }
}
