package cz.equa.camapp.service.applicationservice.model;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@ToString
public class BuildingApplVariantSurDTO implements Serializable {
    private Long surchrgApplVrntSurchrgKey;
    private String surchrgTpId;
    private BigDecimal surchrgVarncRxPct;

    public BuildingApplVariantSurDTO() {
        // needed for deserialization
    }
}
