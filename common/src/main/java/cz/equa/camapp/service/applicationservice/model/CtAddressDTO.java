package cz.equa.camapp.service.applicationservice.model;


import cz.equa.camapp.soap.xjb.adapter.OffsetDateTimeAdapter;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;

@XmlAccessorType(XmlAccessType.FIELD)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CtAddressDTO implements Serializable {
    private String streetName;
    private String streetNum;
    private String cityName;
    private String detail;
    private String zip;
    private String landRegnNum;
    private String cntryId;
    private Long addrInstAddrKey;
    @XmlJavaTypeAdapter(OffsetDateTimeAdapter.class)
    private LocalDate addrSince;
}
