package cz.equa.camapp.service.applicationservice.model;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;

import java.io.Serializable;

@XmlAccessorType(XmlAccessType.FIELD)
public class GetMortApplChildDTO implements Serializable {
    private Long chieldApplKey;
    private String chieldApplBusApplId;
    private String chieldApplStatId;

    public GetMortApplChildDTO() {
        // needed for deserialization
    }
    public Long getChieldApplKey() {
        return chieldApplKey;
    }

    public void setChieldApplKey(Long chieldApplKey) {
        this.chieldApplKey = chieldApplKey;
    }

    public String getChieldApplBusApplId() {
        return chieldApplBusApplId;
    }

    public void setChieldApplBusApplId(String chieldApplBusApplId) {
        this.chieldApplBusApplId = chieldApplBusApplId;
    }

    public String getChieldApplStatId() {
        return chieldApplStatId;
    }

    public void setChieldApplStatId(String chieldApplStatId) {
        this.chieldApplStatId = chieldApplStatId;
    }
}
