package cz.equa.camapp.service.applicationservice.model;

import cz.equa.camapp.lovs.LovEmailPurp;
import cz.equa.camapp.lovs.LovXMLAdapter;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import java.io.Serializable;
import java.math.BigInteger;

@XmlAccessorType(XmlAccessType.FIELD)
public class EmailDTO implements Serializable {
	private BigInteger emailInstEmailKey;
	@XmlJavaTypeAdapter(LovXMLAdapter.class)
	private LovEmailPurp emailPurpId;
	private String emailId;
	private BigInteger emailDelFlag;

	public BigInteger getEmailInstEmailKey() {
		return emailInstEmailKey;
	}

	public void setEmailInstEmailKey(BigInteger emailInstEmailKey) {
		this.emailInstEmailKey = emailInstEmailKey;
	}

	public LovEmailPurp getEmailPurpId() {
		return emailPurpId;
	}

	public void setEmailPurpId(LovEmailPurp emailPurpId) {
		this.emailPurpId = emailPurpId;
	}

	public String getEmailId() {
		return emailId;
	}

	public void setEmailId(String emailId) {
		this.emailId = emailId;
	}

	public BigInteger getEmailDelFlag() {
		return emailDelFlag;
	}

	public void setEmailDelFlag(BigInteger emailDelFlag) {
		this.emailDelFlag = emailDelFlag;
	}
}
