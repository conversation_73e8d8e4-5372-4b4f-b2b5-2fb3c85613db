package cz.equa.camapp.service.applicationservice.model;

import cz.equa.camapp.soap.xjb.adapter.DateAdapter;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDate;
import java.util.List;

@XmlAccessorType(XmlAccessType.FIELD)
public class NewApplVariantDTO implements Serializable {
	private BigInteger applVariantKey;
	private BigInteger applVariantFlag;
    
	private String applVariantTpId;
    
	private String busProdTpId;
    
	private String busProdSubTpId;
    
	private BigDecimal finaAmt;
	private BigDecimal instlAmt;
	private BigInteger instlCnt;
    
    @XmlJavaTypeAdapter(DateAdapter.class)
	private LocalDate maturityDate;
    
	private BigDecimal intrsRx;
	private BigDecimal rpsn;
	private String camCode;
	private BigDecimal minAvlblAmt;
	private BigDecimal minInstlAmt;
	private BigInteger minInstlCnt;
	private BigDecimal maxAvlblAmt;
	private BigDecimal maxInstlAmt;
	private BigInteger maxInstlCnt;
	private BigDecimal upsellAmt;
    
	private String ccyId;
	
    private BigDecimal totRpmtAmt;
	private BigDecimal refiSavedAmt;
    
	private String loanPurpId;
    
	private BigDecimal actSumInstlAmt;
	private BigDecimal consLoanInstlAmt;
	private BigDecimal purpAmtOblgtn;
	private BigDecimal upsellAmtOblgtn;
	private BigDecimal maxUpsellAmtOblgtn;
	private BigDecimal dayIntrsAmt;
    
    @XmlJavaTypeAdapter(DateAdapter.class)
	private LocalDate repreDrawDate;
    
    @XmlJavaTypeAdapter(DateAdapter.class)
	private LocalDate repreMaturityDate;
	
    private BigDecimal repreInt;
	private BigDecimal repreInt3M;
	private BigDecimal costs3M;
	private BigDecimal approvedFee;
	private Boolean delFlag;
	private List<OblgtnDTO> oblgtns;
	private List<SignChannelDTO> signChannels;
    
    private String declaredPurpose;

	protected BigDecimal maxExtOblgtnAmt;

    private BigDecimal equaOblgtnAmt;
    private BigDecimal nonEquaOblgtnAmt;
    
    private Boolean applVariantSignCnlFlag;

    private String ofrBusProdSubTpId;

    private BigDecimal maxUpsellAmt;
    private Boolean intrsDscntFlag;
    
    private String accNumPrefix;
    
    private String accNum;
    
    private String accBankCode;
    
    private BigDecimal minIrForFinalization;
    private BigDecimal origScOfrIntrsRx;
	private String prodIntrsCodeId;

    public String getAccNum() {
        return accNum;
    }

    public void setAccNum(String accNum) {
        this.accNum = accNum;
    }

    public String getAccBankCode() {
        return accBankCode;
    }

    public void setAccBankCode(String accBankCode) {
        this.accBankCode = accBankCode;
    }
    
    public String getAccNumPrefix() {
        return accNumPrefix;
    }

    public void setAccNumPrefix(String accNumPrefix) {
        this.accNumPrefix = accNumPrefix;
    }
    
	public BigInteger getApplVariantKey() {
		return applVariantKey;
	}

	public void setApplVariantKey(BigInteger applVariantKey) {
		this.applVariantKey = applVariantKey;
	}

	public BigInteger getApplVariantFlag() {
		return applVariantFlag;
	}

	public void setApplVariantFlag(BigInteger applVariantFlag) {
		this.applVariantFlag = applVariantFlag;
	}

	public String getApplVariantTpId() {
		return applVariantTpId;
	}

	public void setApplVariantTpId(String applVariantTpId) {
		this.applVariantTpId = applVariantTpId;
	}

	public String getBusProdTpId() {
		return busProdTpId;
	}

	public void setBusProdTpId(String busProdTpId) {
		this.busProdTpId = busProdTpId;
	}

	public String getBusProdSubTpId() {
		return busProdSubTpId;
	}

	public void setBusProdSubTpId(String busProdSubTpId) {
		this.busProdSubTpId = busProdSubTpId;
	}

	public BigDecimal getFinaAmt() {
		return finaAmt;
	}

	public void setFinaAmt(BigDecimal finaAmt) {
		this.finaAmt = finaAmt;
	}

	public BigDecimal getInstlAmt() {
		return instlAmt;
	}

	public void setInstlAmt(BigDecimal instlAmt) {
		this.instlAmt = instlAmt;
	}

	public BigInteger getInstlCnt() {
		return instlCnt;
	}

	public void setInstlCnt(BigInteger instlCnt) {
		this.instlCnt = instlCnt;
	}

	public LocalDate getMaturityDate() {
		return maturityDate;
	}

	public void setMaturityDate(LocalDate maturityDate) {
		this.maturityDate = maturityDate;
	}

	public BigDecimal getIntrsRx() {
		return intrsRx;
	}

	public void setIntrsRx(BigDecimal intrsRx) {
		this.intrsRx = intrsRx;
	}

	public BigDecimal getRpsn() {
		return rpsn;
	}

	public void setRpsn(BigDecimal rpsn) {
		this.rpsn = rpsn;
	}

	public String getCamCode() {
		return camCode;
	}

	public void setCamCode(String camCode) {
		this.camCode = camCode;
	}

	public BigDecimal getMinAvlblAmt() {
		return minAvlblAmt;
	}

	public void setMinAvlblAmt(BigDecimal minAvlblAmt) {
		this.minAvlblAmt = minAvlblAmt;
	}

	public BigDecimal getMinInstlAmt() {
		return minInstlAmt;
	}

	public void setMinInstlAmt(BigDecimal minInstlAmt) {
		this.minInstlAmt = minInstlAmt;
	}

	public BigInteger getMinInstlCnt() {
		return minInstlCnt;
	}

	public void setMinInstlCnt(BigInteger minInstlCnt) {
		this.minInstlCnt = minInstlCnt;
	}

	public BigDecimal getMaxAvlblAmt() {
		return maxAvlblAmt;
	}

	public void setMaxAvlblAmt(BigDecimal maxAvlblAmt) {
		this.maxAvlblAmt = maxAvlblAmt;
	}

	public BigDecimal getMaxInstlAmt() {
		return maxInstlAmt;
	}

	public void setMaxInstlAmt(BigDecimal maxInstlAmt) {
		this.maxInstlAmt = maxInstlAmt;
	}

	public BigInteger getMaxInstlCnt() {
		return maxInstlCnt;
	}

	public void setMaxInstlCnt(BigInteger maxInstlCnt) {
		this.maxInstlCnt = maxInstlCnt;
	}

	public BigDecimal getUpsellAmt() {
		return upsellAmt;
	}

	public void setUpsellAmt(BigDecimal upsellAmt) {
		this.upsellAmt = upsellAmt;
	}

	public String getCcyId() {
		return ccyId;
	}

	public void setCcyId(String ccyId) {
		this.ccyId = ccyId;
	}

	public BigDecimal getTotRpmtAmt() {
		return totRpmtAmt;
	}

	public void setTotRpmtAmt(BigDecimal totRpmtAmt) {
		this.totRpmtAmt = totRpmtAmt;
	}

	public BigDecimal getRefiSavedAmt() {
		return refiSavedAmt;
	}

	public void setRefiSavedAmt(BigDecimal refiSavedAmt) {
		this.refiSavedAmt = refiSavedAmt;
	}

	public String getLoanPurpId() {
		return loanPurpId;
	}

	public void setLoanPurpId(String loanPurpId) {
		this.loanPurpId = loanPurpId;
	}

	public BigDecimal getActSumInstlAmt() {
		return actSumInstlAmt;
	}

	public void setActSumInstlAmt(BigDecimal actSumInstlAmt) {
		this.actSumInstlAmt = actSumInstlAmt;
	}

	public BigDecimal getConsLoanInstlAmt() {
		return consLoanInstlAmt;
	}

	public void setConsLoanInstlAmt(BigDecimal consLoanInstlAmt) {
		this.consLoanInstlAmt = consLoanInstlAmt;
	}

	public BigDecimal getPurpAmtOblgtn() {
		return purpAmtOblgtn;
	}

	public void setPurpAmtOblgtn(BigDecimal purpAmtOblgtn) {
		this.purpAmtOblgtn = purpAmtOblgtn;
	}

	public BigDecimal getUpsellAmtOblgtn() {
		return upsellAmtOblgtn;
	}

	public void setUpsellAmtOblgtn(BigDecimal upsellAmtOblgtn) {
		this.upsellAmtOblgtn = upsellAmtOblgtn;
	}

	public BigDecimal getMaxUpsellAmtOblgtn() {
		return maxUpsellAmtOblgtn;
	}

	public void setMaxUpsellAmtOblgtn(BigDecimal maxUpsellAmtOblgtn) {
		this.maxUpsellAmtOblgtn = maxUpsellAmtOblgtn;
	}

	public BigDecimal getDayIntrsAmt() {
		return dayIntrsAmt;
	}

	public void setDayIntrsAmt(BigDecimal dayIntrsAmt) {
		this.dayIntrsAmt = dayIntrsAmt;
	}

	public LocalDate getRepreDrawDate() {
		return repreDrawDate;
	}

	public void setRepreDrawDate(LocalDate repreDrawDate) {
		this.repreDrawDate = repreDrawDate;
	}

	public LocalDate getRepreMaturityDate() {
		return repreMaturityDate;
	}

	public void setRepreMaturityDate(LocalDate repreMaturityDate) {
		this.repreMaturityDate = repreMaturityDate;
	}

	public BigDecimal getRepreInt() {
		return repreInt;
	}

	public void setRepreInt(BigDecimal repreInt) {
		this.repreInt = repreInt;
	}

	public BigDecimal getRepreInt3M() {
		return repreInt3M;
	}

	public void setRepreInt3M(BigDecimal repreInt3M) {
		this.repreInt3M = repreInt3M;
	}

	public BigDecimal getCosts3M() {
		return costs3M;
	}

	public void setCosts3M(BigDecimal costs3M) {
		this.costs3M = costs3M;
	}

	public BigDecimal getApprovedFee() {
		return approvedFee;
	}

	public void setApprovedFee(BigDecimal approvedFee) {
		this.approvedFee = approvedFee;
	}

	public Boolean getDelFlag() {
		return delFlag;
	}

	public void setDelFlag(Boolean delFlag) {
		this.delFlag = delFlag;
	}

	public List<OblgtnDTO> getOblgtns() {
		return oblgtns;
	}

	public void setOblgtns(List<OblgtnDTO> oblgtns) {
		this.oblgtns = oblgtns;
	}

	public List<SignChannelDTO> getSignChannels() {
		return signChannels;
	}

	public void setSignChannels(List<SignChannelDTO> signChannels) {
		this.signChannels = signChannels;
	}

    public String getDeclaredPurpose() {
        return declaredPurpose;
    }

    public void setDeclaredPurpose(String declaredPurpose) {
        this.declaredPurpose = declaredPurpose;
    }


	public BigDecimal getMaxExtOblgtnAmt() {
		return maxExtOblgtnAmt;
	}

	public void setMaxExtOblgtnAmt(BigDecimal maxExtOblgtnAmt) {
		this.maxExtOblgtnAmt = maxExtOblgtnAmt;
	}

	public Boolean getApplVariantSignCnlFlag() {
        return applVariantSignCnlFlag;
    }

    public void setApplVariantSignCnlFlag(Boolean applVariantSignCnlFlag) {
        this.applVariantSignCnlFlag = applVariantSignCnlFlag;
    }

    public BigDecimal getEquaOblgtnAmt() {
        return equaOblgtnAmt;
    }

    public void setEquaOblgtnAmt(BigDecimal equaOblgtnAmt) {
        this.equaOblgtnAmt = equaOblgtnAmt;
    }

    public BigDecimal getNonEquaOblgtnAmt() {
        return nonEquaOblgtnAmt;
    }

    public void setNonEquaOblgtnAmt(BigDecimal nonEquaOblgtnAmt) {
        this.nonEquaOblgtnAmt = nonEquaOblgtnAmt;
    }

    public String getOfrBusProdSubTpId() {
        return ofrBusProdSubTpId;
    }

    public void setOfrBusProdSubTpId(String ofrBusProdSubTpId) {
        this.ofrBusProdSubTpId = ofrBusProdSubTpId;
    }

    public BigDecimal getMaxUpsellAmt() {
        return maxUpsellAmt;
    }

    public void setMaxUpsellAmt(BigDecimal maxUpsellAmt) {
        this.maxUpsellAmt = maxUpsellAmt;
    }

    public Boolean getIntrsDscntFlag() {
        return intrsDscntFlag;
    }

    public void setIntrsDscntFlag(Boolean intrsDscntFlag) {
        this.intrsDscntFlag = intrsDscntFlag;
    }

    public BigDecimal getMinIrForFinalization() {
        return minIrForFinalization;
    }

    public void setMinIrForFinalization(BigDecimal minIrForFinalization) {
        this.minIrForFinalization = minIrForFinalization;
    }

    public BigDecimal getOrigScOfrIntrsRx() {
        return origScOfrIntrsRx;
    }

    public void setOrigScOfrIntrsRx(BigDecimal origScOfrIntrsRx) {
        this.origScOfrIntrsRx = origScOfrIntrsRx;
    }

	public String getProdIntrsCodeId() { return prodIntrsCodeId; }

	public void setProdIntrsCodeId(String prodIntrsCodeId) { this.prodIntrsCodeId=prodIntrsCodeId; }
}
