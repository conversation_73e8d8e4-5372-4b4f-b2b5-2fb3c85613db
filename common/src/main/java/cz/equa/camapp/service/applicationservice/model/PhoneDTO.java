package cz.equa.camapp.service.applicationservice.model;

import cz.equa.camapp.lovs.LovPhonePayTp;
import cz.equa.camapp.lovs.LovPhonePurp;
import cz.equa.camapp.lovs.LovPhoneTp;
import cz.equa.camapp.lovs.LovXMLAdapter;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import java.io.Serializable;
import java.math.BigInteger;

@XmlAccessorType(XmlAccessType.FIELD)
public class PhoneDTO implements Serializable {

	private BigInteger phoneInstPhoneKey;
	@XmlJavaTypeAdapter(LovXMLAdapter.class)
	private LovPhonePurp phonePurpId;
	@XmlJavaTypeAdapter(LovXMLAdapter.class)
	private LovPhoneTp phoneTpId;
	private String phoneNum;
	@XmlJavaTypeAdapter(LovXMLAdapter.class)
	private LovPhonePayTp phonePayTpId;
	private BigInteger phoneDelFlag;

	public BigInteger getPhoneInstPhoneKey() {
		return phoneInstPhoneKey;
	}

	public void setPhoneInstPhoneKey(BigInteger phoneInstPhoneKey) {
		this.phoneInstPhoneKey = phoneInstPhoneKey;
	}

	public LovPhonePurp getPhonePurpId() {
		return phonePurpId;
	}

	public void setPhonePurpId(LovPhonePurp phonePurpId) {
		this.phonePurpId = phonePurpId;
	}

	public LovPhoneTp getPhoneTpId() {
		return phoneTpId;
	}

	public void setPhoneTpId(LovPhoneTp phoneTpId) {
		this.phoneTpId = phoneTpId;
	}

	public String getPhoneNum() {
		return phoneNum;
	}

	public void setPhoneNum(String phoneNum) {
		this.phoneNum = phoneNum;
	}

	public LovPhonePayTp getPhonePayTpId() {
		return phonePayTpId;
	}

	public void setPhonePayTpId(LovPhonePayTp phonePayTpId) {
		this.phonePayTpId = phonePayTpId;
	}

	public BigInteger getPhoneDelFlag() {
		return phoneDelFlag;
	}

	public void setPhoneDelFlag(BigInteger phoneDelFlag) {
		this.phoneDelFlag = phoneDelFlag;
	}
}
