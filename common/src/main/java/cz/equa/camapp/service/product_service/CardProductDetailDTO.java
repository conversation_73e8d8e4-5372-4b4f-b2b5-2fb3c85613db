package cz.equa.camapp.service.product_service;

import cz.equa.camapp.service.partyservice.model.get_party.SrcIdDTO;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@XmlAccessorType(XmlAccessType.FIELD)
public class CardProductDetailDTO extends AbstractProductDTO implements Serializable {
    
    private SrcIdDTO cardAccSrcId;
    private String cardAccNum;

    private String cardAccCcy;

    private String cardAccStat;

    private SrcIdDTO liveCardSrcId;
    private List<PlasticCardDTO> plasticCards;

    private String rpcProductCode;
    private String rpcBranchCode;
    private BigDecimal creditLimit;
    private String siebelPtId;
    
    public CardProductDetailDTO() {
        //needed for serialization
    }
    
    public SrcIdDTO getCardAccSrcId() {
        return cardAccSrcId;
    }

    public void setCardAccSrcId(SrcIdDTO cardAccSrcId) {
        this.cardAccSrcId = cardAccSrcId;
    }

    public String getCardAccNum() {
        return cardAccNum;
    }

    public void setCardAccNum(String cardAccNum) {
        this.cardAccNum = cardAccNum;
    }

    public String getCardAccCcy() {
        return cardAccCcy;
    }

    public void setCardAccCcy(String cardAccCcy) {
        this.cardAccCcy = cardAccCcy;
    }

    public String getCardAccStat() {
        return cardAccStat;
    }

    public void setCardAccStat(String cardAccStat) {
        this.cardAccStat = cardAccStat;
    }

    public SrcIdDTO getLiveCardSrcId() {
        return liveCardSrcId;
    }

    public void setLiveCardSrcId(SrcIdDTO liveCardSrcId) {
        this.liveCardSrcId = liveCardSrcId;
    }

    public List<PlasticCardDTO> getPlasticCards() {
        return plasticCards;
    }

    public void setPlasticCards(List<PlasticCardDTO> plasticCards) {
        this.plasticCards = plasticCards;
    }

    public String getRpcProductCode() {
        return rpcProductCode;
    }

    public void setRpcProductCode(String rpcProductCode) {
        this.rpcProductCode = rpcProductCode;
    }

    public String getRpcBranchCode() {
        return rpcBranchCode;
    }

    public void setRpcBranchCode(String rpcBranchCode) {
        this.rpcBranchCode = rpcBranchCode;
    }

    public BigDecimal getCreditLimit() {
        return creditLimit;
    }

    public void setCreditLimit(BigDecimal creditLimit) {
        this.creditLimit = creditLimit;
    }

    public String getSiebelPtId() {
        return siebelPtId;
    }

    public void setSiebelPtId(String siebelPtId) {
        this.siebelPtId = siebelPtId;
    }

    @Override
    public String toString() {
        return "CardProductDetailDTO{" +
                "prodSrcId=" + prodSrcId +
                ", status=" + status +
                ", contrBusId='" + contrBusId + '\'' +
                ", prodClass=" + prodClass +
                ", prodTp=" + prodTp +
                ", busProdSubTp=" + busProdSubTp +
                ", firstTouchPoint='" + firstTouchPoint + '\'' +
                ", fullfilmentCnl='" + fullfilmentCnl + '\'' +
                ", createDate=" + createDate +
                ", closeDate=" + closeDate +
                ", descr='" + descr + '\'' +
                ", cardAccSrcId=" + cardAccSrcId +
                ", cardAccNum='" + cardAccNum + '\'' +
                ", cardAccCcy=" + cardAccCcy +
                ", cardAccStat=" + cardAccStat +
                ", liveCardSrcId=" + liveCardSrcId +
                ", plasticCards=" + plasticCards +
                ", rpcProductCode=" + rpcProductCode +
                ", rpcBranchCode=" + rpcBranchCode +
                ", creditLimit=" + creditLimit +
                ", siebelPtId=" + siebelPtId +
                '}';
    }
}
