package cz.equa.camapp.service.applicationservice.model;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import lombok.Data;

import java.io.Serializable;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
public class GetMortApplPersDTO implements Serializable {
    private Long adbInstPtKey;
    private String applPtRoleTpId;
    private String extnRejRsnTpDescr;
    private String relationToApplicant;

    public GetMortApplPersDTO() {
        // needed for deserialization
    }
}
