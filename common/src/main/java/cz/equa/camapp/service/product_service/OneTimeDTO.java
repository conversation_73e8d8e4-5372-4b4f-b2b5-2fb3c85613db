package cz.equa.camapp.service.product_service;

import cz.equa.camapp.soap.xjb.adapter.DateAdapter;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import java.io.Serializable;
import java.time.LocalDate;

@XmlAccessorType(XmlAccessType.FIELD)
public class OneTimeDTO implements Serializable {

    @XmlJavaTypeAdapter(DateAdapter.class)
    private LocalDate maturityDate;

    public LocalDate getMaturityDate() {
        return maturityDate;
    }

    public void setMaturityDate(LocalDate maturityDate) {
        this.maturityDate = maturityDate;
    }

    @Override
    public String toString() {
        return "OneTimeDTO{" +
                "maturityDate=" + maturityDate +
                '}';
    }
}
