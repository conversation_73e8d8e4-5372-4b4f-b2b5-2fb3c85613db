package cz.equa.camapp.service.applicationservice.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Schema(name = "ApplicationStatusChangeDTO", title = "ApplicationStatusChangeDTO", description = "Payload for event <code>onbChannelChange</code>.")
@Data
public class ApplicationStatusChangeDTO implements Serializable {
    @Schema(name = "rejectionReason", description = "rejectionReason", requiredMode = Schema.RequiredMode.NOT_REQUIRED, example = "43")
    private String rejectionReason;

    @Schema(name = "state", description = "state", requiredMode = Schema.RequiredMode.REQUIRED, example = "CANCELED")
    private String state;

    @Schema(name = "subState", description = "subState", requiredMode = Schema.RequiredMode.REQUIRED, example = "BRANCH_REDIRECTED")
    private String subState;
}
