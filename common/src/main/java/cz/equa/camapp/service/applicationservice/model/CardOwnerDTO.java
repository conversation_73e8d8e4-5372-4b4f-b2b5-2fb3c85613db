package cz.equa.camapp.service.applicationservice.model;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;

import java.io.Serializable;

@XmlAccessorType(XmlAccessType.FIELD)
public class CardOwnerDTO implements Serializable {
	private String instPtSrcId;
	private String instPtSrcSysId;
	private String instPtSiebelId;
	private String ptRoleTpId;
	private Boolean ptRoleDelFlag;

	public String getInstPtSrcId() {
		return instPtSrcId;
	}

	public void setInstPtSrcId(String instPtSrcId) {
		this.instPtSrcId = instPtSrcId;
	}

	public String getInstPtSrcSysId() {
		return instPtSrcSysId;
	}

	public void setInstPtSrcSysId(String instPtSrcSysId) {
		this.instPtSrcSysId = instPtSrcSysId;
	}

	public String getInstPtSiebelId() {
		return instPtSiebelId;
	}

	public void setInstPtSiebelId(String instPtSiebelId) {
		this.instPtSiebelId = instPtSiebelId;
	}

	public String getPtRoleTpId() {
		return ptRoleTpId;
	}

	public void setPtRoleTpId(String ptRoleTpId) {
		this.ptRoleTpId = ptRoleTpId;
	}

	public Boolean getPtRoleDelFlag() {
		return ptRoleDelFlag;
	}

	public void setPtRoleDelFlag(Boolean ptRoleDelFlag) {
		this.ptRoleDelFlag = ptRoleDelFlag;
	}
}
