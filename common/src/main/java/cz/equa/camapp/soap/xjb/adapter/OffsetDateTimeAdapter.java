package cz.equa.camapp.soap.xjb.adapter;

import jakarta.xml.bind.annotation.adapters.XmlAdapter;
import lombok.extern.slf4j.Slf4j;

import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

@Slf4j
public class OffsetDateTimeAdapter extends XmlAdapter<String, OffsetDateTime> {

    private static final DateTimeFormatter DATE_TIME_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");
    private static final DateTimeFormatter DATE_TIME_WITHOUT_MILIS_ZONE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss").withZone(ZoneId.systemDefault());
    
    @Override
	public OffsetDateTime unmarshal(String iso8601Date) throws Exception {
		if (iso8601Date == null || iso8601Date.trim().isEmpty()) {
			return null;
		}
        try {
            return OffsetDateTime.parse(iso8601Date, DATE_TIME_FORMAT);
        } catch (DateTimeParseException dtpe) {
            try {
                return OffsetDateTime.parse(iso8601Date, DATE_TIME_WITHOUT_MILIS_ZONE_FORMAT);
            }
            catch (DateTimeParseException dtpeTimeDateWithZone) {
                log.error("Unable to parse date {}", iso8601Date, dtpe);
                throw dtpe;
            }
        }
	}

	@Override
	public String marshal(OffsetDateTime javaDate) throws Exception {
		if (javaDate == null) {
			return null;
		}
		return DATE_TIME_FORMAT.format(javaDate);
	}
}
