package cz.equa.camapp.soap.xjb.adapter;

import java.text.SimpleDateFormat;
import java.util.Calendar;

public class OldDateAdapter extends AbstractDateAdapter {
	private static final String DATE_FORMAT = "yyyy-MM-dd";

	@Override
	protected String marshalCalendar(Calendar date) {
		//Commented because sometimes ESB fails on date with timezone, so date is now without it
		//return javax.xml.bind.DatatypeConverter.printDate(date);
		return new SimpleDateFormat(DATE_FORMAT).format(date.getTime());
	}

}
