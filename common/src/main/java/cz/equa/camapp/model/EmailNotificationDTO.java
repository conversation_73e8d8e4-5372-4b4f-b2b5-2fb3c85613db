package cz.equa.camapp.model;

import cz.equa.camapp.service.applicationservice.model.LoanApplicationDTO;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class EmailNotificationDTO implements Serializable {

    String ftp;
    String to;
    String applState;
    String siebelId;
    String productTp;
    String applTp;
    String applId;
    String rejectDate;
    String approvalDate;
    String cancellationDate;
    String amount;
    String interestRate;
    String instAmt;
    String maturity;

    public EmailNotificationDTO() {
    }

    public static EmailNotificationDTO fromLoanApplication(LoanApplicationDTO dto) {
        EmailNotificationDTO result = new EmailNotificationDTO();
        result.ftp = dto.getFirstTouchPoint();
        result.productTp = dto.getBusProdSubTp();
        result.applTp = dto.getApplTpId();

        return result;
    }

}
