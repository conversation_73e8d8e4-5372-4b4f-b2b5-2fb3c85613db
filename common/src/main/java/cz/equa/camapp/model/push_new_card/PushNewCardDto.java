package cz.equa.camapp.model.push_new_card;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;

import java.math.BigInteger;

@XmlAccessorType(XmlAccessType.FIELD)
public class PushNewCardDto {
    private BigInteger sourceSystemId;
    private ClientDto client;
    private AccountDto creditCardAccount;
    private CardDto card;
    private ServicesDto services;

    public BigInteger getSourceSystemId() {
        return sourceSystemId;
    }

    public void setSourceSystemId(BigInteger sourceSystemId) {
        this.sourceSystemId = sourceSystemId;
    }

    public ClientDto getClient() {
        return client;
    }

    public void setClient(ClientDto client) {
        this.client = client;
    }

    public AccountDto getCreditCardAccount() {
        return creditCardAccount;
    }

    public void setCreditCardAccount(AccountDto creditCardAccount) {
        this.creditCardAccount = creditCardAccount;
    }

    public CardDto getCard() {
        return card;
    }

    public void setCard(CardDto card) {
        this.card = card;
    }

    public ServicesDto getServices() {
        return services;
    }

    public void setServices(ServicesDto services) {
        this.services = services;
    }
}
