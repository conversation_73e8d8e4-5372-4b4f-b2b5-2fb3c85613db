package cz.equa.camapp.model;

import java.io.Serializable;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;

public class RepaidContractNumListDTO implements Serializable {
	private List<BigInteger> repaiedContractNums;

	public RepaidContractNumListDTO() {
		repaiedContractNums = new ArrayList<>();
	}

    public RepaidContractNumListDTO(List<BigInteger> repaiedContractNums) {
        this.repaiedContractNums = repaiedContractNums;
    }
    
    public List<BigInteger> getRepaiedContractNums() {
        return repaiedContractNums;
    }

    public void setRepaiedContractNums(List<BigInteger> repaiedContractNums) {
        this.repaiedContractNums = repaiedContractNums;
    }
    
    public void addRepaiedContractNum(BigInteger repaiedContractNum) {
        this.repaiedContractNums.add(repaiedContractNum);
    }
}
