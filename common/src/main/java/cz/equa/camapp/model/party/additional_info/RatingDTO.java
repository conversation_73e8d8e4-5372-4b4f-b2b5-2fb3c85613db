package cz.equa.camapp.model.party.additional_info;

import cz.equa.camapp.soap.xjb.adapter.DateTimeAdapter;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import java.io.Serializable;
import java.time.ZonedDateTime;

@XmlAccessorType(XmlAccessType.FIELD)
public class RatingDTO implements Serializable {
    
    private String approvedRating;
    
    @XmlJavaTypeAdapter(DateTimeAdapter.class)
    private ZonedDateTime balanceSheetDate;
    
    private String countryOfRisk;
    private String countryRating;
    private String riskClassification;
    
    @XmlJavaTypeAdapter(DateTimeAdapter.class)
    private ZonedDateTime ratingDateofApproval;
    private String ratingModel;
    private String totalSales;

    public RatingDTO() {
        //needed for deserialization
    }

    public String getApprovedRating() {
        return approvedRating;
    }

    public void setApprovedRating(String approvedRating) {
        this.approvedRating = approvedRating;
    }

    public ZonedDateTime getBalanceSheetDate() {
        return balanceSheetDate;
    }

    public void setBalanceSheetDate(ZonedDateTime balanceSheetDate) {
        this.balanceSheetDate = balanceSheetDate;
    }

    public String getCountryOfRisk() {
        return countryOfRisk;
    }

    public void setCountryOfRisk(String countryOfRisk) {
        this.countryOfRisk = countryOfRisk;
    }

    public String getCountryRating() {
        return countryRating;
    }

    public void setCountryRating(String countryRating) {
        this.countryRating = countryRating;
    }

    public String getRiskClassification() {
        return riskClassification;
    }

    public void setRiskClassification(String riskClassification) {
        this.riskClassification = riskClassification;
    }

    public ZonedDateTime getRatingDateofApproval() {
        return ratingDateofApproval;
    }

    public void setRatingDateofApproval(ZonedDateTime ratingDateofApproval) {
        this.ratingDateofApproval = ratingDateofApproval;
    }

    public String getRatingModel() {
        return ratingModel;
    }

    public void setRatingModel(String ratingModel) {
        this.ratingModel = ratingModel;
    }

    public String getTotalSales() {
        return totalSales;
    }

    public void setTotalSales(String totalSales) {
        this.totalSales = totalSales;
    }
}
