package cz.equa.camapp.model.push_new_card;


import cz.equa.camapp.soap.xjb.adapter.DateAdapter;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import java.time.LocalDate;

@XmlAccessorType(XmlAccessType.FIELD)
public class ClientDto {
    private String ownerPartyId;
    private String holderPartyId;
    private String firstName;
    private String lastName;
    private String birthCode;
    @XmlJavaTypeAdapter(DateAdapter.class)
    private LocalDate birthDate;
    private String rpcClientId;
    private String phoneNumber;

    public String getOwnerPartyId() {
        return ownerPartyId;
    }

    public void setOwnerPartyId(String ownerPartyId) {
        this.ownerPartyId = ownerPartyId;
    }

    public String getHolderPartyId() {
        return holderPartyId;
    }

    public void setHolderPartyId(String holderPartyId) {
        this.holderPartyId = holderPartyId;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getBirthCode() {
        return birthCode;
    }

    public void setBirthCode(String birthCode) {
        this.birthCode = birthCode;
    }

    public LocalDate getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(LocalDate birthDate) {
        this.birthDate = birthDate;
    }

    public String getRpcClientId() {
        return rpcClientId;
    }

    public void setRpcClientId(String rpcClientId) {
        this.rpcClientId = rpcClientId;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }
}
