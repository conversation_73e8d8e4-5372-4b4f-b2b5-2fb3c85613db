package cz.equa.camapp.model.parametrization;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;

import java.io.Serializable;
import java.math.BigDecimal;

@XmlAccessorType(XmlAccessType.FIELD)
public class RODInterestProductParametrizationDTO implements Serializable {
    private BigDecimal productInterestId;
    private String productInterestCode;
    private String productInterestCodeId;
    private Boolean isDefault;
    private Double interestRate;
    private Long interestFreeReserveAmount;
    private Long minLimit;
    private Long maxLimit;
    private Long defaultLimit;
    private Long limitMinIncrease1;
    private Long limitMinIncrease2;
    private Long limitIncreaseDivider;
    private Integer feeInit;
    private Integer feeMonthlyUtilization;


    public BigDecimal getProductInterestId() {
        return productInterestId;
    }

    public void setProductInterestId(BigDecimal productInterestId) {
        this.productInterestId = productInterestId;
    }

    public String getProductInterestCode() {
        return productInterestCode;
    }

    public void setProductInterestCode(String productInterestCode) {
        this.productInterestCode = productInterestCode;
    }

    public String getProductInterestCodeId() {
        return productInterestCodeId;
    }

    public void setProductInterestCodeId(String productInterestCodeId) {
        this.productInterestCodeId = productInterestCodeId;
    }

    public Boolean getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Boolean isDefault) {
        this.isDefault = isDefault;
    }

    public Double getInterestRate() {
        return interestRate;
    }

    public void setInterestRate(Double interestRate) {
        this.interestRate = interestRate;
    }

    public Long getInterestFreeReserveAmount() {
        return interestFreeReserveAmount;
    }

    public void setInterestFreeReserveAmount(Long interestFreeReserveAmount) {
        this.interestFreeReserveAmount = interestFreeReserveAmount;
    }

    public Long getMinLimit() {
        return minLimit;
    }

    public void setMinLimit(Long minLimit) {
        this.minLimit = minLimit;
    }

    public Long getMaxLimit() {
        return maxLimit;
    }

    public void setMaxLimit(Long maxLimit) {
        this.maxLimit = maxLimit;
    }

    public Long getDefaultLimit() {
        return defaultLimit;
    }

    public void setDefaultLimit(Long defaultLimit) {
        this.defaultLimit = defaultLimit;
    }

    public Long getLimitMinIncrease1() {
        return limitMinIncrease1;
    }

    public void setLimitMinIncrease1(Long limitMinIncrease1) {
        this.limitMinIncrease1 = limitMinIncrease1;
    }

    public Long getLimitMinIncrease2() {
        return limitMinIncrease2;
    }

    public void setLimitMinIncrease2(Long limitMinIncrease2) {
        this.limitMinIncrease2 = limitMinIncrease2;
    }

    public Long getLimitIncreaseDivider() {
        return limitIncreaseDivider;
    }

    public void setLimitIncreaseDivider(Long limitIncreaseDivider) {
        this.limitIncreaseDivider = limitIncreaseDivider;
    }

    public Integer getFeeInit() {
        return feeInit;
    }

    public void setFeeInit(Integer feeInit) {
        this.feeInit = feeInit;
    }

    public Integer getFeeMonthlyUtilization() {
        return feeMonthlyUtilization;
    }

    public void setFeeMonthlyUtilization(Integer feeMonthlyUtilization) {
        this.feeMonthlyUtilization = feeMonthlyUtilization;
    }
}
