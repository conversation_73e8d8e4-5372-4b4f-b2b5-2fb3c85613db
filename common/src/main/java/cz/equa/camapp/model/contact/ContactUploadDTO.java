package cz.equa.camapp.model.contact;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@XmlAccessorType(XmlAccessType.FIELD)
public class ContactUploadDTO implements Serializable {

    private String partId;
    private AddressToInsertDTO addressToInsert;
    private List<AddressToInsertDTO> addressesToInsert;
    private EmailToInsertDTO emailToInsert;
    private PhoneToInsertDTO phoneToInsert;
    private PhoneToUpdateDTO phoneToUpdate;

    public String getPartId() {
        return partId;
    }

    public void setPartId(String partId) {
        this.partId = partId;
    }

    public AddressToInsertDTO getAddressToInsert() {
        return addressToInsert;
    }

    public void setAddressToInsert(AddressToInsertDTO addressToInsert) {
        this.addressToInsert = addressToInsert;
    }

    public List<AddressToInsertDTO> getAddressesToInsert() {
        if (addressesToInsert == null) {
            addressesToInsert = new ArrayList<>();
        }
        return addressesToInsert;
    }

    public void setAddressesToInsert(List<AddressToInsertDTO> addressesToInsert) {
        this.addressesToInsert = addressesToInsert;
    }

    public EmailToInsertDTO getEmailToInsert() {
        return emailToInsert;
    }

    public void setEmailToInsert(EmailToInsertDTO emailToInsert) {
        this.emailToInsert = emailToInsert;
    }

    public PhoneToInsertDTO getPhoneToInsert() {
        return phoneToInsert;
    }

    public void setPhoneToInsert(PhoneToInsertDTO phoneToInsert) {
        this.phoneToInsert = phoneToInsert;
    }

    public PhoneToUpdateDTO getPhoneToUpdate() {
        return phoneToUpdate;
    }

    public void setPhoneToUpdate(PhoneToUpdateDTO phoneToUpdate) {
        this.phoneToUpdate = phoneToUpdate;
    }
}
