package cz.equa.camapp.model.update_party.additional_info;

import cz.equa.camapp.model.update_party.UpdateItemDTO;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;

import java.io.Serializable;

@XmlAccessorType(XmlAccessType.FIELD)
public class TaxationDTO implements Serializable {
    
    private UpdateItemDTO taxResidency;
    private UpdateItemDTO taxId;
    private CrsDTO crs;

    public TaxationDTO() {
        //needed for deserialization
    }

    public UpdateItemDTO getTaxResidency() {
        return taxResidency;
    }

    public void setTaxResidency(UpdateItemDTO taxResidency) {
        this.taxResidency = taxResidency;
    }

    public UpdateItemDTO getTaxId() {
        return taxId;
    }

    public void setTaxId(UpdateItemDTO taxId) {
        this.taxId = taxId;
    }

    public CrsDTO getCrs() {
        return crs;
    }

    public void setCrs(CrsDTO crs) {
        this.crs = crs;
    }
}
