package cz.equa.camapp.model;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;

import java.io.Serializable;
import java.util.List;

@XmlAccessorType(XmlAccessType.FIELD)
public class ListOfLovFinInstnDTO implements Serializable {
    
    private List<LovFinInstnDTO> listOfLovFinInstn;
    
    public ListOfLovFinInstnDTO() {
        //needed for deserialization
    }
    
    public ListOfLovFinInstnDTO(List<LovFinInstnDTO> listOfLovFinInstn) {
        this.listOfLovFinInstn = listOfLovFinInstn;
    }

    public List<LovFinInstnDTO> getListOfLovFinInstn() {
        return listOfLovFinInstn;
    }

    public void setListOfLovFinInstn(List<LovFinInstnDTO> listOfLovFinInstn) {
        this.listOfLovFinInstn = listOfLovFinInstn;
    }
}
