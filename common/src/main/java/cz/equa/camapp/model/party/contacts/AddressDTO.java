package cz.equa.camapp.model.party.contacts;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;

import java.io.Serializable;

@XmlAccessorType(XmlAccessType.FIELD)
public class AddressDTO implements Serializable {
    public static final String ADDRESS_TYPE_RESIDENCY = "RESIDENCY";
    public static final String ADDRESS_TYPE_MAIL1 = "MAIL1";

    private String srcReferenceId;
    private String addressId;
    private String addressName;
    private String sblAddressSlot;
    private String customerRole;
    private String addressee;
    private String additionalInfo;
    private String street;
    private String number;
    private String city;
    private String postalCode;
    private String country;
    private Boolean isResidency;
    private Boolean preferred;
    private Boolean undeliverableFlag;
    private String classification;
    private String comment;
    private String addressPointId;
    private Boolean dataQualityVerified;
    private Boolean modifiedAfterVerification;
    private String addressType;

    public AddressDTO() {
        //needed for deserialization
    }

    public String getSrcReferenceId() {
        return srcReferenceId;
    }

    public void setSrcReferenceId(String srcReferenceId) {
        this.srcReferenceId = srcReferenceId;
    }

    public String getAddressId() {
        return addressId;
    }

    public void setAddressId(String addressId) {
        this.addressId = addressId;
    }

    public String getAddressName() {
        return addressName;
    }

    public void setAddressName(String addressName) {
        this.addressName = addressName;
    }

    public String getSblAddressSlot() {
        return sblAddressSlot;
    }

    public void setSblAddressSlot(String sblAddressSlot) {
        this.sblAddressSlot = sblAddressSlot;
    }

    public String getCustomerRole() {
        return customerRole;
    }

    public void setCustomerRole(String customerRole) {
        this.customerRole = customerRole;
    }

    public String getAddressee() {
        return addressee;
    }

    public void setAddressee(String addressee) {
        this.addressee = addressee;
    }

    public String getAdditionalInfo() {
        return additionalInfo;
    }

    public void setAdditionalInfo(String additionalInfo) {
        this.additionalInfo = additionalInfo;
    }

    public String getStreet() {
        return street;
    }

    public void setStreet(String street) {
        this.street = street;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getPostalCode() {
        return postalCode;
    }

    public void setPostalCode(String postalCode) {
        this.postalCode = postalCode;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public Boolean getIsResidency() {
        return isResidency;
    }

    public void setIsResidency(Boolean isResidency) {
        this.isResidency = isResidency;
    }

    public Boolean getPreferred() {
        return preferred;
    }

    public void setPreferred(Boolean preferred) {
        this.preferred = preferred;
    }

    public Boolean getUndeliverableFlag() {
        return undeliverableFlag;
    }

    public void setUndeliverableFlag(Boolean undeliverableFlag) {
        this.undeliverableFlag = undeliverableFlag;
    }

    public String getClassification() {
        return classification;
    }

    public void setClassification(String classification) {
        this.classification = classification;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getAddressPointId() {
        return addressPointId;
    }

    public void setAddressPointId(String addressPointId) {
        this.addressPointId = addressPointId;
    }

    public Boolean getDataQualityVerified() {
        return dataQualityVerified;
    }

    public void setDataQualityVerified(Boolean dataQualityVerified) {
        this.dataQualityVerified = dataQualityVerified;
    }

    public Boolean getModifiedAfterVerification() {
        return modifiedAfterVerification;
    }

    public void setModifiedAfterVerification(Boolean modifiedAfterVerification) {
        this.modifiedAfterVerification = modifiedAfterVerification;
    }

    public String getAddressType() {
        return addressType;
    }

    public void setAddressType(String addressType) {
        this.addressType = addressType;
    }
}
