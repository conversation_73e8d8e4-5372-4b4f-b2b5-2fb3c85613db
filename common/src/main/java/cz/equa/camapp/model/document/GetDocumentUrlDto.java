package cz.equa.camapp.model.document;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

@Getter
@Setter
public class GetDocumentUrlDto implements Serializable {
    private String format;
    private String filename;
    private String documentUrl;
    private BigDecimal contentSize;

    public GetDocumentUrlDto() {
        //needed for deserialization
    }

    public GetDocumentUrlDto(String format, String filename, String documentUrl) {
        this.format = format;
        this.filename = filename;
        this.documentUrl = documentUrl;
        this.contentSize = null;
    }

    public GetDocumentUrlDto(String format, String filename, String documentUrl, BigDecimal contentSize) {
        this.format = format;
        this.filename = filename;
        this.documentUrl = documentUrl;
        this.contentSize = contentSize;
    }
}
