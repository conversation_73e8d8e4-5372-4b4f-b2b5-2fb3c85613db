package cz.equa.camapp.model.party.basic_info;

import cz.equa.camapp.model.party.BirthInfoDTO;
import cz.equa.camapp.model.party.DeathInfoDTO;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlType;

import java.io.Serializable;
import java.util.List;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "basic_info.PrivatePartyDTO")
public class PrivatePartyDTO implements Serializable {
    
    private String titlesBefore;
    private String firstName;
    private String middleNames;
    private String lastName;
    private String birthLastName;
    private String titlesBehind;
    private String vocative;
    private String gender;
    private String title;
    private BirthInfoDTO birthInfo;
    private DeathInfoDTO deathInfo;
    private Boolean minor;
    private Boolean isStaff;
    private String citizenship;
    private List<String> secondaryCitizenships;

    public PrivatePartyDTO() {
        //needed for deserialization
    }
    
    public String getTitlesBefore() {
        return titlesBefore;
    }

    public void setTitlesBefore(String titlesBefore) {
        this.titlesBefore = titlesBefore;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getMiddleNames() {
        return middleNames;
    }

    public void setMiddleNames(String middleNames) {
        this.middleNames = middleNames;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getBirthLastName() {
        return birthLastName;
    }

    public void setBirthLastName(String birthLastName) {
        this.birthLastName = birthLastName;
    }

    public String getTitlesBehind() {
        return titlesBehind;
    }

    public void setTitlesBehind(String titlesBehind) {
        this.titlesBehind = titlesBehind;
    }

    public String getVocative() {
        return vocative;
    }

    public void setVocative(String vocative) {
        this.vocative = vocative;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public BirthInfoDTO getBirthInfo() {
        return birthInfo;
    }

    public void setBirthInfo(BirthInfoDTO birthInfo) {
        this.birthInfo = birthInfo;
    }

    public DeathInfoDTO getDeathInfo() {
        return deathInfo;
    }

    public void setDeathInfo(DeathInfoDTO deathInfo) {
        this.deathInfo = deathInfo;
    }

    public Boolean getMinor() {
        return minor;
    }

    public void setMinor(Boolean minor) {
        this.minor = minor;
    }

    public Boolean getIsStaff() {
        return isStaff;
    }

    public void setIsStaff(Boolean isStaff) {
        this.isStaff = isStaff;
    }

    public String getCitizenship() {
        return citizenship;
    }

    public void setCitizenship(String citizenship) {
        this.citizenship = citizenship;
    }

    public List<String> getSecondaryCitizenships() {
        return secondaryCitizenships;
    }

    public void setSecondaryCitizenships(List<String> secondaryCitizenships) {
        this.secondaryCitizenships = secondaryCitizenships;
    }
}
