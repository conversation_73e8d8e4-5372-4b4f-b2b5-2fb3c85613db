package cz.equa.camapp.model.parametrization;


import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;

import java.io.Serializable;

@XmlAccessorType(XmlAccessType.FIELD)
public class RCCInsuranceParametrizationDTO implements Serializable {
    private String insuranceCode;
    private String insuranceName;
    private String rpCServiceCode;
    private Boolean active;
    private TypeEnum type;
    private String priceCzk;
    private String pricePct;


    public enum TypeEnum {
        M, O;

        public static TypeEnum fromValue(String value) {
            return TypeEnum.valueOf(value);
        }
    }

    public String getInsuranceCode() {
        return insuranceCode;
    }

    public void setInsuranceCode(String insuranceCode) {
        this.insuranceCode = insuranceCode;
    }

    public String getInsuranceName() {
        return insuranceName;
    }

    public void setInsuranceName(String insuranceName) {
        this.insuranceName = insuranceName;
    }

    public String getRPCServiceCode() {
        return rpCServiceCode;
    }

    public void setRPCServiceCode(String rpCServiceCode) {
        this.rpCServiceCode = rpCServiceCode;
    }

    public Boolean getActive() {
        return active;
    }

    public void setActive(Boolean active) {
        this.active = active;
    }

    public TypeEnum getType() {
        return type;
    }

    public void setType(TypeEnum type) {
        this.type = type;
    }

    public String getPriceCzk() {
        return priceCzk;
    }

    public void setPriceCzk(String priceCzk) {
        this.priceCzk = priceCzk;
    }

    public String getPricePct() {
        return pricePct;
    }

    public void setPricePct(String pricePct) {
        this.pricePct = pricePct;
    }
}


