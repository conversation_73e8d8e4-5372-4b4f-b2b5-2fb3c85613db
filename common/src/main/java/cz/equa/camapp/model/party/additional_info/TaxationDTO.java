package cz.equa.camapp.model.party.additional_info;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;

import java.io.Serializable;

@XmlAccessorType(XmlAccessType.FIELD)
public class TaxationDTO implements Serializable {
    
    private String taxResidency;
    private String taxId;
    private String taxOffice;
    private Boolean beneficialStatement;
    private String beneficialOwner;
    private FatcaDTO fatca;
    private CrsDTO crs;

    public TaxationDTO() {
        //needed for deserialization
    }

    public String getTaxResidency() {
        return taxResidency;
    }

    public void setTaxResidency(String taxResidency) {
        this.taxResidency = taxResidency;
    }

    public String getTaxId() {
        return taxId;
    }

    public void setTaxId(String taxId) {
        this.taxId = taxId;
    }

    public String getTaxOffice() {
        return taxOffice;
    }

    public void setTaxOffice(String taxOffice) {
        this.taxOffice = taxOffice;
    }

    public Boolean getBeneficialStatement() {
        return beneficialStatement;
    }

    public void setBeneficialStatement(Boolean beneficialStatement) {
        this.beneficialStatement = beneficialStatement;
    }

    public String getBeneficialOwner() {
        return beneficialOwner;
    }

    public void setBeneficialOwner(String beneficialOwner) {
        this.beneficialOwner = beneficialOwner;
    }

    public FatcaDTO getFatca() {
        return fatca;
    }

    public void setFatca(FatcaDTO fatca) {
        this.fatca = fatca;
    }

    public CrsDTO getCrs() {
        return crs;
    }

    public void setCrs(CrsDTO crs) {
        this.crs = crs;
    }
}
