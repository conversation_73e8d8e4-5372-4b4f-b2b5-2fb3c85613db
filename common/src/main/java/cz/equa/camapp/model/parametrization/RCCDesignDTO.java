package cz.equa.camapp.model.parametrization;


import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@XmlAccessorType(XmlAccessType.FIELD)
public class RCCDesignDTO implements Serializable {
    private String ribbon;
    private String name;
    private String design;
    private String motto;
    private String language;
    private Integer listOrder;
    private List<RCCDesignDescriptionDTO> reviewDescriptions = new ArrayList<>();
    private List<RCCDesignDescriptionDTO> detailDescriptions = new ArrayList<>();

    public String getRibbon() {
        return ribbon;
    }

    public void setRibbon(String ribbon) {
        this.ribbon = ribbon;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDesign() {
        return design;
    }

    public void setDesign(String design) {
        this.design = design;
    }

    public String getMotto() {
        return motto;
    }

    public void setMotto(String motto) {
        this.motto = motto;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public Integer getListOrder() {
        return listOrder;
    }

    public void setListOrder(Integer listOrder) {
        this.listOrder = listOrder;
    }

    public List<RCCDesignDescriptionDTO> getReviewDescriptions() {
        return reviewDescriptions;
    }

    public void setReviewDescriptions(List<RCCDesignDescriptionDTO> reviewDescriptions) {
        this.reviewDescriptions = reviewDescriptions;
    }

    public List<RCCDesignDescriptionDTO> getDetailDescriptions() {
        return detailDescriptions;
    }

    public void setDetailDescriptions(List<RCCDesignDescriptionDTO> detailDescriptions) {
        this.detailDescriptions = detailDescriptions;
    }
}


