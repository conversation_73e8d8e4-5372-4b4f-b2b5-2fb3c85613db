package cz.equa.camapp.model.party.basic_info;

import cz.equa.camapp.soap.xjb.adapter.DateAdapter;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import java.io.Serializable;
import java.time.LocalDate;

@XmlAccessorType(XmlAccessType.FIELD)
public class IdentificationDTO implements Serializable {
    
    private String type;
    private String code;
    
    @XmlJavaTypeAdapter(DateAdapter.class)
    private LocalDate validFrom;

    public IdentificationDTO() {
        //needed for deserialization
    }
    
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public LocalDate getValidFrom() {
        return validFrom;
    }

    public void setValidFrom(LocalDate validFrom) {
        this.validFrom = validFrom;
    }
}
