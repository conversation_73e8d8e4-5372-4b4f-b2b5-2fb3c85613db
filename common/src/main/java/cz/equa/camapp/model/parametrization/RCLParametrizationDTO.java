package cz.equa.camapp.model.parametrization;

import cz.equa.camapp.soap.xjb.adapter.DateAdapter;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@XmlAccessorType(XmlAccessType.FIELD)
public class RCLParametrizationDTO implements Serializable {
    private String busProdTp;
    private String busProdTpName;
    private String busProdClassId;
    private Integer version;
    @XmlJavaTypeAdapter(DateAdapter.class)
    private LocalDate validFrom;
    @XmlJavaTypeAdapter(DateAdapter.class)
    private LocalDate validTo;

    private List<RCLSubtypeParametrizationDTO> subtypes = new ArrayList<>();

    public String getBusProdTp() {
        return busProdTp;
    }

    public void setBusProdTp(String busProdTp) {
        this.busProdTp = busProdTp;
    }

    public String getBusProdTpName() {
        return busProdTpName;
    }

    public void setBusProdTpName(String busProdTpName) {
        this.busProdTpName = busProdTpName;
    }

    public String getBusProdClassId() {
        return busProdClassId;
    }

    public void setBusProdClassId(String busProdClassId) {
        this.busProdClassId = busProdClassId;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public LocalDate getValidFrom() {
        return validFrom;
    }

    public void setValidFrom(LocalDate validFrom) {
        this.validFrom = validFrom;
    }

    public LocalDate getValidTo() {
        return validTo;
    }

    public void setValidTo(LocalDate validTo) {
        this.validTo = validTo;
    }

    public List<RCLSubtypeParametrizationDTO> getSubtypes() {
        return subtypes;
    }

    public void setSubtypes(List<RCLSubtypeParametrizationDTO> subtypes) {
        this.subtypes = subtypes;
    }
}
