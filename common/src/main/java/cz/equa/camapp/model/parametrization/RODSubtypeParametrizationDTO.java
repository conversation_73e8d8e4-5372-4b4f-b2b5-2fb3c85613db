package cz.equa.camapp.model.parametrization;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

@XmlAccessorType(XmlAccessType.FIELD)
public class RODSubtypeParametrizationDTO implements Serializable {
    private String busProdSubTp;
    private String busProdSubTpName;
    private Long busProdSubTpId;
    private LocalDate validFrom;
    private LocalDate validTo;
    private Integer MPROD;
    private List<RODInterestProductParametrizationDTO> interestProducts;

    public String getBusProdSubTp() {
        return busProdSubTp;
    }

    public void setBusProdSubTp(String busProdSubTp) {
        this.busProdSubTp = busProdSubTp;
    }

    public String getBusProdSubTpName() {
        return busProdSubTpName;
    }

    public void setBusProdSubTpName(String busProdSubTpName) {
        this.busProdSubTpName = busProdSubTpName;
    }

    public Long getBusProdSubTpId() {
        return busProdSubTpId;
    }

    public void setBusProdSubTpId(Long busProdSubTpId) {
        this.busProdSubTpId = busProdSubTpId;
    }

    public LocalDate getValidFrom() {
        return validFrom;
    }

    public void setValidFrom(LocalDate validFrom) {
        this.validFrom = validFrom;
    }

    public LocalDate getValidTo() {
        return validTo;
    }

    public void setValidTo(LocalDate validTo) {
        this.validTo = validTo;
    }

    public Integer getMPROD() {
        return MPROD;
    }

    public void setMPROD(Integer MPROD) {
        this.MPROD = MPROD;
    }

    public List<RODInterestProductParametrizationDTO> getInterestProducts() {
        return interestProducts;
    }

    public void setInterestProducts(List<RODInterestProductParametrizationDTO> interestProducts) {
        this.interestProducts = interestProducts;
    }

}
