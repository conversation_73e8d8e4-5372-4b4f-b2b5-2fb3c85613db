package cz.equa.camapp.model.parametrization;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@XmlAccessorType(XmlAccessType.FIELD)
public class RODParametrizationDTO implements Serializable {
    private String busProdTp;
    private String busProdTpName;
    private String busProdClassId;
    private Integer version;
    private LocalDate validFrom;
    private LocalDate validTo;

    private List<RODSubtypeParametrizationDTO> subtypes = new ArrayList<>();

    public String getBusProdTp() {
        return busProdTp;
    }

    public void setBusProdTp(String busProdTp) {
        this.busProdTp = busProdTp;
    }

    public String getBusProdTpName() {
        return busProdTpName;
    }

    public void setBusProdTpName(String busProdTpName) {
        this.busProdTpName = busProdTpName;
    }

    public String getBusProdClassId() {
        return busProdClassId;
    }

    public void setBusProdClassId(String busProdClassId) {
        this.busProdClassId = busProdClassId;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public LocalDate getValidFrom() {
        return validFrom;
    }

    public void setValidFrom(LocalDate validFrom) {
        this.validFrom = validFrom;
    }

    public LocalDate getValidTo() {
        return validTo;
    }

    public void setValidTo(LocalDate validTo) {
        this.validTo = validTo;
    }

    public List<RODSubtypeParametrizationDTO> getSubtypes() {
        return subtypes;
    }

    public void setSubtypes(List<RODSubtypeParametrizationDTO> subtypes) {
        this.subtypes = subtypes;
    }
}
