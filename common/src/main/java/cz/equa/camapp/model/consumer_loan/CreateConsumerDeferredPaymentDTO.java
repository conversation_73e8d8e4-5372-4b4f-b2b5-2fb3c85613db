package cz.equa.camapp.model.consumer_loan;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;

public class CreateConsumerDeferredPaymentDTO implements Serializable {
    private String currencyCode;
    private String productCode;
    private CreateConsumerDatesDTO dates;
    private String orderId;
    private String merchantId;
    private String merchantPartyId;
    private String agreementNumber;
    private String documentDmsId;
    private String documentDmsType;
    private String loanName;
    private String loanDescription;
    private BigDecimal requestedLimit;
    private BigDecimal approvedLimit;
    private BigInteger instalmentCount;
    private BigInteger repaymentDays;
    private String loanType;
    private String applicationType;
    private String paramType;
    private String approvalProcess;
    private String businessAction;

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public CreateConsumerDatesDTO getDates() {
        return dates;
    }

    public void setDates(CreateConsumerDatesDTO dates) {
        this.dates = dates;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getMerchantPartyId() {
        return merchantPartyId;
    }

    public void setMerchantPartyId(String merchantPartyId) {
        this.merchantPartyId = merchantPartyId;
    }

    public String getAgreementNumber() {
        return agreementNumber;
    }

    public void setAgreementNumber(String agreementNumber) {
        this.agreementNumber = agreementNumber;
    }

    public String getDocumentDmsId() {
        return documentDmsId;
    }

    public void setDocumentDmsId(String documentDmsId) {
        this.documentDmsId = documentDmsId;
    }

    public String getDocumentDmsType() {
        return documentDmsType;
    }

    public void setDocumentDmsType(String documentDmsType) {
        this.documentDmsType = documentDmsType;
    }

    public String getLoanName() {
        return loanName;
    }

    public void setLoanName(String loanName) {
        this.loanName = loanName;
    }

    public String getLoanDescription() {
        return loanDescription;
    }

    public void setLoanDescription(String loanDescription) {
        this.loanDescription = loanDescription;
    }

    public BigDecimal getRequestedLimit() {
        return requestedLimit;
    }

    public void setRequestedLimit(BigDecimal requestedLimit) {
        this.requestedLimit = requestedLimit;
    }

    public BigDecimal getApprovedLimit() {
        return approvedLimit;
    }

    public void setApprovedLimit(BigDecimal approvedLimit) {
        this.approvedLimit = approvedLimit;
    }

    public BigInteger getInstalmentCount() {
        return instalmentCount;
    }

    public void setInstalmentCount(BigInteger instalmentCount) {
        this.instalmentCount = instalmentCount;
    }

    public BigInteger getRepaymentDays() {
        return repaymentDays;
    }

    public void setRepaymentDays(BigInteger repaymentDays) {
        this.repaymentDays = repaymentDays;
    }

    public String getLoanType() {
        return loanType;
    }

    public void setLoanType(String loanType) {
        this.loanType = loanType;
    }

    public String getApplicationType() {
        return applicationType;
    }

    public void setApplicationType(String applicationType) {
        this.applicationType = applicationType;
    }

    public String getParamType() {
        return paramType;
    }

    public void setParamType(String paramType) {
        this.paramType = paramType;
    }

    public String getApprovalProcess() {
        return approvalProcess;
    }

    public void setApprovalProcess(String approvalProcess) {
        this.approvalProcess = approvalProcess;
    }

    public String getBusinessAction() {
        return businessAction;
    }

    public void setBusinessAction(String businessAction) {
        this.businessAction = businessAction;
    }
}
