package cz.equa.camapp.model.update_party.additional_info;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;

import java.io.Serializable;

@XmlAccessorType(XmlAccessType.FIELD)
public class AmlDTO implements Serializable {

    private String pepCheck;
    private String plannedTransactionsType;

    public AmlDTO() {
        //needed for deserialization
    }

    public String getPepCheck() {
        return pepCheck;
    }

    public void setPepCheck(String pepCheck) {
        this.pepCheck = pepCheck;
    }

    public String getPlannedTransactionsType() {
        return plannedTransactionsType;
    }

    public void setPlannedTransactionsType(String plannedTransactionsType) {
        this.plannedTransactionsType = plannedTransactionsType;
    }
}
