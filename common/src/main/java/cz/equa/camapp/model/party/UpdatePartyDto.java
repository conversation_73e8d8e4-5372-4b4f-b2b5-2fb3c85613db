package cz.equa.camapp.model.party;

import cz.equa.camapp.model.update_party.additional_info.AdditionalInfoDTO;
import cz.equa.camapp.model.update_party.basic_info.BasicInfoDTO;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;

import java.io.Serializable;

@XmlAccessorType(XmlAccessType.FIELD)
public class UpdatePartyDto implements Serializable {

    private String id;
    private BasicInfoDTO basicInfo;
    private AdditionalInfoDTO additionalInfo;


    public UpdatePartyDto() {
        // needed for deserialization
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public BasicInfoDTO getBasicInfo() {
        return basicInfo;
    }

    public void setBasicInfo(BasicInfoDTO basicInfo) {
        this.basicInfo = basicInfo;
    }

    public AdditionalInfoDTO getAdditionalInfo() {
        return additionalInfo;
    }

    public void setAdditionalInfo(AdditionalInfoDTO additionalInfo) {
        this.additionalInfo = additionalInfo;
    }
}
