package cz.equa.camapp.lovs;

import cz.equa.camapp.constants.LovEnum;

public class LovPtStat extends AbstractLov {
	public static final LovPtStat CORPORATE = new LovPtStat("CORP");
	public static final LovPtStat ENTERPRISE_APPLICATION = new LovPtStat("ENTPR_APPL");
	public static final LovPtStat ENTREPRENEUR_CLIENT = new LovPtStat("ENTP_CLIENT");
	public static final LovPtStat INDIVIDUAL = new LovPtStat("INDIV");
	public static final LovPtStat LEAD = new LovPtStat("LEAD");
	public static final LovPtStat REGISTERED_PERSON = new LovPtStat("REGD_PRSN");
	public static final LovPtStat UNVERIFIED_PERSON = new LovPtStat("UNVER_PRSN");

    public LovPtStat() {
        super();
    }
    
	public LovPtStat(String code) {
		super(LovEnum.PT_STAT, code, null);
	}
}
