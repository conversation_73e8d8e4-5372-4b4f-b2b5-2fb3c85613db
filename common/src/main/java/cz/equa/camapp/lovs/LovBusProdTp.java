package cz.equa.camapp.lovs;

import cz.equa.camapp.constants.LovEnum;

public class LovBusProdTp extends AbstractLov {
	public static final LovBusProdTp BALICEK_SLUZEB_LUPUS = new LovBusProdTp("PKGSRVC");
	public static final LovBusProdTp BILL_PROTECTION_INSURANCE = new LovBusProdTp("BPI");
	public static final LovBusProdTp CNB_CLEARING = new LovBusProdTp("CBCL");
	public static final LovBusProdTp CURRENT_PROFITLOSS = new LovBusProdTp("PLCU");
	public static final LovBusProdTp CARD_AND_PERSONAL_BELONGINGS_INSURANCE = new LovBusProdTp("CBI");
	public static final LovBusProdTp COMERCIAL_TERM_DEPOSIT = new LovBusProdTp("CTD");
	public static final LovBusProdTp COMERCIAL_BANK_GUARANTEE = new LovBusProdTp("CBG");
	public static final LovBusProdTp COMERCIAL_BANK_GUARANTEE_FOR_LEGAL_PERSON = new LovBusProdTp("ZAPO");
	public static final LovBusProdTp COMERCIAL_BANK_GUARANTEE_FOR_LEGAL_PERSON2 = new LovBusProdTp("ZRPO");
	public static final LovBusProdTp COMERCIAL_BANK_GUARANTEE_FOR_NATURAL_PERSON = new LovBusProdTp("ZAFO");
	public static final LovBusProdTp COMERCIAL_BANK_GUARANTEE_FOR_NATURAL_PERSON2 = new LovBusProdTp("ZRFO");
	public static final LovBusProdTp COMERCIAL_BANK_GUARANTEE_FOR_NATURAL_PERSON_BUSINESS = new LovBusProdTp("ZAFP");
	public static final LovBusProdTp COMERCIAL_BANK_GUARANTEE_FOR_NATURAL_PERSON_BUSINESS2 = new LovBusProdTp("ZRFP");
	public static final LovBusProdTp COMERCIAL_CLOSED_END_LOAN = new LovBusProdTp("CCL");
	public static final LovBusProdTp COMERCIAL_CURRENCY_FORWARD = new LovBusProdTp("CXF");
	public static final LovBusProdTp COMERCIAL_CURRENT_ACCOUNT = new LovBusProdTp("CCA");
	public static final LovBusProdTp COMERCIAL_FACTORING = new LovBusProdTp("CFA");
	public static final LovBusProdTp COMERCIAL_LETTER_OF_CREDIT = new LovBusProdTp("CLC");
	public static final LovBusProdTp COMERCIAL_OVERDRAFT = new LovBusProdTp("COD");
	public static final LovBusProdTp COMERCIAL_REVOLVING = new LovBusProdTp("CRV");
	public static final LovBusProdTp COMERCIAL_SAVING_ACOUNT = new LovBusProdTp("CSA");
	public static final LovBusProdTp COMERCIAL_SAVING_ACOUNT_2 = new LovBusProdTp("CSA2");
	public static final LovBusProdTp COMERCIAL_SAVING_ACOUNT_2_WITH_NOTICE_PERIOD = new LovBusProdTp("CSA2_NP");
	public static final LovBusProdTp COMMERCIAL_INVESTMENT_LOAN = new LovBusProdTp("CIL");
	public static final LovBusProdTp COMMERCIAL_LOAN_COMMITMENT = new LovBusProdTp("CLCO");
	public static final LovBusProdTp COMMERCIAL_LOAN_COMMITMENT_FOR_LEGAL_PERSON = new LovBusProdTp("PRPO");
	public static final LovBusProdTp COMMERCIAL_LOAN_COMMITMENT_FOR_NATURAL_PERSON = new LovBusProdTp("PRFO");
	public static final LovBusProdTp COMMERCIAL_LOAN_COMMITMENT_FOR_NATURAL_PERSON_BUSINESS = new LovBusProdTp("PRFP");
	public static final LovBusProdTp COMMERCIAL_MORTGAGE = new LovBusProdTp("CMG");
	public static final LovBusProdTp DEBIT_CARD = new LovBusProdTp("SCD");
	public static final LovBusProdTp DIRECT_DEBIT = new LovBusProdTp("SDD");
	public static final LovBusProdTp DROBNÉ_RETAIL_INVESTICE = new LovBusProdTp("MPMIC");
	public static final LovBusProdTp ESCROW_CURRENT_ACCOUNT = new LovBusProdTp("ECA");
	public static final LovBusProdTp FORWARD_BANKS = new LovBusProdTp("FFB0");
	public static final LovBusProdTp FORWARD_DEAL_CORRECTIONS = new LovBusProdTp("FFB1");
	public static final LovBusProdTp HEALTH_ASSISTANCE = new LovBusProdTp("HTA");
	public static final LovBusProdTp HOME_ASSISTANCE = new LovBusProdTp("HMA");
	public static final LovBusProdTp HOUSEHOLD_INSURANCE = new LovBusProdTp("HHI");
	public static final LovBusProdTp INTERNET_BANKING = new LovBusProdTp("IB");
	public static final LovBusProdTp INVESTMENT_LOAN_FOR_LEGAL_PERSON = new LovBusProdTp("UKPO");
	public static final LovBusProdTp INVESTMENT_LOAN_FOR_NATURAL_PERSON = new LovBusProdTp("UKFO");
	public static final LovBusProdTp INVESTMENT_LOAN_FOR_NATURAL_PERSON_BUSINESS = new LovBusProdTp("UKFP");
	public static final LovBusProdTp LORO_ACCOUNTS = new LovBusProdTp("LORA");
	public static final LovBusProdTp LAW_ASSISTANCE = new LovBusProdTp("LWA");
	public static final LovBusProdTp MM_BORROWING_DEAL_STANDARD = new LovBusProdTp("MBST");
	public static final LovBusProdTp MM_DEPOSIT_CNB = new LovBusProdTp("MPCB");
	public static final LovBusProdTp MM_DEPOSIT_DEAL_STANDARD = new LovBusProdTp("MPST");
	public static final LovBusProdTp NOSTRO_ACCOUNTS = new LovBusProdTp("NOSA");
	public static final LovBusProdTp PAYMENT_SUSPENSE_ACCOUNT = new LovBusProdTp("SUSA");
	public static final LovBusProdTp PAYMENT_PROTECTION_INSURANCE = new LovBusProdTp("PABL");
	public static final LovBusProdTp PAYMENT_PROTECTION_INSURANCE2 = new LovBusProdTp("PPI");
	public static final LovBusProdTp PROPERTY_INSURANCE = new LovBusProdTp("PTI");
	public static final LovBusProdTp RETAIL_FAIR_DEAL = new LovBusProdTp("RTF");
	public static final LovBusProdTp RETAIL_STANDARD = new LovBusProdTp("RTS");
	public static final LovBusProdTp RETAIL_CLOSED_END_LOAN = new LovBusProdTp("RCL");
	public static final LovBusProdTp RETAIL_CREDIT_CARD = new LovBusProdTp("RCC");
	public static final LovBusProdTp RETAIL_CURRENT_ACCOUNT = new LovBusProdTp("RCA");
	public static final LovBusProdTp RETAIL_MORTGAGE = new LovBusProdTp("RML");
	public static final LovBusProdTp RETAIL_OVERDRAFT = new LovBusProdTp("ROD");
	public static final LovBusProdTp RETAIL_SAVING_ACOUNT = new LovBusProdTp("RSA");
	public static final LovBusProdTp SIPO = new LovBusProdTp("SIPO");
	public static final LovBusProdTp SAVINGS_ACCOUNT_WITH_NOTICE_PERIOD = new LovBusProdTp("CSA_NP");
	public static final LovBusProdTp SPOT_BANKS = new LovBusProdTp("FPB0");
	public static final LovBusProdTp SPOT_BANKS_T0 = new LovBusProdTp("FPB1");
	public static final LovBusProdTp STANDARDNÍ_RETAIL_INVESTICE = new LovBusProdTp("MPRST");
	public static final LovBusProdTp SUBORDINATED_DEPOSIT = new LovBusProdTp("RPV");
	public static final LovBusProdTp SUBORDINATED_DEPOSITě = new LovBusProdTp("CPV");
	public static final LovBusProdTp SWAP_LONG_LEG_BANKS_FW_FW = new LovBusProdTp("FLB2");
	public static final LovBusProdTp SWAP_LONG_LEG_BANKS_SP_FW = new LovBusProdTp("FLB0");
	public static final LovBusProdTp SWAP_LONG_LEG_BANKS_SP_SP = new LovBusProdTp("FLB1");
	public static final LovBusProdTp SWAP_SHORT_LEG_BANKS_FW_FW = new LovBusProdTp("FSB2");
	public static final LovBusProdTp SWAP_SHORT_LEG_BANKS_SP_FW = new LovBusProdTp("FSB0");
	public static final LovBusProdTp SWAP_SHORT_LEG_BANKS_SP_SP = new LovBusProdTp("FSB1");
	public static final LovBusProdTp TRAVEL_INSURANCE_FAMILIES = new LovBusProdTp("STRIF");
	public static final LovBusProdTp TRAVEL_INSURANCE_INDIVIDUAL = new LovBusProdTp("STRII");
	public static final LovBusProdTp TRAVEL_INSURANCE_FOR_FAMILIES = new LovBusProdTp("TRIF");
	public static final LovBusProdTp TRAVEL_INSURANCE_FOR_INDIVIDUALS = new LovBusProdTp("TRII");
	public static final LovBusProdTp WORKING_CAPITAL_FINANCING_FOR_LEGAL_PERSON = new LovBusProdTp("REPO");
	public static final LovBusProdTp WORKING_CAPITAL_FINANCING_FOR_NATURAL_PERSON = new LovBusProdTp("REFO");
	public static final LovBusProdTp WORKING_CAPITAL_FINANCING_FOR_NATURAL_PERSON_BUSINESS = new LovBusProdTp("REFP");
    
    public LovBusProdTp() {
        super();
    }
    
	public LovBusProdTp(String code) {
		super(LovEnum.BUS_PROD_TP, code, null);
	}
}
