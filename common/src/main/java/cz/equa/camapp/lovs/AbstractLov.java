package cz.equa.camapp.lovs;

import cz.equa.camapp.constants.LovEnum;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * Generic DTO for LOV.
 * <p>
 * This class should be used for communication with external resources <u>only</u>
 * (that is for calling REST/WS APIs and so on) if there is no other way.
 * <p>
 * For CRM itself, prefer using concrete LOV subclasses.
 */
public class AbstractLov implements Serializable {

    private static final long serialVersionUID = -279245247255595971L;

    private String id;
    private String lovId;
    private String lang;
    private String descr;
    private Map<String, String> lovDetail;

    //Necessary for XML serialization in Camunda
    public AbstractLov() {
    }

    /**
     * Primary constructor which sets all the fields.
     */
    public AbstractLov(LovEnum lovEnum, String id, String descr) {
        this.lovId = lovEnum.name();
        this.id = id;
        this.descr = descr;
        this.lovDetail = new HashMap<>();
    }

    public String getLovId() {
        return lovId;
    }

    public void setLovId(String lovId) {
        this.lovId = lovId;
    }

    public String getCode() {
        return id;
    }

    public void setCode(String id) {
        this.id = id;
    }

    public String getLabel() {
        return descr;
    }

    public String getDescr() {
        return descr;
    }

    public void setLabel(String descr) {
        this.descr = descr;
    }

    public void addDetail(final String name, final String value) {
        if (this.lovDetail == null) {
            this.lovDetail = new HashMap<>();
        }
        this.lovDetail.put(name, value);
    }

    public Map<String, String> getLovDetail() {
        return lovDetail;
    }

    public String getLang() {
        return lang;
    }

    public void setLang(String lang) {
        this.lang = lang;
    }

    @Override
    public String toString() {
        //note: we do not need to use label in toString() because it makes it too long
        return getClass().getSimpleName() + "[lovId=" + lovId + ", code=" + id + "]";
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + (id == null ? 0 : id.hashCode());
        result = prime * result + (lovId == null ? 0 : lovId.hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        AbstractLov other = (AbstractLov) obj;
        if (id == null) {
            if (other.id != null) {
                return false;
            }
        } else if (!id.equals(other.id)) {
            return false;
        }
        if (lovId == null) {
            if (other.lovId != null) {
                return false;
            }
        } else if (!lovId.equals(other.lovId)) {
            return false;
        }
        return true;
    }
}
