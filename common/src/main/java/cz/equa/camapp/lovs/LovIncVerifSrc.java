package cz.equa.camapp.lovs;

import cz.equa.camapp.constants.LovEnum;

public class LovIncVerifSrc extends AbstractLov {
    
    public static final LovIncVerifSrc EMPLOYER = new LovIncVerifSrc("EMPLOYER");
    public static final LovIncVerifSrc STATEMENT = new LovIncVerifSrc("STATEMENT");
    
    public LovIncVerifSrc(){
		super();
	}

	public LovIncVerifSrc(String code) {
		super(LovEnum.INC_VERIF_SRC, code, null);
	}
}
