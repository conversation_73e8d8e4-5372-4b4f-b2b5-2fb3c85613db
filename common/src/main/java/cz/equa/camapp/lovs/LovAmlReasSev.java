package cz.equa.camapp.lovs;

import cz.equa.camapp.constants.LovEnum;

public class LovAmlReasSev extends AbstractLov {
    
    private int severityLevel;
    
	public static final LovAmlReasSev AML_REASON_SEVERITY_NONE = new LovAmlReasSev("NONE", 0);
    public static final LovAmlReasSev AML_REASON_SEVERITY_INVESTIGATION = new LovAmlReasSev("INVESTIGATION", 1);
    public static final LovAmlReasSev AML_REASON_SEVERITY_RISK = new LovAmlReasSev("RISK", 2);
    public static final LovAmlReasSev AML_REASON_SEVERITY_UNACCEPTABLE = new LovAmlReasSev("UNACCEPTABLE", 3);

    public LovAmlReasSev() {
        super();
    }
    
    public LovAmlReasSev(String code, int severityLevel) {
		super(LovEnum.LOV_AML_REAS_SEV, code, null);
        this.severityLevel = severityLevel;
	}
    
    public static final int getSeverityLevelOfLovAmlReasSev(String code) {
        if (AML_REASON_SEVERITY_NONE.getCode().equalsIgnoreCase(code)) {
            return AML_REASON_SEVERITY_NONE.severityLevel;
        } else if (AML_REASON_SEVERITY_INVESTIGATION.getCode().equalsIgnoreCase(code)) {
            return AML_REASON_SEVERITY_INVESTIGATION.severityLevel;
        } else if (AML_REASON_SEVERITY_RISK.getCode().equalsIgnoreCase(code)) {
            return AML_REASON_SEVERITY_RISK.severityLevel;
        } else if (AML_REASON_SEVERITY_UNACCEPTABLE.getCode().equalsIgnoreCase(code)) {
            return AML_REASON_SEVERITY_UNACCEPTABLE.severityLevel;
        } else {
            return -1;
        }
    }
    
    @Override
	public int hashCode() {
        final int prime = 31;
		int result = super.hashCode();
		result = prime * result + severityLevel;
		return result;
	}
    
    @Override
	public boolean equals(Object obj) {
        boolean result = super.equals(obj);
        if (!result) {
            return false;
        }
        if (!(obj instanceof LovAmlReasSev)) {
            return false;
        }
        LovAmlReasSev other = (LovAmlReasSev) obj;
		return severityLevel == other.severityLevel;
	}
}
