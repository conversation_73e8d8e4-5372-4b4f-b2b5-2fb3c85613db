package cz.equa.camapp.lovs;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import cz.equa.camapp.constants.LovEnum;

public class LovOblgtnProdTp extends AbstractLov {
    
    public static final LovOblgtnProdTp CONSUMER_LOAN = new LovOblgtnProdTp("CRL", "Customer Loan"); // Spotřebitelský úvěr
    public static final LovOblgtnProdTp BSL = new LovOblgtnProdTp("BSL", "BSL");
	public static final LovOblgtnProdTp ROD = new LovOblgtnProdTp("ROD", "Retail overdraft");
    public static final LovOblgtnProdTp RCC = new LovOblgtnProdTp("RCC", "Retail credit card");

    public LovOblgtnProdTp(String code) {
		super(LovEnum.OBLGTN_PROD_TP, code, null);
	}

	@JsonCreator
	public LovOblgtnProdTp(@JsonProperty("code") String code, @JsonProperty("label") String descritpion) {
		super(LovEnum.OBLGTN_PROD_TP, code, descritpion);
	}
}
