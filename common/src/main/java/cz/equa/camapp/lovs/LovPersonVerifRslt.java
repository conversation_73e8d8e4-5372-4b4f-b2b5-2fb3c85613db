package cz.equa.camapp.lovs;

import cz.equa.camapp.constants.LovEnum;

public class LovPersonVerifRslt extends AbstractLov {
	public static final LovPersonVerifRslt APPROVED = new LovPersonVerifRslt("APPROVED");
	public static final LovPersonVerifRslt REJECTED = new LovPersonVerifRslt("REJECTED");
	public static final LovPersonVerifRslt TCHER = new LovPersonVerifRslt("TCHER");
	public static final LovPersonVerifRslt TOVERIF = new LovPersonVerifRslt("TOVERIF");

    public LovPersonVerifRslt() {
        super();
    }
    
	public LovPersonVerifRslt(String code) {
		super(LovEnum.PERSON_VERIF_RSLT, code, null);
	}
}
