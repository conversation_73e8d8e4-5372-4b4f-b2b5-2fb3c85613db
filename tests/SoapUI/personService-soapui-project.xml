<?xml version="1.0" encoding="UTF-8"?>
<con:soapui-project id="15abbbdd-d0cb-45af-902c-94b4d969d68f" activeEnvironment="Default" name="personService" resourceRoot="" soapui-version="5.4.0" abortOnError="false" runType="SEQUENTIAL" xmlns:con="http://eviware.com/soapui/config"><con:settings/><con:interface xsi:type="con:WsdlInterface" id="f7fa4c67-fd0c-42db-a309-58aa11d81523" wsaVersion="NONE" name="PersonService" type="wsdl" bindingName="{http://www.equabank.cz/client/PersonService-v1}PersonService" soapVersion="1_1" anonymous="optional" definition="http://soa-1.dev1.equabank.loc:8001/soa-infra/services/client/PersonService-v1/PersonService-v1?WSDL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><con:settings/><con:definitionCache type="TEXT" rootPart="http://soa-1.dev1.equabank.loc:8001/soa-infra/services/client/PersonService-v1/PersonService-v1?WSDL"><con:part><con:url>http://soa-1.dev1.equabank.loc:8001/soa-infra/services/client/PersonService-v1/PersonService-v1?WSDL</con:url><con:content><![CDATA[<wsdl:definitions name="PersonService" targetNamespace="http://www.equabank.cz/client/PersonService-v1" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:tns="http://www.equabank.cz/client/PersonService-v1" xmlns:mandatory="http://www.equabank.cz/mandatory" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:plnk="http://docs.oasis-open.org/wsbpel/2.0/plnktype" xmlns:find="http://www.equabank.cz/client/PersonService-v1/findPerson" xmlns:set="http://www.equabank.cz/client/PersonService-v1/setPerson" xmlns:get="http://www.equabank.cz/client/PersonService-v1/getPerson" xmlns:hash="http://www.equabank.cz/client/PersonService-v1/getGfoIdByHash" xmlns:new="http://www.equabank.cz/client/PersonService-v1/newLead" xmlns:applPerson="http://www.equabank.cz/client/PersonService-v1/getApplicationPerson" xmlns:verPerson="http://www.equabank.cz/client/PersonService-v1/getVerifiedPerson">
  <wsdl:documentation>
    <abstractWSDL>http://soa-1.dev1.equabank.loc:8001/soa-infra/services/client/PersonService-v1!17.0.7/PersonService-v1.0.wsdl</abstractWSDL>
  </wsdl:documentation>
  <plnk:partnerLinkType name="PersonService">
    <plnk:role name="PersonServicePlnk" portType="tns:PersonServicePort-v1.0"/>
  </plnk:partnerLinkType>
  <wsdl:types>
    <xsd:schema>
      <xsd:import namespace="http://www.equabank.cz/client/PersonService-v1/findPerson" schemaLocation="http://soa-1.dev1.equabank.loc:8001/soa-infra/services/client/PersonService-v1/apps/public/client/iface/PersonService-v1/xsd/FindPerson-v1.0.xsd"/>
      <xsd:import namespace="http://www.equabank.cz/client/PersonService-v1/setPerson" schemaLocation="http://soa-1.dev1.equabank.loc:8001/soa-infra/services/client/PersonService-v1/apps/public/client/iface/PersonService-v1/xsd/SetPerson-v1.0.xsd"/>
      <xsd:import namespace="http://www.equabank.cz/client/PersonService-v1/newLead" schemaLocation="http://soa-1.dev1.equabank.loc:8001/soa-infra/services/client/PersonService-v1/apps/public/client/iface/PersonService-v1/xsd/NewLead-v1.0.xsd"/>
      <xsd:import namespace="http://www.equabank.cz/client/PersonService-v1/getPerson" schemaLocation="http://soa-1.dev1.equabank.loc:8001/soa-infra/services/client/PersonService-v1/apps/public/client/iface/PersonService-v1/xsd/GetPerson-v1.0.xsd"/>
      <xsd:import namespace="http://www.equabank.cz/client/PersonService-v1/getApplicationPerson" schemaLocation="http://soa-1.dev1.equabank.loc:8001/soa-infra/services/client/PersonService-v1/apps/public/client/iface/PersonService-v1/xsd/GetApplicationPerson-v1.0.xsd"/>
      <xsd:import namespace="http://www.equabank.cz/client/PersonService-v1/getVerifiedPerson" schemaLocation="http://soa-1.dev1.equabank.loc:8001/soa-infra/services/client/PersonService-v1/apps/public/client/iface/PersonService-v1/xsd/GetVerifiedPerson-v1.0.xsd"/>
      <xsd:import namespace="http://www.equabank.cz/client/PersonService-v1/getGfoIdByHash" schemaLocation="http://soa-1.dev1.equabank.loc:8001/soa-infra/services/client/PersonService-v1/apps/public/client/iface/PersonService-v1/xsd/GetGfoIdByHash-v1.0.xsd"/>
      <xsd:import namespace="http://www.equabank.cz/mandatory" schemaLocation="http://soa-1.dev1.equabank.loc:8001/soa-infra/services/client/PersonService-v1/apps/CommonInternal/xsd/MandatoryPart.xsd"/>
    </xsd:schema>
  </wsdl:types>
  <wsdl:message name="setPersonRequestMessage">
    <wsdl:part name="payload" element="set:setPersonRequest"/>
  </wsdl:message>
  <wsdl:message name="setPersonReplyMessage">
    <wsdl:part name="payload" element="set:setPersonResponse"/>
  </wsdl:message>
  <wsdl:message name="faultMessage">
    <wsdl:part name="payload" element="mandatory:fault"/>
  </wsdl:message>
  <wsdl:message name="newLeadRequestMessage">
    <wsdl:part name="payload" element="new:newLeadRequest"/>
  </wsdl:message>
  <wsdl:message name="newLeadReplyMessage">
    <wsdl:part name="payload" element="new:newLeadResponse"/>
  </wsdl:message>
  <wsdl:message name="getPersonRequestMessage">
    <wsdl:part name="payload" element="get:getPersonRequest"/>
  </wsdl:message>
  <wsdl:message name="getPersonReplyMessage">
    <wsdl:part name="payload" element="get:getPersonResponse"/>
  </wsdl:message>
  <wsdl:message name="getApplicationPersonRequestMessage">
    <wsdl:part name="payload" element="applPerson:getApplicationPersonRequest"/>
  </wsdl:message>
  <wsdl:message name="getApplicationPersonReplyMessage">
    <wsdl:part name="payload" element="applPerson:getApplicationPersonResponse"/>
  </wsdl:message>
  <wsdl:message name="findPersonRequestMessage">
    <wsdl:part name="payload" element="find:findPersonRequest"/>
  </wsdl:message>
  <wsdl:message name="findPersonReplyMessage">
    <wsdl:part name="payload" element="find:findPersonResponse"/>
  </wsdl:message>
  <wsdl:message name="getVerifiedPersonRequestMessage">
    <wsdl:part name="payload" element="verPerson:getVerifiedPersonRequest"/>
  </wsdl:message>
  <wsdl:message name="getVerifiedPersonReplyMessage">
    <wsdl:part name="payload" element="verPerson:getVerifiedPersonResponse"/>
  </wsdl:message>
  <wsdl:message name="getGfoIdByHashRequestMessage">
    <wsdl:part name="payload" element="hash:getGfoIdByHashRequest"/>
  </wsdl:message>
  <wsdl:message name="getGfoIdByHashReplyMessage">
    <wsdl:part name="payload" element="hash:getGfoIdByHashResponse"/>
  </wsdl:message>
  <wsdl:portType name="PersonServicePort-v1.0">
    <wsdl:operation name="setPerson">
      <wsdl:documentation>Operace pro ulozeni osoby do ODS, spousti
                                zaroven unifikaci.</wsdl:documentation>
      <wsdl:input message="tns:setPersonRequestMessage"/>
      <wsdl:output message="tns:setPersonReplyMessage"/>
      <wsdl:fault name="setPersonFault" message="tns:faultMessage"/>
    </wsdl:operation>
    <wsdl:operation name="newLead">
      <wsdl:documentation>Zalozi noveho GFO leada a notifikacie i do
                                Taurusu.</wsdl:documentation>
      <wsdl:input message="tns:newLeadRequestMessage"/>
      <wsdl:output message="tns:newLeadReplyMessage"/>
      <wsdl:fault name="newLeadFault" message="tns:faultMessage"/>
    </wsdl:operation>
    <wsdl:operation name="getPerson">
      <wsdl:documentation>Operace pro vyhledani osoby na zaklade filtru
                                urceneho na vstupu. Vrati kompletni info o osobe.</wsdl:documentation>
      <wsdl:input message="tns:getPersonRequestMessage"/>
      <wsdl:output message="tns:getPersonReplyMessage"/>
      <wsdl:fault name="getPersonFault" message="tns:faultMessage"/>
    </wsdl:operation>
    <wsdl:operation name="getApplicationPerson">
      <wsdl:documentation>Operace pro vyhledani osoby na zaklade ID
                                zadosti o cashloan. Vrati kompletni info o osobe.</wsdl:documentation>
      <wsdl:input message="tns:getApplicationPersonRequestMessage"/>
      <wsdl:output message="tns:getApplicationPersonReplyMessage"/>
      <wsdl:fault name="getApplicationPersonFault" message="tns:faultMessage"/>
    </wsdl:operation>
    <wsdl:operation name="findPerson">
      <wsdl:documentation>Operace pro vyhledani osoby a vrati jej vsetky
                                relevantne instancie.</wsdl:documentation>
      <wsdl:input message="tns:findPersonRequestMessage"/>
      <wsdl:output message="tns:findPersonReplyMessage"/>
      <wsdl:fault name="findPersonFault" message="tns:faultMessage"/>
    </wsdl:operation>
    <wsdl:operation name="getVerifiedPerson">
      <wsdl:documentation>Operace pro vyhledani overene osoby na zaklade telefonniho cisla.</wsdl:documentation>
      <wsdl:input message="tns:getVerifiedPersonRequestMessage"/>
      <wsdl:output message="tns:getVerifiedPersonReplyMessage"/>
      <wsdl:fault name="getVerifiedPersonFault" message="tns:faultMessage"/>
    </wsdl:operation>
    <wsdl:operation name="getGfoIdByHash">
      <wsdl:documentation>Operace pro nacteni gfo id klienta dle hashe.</wsdl:documentation>
      <wsdl:input message="tns:getGfoIdByHashRequestMessage"/>
      <wsdl:output message="tns:getGfoIdByHashReplyMessage"/>
      <wsdl:fault name="getGfoIdByHashFault" message="tns:faultMessage"/>
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="PersonService" type="tns:PersonServicePort-v1.0">
    <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    <wsdl:operation name="setPerson">
      <soap:operation style="document" soapAction="http://www.equabank.cz/client/PersonService-v1/setPerson"/>
      <wsdl:input>
        <soap:body use="literal" parts="payload"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" parts="payload"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="newLead">
      <soap:operation style="document" soapAction="http://www.equabank.cz/client/PersonService-v1/newLead"/>
      <wsdl:input>
        <soap:body use="literal" parts="payload"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" parts="payload"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getPerson">
      <soap:operation style="document" soapAction="http://www.equabank.cz/client/PersonService-v1/getPerson"/>
      <wsdl:input>
        <soap:body use="literal" parts="payload"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" parts="payload"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getApplicationPerson">
      <soap:operation style="document" soapAction="http://www.equabank.cz/client/PersonService-v1/getApplicationPerson"/>
      <wsdl:input>
        <soap:body use="literal" parts="payload"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" parts="payload"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="findPerson">
      <soap:operation style="document" soapAction="http://www.equabank.cz/client/PersonService-v1/findPerson"/>
      <wsdl:input>
        <soap:body use="literal" parts="payload"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" parts="payload"/>
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="getVerifiedPerson">
      <soap:operation style="document" soapAction="http://www.equabank.cz/client/PersonService-v1/getVerifiedPerson"/>
      <wsdl:input>
        <soap:body use="literal" parts="payload"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" parts="payload"/>
      </wsdl:output>
      <wsdl:fault name="getVerifiedPersonFault">
        <soap:fault name="getVerifiedPersonFault" use="literal"/>
      </wsdl:fault>
    </wsdl:operation>
    <wsdl:operation name="getGfoIdByHash">
      <soap:operation style="document" soapAction="http://www.equabank.cz/client/PersonService-v1/getGfoIdByHash"/>
      <wsdl:input>
        <soap:body use="literal" parts="payload"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" parts="payload"/>
      </wsdl:output>
      <wsdl:fault name="getGfoIdByHashFault">
        <soap:fault name="getGfoIdByHashFault" use="literal"/>
      </wsdl:fault>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="PersonService-v1">
    <wsdl:port name="PersonServicePort-v1.0_pt" binding="tns:PersonService">
      <soap:address location="http://soa-1.dev1.equabank.loc:8001/soa-infra/services/client/PersonService-v1/PersonService-v1"/>
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>]]></con:content><con:type>http://schemas.xmlsoap.org/wsdl/</con:type></con:part><con:part><con:url>http://soa-1.dev1.equabank.loc:8001/soa-infra/services/client/PersonService-v1/apps/public/client/iface/PersonService-v1/xsd/FindPerson-v1.0.xsd</con:url><con:content><![CDATA[<xsd:schema targetNamespace="http://www.equabank.cz/client/PersonService-v1/findPerson" elementFormDefault="qualified" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://www.equabank.cz/client/PersonService-v1/findPerson" xmlns:mandatory="http://www.equabank.cz/mandatory" xmlns:lovs="http://www.equabank.cz/lovs">
  <xsd:import namespace="http://www.equabank.cz/mandatory" schemaLocation="http://soa-1.dev1.equabank.loc:8001/soa-infra/services/client/PersonService-v1/apps/CommonInternal/xsd/MandatoryPart.xsd"/>
  <xsd:import namespace="http://www.equabank.cz/lovs" schemaLocation="http://soa-1.dev1.equabank.loc:8001/soa-infra/services/client/PersonService-v1/apps/CommonInternal/xsd/LovPart.xsd"/>
  <xsd:element name="findPersonRequest">
    <xsd:complexType>
      <xsd:complexContent>
        <xsd:extension base="mandatory:mandatoryRequest">
          <xsd:sequence>
            <xsd:element name="requestBody" type="findPersonsReq"/>
          </xsd:sequence>
        </xsd:extension>
      </xsd:complexContent>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="findPersonResponse">
    <xsd:complexType>
      <xsd:complexContent>
        <xsd:extension base="mandatory:mandatoryResponse">
          <xsd:sequence>
            <xsd:element name="responseBody" type="findPersonGroupsResp"/>
          </xsd:sequence>
        </xsd:extension>
      </xsd:complexContent>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="groupTmp">
    <xsd:complexType>
      <xsd:complexContent>
        <xsd:extension base="findPersonGroupsResp"/>
      </xsd:complexContent>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="findPersonGroupsResp">
    <xsd:annotation>
      <xsd:documentation>Instance party keys of inserted / updated persons</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence maxOccurs="unbounded" minOccurs="0">
      <xsd:element name="group" maxOccurs="unbounded" minOccurs="0">
        <xsd:complexType>
          <xsd:complexContent>
            <xsd:extension base="findPersonGroupResp">
              <xsd:attribute name="groupId" type="xsd:string"/>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="findPersonGroupResp">
    <xsd:sequence maxOccurs="unbounded" minOccurs="0">
      <xsd:element name="person" maxOccurs="unbounded" minOccurs="0">
        <xsd:complexType>
          <xsd:complexContent>
            <xsd:extension base="findPersonResp">
              <xsd:attribute name="systemId" type="xsd:string"/>
            </xsd:extension>
          </xsd:complexContent>
        </xsd:complexType>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="findPersonResp">
    <xsd:sequence>
      <xsd:element name="ptSrcId" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Party Source ID - person ID of the source system (GFO)</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="firstName" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>First name of the person</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="lastName" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Family - Last name of the person</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="rcNumber" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Birth Number of the person</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="birthDate" type="xsd:date" minOccurs="0"/>
      <xsd:element name="busName" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Business name of the person</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="icoNumber" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Business Number Identifiaction Number</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="ptStat" type="lovs:LovPtStat" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="findPersonsReq">
    <xsd:annotation>
      <xsd:documentation>Persons to be stored</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="person" type="findPersonReq"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="findPersonReq">
    <xsd:annotation>
      <xsd:documentation>person to be stored</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="ptSrcId" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>ID zdrojoveho systemu</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="busTpId" type="lovs:LovPtTp" minOccurs="1">
        <xsd:annotation>
          <xsd:documentation>Party Type - FO, FOP, PO</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="firstName" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>jmeno osoby</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="lastName" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>prijmeni osoby</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="rcNumber" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>rodni cislo osoby</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="icoNumber" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>ICO osoby</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="busName" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>obchodni jmeno osoby</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="birthDate" type="xsd:date" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>datum narodenia osoby</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="zecoNumber" minOccurs="0" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation/>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="phones" type="findPersonPhonesReq" minOccurs="0"/>
      <xsd:element name="streets" minOccurs="0">
        <xsd:complexType>
          <xsd:sequence minOccurs="0" maxOccurs="unbounded">
            <xsd:element name="street" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
          </xsd:sequence>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="streetNumbers" minOccurs="0">
        <xsd:complexType>
          <xsd:sequence minOccurs="0" maxOccurs="unbounded">
            <xsd:element name="streetNumber" type="xsd:string" minOccurs="0" maxOccurs="unbounded"/>
          </xsd:sequence>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="cities" type="findPersonCitiesReq" minOccurs="0"/>
      <xsd:element name="zips" type="findPersonZipsReq" minOccurs="0"/>
      <xsd:element name="emails" type="findPersonMailsReq" minOccurs="0"/>
      <xsd:element name="firstIdCardNums" type="findPersonIdCardsReq" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="findPersonPhonesReq">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:sequence maxOccurs="unbounded" minOccurs="0">
      <xsd:element name="phone" type="xsd:string" maxOccurs="unbounded"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="findPersonCitiesReq">
    <xsd:sequence maxOccurs="unbounded" minOccurs="0">
      <xsd:element name="city" type="xsd:string" maxOccurs="unbounded"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="findPersonZipsReq">
    <xsd:sequence maxOccurs="unbounded" minOccurs="0">
      <xsd:element name="zip" type="xsd:string" maxOccurs="unbounded"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="findPersonMailsReq">
    <xsd:sequence maxOccurs="unbounded" minOccurs="0">
      <xsd:element name="email" type="xsd:string" maxOccurs="unbounded"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="findPersonIdCardsReq">
    <xsd:sequence maxOccurs="unbounded" minOccurs="0">
      <xsd:element name="firstIdCardNum" type="xsd:string" maxOccurs="unbounded"/>
    </xsd:sequence>
  </xsd:complexType>
</xsd:schema>]]></con:content><con:type>http://www.w3.org/2001/XMLSchema</con:type></con:part><con:part><con:url>http://soa-1.dev1.equabank.loc:8001/soa-infra/services/client/PersonService-v1/apps/CommonInternal/xsd/MandatoryPart.xsd</con:url><con:content><![CDATA[<xsd:schema version="1.0" targetNamespace="http://www.equabank.cz/mandatory" elementFormDefault="qualified" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://www.equabank.cz/mandatory">
  <xsd:complexType name="mandatoryRequest">
    <xsd:annotation>
      <xsd:documentation>base system info for the message</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="systemId" type="xsd:string" minOccurs="1" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>$LOV$ - id of the caller system (GFO, FC, C3G.. etc)</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="userId" type="xsd:string" minOccurs="1" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>id of the user who call the service</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="processId" type="xsd:string" minOccurs="1" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>unique process ID for identification complete the process. This ID is generaged by first caller and flows thru
                                      all subservices in global service from begin until end</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="messageId" type="xsd:string" minOccurs="1" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>unique message ID for indentification subservice call.</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="wflProcessId" type="xsd:string" minOccurs="0" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>unique message ID for indentification workflow process. Exists in case of message is workflow instance</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="mandatoryResponse">
    <xsd:sequence>
      <xsd:element name="resultCode" type="xsd:int" maxOccurs="1">
        <xsd:annotation>
          <xsd:documentation>result code of the service 0 .. service ends with no error 4 .. warning, business, process can continue 8 ..
                                      error, bussiess, process can not continue 255 .. critical error, technical error, undocumented exception,
                                      process can not continue</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="assynchronousProcessId" type="xsd:string" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>unique ID represents the invoked asynchronous process. Exists only in the case of service is asynchronous</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="recordCount" type="xsd:int" minOccurs="0" maxOccurs="1"/>
      <xsd:element name="error" type="errorMessage" maxOccurs="1" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="errorMessage">
    <xsd:annotation>
      <xsd:documentation>error handling message can contains more errors</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="errorCode" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>$LOV$ code of error from the list of values. This service can return: - PARSE_REQUEST_ERROR - error in parsing
                                      request message, invalid request inadequate specification - PREPARE_RESPONSE_ERROR - error in response preparing
                                      process, backend returns unexpectable reason - VALIDATION_REQUEST_ERROR - validation error, request data are not
                                      valid - INTERNAL_ESB_ERROR - internal error of ESB in parsing auxiliary source - DATABASE_SQL_ERROR - database
                                      response error - UNKNOWN - unknown, undocummented error - WARNINGS - unknown, undocummented error</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="errorDescription" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>could be more detail about exception, for example the stacktrace in string, can be structured</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:element name="fault" type="errorMessage"/>
</xsd:schema>]]></con:content><con:type>http://www.w3.org/2001/XMLSchema</con:type></con:part><con:part><con:url>http://soa-1.dev1.equabank.loc:8001/soa-infra/services/client/PersonService-v1/apps/CommonInternal/xsd/LovPart.xsd</con:url><con:content><![CDATA[<xsd:schema version="1.0" targetNamespace="http://www.equabank.cz/lovs" elementFormDefault="qualified" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://www.equabank.cz/lovs">
  <xsd:complexType name="LovAccBalTp">
    <xsd:annotation>
      <xsd:documentation>Account Balance Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="ACC_BAL_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovAccClass">
    <xsd:annotation>
      <xsd:documentation>Account class (from Flexcube) and reference to
                               deposit type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="ACC_CLASS" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovAccRoleTp">
    <xsd:annotation>
      <xsd:documentation>Account Role Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="ACC_ROLE_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovAccStat">
    <xsd:annotation>
      <xsd:documentation>Account Status</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="ACC_STAT" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovAccTrnBusStat">
    <xsd:annotation>
      <xsd:documentation>Account Transaction Business Status</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="ACC_TRN_BUS_STAT" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovAccTrnFnlStat">
    <xsd:annotation>
      <xsd:documentation>Account Transaction Final Status</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="ACC_TRN_FNL_STAT" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovAccTrnPtRoleTp">
    <xsd:annotation>
      <xsd:documentation>Account Transaction Party Role Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="ACC_TRN_PT_ROLE_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovAccTrnStat">
    <xsd:annotation>
      <xsd:documentation>Account Transaction Status</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="ACC_TRN_STAT" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovAccTrnSubTp">
    <xsd:annotation>
      <xsd:documentation>Account Transaction Sub Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="ACC_TRN_SUB_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovAccTrnTp">
    <xsd:annotation>
      <xsd:documentation>Account Transaction Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="ACC_TRN_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovAccPurp">
    <xsd:annotation>
      <xsd:documentation>Account class (from Flexcube) and reference to
                               deposit type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="ACC_PURP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovAcrBas">
    <xsd:annotation>
      <xsd:documentation>Accrual Basis</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="ACR_BAS" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovActivationCnl">
    <xsd:annotation>
      <xsd:documentation>Activation Channel</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="ACTIVATION_CNL" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovAddrPurp">
    <xsd:annotation>
      <xsd:documentation>Address Purpose</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="ADDR_PURP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovAgreementStat">
    <xsd:annotation>
      <xsd:documentation>Agreement status</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="AGREEMENT_STAT" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovAgreementSubTp">
    <xsd:annotation>
      <xsd:documentation>Agreement sub type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="AGREEMENT_SUB_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovAgreementTp">
    <xsd:annotation>
      <xsd:documentation>Type of agreement</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="AGREEMENT_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovCardBlocRsnTp">
    <xsd:annotation>
      <xsd:documentation>Type of card blocation reason</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="CARD_BLOC_RSN" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovCardRplcRsnTp">
    <xsd:annotation>
      <xsd:documentation>Type of card replace reason</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="CARD_RPLC_RSN" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovApplApplRelTp">
    <xsd:annotation>
      <xsd:documentation>Application Application Relationship Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="APPL_APPL_REL_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovApplDateTp">
    <xsd:annotation>
      <xsd:documentation>Application Date Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="APPL_DATE_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovApplPtRoleTp">
    <xsd:annotation>
      <xsd:documentation>Application Party Role Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="APPL_PT_ROLE_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovApplStat">
    <xsd:annotation>
      <xsd:documentation>Application Status</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="APPL_STAT" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovApplStateChgEveTp">
    <xsd:annotation>
      <xsd:documentation>Application State Change Event Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="APPL_STATE_CHG_EVE_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovApplTechStat">
    <xsd:annotation>
      <xsd:documentation>Application Technical Status</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="APPL_TECH_STAT" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovApplTp">
    <xsd:annotation>
      <xsd:documentation>Application Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="APPL_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovApplVariantTp">
    <xsd:annotation>
      <xsd:documentation>Application Variant Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="APPL_VARIANT_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovAvlblStat">
    <xsd:annotation>
      <xsd:documentation>State of availability</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="AVLBL_STAT" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovBankCzCode">
    <xsd:annotation>
      <xsd:documentation>Bank CZ Code</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="BANK_CZ_CODE" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovBlocTp">
    <xsd:annotation>
      <xsd:documentation>Blocation Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="BLOC_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovBrn">
    <xsd:annotation>
      <xsd:documentation>Branch table</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="BRN" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovBusProdClass">
    <xsd:annotation>
      <xsd:documentation>Business Product Class</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="BUS_PROD_CLASS" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovBusProdSubTp">
    <xsd:annotation>
      <xsd:documentation>Business Product Sub Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="BUS_PROD_SUB_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovBusProdTp">
    <xsd:annotation>
      <xsd:documentation>Business Product Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="BUS_PROD_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovBusSector">
    <xsd:annotation>
      <xsd:documentation>Business Sector</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="BUS_SECTOR" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovCardStat">
    <xsd:annotation>
      <xsd:documentation>Card Status</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="CARD_STAT" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovCardTp">
    <xsd:annotation>
      <xsd:documentation>Card Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="CARD_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovCcy">
    <xsd:annotation>
      <xsd:documentation>Currency</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="CCY" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovChecklistDocStat">
    <xsd:annotation>
      <xsd:documentation>Checklist Document Status</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="CHECKLIST_DOC_STAT" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovClientCatgRpt">
    <xsd:annotation>
      <xsd:documentation>Client Category Reporting</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="CLIENT_CATG_RPT" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovCltTp">
    <xsd:annotation>
      <xsd:documentation>Collateral Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="CLT_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovCmdt">
    <xsd:annotation>
      <xsd:documentation>Commodity</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="CMDT" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovCnbSecurTp">
    <xsd:annotation>
      <xsd:documentation>CNB security Type (CNB reference BA0088)</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="CNB_SECUR_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovCnl">
    <xsd:annotation>
      <xsd:documentation>Chanel</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="CNL" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovCnst">
    <xsd:annotation>
      <xsd:documentation>Consent</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="CNST" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovCnstStat">
    <xsd:annotation>
      <xsd:documentation>Consent Status</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="CNST_STAT" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovCnstSubTp">
    <xsd:annotation>
      <xsd:documentation>Consent Sub-Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="CNST_SUB_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovCnstTp">
    <xsd:annotation>
      <xsd:documentation>Consent Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="CNST_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovCntry">
    <xsd:annotation>
      <xsd:documentation>Country</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="CNTRY" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovCollProcess">
    <xsd:annotation>
      <xsd:documentation>Collection Process</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="COLL_PROCESS" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovComplStat">
    <xsd:annotation>
      <xsd:documentation>Completet Status</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="COMPL_STAT" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovCompActStat">
    <xsd:annotation>
      <xsd:documentation>Company Activity Status</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="COMP_ACT_STAT" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovComCnlTp">
    <xsd:annotation>
      <xsd:documentation>Communication Channel Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="COM_CNL_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovContCaseStat">
    <xsd:annotation>
      <xsd:documentation>Contact Case Status</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="CONT_CASE_STAT" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovContCaseTp">
    <xsd:annotation>
      <xsd:documentation>Contact Case Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="CONT_CASE_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovContDirn">
    <xsd:annotation>
      <xsd:documentation>Contact Direction</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="CONT_DIRN" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovContLine">
    <xsd:annotation>
      <xsd:documentation>Contact Line</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="CONT_LINE" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovContRslt">
    <xsd:annotation>
      <xsd:documentation>Contact resolution</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="CONT_RSLT" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovContRsn">
    <xsd:annotation>
      <xsd:documentation>Contact Reason</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="CONT_RSN" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovContStat">
    <xsd:annotation>
      <xsd:documentation>Contact Status</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="CONT_STAT" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovContTp">
    <xsd:annotation>
      <xsd:documentation>Contact Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="CONT_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovDepoTp">
    <xsd:annotation>
      <xsd:documentation>Deposit Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="DEPO_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovDistrict">
    <xsd:annotation>
      <xsd:documentation>District</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="DISTRICT" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovDistCnl">
    <xsd:annotation>
      <xsd:documentation>Distribution Chanel</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="DIST_CNL" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovDocTp">
    <xsd:annotation>
      <xsd:documentation>Document type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="DOC_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovDomicil">
    <xsd:annotation>
      <xsd:documentation>Domicil</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="DOMICIL" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovDrCr">
    <xsd:annotation>
      <xsd:documentation>Debit Credit</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="DR_CR" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovDsnTp">
    <xsd:annotation>
      <xsd:documentation>Decision Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="DSN_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovEduStat">
    <xsd:annotation>
      <xsd:documentation>Education Status</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="EDU_STAT" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovEmailPurp">
    <xsd:annotation>
      <xsd:documentation>Email Purpose</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="EMAIL_PURP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovEmptTp">
    <xsd:annotation>
      <xsd:documentation>Employment Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="EMPT_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovEmpCntCatg">
    <xsd:annotation>
      <xsd:documentation>Employee Count Category</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="EMP_CNT_CATG" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovEsbError">
    <xsd:annotation>
      <xsd:documentation>List of error in ESB</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="ESB_ERROR" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovEveCatg">
    <xsd:annotation>
      <xsd:documentation>Event Category</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="EVE_CATG" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovEveTp">
    <xsd:annotation>
      <xsd:documentation>Event Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="EVE_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovExtnRejRsnTp">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="EXTN_REJ_RSN_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovFeeAlgTp">
    <xsd:annotation>
      <xsd:documentation>Fee Algorithm Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="FEE_ALG_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovFeeCalcTp">
    <xsd:annotation>
      <xsd:documentation>Fee Calculation Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="FEE_CALC_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovFeeTp">
    <xsd:annotation>
      <xsd:documentation>Fee Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="FEE_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovFinInstn">
    <xsd:annotation>
      <xsd:documentation>Financial Institute</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="FIN_INSTN" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovFrgnFeeTp">
    <xsd:annotation>
      <xsd:documentation>Foreign Fee Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="FRGN_FEE_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovGender">
    <xsd:annotation>
      <xsd:documentation>Gender</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="GENDER" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovHousingStat">
    <xsd:annotation>
      <xsd:documentation>Housing Status</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="HOUSING_STAT" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovIdCardPurp">
    <xsd:annotation>
      <xsd:documentation>ID Card Purpose</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="ID_CARD_PURP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovIdCardTp">
    <xsd:annotation>
      <xsd:documentation>ID Card Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="ID_CARD_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovIdCardStat">
    <xsd:annotation>
      <xsd:documentation>ID Card Status</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="ID_CARD_STAT" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovIncTp">
    <xsd:annotation>
      <xsd:documentation>Income Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="INC_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovLang">
    <xsd:annotation>
      <xsd:documentation>Language</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="LANG" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovLegalForm">
    <xsd:annotation>
      <xsd:documentation>Legal Form</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="LEGAL_FORM" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovLoanPurp">
    <xsd:annotation>
      <xsd:documentation>Loan Purpose</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="LOAN_PURP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovMarAst">
    <xsd:annotation>
      <xsd:documentation>Marital Asset</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="MAR_AST" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovMarStat">
    <xsd:annotation>
      <xsd:documentation>Marital Status</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="MAR_STAT" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovNace">
    <xsd:annotation>
      <xsd:documentation>NACE</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="NACE" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovNotifTp">
    <xsd:annotation>
      <xsd:documentation>Notification Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="NOTIF_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovOblgtnCatg">
    <xsd:annotation>
      <xsd:documentation>Obligation Category</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="OBLGTN_CATG" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovOblgtnDocTp">
    <xsd:annotation>
      <xsd:documentation>Obligation Document Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="OBLGTN_DOC_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovOblgtnLoanPurp">
    <xsd:annotation>
      <xsd:documentation>Obligation Loan Purpose</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="OBLGTN_LOAN_PURP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovOblgtnProdTp">
    <xsd:annotation>
      <xsd:documentation>Obligation Product Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="OBLGTN_PROD_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovOblgtnTp">
    <xsd:annotation>
      <xsd:documentation>Obligation Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="OBLGTN_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovOcc">
    <xsd:annotation>
      <xsd:documentation>Occupation</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="OCC" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovOccCatg">
    <xsd:annotation>
      <xsd:documentation>Occupation Category</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="OCC_CATG" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovPayTp">
    <xsd:annotation>
      <xsd:documentation>Payment Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="PAY_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovPerFrq">
    <xsd:annotation>
      <xsd:documentation>Period Frequency</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="PER_FRQ" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovPhonePayTp">
    <xsd:annotation>
      <xsd:documentation>Phone Payment Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="PHONE_PAY_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovPhonePurp">
    <xsd:annotation>
      <xsd:documentation>Phone Purpose</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="PHONE_PURP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovPhoneTp">
    <xsd:annotation>
      <xsd:documentation>Phone Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="PHONE_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovPortfolioCatg">
    <xsd:annotation>
      <xsd:documentation>Portfolio category</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="PORTFOLIO_CATG" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovPos">
    <xsd:annotation>
      <xsd:documentation>POS</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="POS" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovPosTp">
    <xsd:annotation>
      <xsd:documentation>Point of sale type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="POS_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovProcessTp">
    <xsd:annotation>
      <xsd:documentation>Process type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="PROCESS_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovProdClass">
    <xsd:annotation>
      <xsd:documentation>Product Class</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="PROD_CLASS" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovProdInstEventTp">
    <xsd:annotation>
      <xsd:documentation>Event types of the internet banking</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="PROD_INST_EVENT_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovProdInstRelTp">
    <xsd:annotation>
      <xsd:documentation>Product Instance Relationship Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="PROD_INST_REL_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovProdSeg">
    <xsd:annotation>
      <xsd:documentation>Product Segment</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="PROD_SEG" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovProdSegm">
    <xsd:annotation>
      <xsd:documentation>Product Segmentation</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="PROD_SEGM" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovProdStat">
    <xsd:annotation>
      <xsd:documentation>Product status</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="PROD_STAT" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovPtAcntgTp">
    <xsd:annotation>
      <xsd:documentation>Party Accounting Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="PT_ACNTG_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovPtPtRelTp">
    <xsd:annotation>
      <xsd:documentation>Party Party Relationship Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="PT_PT_REL_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovPtRoleTp">
    <xsd:annotation>
      <xsd:documentation>Party Role Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="PT_ROLE_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovPtSegStat">
    <xsd:annotation>
      <xsd:documentation>Party Segment Status</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="PT_SEG_STAT" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovPtStat">
    <xsd:annotation>
      <xsd:documentation>Party Status</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="PT_STAT" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovPtTp">
    <xsd:annotation>
      <xsd:documentation>Party Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="PT_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovRegTp">
    <xsd:annotation>
      <xsd:documentation>Register type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="REG_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovRestructTp">
    <xsd:annotation>
      <xsd:documentation>Product Restructuring Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="RESTRUCT_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovRiskBand">
    <xsd:annotation>
      <xsd:documentation>Risk Band</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="RISK_BAND" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovRiskClass">
    <xsd:annotation>
      <xsd:documentation>Risk Class</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="RISK_CLASS" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovRpmtMethod">
    <xsd:annotation>
      <xsd:documentation>Repayment Method</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="RPMT_METHOD" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovRpmtMode">
    <xsd:annotation>
      <xsd:documentation>Repayment Mode</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="RPMT_MODE" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovRsnCatg">
    <xsd:annotation>
      <xsd:documentation>Reason Category</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="RSN_CATG" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovRsnTp">
    <xsd:annotation>
      <xsd:documentation>Reason Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="RSN_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovRxTp">
    <xsd:annotation>
      <xsd:documentation>Rx Rate</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="RX_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovSalut">
    <xsd:annotation>
      <xsd:documentation>Salutation</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="SALUT" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovSecurPst">
    <xsd:annotation>
      <xsd:documentation>Security position</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="SECUR_PST" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovSecurTp">
    <xsd:annotation>
      <xsd:documentation>General security type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="SECUR_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovSeg">
    <xsd:annotation>
      <xsd:documentation>Segment</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="SEG" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovSegm">
    <xsd:annotation>
      <xsd:documentation>Segmentation</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="SEGM" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovSegRsnTp">
    <xsd:annotation>
      <xsd:documentation>Segment Reason Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="SEG_RSN_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovSignDocStat">
    <xsd:annotation>
      <xsd:documentation>Sign Document Status</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="SIGN_DOC_STAT" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovSocStat">
    <xsd:annotation>
      <xsd:documentation>Social Status</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="SOC_STAT" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovSpecialCode">
    <xsd:annotation>
      <xsd:documentation>Special Code</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="SPECIAL_CODE" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovSrvcProdStat">
    <xsd:annotation>
      <xsd:documentation>Service product status</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="SRVC_PROD_STAT" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovStayTp">
    <xsd:annotation>
      <xsd:documentation>Stay Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="STAY_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovTermDepoLen">
    <xsd:annotation>
      <xsd:documentation>Source System Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="TERM_DEPO_LEN" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovTrnCnl">
    <xsd:annotation>
      <xsd:documentation>Transaction Channel</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="TRN_CNL" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovTrshdTp">
    <xsd:annotation>
      <xsd:documentation>Treshold Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="TRSHD_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovTurnover">
    <xsd:annotation>
      <xsd:documentation>Turnover</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="TURNOVER" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovSysTp">
    <xsd:annotation>
      <xsd:documentation>Source System Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="SYS_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovInsurTp">
    <xsd:annotation>
      <xsd:documentation>Insurance Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="INSUR_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovAddtSrvcPvdrTp">
    <xsd:annotation>
      <xsd:documentation>Additional services providers</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="ADDT_SRVC_PVDR" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovAddtSrvcTermntnRsnTp">
    <xsd:annotation>
      <xsd:documentation>Additional service termination reason</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="ADDT_SRVC_TERMNTN_RSN" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovAmlInState">
    <xsd:annotation>
      <xsd:documentation>Stav Aml setreni</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" use="required" fixed="AML_IN_STATE" type="xsd:string"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovAmlReasTp">
    <xsd:annotation>
      <xsd:documentation>AML risk type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" use="required" fixed="AML_RSN_TP" type="xsd:string"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovAmlReasSev">
    <xsd:annotation>
      <xsd:documentation>AML risk severity</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" use="required" fixed="AML_RSN_SVRT" type="xsd:string"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovAmlCause">
    <xsd:annotation>
      <xsd:documentation>AML investigation cause</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" use="required" fixed="AML_CAUSE" type="xsd:string"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovAmlAutRole">
    <xsd:annotation>
      <xsd:documentation>AML decision author</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" use="required" fixed="AML_AUT_ROLE" type="xsd:string"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovCrsStat">
    <xsd:annotation>
      <xsd:documentation>Common Reporting Standard entity status</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" use="required" fixed="CRS_STAT" type="xsd:string"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovAoTp">
    <xsd:annotation>
      <xsd:documentation>Type of account opening process</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" use="required" fixed="AO_TP" type="xsd:string"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovPropTp">
    <xsd:annotation>
      <xsd:documentation>Type of property</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" use="required" fixed="PROPERTY_TP" type="xsd:string"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovSrvcActTp">
    <xsd:annotation>
      <xsd:documentation>Application event / activity type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" use="required" fixed="SRVC_ACT_TP" type="xsd:string"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovFixTerm">
    <xsd:annotation>
      <xsd:documentation>Fixing terms</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="FIXING_TERM" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovInvsPurpAprchTp">
    <xsd:annotation>
      <xsd:documentation>Investment Purpose Approach Type Key / Strategie investičního přístupu</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="INVS_PURP_APRCH_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovInvsPurpStat">
    <xsd:annotation>
      <xsd:documentation>Investment Purpose Status Identifier / Stav portfolia</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="INVS_PURP_STAT" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovSepaOrgIdTp">
    <xsd:annotation>
      <xsd:documentation>Sepa original ID Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="SEPA_ORG_ID_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovSepaPersonIdTp">
    <xsd:annotation>
      <xsd:documentation>SEPA Person ID Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="SEPA_PERSON_ID_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovPreApprOfrClass">
    <xsd:annotation>
      <xsd:documentation>Preapproved offer class</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="PRE_APPR_OFR_CLASS" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovPayTpDescrLabel">
    <xsd:annotation>
      <xsd:documentation>Payment Type Description Label</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="PAY_TP_DESCR_LABEL" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovPaySubTp">
    <xsd:annotation>
      <xsd:documentation>Payment Sub Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="PAY_SUB_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovCltRiskTp">
    <xsd:annotation>
      <xsd:documentation>Collateral Risk Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="CLT_RISK_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovPersonVerifTp">
    <xsd:annotation>
      <xsd:documentation>Person Verification Type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="PERSON_VERIF_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovPayOrdStat">
    <xsd:annotation>
      <xsd:documentation>Payment Order Status</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="PAY_ORD_STAT" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovDateioStat">
    <xsd:annotation>
      <xsd:documentation>Dateio status id</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="DATEIO_STAT" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovShopTp">
    <xsd:annotation>
      <xsd:documentation>Dateio shop type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="SHOP_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovMccCodeSubCatg">
    <xsd:annotation>
      <xsd:documentation>Dateio subkategorie obchodníka</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="MCC_CODE_SUB_CATG" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovApplEveTp">
    <xsd:annotation>
      <xsd:documentation>Application event type</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="APPL_EVE_TP" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
  <xsd:complexType name="LovApplProcessPart">
    <xsd:annotation>
      <xsd:documentation>Application process part</xsd:documentation>
    </xsd:annotation>
    <xsd:simpleContent>
      <xsd:extension base="xsd:string">
        <xsd:attribute name="lov" fixed="APPL_PROCESS_PART" type="xsd:string" use="required"/>
      </xsd:extension>
    </xsd:simpleContent>
  </xsd:complexType>
</xsd:schema>]]></con:content><con:type>http://www.w3.org/2001/XMLSchema</con:type></con:part><con:part><con:url>http://soa-1.dev1.equabank.loc:8001/soa-infra/services/client/PersonService-v1/apps/public/client/iface/PersonService-v1/xsd/SetPerson-v1.0.xsd</con:url><con:content><![CDATA[<xsd:schema targetNamespace="http://www.equabank.cz/client/PersonService-v1/setPerson" elementFormDefault="qualified" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://www.equabank.cz/client/PersonService-v1/setPerson" xmlns:mandatory="http://www.equabank.cz/mandatory" xmlns:part="http://www.equabank.cz/client/PersonService-v1/personPart">
  <xsd:import namespace="http://www.equabank.cz/mandatory" schemaLocation="http://soa-1.dev1.equabank.loc:8001/soa-infra/services/client/PersonService-v1/apps/CommonInternal/xsd/MandatoryPart.xsd"/>
  <xsd:import namespace="http://www.equabank.cz/client/PersonService-v1/personPart" schemaLocation="http://soa-1.dev1.equabank.loc:8001/soa-infra/services/client/PersonService-v1/apps/public/client/iface/PersonService-v1/xsd/PersonPart-v1.0.xsd"/>
  <xsd:element name="setPersonRequest">
    <xsd:complexType>
      <xsd:complexContent>
        <xsd:extension base="mandatory:mandatoryRequest">
          <xsd:sequence>
            <xsd:element name="requestBody">
              <xsd:complexType>
                <xsd:complexContent>
                  <xsd:extension base="part:persons">
                    <xsd:sequence>
                      <xsd:element name="waitForUnification" type="xsd:boolean" minOccurs="0" default="false"/>
                    </xsd:sequence>
                  </xsd:extension>
                </xsd:complexContent>
              </xsd:complexType>
            </xsd:element>
          </xsd:sequence>
        </xsd:extension>
      </xsd:complexContent>
    </xsd:complexType>
  </xsd:element>
  <xsd:element name="setPersonResponse">
    <xsd:complexType>
      <xsd:complexContent>
        <xsd:extension base="mandatory:mandatoryResponse">
          <xsd:sequence>
            <xsd:element name="responseBody" type="personsResponse"/>
          </xsd:sequence>
        </xsd:extension>
      </xsd:complexContent>
    </xsd:complexType>
  </xsd:element>
  <xsd:complexType name="personsResponse">
    <xsd:annotation>
      <xsd:documentation>Instance party keys of inserted / updated persons</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence maxOccurs="unbounded">
      <xsd:element name="instPtKey" type="xsd:integer"/>
    </xsd:sequence>
  </xsd:complexType>
</xsd:schema>]]></con:content><con:type>http://www.w3.org/2001/XMLSchema</con:type></con:part><con:part><con:url>http://soa-1.dev1.equabank.loc:8001/soa-infra/services/client/PersonService-v1/apps/public/client/iface/PersonService-v1/xsd/PersonPart-v1.0.xsd</con:url><con:content><![CDATA[<xsd:schema targetNamespace="http://www.equabank.cz/client/PersonService-v1/personPart" elementFormDefault="qualified" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://www.equabank.cz/client/PersonService-v1/personPart" xmlns:mandatory="http://www.equabank.cz/mandatory" xmlns:lovs="http://www.equabank.cz/lovs">
  <xsd:import namespace="http://www.equabank.cz/mandatory" schemaLocation="http://soa-1.dev1.equabank.loc:8001/soa-infra/services/client/PersonService-v1/apps/CommonInternal/xsd/MandatoryPart.xsd"/>
  <xsd:import namespace="http://www.equabank.cz/lovs" schemaLocation="http://soa-1.dev1.equabank.loc:8001/soa-infra/services/client/PersonService-v1/apps/CommonInternal/xsd/LovPart.xsd"/>
  <xsd:complexType name="persons">
    <xsd:annotation>
      <xsd:documentation>Persons to be stored</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence maxOccurs="unbounded" minOccurs="0">
      <xsd:element name="person" type="person" maxOccurs="unbounded"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="person">
    <xsd:annotation>
      <xsd:documentation>person to be stored</xsd:documentation>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="instPtKey" type="xsd:integer" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Instance Party Key</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="instPtFlag" type="xsd:integer" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>riadiaci flag, ci sa ma
                                                      citat/zapisovat data o
                                                      instancii osoby</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="addrFlag" type="xsd:integer" maxOccurs="1" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>riadiaci flag, ci sa maju
                                                      citat/zapisovat data o
                                                      adresach na instancii osoby</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="emailFlag" type="xsd:integer" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>riadiaci flag, ci sa maju
                                                      citat/zapisovat data o
                                                      e-mailovych adresach na
                                                      instancii osoby</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="idCardFlag" type="xsd:integer" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>riadiaci flag, ci sa maju
                                                      citat/zapisovat data o
                                                      preukazoch totoznosti na
                                                      instancii osoby</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="phoneFlag" type="xsd:integer" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>riadiaci flag, ci sa maju
                                                      citat/zapisovat data o
                                                      telefonnych cislach na
                                                      instancii osoby</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="naceFlag" type="xsd:integer" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>riadiaci flag, ci sa maju
                                                      citat/zapisovat data o
                                                      NACE kodoch na instancii
                                                      osoby</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="uniPtKey" type="xsd:integer" minOccurs="0"/>
      <xsd:element name="firstName" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>First name of a person</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="lastName" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Last name of a person.</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="busTpId" type="lovs:LovPtTp" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Party Type - FO, FOP, PO</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="cntryId" type="lovs:LovCntry" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Country ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="busSectorId" type="lovs:LovBusSector" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>business sector</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="occId" type="lovs:LovOcc" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>occupation</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="stayTpId" type="lovs:LovStayTp" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>stay type</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="legalFormId" type="lovs:LovLegalForm" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>legal form</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="genderId" type="lovs:LovGender" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>gender</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="marStatId" type="lovs:LovMarStat" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>marital status</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="housingStatId" type="lovs:LovHousingStat" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>housing status</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="eduStatId" type="lovs:LovEduStat" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>education status</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="birthCntryId" type="lovs:LovCntry" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>birth country</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="birthPlace" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>birth place</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="rcNumber" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>birth number</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="icoNumber" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>ICO</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="dicNumber" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>DIC</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="midName" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>middle name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="birthName" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>birth name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="birthDate" type="xsd:date" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>date of birth</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="deathDate" type="xsd:date" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>date of death</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="titlBf" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>title before name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="titlAf" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>title after name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="salutation" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>salutation</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="busName" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>business name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="shortName" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>short name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="startDate" type="xsd:date" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>company start name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="residentFlag" type="xsd:integer" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Indicates whether Instance
                                                      Party is resident or not</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="dphRegnFlag" type="xsd:integer" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Indicates whether Instance
                                                      Party is registered to pay
                                                      DPH or not</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="stats101Flag" type="xsd:integer" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Indicates whether Instance
                                                      Party has signed 101 or not</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="taxDomicile" type="lovs:LovCntry" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Zeme danoveho domicilu</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="TIN" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Danove identifikacni cislo v zahranici</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="PEP" type="xsd:boolean" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Priznak, zda se jedna o politicky
                                          exponovanou osobu</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="cbId" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Credit Buro identifier</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="behSc" type="xsd:decimal" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Klientské behaviorální
                                                      score.</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="behScGrade" type="xsd:decimal" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Stupnice klientského beh.
                                                      score.</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="empNum" type="xsd:decimal" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Count of employees / Po?et zam?stnanc?</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="cribisRating" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>External rating from Cribis Database</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="inscyRec" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Insolvency record / Poslední záznam v
                                          insolven?ním rejst?íku</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="dbtrDbRec" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Debtor Database record / Posledni
                                          zaznam v databazi dluzniku</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="insurDebtRec" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Insurance Debt Record / Posledni
                                          zaznam o dluznosti na zdarvotnim nebo
                                          socialnim pojisteni</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="compRegRec" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Company Register Record / Posledni
                                          negativni zaznam/status v obchodnim
                                          rejstriku</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="finStmRec" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Financial Statement Record / Posledni
                                          negativni zaznam z financnich vykazu</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="interruptRec" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Enterprice interrupt record / Posledni
                                          zaznam o preruseni podnikani</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="delFlag" type="xsd:integer" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Indicates that row is deleted</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="srcId" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation/>
        </xsd:annotation>
        <xsd:complexType>
          <xsd:simpleContent>
            <xsd:extension base="xsd:string">
              <xsd:attribute name="srcSystemId" type="xsd:string"/>
            </xsd:extension>
          </xsd:simpleContent>
        </xsd:complexType>
      </xsd:element>
      <xsd:element name="emailContacts" type="emailContacts" minOccurs="0"/>
      <xsd:element name="addressContacts" type="addressContacts" minOccurs="0"/>
      <xsd:element name="phoneContacts" type="phoneContacts" minOccurs="0"/>
      <xsd:element name="idCards" type="idCards" minOccurs="0"/>
      <xsd:element name="naces" type="naces" minOccurs="0"/>
      <xsd:element name="frozenFlag" type="xsd:integer" minOccurs="0"/>
      <xsd:element name="cif" type="xsd:string" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="emailContacts">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:sequence maxOccurs="unbounded" minOccurs="0">
      <xsd:element name="email" type="emailContact" maxOccurs="unbounded" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="addressContacts">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:sequence maxOccurs="unbounded" minOccurs="0">
      <xsd:element name="address" type="addressContact" maxOccurs="unbounded" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="phoneContacts">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:sequence maxOccurs="unbounded" minOccurs="0">
      <xsd:element name="phone" type="phoneContact" maxOccurs="unbounded" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="idCards">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:sequence maxOccurs="unbounded" minOccurs="0">
      <xsd:element name="idCard" type="idCard" maxOccurs="unbounded" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="naces">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:sequence maxOccurs="unbounded" minOccurs="0">
      <xsd:element name="nace" type="nace" maxOccurs="unbounded" minOccurs="0"/>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="emailContact">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="email" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>e-mail address</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="emailPurpId" type="lovs:LovEmailPurp" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Email Purpose</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="emailDescr" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Email description</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="emailSrcId" type="xsd:string">
        <xsd:annotation>
          <xsd:documentation>Source ID of the record</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="emailDelFlag" type="xsd:integer">
        <xsd:annotation>
          <xsd:documentation>Indicates whether Instance E-mail is deleted or not</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="addressContact">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="addrPurpId" type="lovs:LovAddrPurp" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Address Purpose ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="addrCntryId" type="lovs:LovCntry" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Address Country ID</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="addrStreetName" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Street Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="addrStreetNum" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Street Number</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="addrCity" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>City Name</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="addrZip" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Address ZIP Code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="addrValidDate" type="xsd:date" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Deprecated - removed from
                                                      ODS API</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="addrVerifiedFlag" type="xsd:integer" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Deprecated - removed from
                                                      ODS API</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="addrSrcId" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Source ID of the record</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="addrDelFlag" type="xsd:integer" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Indicates whether Instance E-mail is deleted or not</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="phoneContact">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="phoneTpId" type="lovs:LovPhoneTp" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Phone Type</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="phonePurpId" type="lovs:LovPhonePurp" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Phone Purpose</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="phoneNum" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>hone Number</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="phoneCntryId" type="lovs:LovCntry" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Deprecated - removed from
                                                      ODS API</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="phoneBlockFlag" type="xsd:integer" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Deprecated - removed from
                                                      ODS API</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="phoneSrcId" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Source ID of the record</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="phoneDelFlag" type="xsd:integer" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Indicates whether Instance E-mail is deleted or not</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="idCard">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="idCardTpId" type="lovs:LovIdCardTp" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Type of ID card</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="idCardCntryId" type="lovs:LovCntry" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Card Issuer Country</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="idCardIssueDate" type="xsd:date" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Card Issue Date</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="idCardExprDate" type="xsd:date" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Card Expiration Date</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="idCardId" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Card Identifier</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="idCardIssuer" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Card Issuer</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="idCardSrcId" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Source ID of the record</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="idCardDelFlag" type="xsd:integer" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Indicates whether Instance E-mail is deleted or not</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
  <xsd:complexType name="nace">
    <xsd:annotation>
      <xsd:documentation/>
    </xsd:annotation>
    <xsd:sequence>
      <xsd:element name="naceCodeId" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>NACE code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="naceDescr" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>NACE description</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="naceMainFlag" type="xsd:integer" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Indicates whether Instance NACE is main or not</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="naceSrcId" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Source ID of the record</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="naceSectorCodeId" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>NACE sector code</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="naceSectorDescr" type="xsd:string" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>NACE sector description</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
      <xsd:element name="naceDelFlag" type="xsd:integer" minOccurs="0">
        <xsd:annotation>
          <xsd:documentation>Indicates whether Instance E-mail is deleted or not</xsd:documentation>
        </xsd:annotation>
      </xsd:element>
    </xsd:sequence>
  </xsd:complexType>
</xsd:schema>]]></con:content><con:type>http://www.w3.org/2001/XMLSchema</con:type></con:part><con:part><con:url>http://soa-1.dev1.equabank.loc:8001/soa-infra/services/client/PersonService-v1/apps/public/client/iface/PersonService-v1/xsd/NewLead-v1.0.xsd</con:url><con:content><![CDATA[<schema targetNamespace="http://www.equabank.cz/client/PersonService-v1/newLead" elementFormDefault="qualified" xmlns="http://www.w3.org/2001/XMLSchema" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:tns="http://www.equabank.cz/client/PersonService-v1/newLead" xmlns:mandatory="http://www.equabank.cz/mandatory" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <xs:import namespace="http://www.equabank.cz/mandatory" schemaLocation="http://soa-1.dev1.equabank.loc:8001/soa-infra/services/client/PersonService-v1/apps/CommonInternal/xsd/MandatoryPart.xsd"/>
  <xs:element name="newLeadRequest">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="mandatory:mandatoryRequest">
          <xs:sequence>
            <xs:element name="requestBody" type="tns:ctLeadsReq"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <xs:element name="newLeadResponse">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="mandatory:mandatoryResponse">
          <xs:sequence>
            <xs:element name="responseBody" type="tns:ctLeadsResp" minOccurs="0" maxOccurs="1"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <element name="newLeadJMSResponse">
    <complexType>
      <complexContent>
        <extension base="mandatory:mandatoryResponse">
          <sequence>
            <element name="responseBody" type="tns:ctLeadResp"/>
          </sequence>
        </extension>
      </complexContent>
    </complexType>
  </element>
  <complexType name="ctLeadsReq">
    <sequence maxOccurs="1">
      <element name="lead" type="tns:ctLeadReq" maxOccurs="1"/>
    </sequence>
  </complexType>
  <complexType name="ctLeadReq">
    <sequence>
      <element name="gfoLeadId" type="string" maxOccurs="1" minOccurs="0"/>
      <element name="name" minOccurs="1" maxOccurs="1">
        <annotation>
          <documentation>first name of lead</documentation>
        </annotation>
        <simpleType>
          <restriction base="string">
            <maxLength value="105"/>
          </restriction>
        </simpleType>
      </element>
      <element name="surname" minOccurs="1" maxOccurs="1">
        <annotation>
          <documentation>surname of lead</documentation>
        </annotation>
        <simpleType>
          <restriction base="string">
            <maxLength value="105"/>
          </restriction>
        </simpleType>
      </element>
      <element name="mobilePhone" type="string" minOccurs="0">
        <annotation>
          <documentation>mobile phone or email is mandatory</documentation>
        </annotation>
      </element>
      <element name="email" type="string" minOccurs="0">
        <annotation>
          <documentation>mobile phone or email is mandatory</documentation>
        </annotation>
      </element>
      <element name="servicingBranch" type="string" minOccurs="0">
        <annotation>
          <documentation>Servicing branch code used in FlexCube</documentation>
        </annotation>
      </element>
      <element name="contactTime" type="time" minOccurs="0">
        <annotation>
          <documentation>Preferrable time when the client wants to be contacted: 15:00:00 means 15:00:00-16:00:00</documentation>
        </annotation>
      </element>
      <element name="comment" type="string" minOccurs="0"/>
      <element name="product" type="string" maxOccurs="unbounded">
        <annotation>
          <documentation>One code for a combination of a product type and currency. (CAEUR,CACZK,CAUSD,SACZK,....)</documentation>
        </annotation>
      </element>
      <element name="cookiiId" type="string" minOccurs="0">
        <annotation>
          <documentation>cookieId</documentation>
        </annotation>
      </element>
      <element name="webRequestId" type="string" minOccurs="0">
        <annotation>
          <documentation>web requestId</documentation>
        </annotation>
      </element>
      <element name="firstTouchPoint" type="string" minOccurs="1" maxOccurs="1"/>
      <element name="activationChannel" type="string" minOccurs="0">
        <annotation>
          <documentation>Activation channel, e.g., brach, kiosk, broker,...</documentation>
        </annotation>
      </element>
      <element name="activationBranch" minOccurs="0" type="string">
        <annotation>
          <documentation>Activation branch or another place</documentation>
        </annotation>
      </element>
      <element name="activationActor" minOccurs="0" type="string">
        <annotation>
          <documentation>Employee code, etc. Dependent on the activation channel.</documentation>
        </annotation>
      </element>
      <element name="identificationNumber" type="string" minOccurs="0" maxOccurs="1">
        <annotation>
          <documentation>identification number (usually a birth number)</documentation>
        </annotation>
      </element>
    </sequence>
  </complexType>
  <complexType name="ctLeadsResp">
    <sequence>
      <element name="lead" type="tns:ctLeadResp"/>
    </sequence>
  </complexType>
  <complexType name="ctLeadResp">
    <sequence>
      <element name="gfoLeadId" type="string">
        <annotation>
          <documentation>Id of lead in GFO database (could be id of created or allready existing with it was merged)</documentation>
        </annotation>
      </element>
      <element name="leadMerged" type="boolean" default="false">
        <annotation>
          <documentation>true if lead already exist in GFO and was merged</documentation>
        </annotation>
      </element>
    </sequence>
  </complexType>
</schema>]]></con:content><con:type>http://www.w3.org/2001/XMLSchema</con:type></con:part><con:part><con:url>http://soa-1.dev1.equabank.loc:8001/soa-infra/services/client/PersonService-v1/apps/public/client/iface/PersonService-v1/xsd/GetPerson-v1.0.xsd</con:url><con:content><![CDATA[<xs:schema version="1.0" targetNamespace="http://www.equabank.cz/client/PersonService-v1/getPerson" elementFormDefault="qualified" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.equabank.cz/client/PersonService-v1/getPerson" xmlns:mandatory="http://www.equabank.cz/mandatory" xmlns:part="http://www.equabank.cz/client/PersonService-v1/personPart" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <xs:import namespace="http://www.equabank.cz/mandatory" schemaLocation="http://soa-1.dev1.equabank.loc:8001/soa-infra/services/client/PersonService-v1/apps/CommonInternal/xsd/MandatoryPart.xsd"/>
  <xs:import namespace="http://www.equabank.cz/client/PersonService-v1/personPart" schemaLocation="http://soa-1.dev1.equabank.loc:8001/soa-infra/services/client/PersonService-v1/apps/public/client/iface/PersonService-v1/xsd/PersonPart-v1.0.xsd"/>
  <xs:element name="getPersonRequest">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="mandatory:mandatoryRequest">
          <xs:sequence>
            <xs:element name="requestBody" type="ctGetPersonsRequest"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <xs:element name="getPersonResponse">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="mandatory:mandatoryResponse">
          <xs:sequence minOccurs="0">
            <xs:element name="responseBody" type="ctGetPersonsResponse" minOccurs="1"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <xs:complexType name="ctGetPersonsResponse">
    <xs:annotation>
      <xs:documentation>Instance party keys of inserted / updated persons</xs:documentation>
    </xs:annotation>
    <xs:sequence maxOccurs="unbounded" minOccurs="0">
      <xs:element name="person" type="part:person"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="ctGetPersonRequest">
    <xs:sequence>
      <xs:element name="instPtKey" type="xs:integer" minOccurs="0">
        <xs:annotation>
          <xs:documentation>key v ODS pozadovanej osoby (osobu treba identifikovat bud instPtKey alebo
      srcId/srcSystemId)</xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element name="instPtFlag" type="xs:boolean" minOccurs="0">
        <xs:annotation>
          <xs:documentation>riadiaci flag, ci sa maju vratit data o instancii osoby</xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element name="addrFlag" type="xs:boolean" minOccurs="0">
        <xs:annotation>
          <xs:documentation>riadiaci flag, ci sa maju vratit data o adresach na instancii osoby</xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element name="emailFlag" type="xs:boolean" minOccurs="0">
        <xs:annotation>
          <xs:documentation>riadiaci flag, ci sa maju vratit data o e-mailovych adresach na instancii
      osoby</xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element name="idCardFlag" type="xs:boolean" minOccurs="0">
        <xs:annotation>
          <xs:documentation>riadiaci flag, ci sa maju vratit data o preukazoch totoznosti na instancii
      osoby</xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element name="phoneFlag" type="xs:boolean" minOccurs="0">
        <xs:annotation>
          <xs:documentation>riadiaci flag, ci sa maju vratit data o telefonnych cislach na instancii
      osoby</xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element name="naceFlag" type="xs:boolean" minOccurs="0">
        <xs:annotation>
          <xs:documentation>riadiaci flag, ci sa maju vratit data o NACE kodoch na instancii osoby</xs:documentation>
        </xs:annotation>
      </xs:element>
      <xs:element name="srcId" minOccurs="0">
        <xs:annotation>
          <xs:documentation>ID v zdrojovom systeme pozadovanej osoby (osobu treba identifikovat bud
      instPtKey alebo
      srcId/srcSystemId)</xs:documentation>
        </xs:annotation>
        <xs:complexType>
          <xs:simpleContent>
            <xs:extension base="xs:string">
              <xs:attribute name="srcSystemId" type="xs:string">
                <xs:annotation>
                  <xs:documentation>identifikacia zdrojoveho systemu, napr. ADB, FC, GFO, ELB, atd.</xs:documentation>
                </xs:annotation>
              </xs:attribute>
            </xs:extension>
          </xs:simpleContent>
        </xs:complexType>
      </xs:element>
      <xs:element name="trgSystemId" minOccurs="0" type="xs:string">
        <xs:annotation>
          <xs:documentation>identifikacia systemu, ktoreho instanciu hladame. mozeme teda
      identifikovat osobu prostrednictvom instPtKey alebo srcId/srcSystemId a
      tymto elementom povedat, ze pre tu osobu hladame napr. FC instanciu</xs:documentation>
        </xs:annotation>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="ctGetPersonsRequest">
    <xs:sequence maxOccurs="unbounded">
      <xs:element name="person" type="ctGetPersonRequest"/>
    </xs:sequence>
  </xs:complexType>
</xs:schema>]]></con:content><con:type>http://www.w3.org/2001/XMLSchema</con:type></con:part><con:part><con:url>http://soa-1.dev1.equabank.loc:8001/soa-infra/services/client/PersonService-v1/apps/public/client/iface/PersonService-v1/xsd/GetApplicationPerson-v1.0.xsd</con:url><con:content><![CDATA[<xs:schema version="1.0" targetNamespace="http://www.equabank.cz/client/PersonService-v1/getApplicationPerson" elementFormDefault="qualified" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.equabank.cz/client/PersonService-v1/getApplicationPerson" xmlns:mandatory="http://www.equabank.cz/mandatory" xmlns:part="http://www.equabank.cz/client/PersonService-v1/personPart" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <xs:import namespace="http://www.equabank.cz/mandatory" schemaLocation="http://soa-1.dev1.equabank.loc:8001/soa-infra/services/client/PersonService-v1/apps/CommonInternal/xsd/MandatoryPart.xsd"/>
  <xs:import namespace="http://www.equabank.cz/client/PersonService-v1/personPart" schemaLocation="http://soa-1.dev1.equabank.loc:8001/soa-infra/services/client/PersonService-v1/apps/public/client/iface/PersonService-v1/xsd/PersonPart-v1.0.xsd"/>
  <xs:element name="getApplicationPersonRequest">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="mandatory:mandatoryRequest">
          <xs:sequence>
            <xs:element name="requestBody">
              <xs:complexType>
                <xs:complexContent>
                  <xs:extension base="ctGetApplicationPersonsRequest">
                    <xs:sequence>
                      <xs:choice>
                        <xs:element name="applSrcId" minOccurs="1">
                          <xs:annotation>
                            <xs:documentation>Application Identifier in Source System. In case
                                of RAS it is applKey.</xs:documentation>
                          </xs:annotation>
                          <xs:complexType>
                            <xs:simpleContent>
                              <xs:extension base="xs:string">
                                <xs:attribute name="srcSystemId" type="xs:string"/>
                              </xs:extension>
                            </xs:simpleContent>
                          </xs:complexType>
                        </xs:element>
                        <xs:element name="busApplId" minOccurs="1">
                          <xs:annotation>
                            <xs:documentation>Business Application Identifier</xs:documentation>
                          </xs:annotation>
                          <xs:complexType>
                            <xs:simpleContent>
                              <xs:extension base="xs:string">
                                <xs:attribute name="srcSystemId" type="xs:string"/>
                              </xs:extension>
                            </xs:simpleContent>
                          </xs:complexType>
                        </xs:element>
                      </xs:choice>
                      <xs:element name="instPtFlag" type="xs:boolean" minOccurs="0">
                        <xs:annotation>
                          <xs:documentation>riadiaci flag, ci sa maju vratit data o instancii
                               osoby</xs:documentation>
                        </xs:annotation>
                      </xs:element>
                      <xs:element name="addrFlag" type="xs:boolean" minOccurs="0">
                        <xs:annotation>
                          <xs:documentation>riadiaci flag, ci sa maju vratit data o adresach
                               na instancii osoby</xs:documentation>
                        </xs:annotation>
                      </xs:element>
                      <xs:element name="emailFlag" type="xs:boolean" minOccurs="0">
                        <xs:annotation>
                          <xs:documentation>riadiaci flag, ci sa maju vratit data o
                               e-mailovych adresach na instancii osoby</xs:documentation>
                        </xs:annotation>
                      </xs:element>
                      <xs:element name="idCardFlag" type="xs:boolean" minOccurs="0">
                        <xs:annotation>
                          <xs:documentation>riadiaci flag, ci sa maju vratit data o
                               preukazoch totoznosti na instancii osoby</xs:documentation>
                        </xs:annotation>
                      </xs:element>
                      <xs:element name="phoneFlag" type="xs:boolean" minOccurs="0">
                        <xs:annotation>
                          <xs:documentation>riadiaci flag, ci sa maju vratit data o
                               telefonnych cislach na instancii osoby</xs:documentation>
                        </xs:annotation>
                      </xs:element>
                      <xs:element name="naceFlag" type="xs:boolean" minOccurs="0">
                        <xs:annotation>
                          <xs:documentation>riadiaci flag, ci sa maju vratit data o NACE
                               kodoch na instancii osoby</xs:documentation>
                        </xs:annotation>
                      </xs:element>
                    </xs:sequence>
                  </xs:extension>
                </xs:complexContent>
              </xs:complexType>
            </xs:element>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <xs:element name="getApplicationPersonResponse">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="mandatory:mandatoryResponse">
          <xs:sequence minOccurs="0">
            <xs:element name="responseBody" type="ctGetApplicationPersonsResponse" minOccurs="1"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <xs:complexType name="ctGetApplicationPersonsResponse">
    <xs:annotation>
      <xs:documentation>Instance party keys of inserted / updated persons</xs:documentation>
    </xs:annotation>
    <xs:sequence maxOccurs="unbounded" minOccurs="0">
      <xs:element name="person" type="part:person"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="ctGetApplicationPersonRequest"/>
  <xs:complexType name="ctGetApplicationPersonsRequest"/>
</xs:schema>]]></con:content><con:type>http://www.w3.org/2001/XMLSchema</con:type></con:part><con:part><con:url>http://soa-1.dev1.equabank.loc:8001/soa-infra/services/client/PersonService-v1/apps/public/client/iface/PersonService-v1/xsd/GetVerifiedPerson-v1.0.xsd</con:url><con:content><![CDATA[<xs:schema version="1.0" targetNamespace="http://www.equabank.cz/client/PersonService-v1/getVerifiedPerson" elementFormDefault="qualified" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.equabank.cz/client/PersonService-v1/getVerifiedPerson" xmlns:mandatory="http://www.equabank.cz/mandatory" xmlns:part="http://www.equabank.cz/client/PersonService-v1/personPart" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <xs:import namespace="http://www.equabank.cz/mandatory" schemaLocation="http://soa-1.dev1.equabank.loc:8001/soa-infra/services/client/PersonService-v1/apps/CommonInternal/xsd/MandatoryPart.xsd"/>
  <xs:import namespace="http://www.equabank.cz/client/PersonService-v1/personPart" schemaLocation="http://soa-1.dev1.equabank.loc:8001/soa-infra/services/client/PersonService-v1/apps/public/client/iface/PersonService-v1/xsd/PersonPart-v1.0.xsd"/>
  <xs:element name="getVerifiedPersonRequest">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="mandatory:mandatoryRequest">
          <xs:sequence>
            <xs:element name="requestBody" type="ctGetVerifiedPersonsRequest"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <xs:element name="getVerifiedPersonResponse">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="mandatory:mandatoryResponse">
          <xs:sequence minOccurs="1">
            <xs:element name="responseBody" minOccurs="0">
              <xs:complexType>
                <xs:complexContent>
                  <xs:extension base="ctGetVerifiedPersonsResponse">
                    <xs:sequence>
                      <xs:element name="verifiedPersons" type="ctVerifiedPersons"/>
                    </xs:sequence>
                  </xs:extension>
                </xs:complexContent>
              </xs:complexType>
            </xs:element>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <xs:complexType name="ctGetVerifiedPersonsRequest">
    <xs:sequence>
      <xs:element name="phoneNum" type="xsd:string"/>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="ctGetVerifiedPersonsResponse">
    <xs:annotation>
      <xs:documentation>Instance party keys of inserted / updated persons</xs:documentation>
    </xs:annotation>
  </xs:complexType>
  <xs:complexType name="ctVerifiedPersons">
    <xs:sequence>
      <xs:element name="verifiedPerson" maxOccurs="unbounded">
        <xs:complexType>
          <xs:sequence>
            <xs:element name="gfoId" minOccurs="0" type="xsd:string"/>
            <xs:element name="birthNumber" minOccurs="0" type="xsd:string"/>
            <xs:element name="birthDate" minOccurs="0" type="xsd:date"/>
          </xs:sequence>
        </xs:complexType>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
</xs:schema>]]></con:content><con:type>http://www.w3.org/2001/XMLSchema</con:type></con:part><con:part><con:url>http://soa-1.dev1.equabank.loc:8001/soa-infra/services/client/PersonService-v1/apps/public/client/iface/PersonService-v1/xsd/GetGfoIdByHash-v1.0.xsd</con:url><con:content><![CDATA[<xs:schema version="1.0" targetNamespace="http://www.equabank.cz/client/PersonService-v1/getGfoIdByHash" elementFormDefault="qualified" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="http://www.equabank.cz/client/PersonService-v1/getGfoIdByHash" xmlns:man="http://www.equabank.cz/mandatory" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <xs:import namespace="http://www.equabank.cz/mandatory" schemaLocation="http://soa-1.dev1.equabank.loc:8001/soa-infra/services/client/PersonService-v1/apps/CommonInternal/xsd/MandatoryPart.xsd"/>
  <xs:element name="getGfoIdByHashRequest">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="man:mandatoryRequest">
          <xs:sequence>
            <xs:element name="requestBody" type="ctGetGfoIdByHashRequest"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <xs:element name="getGfoIdByHashResponse">
    <xs:complexType>
      <xs:complexContent>
        <xs:extension base="man:mandatoryResponse">
          <xs:sequence minOccurs="1">
            <xs:element name="responseBody" type="ctGetGfoIdByHashResponse" minOccurs="0"/>
          </xs:sequence>
        </xs:extension>
      </xs:complexContent>
    </xs:complexType>
  </xs:element>
  <xs:complexType name="ctGetGfoIdByHashResponse">
    <xs:sequence minOccurs="1">
      <xs:element name="gfoId" type="xs:string">
        <xs:annotation>
          <xs:documentation>gfo id klienta</xs:documentation>
        </xs:annotation>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
  <xs:complexType name="ctGetGfoIdByHashRequest">
    <xs:sequence>
      <xs:element name="hash" type="xs:string">
        <xs:annotation>
          <xs:documentation>Hash klienta</xs:documentation>
        </xs:annotation>
      </xs:element>
    </xs:sequence>
  </xs:complexType>
</xs:schema>]]></con:content><con:type>http://www.w3.org/2001/XMLSchema</con:type></con:part></con:definitionCache><con:endpoints><con:endpoint>http://soa-1.dev1.equabank.loc:8001/soa-infra/services/client/PersonService-v1/PersonService-v1</con:endpoint><con:endpoint>http://soa-1.sit1.equabank.loc/soa-infra/services/client/PersonService-v1/PersonService-v1</con:endpoint></con:endpoints><con:operation id="79caaa51-6abb-4187-ac82-018535096fb2" isOneWay="false" action="http://www.equabank.cz/client/PersonService-v1/findPerson" name="findPerson" bindingOperationName="findPerson" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional"><con:settings/><con:call id="d012d57e-e362-4bf8-9590-ae70273a7388" name="Request 1"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:encoding>UTF-8</con:encoding><con:endpoint>http://soa.sit1.equabank.loc/soa-infra/services/client/PersonService-v1/PersonService-v1</con:endpoint><con:request><![CDATA[<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:fin="http://www.equabank.cz/client/PersonService-v1/findPerson" xmlns:man="http://www.equabank.cz/mandatory">
   <soapenv:Header/>
   <soapenv:Body>
      <fin:findPersonRequest>
         <man:systemId>CAM</man:systemId>
         <man:userId>CAM_USER</man:userId>
         <man:processId>1</man:processId>
         <man:messageId>1</man:messageId>         
         <fin:requestBody>
            <fin:person>               
               <fin:busTpId lov="PT_TP">I</fin:busTpId>               
               <fin:rcNumber>810319/7443</fin:rcNumber>               
            </fin:person>
         </fin:requestBody>
      </fin:findPersonRequest>
   </soapenv:Body>
</soapenv:Envelope>]]></con:request><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:wsaConfig mustUnderstand="NONE" version="200508" action="http://www.equabank.cz/client/PersonService-v1/findPerson"/><con:wsrmConfig version="1.2"/></con:call><con:call id="e8bbe513-1a9a-4f8f-bb82-193b51e1a2a8" name="Copy of Request 1"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:encoding>UTF-8</con:encoding><con:endpoint>http://soa-1.dev1.equabank.loc:8001/soa-infra/services/client/PersonService-v1/PersonService-v1</con:endpoint><con:request><![CDATA[<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:fin="http://www.equabank.cz/client/PersonService-v1/findPerson" xmlns:man="http://www.equabank.cz/mandatory">
   <soapenv:Header/>
   <soapenv:Body>
      <fin:findPersonRequest>
         <man:systemId>CAM</man:systemId>
         <man:userId>CAM_USER</man:userId>
         <man:processId>1</man:processId>
         <man:messageId>1</man:messageId>
         <!--Optional:-->
         <man:wflProcessId>?</man:wflProcessId>
         <fin:requestBody>
            <fin:person>               
               <!--Optional:-->
               <fin:ptSrcId>?</fin:ptSrcId>
               <fin:busTpId lov="PT_TP">I</fin:busTpId>
               <!--Optional:-->
               <fin:firstName>?</fin:firstName>
               <!--Optional:-->
               <fin:lastName>?</fin:lastName>
               <!--Optional:-->
               <fin:rcNumber>**********</fin:rcNumber>
               <!--Optional:-->
               <fin:icoNumber>?</fin:icoNumber>
               <!--Optional:-->
               <fin:busName>?</fin:busName>
               <!--Optional:-->
               <fin:birthDate>?</fin:birthDate>
               <!--Optional:-->
               <fin:zecoNumber>?</fin:zecoNumber>
               <!--Optional:-->
               <fin:phones>
                  <!--1 or more repetitions:-->
                  <fin:phone>?</fin:phone>
               </fin:phones>
               <!--Optional:-->
               <fin:streets>
                  <!--Zero or more repetitions:-->
                  <fin:street>?</fin:street>
               </fin:streets>
               <!--Optional:-->
               <fin:streetNumbers>
                  <!--Zero or more repetitions:-->
                  <fin:streetNumber>?</fin:streetNumber>
               </fin:streetNumbers>
               <!--Optional:-->
               <fin:cities>
                  <!--1 or more repetitions:-->
                  <fin:city>?</fin:city>
               </fin:cities>
               <!--Optional:-->
               <fin:zips>
                  <!--1 or more repetitions:-->
                  <fin:zip>?</fin:zip>
               </fin:zips>
               <!--Optional:-->
               <fin:emails>
                  <!--1 or more repetitions:-->
                  <fin:email>?</fin:email>
               </fin:emails>
               <!--Optional:-->
               <fin:firstIdCardNums>
                  <!--1 or more repetitions:-->
                  <fin:firstIdCardNum>?</fin:firstIdCardNum>
               </fin:firstIdCardNums>
            </fin:person>
         </fin:requestBody>
      </fin:findPersonRequest>
   </soapenv:Body>
</soapenv:Envelope>]]></con:request><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:wsaConfig mustUnderstand="NONE" version="200508" action="http://www.equabank.cz/client/PersonService-v1/findPerson"/><con:wsrmConfig version="1.2"/></con:call></con:operation><con:operation id="70fe72ac-5ce0-4766-aca2-158ee358f77e" isOneWay="false" action="http://www.equabank.cz/client/PersonService-v1/getApplicationPerson" name="getApplicationPerson" bindingOperationName="getApplicationPerson" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional"><con:settings/><con:call id="f1568413-b3ba-43fe-bafd-748d1654fd48" name="Request 1"><con:settings/><con:encoding>UTF-8</con:encoding><con:endpoint>http://soa-1.dev1.equabank.loc:8001/soa-infra/services/client/PersonService-v1/PersonService-v1</con:endpoint><con:request><![CDATA[<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:get="http://www.equabank.cz/client/PersonService-v1/getApplicationPerson" xmlns:man="http://www.equabank.cz/mandatory">
   <soapenv:Header/>
   <soapenv:Body>
      <get:getApplicationPersonRequest>
         <man:systemId>?</man:systemId>
         <man:userId>?</man:userId>
         <man:processId>?</man:processId>
         <man:messageId>?</man:messageId>
         <!--Optional:-->
         <man:wflProcessId>?</man:wflProcessId>
         <get:requestBody>
            <!--You have a CHOICE of the next 2 items at this level-->
            <get:applSrcId srcSystemId="?">?</get:applSrcId>
            <get:busApplId srcSystemId="?">?</get:busApplId>
            <!--Optional:-->
            <get:instPtFlag>?</get:instPtFlag>
            <!--Optional:-->
            <get:addrFlag>?</get:addrFlag>
            <!--Optional:-->
            <get:emailFlag>?</get:emailFlag>
            <!--Optional:-->
            <get:idCardFlag>?</get:idCardFlag>
            <!--Optional:-->
            <get:phoneFlag>?</get:phoneFlag>
            <!--Optional:-->
            <get:naceFlag>?</get:naceFlag>
         </get:requestBody>
      </get:getApplicationPersonRequest>
   </soapenv:Body>
</soapenv:Envelope>]]></con:request><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:wsaConfig mustUnderstand="NONE" version="200508" action="http://www.equabank.cz/client/PersonService-v1/getApplicationPerson"/><con:wsrmConfig version="1.2"/></con:call></con:operation><con:operation id="a054bdfb-ebb2-45f8-89a0-60d36cd00cd1" isOneWay="false" action="http://www.equabank.cz/client/PersonService-v1/getGfoIdByHash" name="getGfoIdByHash" bindingOperationName="getGfoIdByHash" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional"><con:settings/><con:call id="0c3a89af-6369-4a2f-9966-bbcb47eaacd7" name="Request 1"><con:settings/><con:encoding>UTF-8</con:encoding><con:endpoint>http://soa-1.dev1.equabank.loc:8001/soa-infra/services/client/PersonService-v1/PersonService-v1</con:endpoint><con:request><![CDATA[<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:get="http://www.equabank.cz/client/PersonService-v1/getGfoIdByHash" xmlns:man="http://www.equabank.cz/mandatory">
   <soapenv:Header/>
   <soapenv:Body>
      <get:getGfoIdByHashRequest>
         <man:systemId>?</man:systemId>
         <man:userId>?</man:userId>
         <man:processId>?</man:processId>
         <man:messageId>?</man:messageId>
         <!--Optional:-->
         <man:wflProcessId>?</man:wflProcessId>
         <get:requestBody>
            <get:hash>?</get:hash>
         </get:requestBody>
      </get:getGfoIdByHashRequest>
   </soapenv:Body>
</soapenv:Envelope>]]></con:request><con:wsaConfig mustUnderstand="NONE" version="200508" action="http://www.equabank.cz/client/PersonService-v1/getGfoIdByHash"/></con:call></con:operation><con:operation id="9c556196-1677-4c43-954e-1e3d140fca40" isOneWay="false" action="http://www.equabank.cz/client/PersonService-v1/getPerson" name="getPerson" bindingOperationName="getPerson" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional"><con:settings/><con:call id="bf7dc0bf-f649-4bfc-a46c-6fefebbbc3e2" name="Request 1"><con:settings><con:setting id="com.eviware.soapui.impl.wsdl.WsdlRequest@request-headers">&lt;xml-fragment/></con:setting></con:settings><con:encoding>UTF-8</con:encoding><con:endpoint>http://soa-1.dev1.equabank.loc:8001/soa-infra/services/client/PersonService-v1/PersonService-v1</con:endpoint><con:request><![CDATA[<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:get="http://www.equabank.cz/client/PersonService-v1/getPerson" xmlns:man="http://www.equabank.cz/mandatory">
   <soapenv:Header/>
   <soapenv:Body>
      <get:getPersonRequest>
         <man:systemId>CAM</man:systemId>
         <man:userId>CAM_USER</man:userId>
         <man:processId>1</man:processId>
         <man:messageId>1</man:messageId>         
         <get:requestBody>
            <get:person>               
               <get:srcId srcSystemId="GFO">2898601</get:srcId>               
            </get:person>
         </get:requestBody>
      </get:getPersonRequest>
   </soapenv:Body>
</soapenv:Envelope>]]></con:request><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:wsaConfig mustUnderstand="NONE" version="200508" action="http://www.equabank.cz/client/PersonService-v1/getPerson"/><con:wsrmConfig version="1.2"/></con:call></con:operation><con:operation id="fc2b439d-9b4e-46df-94c0-b84493b2faae" isOneWay="false" action="http://www.equabank.cz/client/PersonService-v1/getVerifiedPerson" name="getVerifiedPerson" bindingOperationName="getVerifiedPerson" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional"><con:settings/><con:call id="c3698ea5-b789-4eb0-8332-4f3c7352cf61" name="Request 1"><con:settings/><con:encoding>UTF-8</con:encoding><con:endpoint>http://soa-1.dev1.equabank.loc:8001/soa-infra/services/client/PersonService-v1/PersonService-v1</con:endpoint><con:request><![CDATA[<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:get="http://www.equabank.cz/client/PersonService-v1/getVerifiedPerson" xmlns:man="http://www.equabank.cz/mandatory">
   <soapenv:Header/>
   <soapenv:Body>
      <get:getVerifiedPersonRequest>
         <man:systemId>?</man:systemId>
         <man:userId>?</man:userId>
         <man:processId>?</man:processId>
         <man:messageId>?</man:messageId>
         <!--Optional:-->
         <man:wflProcessId>?</man:wflProcessId>
         <get:requestBody>
            <get:phoneNum>?</get:phoneNum>
         </get:requestBody>
      </get:getVerifiedPersonRequest>
   </soapenv:Body>
</soapenv:Envelope>]]></con:request><con:wsaConfig mustUnderstand="NONE" version="200508" action="http://www.equabank.cz/client/PersonService-v1/getVerifiedPerson"/></con:call></con:operation><con:operation id="a11786e5-8f62-425e-8171-8268fdbfabc1" isOneWay="false" action="http://www.equabank.cz/client/PersonService-v1/newLead" name="newLead" bindingOperationName="newLead" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional"><con:settings/><con:call id="f148c061-481f-4d18-86ae-5852a4e28147" name="Request 1"><con:settings/><con:encoding>UTF-8</con:encoding><con:endpoint>http://soa-1.dev1.equabank.loc:8001/soa-infra/services/client/PersonService-v1/PersonService-v1</con:endpoint><con:request><![CDATA[<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:new="http://www.equabank.cz/client/PersonService-v1/newLead" xmlns:man="http://www.equabank.cz/mandatory">
   <soapenv:Header/>
   <soapenv:Body>
      <new:newLeadRequest>
         <man:systemId>?</man:systemId>
         <man:userId>?</man:userId>
         <man:processId>?</man:processId>
         <man:messageId>?</man:messageId>
         <!--Optional:-->
         <man:wflProcessId>?</man:wflProcessId>
         <new:requestBody>
            <new:lead>
               <!--Optional:-->
               <new:gfoLeadId>?</new:gfoLeadId>
               <new:name>?</new:name>
               <new:surname>?</new:surname>
               <!--Optional:-->
               <new:mobilePhone>?</new:mobilePhone>
               <!--Optional:-->
               <new:email>?</new:email>
               <!--Optional:-->
               <new:servicingBranch>?</new:servicingBranch>
               <!--Optional:-->
               <new:contactTime>?</new:contactTime>
               <!--Optional:-->
               <new:comment>?</new:comment>
               <!--1 or more repetitions:-->
               <new:product>?</new:product>
               <!--Optional:-->
               <new:cookiiId>?</new:cookiiId>
               <!--Optional:-->
               <new:webRequestId>?</new:webRequestId>
               <new:firstTouchPoint>?</new:firstTouchPoint>
               <!--Optional:-->
               <new:activationChannel>?</new:activationChannel>
               <!--Optional:-->
               <new:activationBranch>?</new:activationBranch>
               <!--Optional:-->
               <new:activationActor>?</new:activationActor>
               <!--Optional:-->
               <new:identificationNumber>?</new:identificationNumber>
            </new:lead>
         </new:requestBody>
      </new:newLeadRequest>
   </soapenv:Body>
</soapenv:Envelope>]]></con:request><con:wsaConfig mustUnderstand="NONE" version="200508" action="http://www.equabank.cz/client/PersonService-v1/newLead"/></con:call></con:operation><con:operation id="4daca690-e491-497f-af3b-4e82550cb34c" isOneWay="false" action="http://www.equabank.cz/client/PersonService-v1/setPerson" name="setPerson" bindingOperationName="setPerson" type="Request-Response" inputName="" receivesAttachments="false" sendsAttachments="false" anonymous="optional"><con:settings/><con:call id="f46d8609-b118-4137-b418-84691ed0ec7c" name="Request 1"><con:settings/><con:encoding>UTF-8</con:encoding><con:endpoint>http://soa-1.dev1.equabank.loc:8001/soa-infra/services/client/PersonService-v1/PersonService-v1</con:endpoint><con:request><![CDATA[<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:set="http://www.equabank.cz/client/PersonService-v1/setPerson" xmlns:man="http://www.equabank.cz/mandatory" xmlns:per="http://www.equabank.cz/client/PersonService-v1/personPart">
   <soapenv:Header/>
   <soapenv:Body>
      <set:setPersonRequest>
         <man:systemId>?</man:systemId>
         <man:userId>?</man:userId>
         <man:processId>?</man:processId>
         <man:messageId>?</man:messageId>
         <!--Optional:-->
         <man:wflProcessId>?</man:wflProcessId>
         <set:requestBody>
            <!--1 or more repetitions:-->
            <per:person>
               <!--Optional:-->
               <per:instPtKey>?</per:instPtKey>
               <!--Optional:-->
               <per:instPtFlag>?</per:instPtFlag>
               <!--Optional:-->
               <per:addrFlag>?</per:addrFlag>
               <!--Optional:-->
               <per:emailFlag>?</per:emailFlag>
               <!--Optional:-->
               <per:idCardFlag>?</per:idCardFlag>
               <!--Optional:-->
               <per:phoneFlag>?</per:phoneFlag>
               <!--Optional:-->
               <per:naceFlag>?</per:naceFlag>
               <!--Optional:-->
               <per:uniPtKey>?</per:uniPtKey>
               <!--Optional:-->
               <per:firstName>?</per:firstName>
               <!--Optional:-->
               <per:lastName>?</per:lastName>
               <!--Optional:-->
               <per:busTpId lov="PT_TP">?</per:busTpId>
               <!--Optional:-->
               <per:cntryId lov="CNTRY">?</per:cntryId>
               <!--Optional:-->
               <per:busSectorId lov="BUS_SECTOR">?</per:busSectorId>
               <!--Optional:-->
               <per:occId lov="OCC">?</per:occId>
               <!--Optional:-->
               <per:stayTpId lov="STAY_TP">?</per:stayTpId>
               <!--Optional:-->
               <per:legalFormId lov="LEGAL_FORM">?</per:legalFormId>
               <!--Optional:-->
               <per:genderId lov="GENDER">?</per:genderId>
               <!--Optional:-->
               <per:marStatId lov="MAR_STAT">?</per:marStatId>
               <!--Optional:-->
               <per:housingStatId lov="HOUSING_STAT">?</per:housingStatId>
               <!--Optional:-->
               <per:eduStatId lov="EDU_STAT">?</per:eduStatId>
               <!--Optional:-->
               <per:birthCntryId lov="CNTRY">?</per:birthCntryId>
               <!--Optional:-->
               <per:birthPlace>?</per:birthPlace>
               <!--Optional:-->
               <per:rcNumber>?</per:rcNumber>
               <!--Optional:-->
               <per:icoNumber>?</per:icoNumber>
               <!--Optional:-->
               <per:dicNumber>?</per:dicNumber>
               <!--Optional:-->
               <per:midName>?</per:midName>
               <!--Optional:-->
               <per:birthName>?</per:birthName>
               <!--Optional:-->
               <per:birthDate>?</per:birthDate>
               <!--Optional:-->
               <per:deathDate>?</per:deathDate>
               <!--Optional:-->
               <per:titlBf>?</per:titlBf>
               <!--Optional:-->
               <per:titlAf>?</per:titlAf>
               <!--Optional:-->
               <per:salutation>?</per:salutation>
               <!--Optional:-->
               <per:busName>?</per:busName>
               <!--Optional:-->
               <per:shortName>?</per:shortName>
               <!--Optional:-->
               <per:startDate>?</per:startDate>
               <!--Optional:-->
               <per:residentFlag>?</per:residentFlag>
               <!--Optional:-->
               <per:dphRegnFlag>?</per:dphRegnFlag>
               <!--Optional:-->
               <per:stats101Flag>?</per:stats101Flag>
               <!--Optional:-->
               <per:taxDomicile lov="CNTRY">?</per:taxDomicile>
               <!--Optional:-->
               <per:TIN>?</per:TIN>
               <!--Optional:-->
               <per:PEP>?</per:PEP>
               <!--Optional:-->
               <per:cbId>?</per:cbId>
               <!--Optional:-->
               <per:behSc>?</per:behSc>
               <!--Optional:-->
               <per:behScGrade>?</per:behScGrade>
               <!--Optional:-->
               <per:empNum>?</per:empNum>
               <!--Optional:-->
               <per:cribisRating>?</per:cribisRating>
               <!--Optional:-->
               <per:inscyRec>?</per:inscyRec>
               <!--Optional:-->
               <per:dbtrDbRec>?</per:dbtrDbRec>
               <!--Optional:-->
               <per:insurDebtRec>?</per:insurDebtRec>
               <!--Optional:-->
               <per:compRegRec>?</per:compRegRec>
               <!--Optional:-->
               <per:finStmRec>?</per:finStmRec>
               <!--Optional:-->
               <per:interruptRec>?</per:interruptRec>
               <!--Optional:-->
               <per:delFlag>?</per:delFlag>
               <!--Optional:-->
               <per:srcId srcSystemId="?">?</per:srcId>
               <!--Optional:-->
               <per:emailContacts>
                  <!--Zero or more repetitions:-->
                  <per:email>
                     <per:email>?</per:email>
                     <!--Optional:-->
                     <per:emailPurpId lov="EMAIL_PURP">?</per:emailPurpId>
                     <!--Optional:-->
                     <per:emailDescr>?</per:emailDescr>
                     <per:emailSrcId>?</per:emailSrcId>
                     <per:emailDelFlag>?</per:emailDelFlag>
                  </per:email>
               </per:emailContacts>
               <!--Optional:-->
               <per:addressContacts>
                  <!--Zero or more repetitions:-->
                  <per:address>
                     <!--Optional:-->
                     <per:addrPurpId lov="ADDR_PURP">?</per:addrPurpId>
                     <!--Optional:-->
                     <per:addrCntryId lov="CNTRY">?</per:addrCntryId>
                     <!--Optional:-->
                     <per:addrStreetName>?</per:addrStreetName>
                     <!--Optional:-->
                     <per:addrStreetNum>?</per:addrStreetNum>
                     <!--Optional:-->
                     <per:addrCity>?</per:addrCity>
                     <!--Optional:-->
                     <per:addrZip>?</per:addrZip>
                     <!--Optional:-->
                     <per:addrValidDate>?</per:addrValidDate>
                     <!--Optional:-->
                     <per:addrVerifiedFlag>?</per:addrVerifiedFlag>
                     <!--Optional:-->
                     <per:addrSrcId>?</per:addrSrcId>
                     <!--Optional:-->
                     <per:addrDelFlag>?</per:addrDelFlag>
                  </per:address>
               </per:addressContacts>
               <!--Optional:-->
               <per:phoneContacts>
                  <!--Zero or more repetitions:-->
                  <per:phone>
                     <!--Optional:-->
                     <per:phoneTpId lov="PHONE_TP">?</per:phoneTpId>
                     <!--Optional:-->
                     <per:phonePurpId lov="PHONE_PURP">?</per:phonePurpId>
                     <!--Optional:-->
                     <per:phoneNum>?</per:phoneNum>
                     <!--Optional:-->
                     <per:phoneCntryId lov="CNTRY">?</per:phoneCntryId>
                     <!--Optional:-->
                     <per:phoneBlockFlag>?</per:phoneBlockFlag>
                     <!--Optional:-->
                     <per:phoneSrcId>?</per:phoneSrcId>
                     <!--Optional:-->
                     <per:phoneDelFlag>?</per:phoneDelFlag>
                  </per:phone>
               </per:phoneContacts>
               <!--Optional:-->
               <per:idCards>
                  <!--Zero or more repetitions:-->
                  <per:idCard>
                     <!--Optional:-->
                     <per:idCardTpId lov="ID_CARD_TP">?</per:idCardTpId>
                     <!--Optional:-->
                     <per:idCardCntryId lov="CNTRY">?</per:idCardCntryId>
                     <!--Optional:-->
                     <per:idCardIssueDate>?</per:idCardIssueDate>
                     <!--Optional:-->
                     <per:idCardExprDate>?</per:idCardExprDate>
                     <!--Optional:-->
                     <per:idCardId>?</per:idCardId>
                     <!--Optional:-->
                     <per:idCardIssuer>?</per:idCardIssuer>
                     <!--Optional:-->
                     <per:idCardSrcId>?</per:idCardSrcId>
                     <!--Optional:-->
                     <per:idCardDelFlag>?</per:idCardDelFlag>
                  </per:idCard>
               </per:idCards>
               <!--Optional:-->
               <per:naces>
                  <!--Zero or more repetitions:-->
                  <per:nace>
                     <!--Optional:-->
                     <per:naceCodeId>?</per:naceCodeId>
                     <!--Optional:-->
                     <per:naceDescr>?</per:naceDescr>
                     <!--Optional:-->
                     <per:naceMainFlag>?</per:naceMainFlag>
                     <!--Optional:-->
                     <per:naceSrcId>?</per:naceSrcId>
                     <!--Optional:-->
                     <per:naceSectorCodeId>?</per:naceSectorCodeId>
                     <!--Optional:-->
                     <per:naceSectorDescr>?</per:naceSectorDescr>
                     <!--Optional:-->
                     <per:naceDelFlag>?</per:naceDelFlag>
                  </per:nace>
               </per:naces>
               <!--Optional:-->
               <per:frozenFlag>?</per:frozenFlag>
               <!--Optional:-->
               <per:cif>?</per:cif>
            </per:person>
            <!--Optional:-->
            <set:waitForUnification>false</set:waitForUnification>
         </set:requestBody>
      </set:setPersonRequest>
   </soapenv:Body>
</soapenv:Envelope>]]></con:request><con:credentials><con:authType>No Authorization</con:authType></con:credentials><con:jmsConfig JMSDeliveryMode="PERSISTENT"/><con:jmsPropertyConfig/><con:wsaConfig mustUnderstand="NONE" version="200508" action="http://www.equabank.cz/client/PersonService-v1/setPerson"/><con:wsrmConfig version="1.2"/></con:call></con:operation></con:interface><con:properties/><con:wssContainer/><con:oAuth2ProfileContainer/><con:oAuth1ProfileContainer/></con:soapui-project>