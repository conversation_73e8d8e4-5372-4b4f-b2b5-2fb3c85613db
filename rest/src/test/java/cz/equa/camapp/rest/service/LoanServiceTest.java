package cz.equa.camapp.rest.service;

import cz.equa.camapp.rest.model.ResultCalculateOverdraftParamDTO;
import cz.equa.camapp.service.ServiceException;
import org.junit.BeforeClass;
import org.junit.Test;
import org.mockserver.model.HttpRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.TestPropertySource;

import java.io.File;
import java.math.BigDecimal;
import java.util.UUID;

import static org.junit.Assert.*;
import static org.mockserver.integration.ClientAndServer.startClientAndServer;
import static org.mockserver.mock.OpenAPIExpectation.openAPIExpectation;
import static org.mockserver.model.HttpResponse.response;

@TestPropertySource(locations = "classpath:application-test.properties")
public class LoanServiceTest extends AbstractServiceTest {

    @Autowired
    private LoanService loanService;

    @BeforeClass
    public static void initServer() {
        mockServer = startClientAndServer(9090);
        assertTrue(mockServer.hasStarted());

        File operationFile = new File("src/main/resources/rest/bai-loan-v1.yaml");
        mockServer.upsert(
                openAPIExpectation(operationFile.toURI().toString()));
        mockServer.upsert(mockServer
                .when(new HttpRequest().withMethod("GET")
                        .withPath("/rbapl/bai/loan/v1/parties/********/loans/calculateOverdraftParam")
                        .withQueryStringParameter("ovdCurrency", "CZK")
                        .withQueryStringParameter("ovdProduct", "PDU")
                        .withQueryStringParameter("ovdLimit", "5000")
                        .withQueryStringParameter("ovdInterestProduct", "PDPLUS")
                        .withQueryStringParameter("numberPart2", "**********")
                        .withQueryStringParameter("bankCode", "5500"))
                .respond(response().withHeader("content-type", "application/json")
                        .withBody("{\"loan\":{\"acceptanceFee\":200,\"bankAccountMonthlyFee\":0," +
                                "\"interestFreeAmount\":1000,\"interestProduct\":\"PDPLUS\",\"interestRate\":21.9," +
                                "\"monthlyUsageFee\":90,\"noticeFee\":499,\"payableTotalAmount\":7252.6," +
                                "\"penaltyRate\":8,\"rpsn\":57.53}}")));
    }
    
//     pokud spadne, pak je nutne zkontrolovat, že je v yaml souboru response mapovaná jako #ref objektu ResultCalculateOverdraftParamDTO a ne jako anonymní object. Takto:
//        schema:
//        $ref: "#/components/schemas/ResultCalculateOverdraftParam"
    @Test
    public void calculateOverdraftParam() throws ServiceException {
        ResultCalculateOverdraftParamDTO resp = loanService.calculateOverdraftParam("********", "CZK", "PDU", 5000,
                "PDPLUS", "**********", "5500", "SYS", "", UUID.randomUUID().toString());
        assertNotNull(resp);
        assertEquals("PDPLUS", resp.getLoan().getInterestProduct());
        assertEquals(new BigDecimal(1000), resp.getLoan().getInterestFreeAmount());
    }

}