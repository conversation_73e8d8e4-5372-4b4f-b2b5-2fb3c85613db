package cz.equa.camapp.rest.service;

import cz.equa.camapp.service.ServiceException;
import org.junit.BeforeClass;
import org.junit.Test;
import org.mockserver.model.HttpRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.TestPropertySource;

import java.io.File;
import java.util.UUID;

import static org.junit.Assert.assertTrue;
import static org.mockserver.integration.ClientAndServer.startClientAndServer;
import static org.mockserver.mock.OpenAPIExpectation.openAPIExpectation;
import static org.mockserver.model.HttpResponse.response;

@TestPropertySource(locations = "classpath:application-test.properties")
public class CashloanApplicationServiceTest extends AbstractServiceTest {

	@Autowired
	private CashloanApplicationService cashloanApplicationService;

    @BeforeClass
	public static void initServer() {
        mockServer = startClientAndServer(9090);
		assertTrue(mockServer.hasStarted());
        
        File cusApplication = new File("src/main/resources/rest/cus-application-v1.yaml");
        mockServer.upsert(
				openAPIExpectation(cusApplication.toURI().toString()));
        mockServer.upsert( mockServer
            .when(new HttpRequest().withMethod("PUT").withPath("/rbepi/cus/application/v1/applications/123/client-action"))
            .respond(response()));
        mockServer.upsert( mockServer
            .when(new HttpRequest().withMethod("PUT").withPath("/rbepi/cus/application/v1/applications/123/interest-change"))
            .respond(response()));
    }
    
	@Test(expected = Test.None.class /* no exception expected */)
	public void setClientAction() throws ServiceException {
		cashloanApplicationService.setClientAction(123L, "PENNY_VERIF_PROMISE", UUID.randomUUID().toString());
	}

	@Test(expected = Test.None.class /* no exception expected */)
	public void setApplIntrsChng() throws ServiceException {
		cashloanApplicationService.setApplIntrsChng(123L, "busApplId", UUID.randomUUID().toString());
	}
}
