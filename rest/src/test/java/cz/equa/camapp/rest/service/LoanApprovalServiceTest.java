package cz.equa.camapp.rest.service;

import cz.equa.camapp.service.ServiceException;
import cz.rb.bai.loan.approval.model.NotificationType;
import org.junit.BeforeClass;
import org.junit.Test;
import org.mockserver.model.HttpRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.TestPropertySource;

import java.io.File;

import static org.junit.Assert.assertTrue;
import static org.mockserver.integration.ClientAndServer.startClientAndServer;
import static org.mockserver.mock.OpenAPIExpectation.openAPIExpectation;
import static org.mockserver.model.HttpResponse.response;

@TestPropertySource(locations = "classpath:application-test.properties")
public class LoanApprovalServiceTest extends AbstractServiceTest {

    @Autowired
    private LoanApprovalService loanApprovalService;

    @BeforeClass
    public static void initServer() {
        mockServer = startClientAndServer(9090);
        assertTrue(mockServer.hasStarted());

        File operationFile = new File("src/main/resources/rest/bai-loan-approval-v1.yaml");
        mockServer.upsert(
                openAPIExpectation(operationFile.toURI().toString()));
        mockServer.upsert(mockServer
                .when(new HttpRequest().withMethod("POST")
                        .withPath("/rbapl/bai/loan-approval/v1/applications/26798282/notifications"))
                .respond(response().withStatusCode(400)
                        .withHeader("content-type", "application/json")
                        .withBody("{\n" +
                                "\t\"requestId\": \"9b0abcac-a686-4e43-88cf-4c2674e7c4a2\",\n" +
                                "\t\"correlationId\": \"2d8b7ce8-5d16-4bf3-baba-a7959061da6f\",\n" +
                                "\t\"status\": 400,\n" +
                                "\t\"reasons\": [\n" +
                                "\t\t{\n" +
                                "\t\t\t\"code\": \"7\",\n" +
                                "\t\t\t\"severity\": \"ERROR\",\n" +
                                "\t\t\t\"message\": \"HTTPS-PRODUCER-CUT://clncut1at2.rb.cz:443/underwriting/ManualApprovingService.asmx?throwExceptionOnFailure=false: cz.rb.msa.processor.es.notifyManualApproving.ES_RES_NotifyManualApproving$1$1: Exception 7 in A_CUT_ManualApproving-100: ERROR: data sync for appl key 853599067720did not succeed, TALEND file in use.\",\n" +
                                "\t\t\t\"path\": \"[application endpoint: http://bai-loan-approval-v1.apps.ot.rb.cz/bai/loan-approval/v1/applications/853599067720/notifications] [backend POST A_CUT_ManualApproving-100 endpoint error: HTTPS-PRODUCER-CUT://clncut1at2.rb.cz:443/underwriting/ManualApprovingService.asmx?throwExceptionOnFailure=false]\",\n" +
                                "\t\t\t\"moreInfo\": \"<?xml version=\\\"1.0\\\" encoding=\\\"utf-8\\\"?><soap:Envelope xmlns:soap=\\\"http://schemas.xmlsoap.org/soap/envelope/\\\" xmlns:xsi=\\\"http://www.w3.org/2001/XMLSchema-instance\\\" xmlns:xsd=\\\"http://www.w3.org/2001/XMLSchema\\\"><soap:Body><notifyManualApprovingResponse xmlns=\\\"http://risk.equabank.cz\\\"><notifyManualApprovingResult><resultCode>8</resultCode><errorCode>7</errorCode><errorDescription>ERROR: data sync for appl key 853599067720did not succeed, TALEND file in use.</errorDescription></notifyManualApprovingResult></notifyManualApprovingResponse></soap:Body></soap:Envelope>\"\n" +
                                "\t\t}\n" +
                                "\t]\n" +
                                "}")));
        mockServer.upsert(mockServer
                .when(new HttpRequest().withMethod("POST")
                        .withPath("/rbapl/bai/loan-approval/v1/applications/1/notifications"))
                .respond(response().withStatusCode(200)));
    }

    @Test(expected = Test.None.class)
    public void testNotifyManualApprovingOK() throws ServiceException {
        loanApprovalService.notifyManualApproving(1L, NotificationType.START, "NO_DECLARED_PURPOSE", null, true, "483cb48a-c30a-422b-95e6-9312a6ac9c47");
    }

    @Test(expected = ServiceException.class)
    public void testNotifyManualApprovingTimeout() throws ServiceException {
        loanApprovalService.notifyManualApproving(26798282L, NotificationType.START, "NO_DECLARED_PURPOSE", null, true, "483cb48a-c30a-422b-95e6-9312a6ac9c47");
    }
}