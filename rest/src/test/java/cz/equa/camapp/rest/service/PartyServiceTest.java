package cz.equa.camapp.rest.service;

import cz.equa.camapp.model.account_number.AccountNumberDto;
import cz.equa.camapp.model.party.PartyCategoryDTO;
import cz.equa.camapp.model.update_party.additional_info.AdditionalInfoDTO;
import cz.equa.camapp.model.update_party.basic_info.BasicInfoDTO;
import cz.equa.camapp.rest.model.cus.party.DocumentsDto;
import cz.equa.camapp.rest.model.cus.party.GetContactsResultDto;
import cz.equa.camapp.rest.model.cus.party.GetDetailResultDto;
import cz.equa.camapp.service.ServiceException;
import cz.equa.camapp.service.applicationservice.model.NewPersonDTO;
import cz.equa.camapp.service.partyservice.model.find_party.FindPartyResponseDTO;
import cz.rb.cus.party.handler.ApiException;
import org.junit.BeforeClass;
import org.mockserver.verify.VerificationTimes;
import org.junit.Test;
import org.mockserver.model.HttpRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.TestPropertySource;

import java.io.File;
import java.util.List;
import java.util.UUID;

import static cz.equa.camapp.rest.Utils.getPodamFactoryWithLovs;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockserver.integration.ClientAndServer.startClientAndServer;
import static org.mockserver.mock.OpenAPIExpectation.openAPIExpectation;
import static org.mockserver.model.HttpResponse.response;

@TestPropertySource(locations = "classpath:application-test.properties")
public class PartyServiceTest extends AbstractServiceTest {

	@Autowired
	private PartyService partyService;

    @BeforeClass
	public static void initServer() {
        mockServer = startClientAndServer(9090);
		assertTrue(mockServer.hasStarted());

        File partyFile = new File("src/main/resources/rest/cus-party-v1.yaml");
        mockServer.upsert(
				openAPIExpectation(partyFile.toURI().toString()));
        mockServer.upsert( mockServer
            .when(new HttpRequest().withMethod("GET").withPath("/rbepi/cus/party/v1/parties/partyId"))
            .respond(response().withHeader("content-type", "application/json")
                                         .withBody("{}")));
		mockServer.upsert( mockServer
				.when(new HttpRequest().withMethod("GET").withPath("/rbepi/cus/party/v1/parties/partyId123/documents"))
				.respond(response().withHeader("content-type", "application/json")
						.withBody("[ {\"documentId\": \"documentId\", \"documentType\": \"documentType\"}]")));

		mockServer.upsert( mockServer
				.when(new HttpRequest().withMethod("GET").withPath("/rbepi/cus/party/v1/parties/partyId/contacts"))
				.respond(response().withHeader("content-type", "application/json")
						.withBody("{}")));

		mockServer.upsert( mockServer
				.when(new HttpRequest().withMethod("PUT").withPath("/rbepi/cus/party/v1/parties/partyId"))
				.respond(response().withHeader("content-type", "application/json")
						.withBody("{}")));

        mockServer.upsert( mockServer
                .when(new HttpRequest().withMethod("PUT").withPath("/rbepi/cus/party/v1/parties/partyId123/categories"))
                .respond(response().withHeader("content-type", "application/json")
                        .withBody("{}")));
		mockServer.upsert(mockServer
				.when(new HttpRequest().withMethod("PUT").withPath("/rbepi/cus/party/v1/parties/123/init-channels"))
				.respond(response().withHeader("content-type", "application/json")
						.withBody("{}")));
    }

	@Test
	public void findPerson() {
		NewPersonDTO newPersonDTO = getPodamFactoryWithLovs().manufacturePojo(NewPersonDTO.class);
		FindPartyResponseDTO response = partyService.findPerson(newPersonDTO, UUID.randomUUID().toString());
		assertNotNull(response);
	}

	@Test
	public void getDetail() throws ServiceException {
		GetDetailResultDto response = partyService.getDetail("partyId", UUID.randomUUID().toString());
		assertNotNull(response);
	}
	@Test
	public void assignCategory() {
		PartyCategoryDTO partyCategoryRequestDto = getPodamFactoryWithLovs().manufacturePojo(PartyCategoryDTO.class);
        assertDoesNotThrow(() -> partyService.assignCategory("partyId123", partyCategoryRequestDto, UUID.randomUUID().toString()));

	}

	@Test
	public void update() {
		BasicInfoDTO bascInfo = getPodamFactoryWithLovs().manufacturePojo(BasicInfoDTO.class);
		AdditionalInfoDTO additionalInfo = getPodamFactoryWithLovs().manufacturePojo(AdditionalInfoDTO.class);
        assertDoesNotThrow(() -> partyService.update("partyId",bascInfo, additionalInfo, UUID.randomUUID().toString()));
	}

    @Test
    public void getDocuments() throws ServiceException, ApiException {
        List<DocumentsDto> response = partyService.getDocuments("partyId123", UUID.randomUUID().toString());
        assertNotNull(response);
    }

    @Test
    public void getContacts() throws ServiceException {
        GetContactsResultDto response = partyService.getContacts(
                "partyId", "channel",UUID.randomUUID().toString()
        );
        assertNotNull(response);
    }

	@Test
	public void initChannel() throws ServiceException {
		partyService.initChannel(new AccountNumberDto(), "123", null, null, UUID.randomUUID().toString());

		mockServer.verify(new HttpRequest()
						.withMethod("PUT")
						.withPath("/rbepi/cus/party/v1/parties/123/init-channels"),
				VerificationTimes.once());
	}
}
