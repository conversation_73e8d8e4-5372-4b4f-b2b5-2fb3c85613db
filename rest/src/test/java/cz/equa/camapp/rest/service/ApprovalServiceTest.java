package cz.equa.camapp.rest.service;

import cz.equa.camapp.lovs.LovDsnTp;
import cz.equa.camapp.service.ServiceException;
import org.junit.*;
import org.mockito.Mock;
import org.mockserver.model.HttpRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestTemplate;

import java.io.File;
import java.util.UUID;

import static cz.equa.camapp.rest.service.ApprovalService.FIREWALL_ERROR_MESSAGE;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import static org.mockserver.integration.ClientAndServer.startClientAndServer;
import static org.mockserver.mock.OpenAPIExpectation.openAPIExpectation;
import static org.mockserver.model.HttpResponse.response;

@TestPropertySource(locations = "classpath:application-test.properties")
public class ApprovalServiceTest extends AbstractServiceTest {

    @Autowired
    private ApprovalService approvalService;

    @Mock
    private RestTemplate restTemplate;

    @BeforeClass
    public static void initServer() {
        mockServer = startClientAndServer(9090);
        assertTrue(mockServer.hasStarted());

        File approvalFile = new File("src/main/resources/rest/las-approval-v1.yaml");
        mockServer.upsert(
                openAPIExpectation(approvalFile.toURI().toString()));
        mockServer.upsert(mockServer
                .when(new HttpRequest().withMethod("PUT").withPath("/rbapl/las/approval/v1/cashloans/call-approval"))
                .respond(response().withHeader("content-type", "application/json")
                        .withBody("{}")));
    }

    @Before
    public void setUp() {
        ReflectionTestUtils.setField(approvalService, "restTemplate", restTemplate);
    }

    @Test
    public void callApproval() {
        try {
            LovDsnTp approval = approvalService.callApproval(123L, 1, "abv", UUID.randomUUID().toString());
            assertNotNull(approval);
        } catch (ServiceException e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    @Ignore
    public void callMortgageApproval() {
        try {
            LovDsnTp approval = approvalService.callMortgageApproval(123L, 123L, "RML_GOFR", UUID.randomUUID().toString());
            assertNotNull(approval);
        } catch (ServiceException e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    public void notifyMerchant() throws ServiceException {
        ResponseEntity<String> response = mock(ResponseEntity.class);
        when(response.getStatusCode()).thenReturn(HttpStatusCode.valueOf(200));
        when(response.getBody()).thenReturn("TEST");
        when(restTemplate.getForEntity(anyString(), eq(String.class))).thenReturn(response);
        approvalService.notifyMerchant("https://www.rb.cz", "1");

        when(response.getBody()).thenReturn("McAfee Web Gateway");
        Exception exception = Assert.assertThrows(ServiceException.class, () -> {
            approvalService.notifyMerchant("https://www.rb.cz", "2");
        });
        assertEquals(FIREWALL_ERROR_MESSAGE, exception.getMessage());
    }
}