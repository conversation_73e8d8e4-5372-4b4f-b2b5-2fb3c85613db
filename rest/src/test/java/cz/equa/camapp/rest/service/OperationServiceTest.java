package cz.equa.camapp.rest.service;

import cz.equa.camapp.lovs.LovApplTp;
import cz.equa.camapp.lovs.LovBusProdSubTp;
import cz.equa.camapp.lovs.LovIncTp;
import cz.equa.camapp.rest.model.LovQuery;
import cz.equa.camapp.rest.model.RpsnCalculationResponseDTO;
import cz.equa.camapp.service.ServiceException;
import cz.equa.camapp.service.applicationservice.model.LoanApplicationDTO;
import cz.equa.camapp.service.product_service.LoanApplicationSrcSystem;
import cz.equa.camapp.service.product_service.PersonCLInfoDTO;
import cz.equa.camapp.service.product_service.ProductDTO;
import cz.rb.las.operation.model.SetApplicationEventRequest;
import cz.rb.las.operation.model.SetOdsApplicationStateRequest;
import org.junit.BeforeClass;
import org.junit.Test;
import org.mockserver.model.HttpRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.TestPropertySource;

import java.io.File;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

import static cz.equa.camapp.rest.Utils.getPodamFactoryWithLovs;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockserver.integration.ClientAndServer.startClientAndServer;
import static org.mockserver.mock.OpenAPIExpectation.openAPIExpectation;
import static org.mockserver.model.HttpResponse.response;

@TestPropertySource(locations = "classpath:application-test.properties")
public class OperationServiceTest extends AbstractServiceTest {

	@Autowired
	private OperationService operationService;

    @BeforeClass
	public static void initServer() {
        mockServer = startClientAndServer(9090);
		assertTrue(mockServer.hasStarted());
        
        File operationFile = new File("src/main/resources/rest/las-operation-v1.yaml");
        mockServer.upsert(
				openAPIExpectation(operationFile.toURI().toString()));
        mockServer.upsert( mockServer
            .when(new HttpRequest().withMethod("GET")
                    .withPath("/rbapl/las/operation/v1/persons/12345/appl-user-rights")
                    .withQueryStringParameter("personIdSource", "personIdSource")
                    .withQueryStringParameter("productIdValue", LovBusProdSubTp.RCL_STANDARD.getCode())
                    .withQueryStringParameter("productIdType", "SUBTYPE")
                    .withQueryStringParameter("applKey", "1")
                    .withQueryStringParameter("ccyId", "CZK"))
            .respond(response().withHeader("content-type", "application/json")
                                         .withBody("{\"result\": \"true\"}")));
        mockServer.upsert( mockServer
            .when(new HttpRequest().withMethod("GET")
                    .withPath("/rbapl/las/operation/v1/lovs/APPL_TP"))
            .respond(response().withHeader("content-type", "application/json")
                                         .withBody("{}")));
        mockServer.upsert( mockServer
            .when(new HttpRequest().withMethod("GET")
                    .withPath("/rbapl/las/operation/v1/products/scIntSourceSystem")
                    .withQueryStringParameter("srcSystemId", "scIntSourceSystemId"))
            .respond(response().withHeader("content-type", "application/json")
                                         .withBody("{}")));
        mockServer.upsert( mockServer
            .when(new HttpRequest().withMethod("PUT")
                    .withPath("/rbapl/las/operation/v1/applications/1/status"))
            .respond(response().withHeader("content-type", "application/json")
                                         .withBody("{}")));
        mockServer.upsert( mockServer
            .when(new HttpRequest().withMethod("GET")
                    .withPath("/rbapl/las/operation/v1/persons/id/info")
                    .withQueryStringParameter("personIdType", "PHONENUM")
                    .withQueryStringParameter("sourceIdSystem", "siId")
                    .withQueryStringParameter("applStateSYS", "false"))
            .respond(response().withHeader("content-type", "application/json")
                                         .withBody("[{}]")));
        mockServer.upsert( mockServer
            .when(new HttpRequest().withMethod("GET")
                    .withPath("/rbapl/las/operation/v1/applications/1/status-history")
                    .withQueryStringParameter("applicationIdType", "APPLKEY"))
            .respond(response().withHeader("content-type", "application/json")
                                         .withBody("[{}]")));
        mockServer.upsert( mockServer
            .when(new HttpRequest().withMethod("PUT")
                    .withPath("/rbapl/las/operation/v1/applications/1/client-action"))
            .respond(response().withHeader("content-type", "application/json")
                                         .withBody("{}")));
        mockServer.upsert( mockServer
            .when(new HttpRequest().withMethod("POST")
                    .withPath("/rbapl/las/operation/v1/products/rcc/calculations"))
            .respond(response().withHeader("content-type", "application/json")
                                         .withBody("{}")));

		mockServer.upsert(mockServer
				.when(new HttpRequest().withMethod("GET")
						.withPath("/rbapl/las/operation/v1/lovs/INC_TP"))
				.respond(response().withHeader("content-type", "application/json")
						.withBody("""
								{
								    "item": [
								        {
								            "id": "OWNBS",
								            "lang": "CZ",
								            "descr": "Příjem z podnikání",
								            "lovDetail": [
								                {"name": "INC_CATG_ID", "value": "BUSIN"},
								                {"name": "ORD_NUM", "value": "1"},
								                {"name": "VISIBILITY_FLAG", "value": "1"}
								            ]
								        },
								        {
								            "id": "EMPLT",
								            "lang": "CZ",
								            "descr": "Příjem ze zaměstnání",
								            "lovDetail": [
								                {"name": "INC_CATG_ID", "value": "EMPL"},
								                {"name": "ORD_NUM", "value": "1"},
								                {"name": "VISIBILITY_FLAG", "value": "1"}
								            ]
								        },
								        {
								            "id": "ALIM",
								            "lang": "CZ",
								            "descr": "Výživné (Alimony)",
								            "lovDetail": [
								                {"name": "INC_CATG_ID", "value": "OTH"},
								                {"name": "ORD_NUM", "value": "11"},
								                {"name": "VISIBILITY_FLAG", "value": "1"}
								            ]
								        }
								    ],
								    "id": "INC_TP"
								}
								""")));
    }
    
	@Test
	public void getPersonInfo() throws ServiceException {
		List<PersonCLInfoDTO> personInfo = operationService.getPersonInfo("PHONENUM", "id", "siId", false, UUID.randomUUID().toString());
		assertNotNull(personInfo);
	}

	@Test
	public void getLov() throws ServiceException {
		LovQuery<LovApplTp> query = new LovQuery<>(LovApplTp.class);
		query.setLanguage(LovQuery.EsbLanguage.CZ);
		query.setDetail("abc", "efg");

		List<LovApplTp> lov = operationService.getLov(query, UUID.randomUUID().toString());
		assertNotNull(lov);
	}

	@Test
	public void setClientAction() throws ServiceException {
		SetApplicationEventRequest request = getPodamFactoryWithLovs().manufacturePojo(SetApplicationEventRequest.class);
		operationService.setClientAction(1L, request, UUID.randomUUID().toString());
	}

	@Test
	public void checkApplUserRights() throws ServiceException {
		LoanApplicationDTO application = getPodamFactoryWithLovs().manufacturePojo(LoanApplicationDTO.class);
		application.setBusProdSubTp(LovBusProdSubTp.RCL_STANDARD.getCode());
		Boolean checkResponse = operationService.checkApplUserRights("12345", "personIdSource", 1L, application, UUID.randomUUID().toString());
		assertNotNull(checkResponse);
	}

	@Test
	public void setApplStatus() throws ServiceException {
		SetOdsApplicationStateRequest request = getPodamFactoryWithLovs().manufacturePojo(SetOdsApplicationStateRequest.class);
        assertDoesNotThrow(() -> operationService.setApplStatus(request, 1L, UUID.randomUUID().toString()));
	}

	@Test
	public void getProductDetail() throws ServiceException {
		LoanApplicationSrcSystem sourcesSystem = new LoanApplicationSrcSystem("scIntSourceSystem", "scIntSourceSystemId");
		List<LoanApplicationSrcSystem> sourcesSystems = Collections.singletonList(sourcesSystem);
		List<ProductDTO> productDetail = operationService.getProductDetail(sourcesSystems, UUID.randomUUID().toString());
		assertNotNull(productDetail);
	}
    
    @Test
	public void getCalcValues() throws ServiceException {
		RpsnCalculationResponseDTO calcValues = operationService.getRccCalculation(new BigDecimal("5000"), new BigDecimal("10"), new BigDecimal("5"), UUID.randomUUID().toString());
		assertNotNull(calcValues);
	}

	@Test
	public void getLovIncTp() throws ServiceException {
		LovQuery<LovIncTp> query = new LovQuery<>(LovIncTp.class);
		query.setLanguage(LovQuery.EsbLanguage.CZ);

		List<LovIncTp> lovIncomes = operationService.getLov(query, UUID.randomUUID().toString());

		assertNotNull("LOV list should not be null", lovIncomes);
		assertTrue("LOV list should not be empty", lovIncomes.size() > 0);

		LovIncTp ownbs = lovIncomes.stream()
				.filter(lov -> "OWNBS".equals(lov.getCode()))
				.findFirst()
				.orElse(null);

		assertNotNull("OWNBS income type should be found", ownbs);
		assertEquals("OWNBS", ownbs.getCode());
		assertEquals("Příjem z podnikání", ownbs.getLabel());
		assertEquals("1", ownbs.getLovDetail().get("ORD_NUM"));
		assertEquals("BUSIN", ownbs.getLovDetail().get("INC_CATG_ID"));
		assertEquals("1", ownbs.getLovDetail().get("VISIBILITY_FLAG"));

		LovIncTp emplt = lovIncomes.stream()
				.filter(lov -> "EMPLT".equals(lov.getCode()))
				.findFirst()
				.orElse(null);

		assertNotNull("EMPLT income type should be found", emplt);
		assertEquals("EMPLT", emplt.getCode());
		assertEquals("Příjem ze zaměstnání", emplt.getLabel());
		assertEquals("1", emplt.getLovDetail().get("ORD_NUM"));
		assertEquals("EMPL", emplt.getLovDetail().get("INC_CATG_ID"));
	}
}
