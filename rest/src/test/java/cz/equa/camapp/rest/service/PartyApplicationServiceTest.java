package cz.equa.camapp.rest.service;

import cz.equa.camapp.rest.model.cus.partyApplication.ApplicationDTO;
import cz.equa.camapp.service.ServiceException;
import cz.rb.cus.party.application.model.CusPartyApplicationV1Update200Response;
import cz.rb.cus.party.application.model.ReqApplicationUpdate;
import cz.rb.cus.party.application.model.UpdateStatusRequest;
import org.junit.BeforeClass;
import org.junit.Test;
import org.mockserver.model.HttpRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.TestPropertySource;

import java.io.File;
import java.util.List;
import java.util.UUID;

import static cz.equa.camapp.rest.Utils.getPodamFactoryWithLovs;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockserver.integration.ClientAndServer.startClientAndServer;
import static org.mockserver.mock.OpenAPIExpectation.openAPIExpectation;
import static org.mockserver.model.HttpResponse.response;

@TestPropertySource(locations = "classpath:application-test.properties")
public class PartyApplicationServiceTest extends AbstractServiceTest {

    @Autowired
    private PartyApplicationService partyApplicationService;

    @BeforeClass
    public static void initServer() {
        mockServer = startClientAndServer(9090);
        assertTrue(mockServer.hasStarted());

        File partyApplicationFile = new File("src/main/resources/rest/cus-party-application-v1.yaml");
        mockServer.upsert(
                openAPIExpectation(partyApplicationFile.toURI().toString()));
        mockServer.upsert(mockServer
                .when(new HttpRequest().withMethod("PUT").withPath("/rbepi/cus/party-application/v1/parties/ab/applications/cd"))
                .respond(response().withHeader("content-type", "application/json")
                        .withBody("{\"resultObject\": {\"partyId\": \"********\",\"siebelApplicationId\": \"some_string_value\"},"
                                + "\"resultInfo\": [{\"code\": \"XYZ8000\",\"severity\": \"ERROR\",\"message\": \"Payment rejected. Missing creditor iban.\","
                                + "\"path\": \"creditorAccount.iban\",\"moreInfo\": \"some_string_value\"}]}")));
        mockServer.upsert(mockServer
                .when(new HttpRequest().withMethod("PUT").withPath("/rbepi/cus/party-application/v1/parties/ab/applications/cd"))
                .respond(response().withHeader("content-type", "application/json")
                        .withBody("{}")));
        mockServer.upsert(mockServer
                .when(new HttpRequest().withMethod("GET").withPath("/cus/party-application/v1/parties/123/applications"))
                .respond(response().withHeader("content-type", "application/json")
                        .withBody("""
                                {
                                	"applications": [
                                		{
                                			"entityId": "123",
                                			"srcRefId": "srcRefId",
                                			"applicationChannel": "applicationChannel",
                                			"submitChannel": "submitChannel",
                                			"applicationStatus": "applicationStatus",
                                			"applicationId": "123",
                                			"terms": {
                                				"createdDate": null,
                                				"expiryDate": null,
                                				"approvalDate": null,
                                			},
                                			"statusDescription": "statusDescription",
                                			"productDetail": {
                                				"mprodCode": "mprodCode",
                                				"productCode": "productCode",
                                				"productCategory": "productCategory",
                                				"productName": "productName",
                                				"productType": "productType"
                                			},
                                			"limits": {
                                				"currencyCode": "currencyCode",
                                				"requestedAmount": 1,
                                				"totalLimitAmount": 2
                                			},
                                			"applicationType": "applicationType",
                                		}
                                	]
                                }""")));
    }

    @Test
    public void updateApplication() throws ServiceException {
//        ReqApplicationUpdate request = getPodamFactoryWithLovs().manufacturePojo(ReqApplicationUpdate.class);
        ReqApplicationUpdate request = new ReqApplicationUpdate();
        CusPartyApplicationV1Update200Response response = partyApplicationService.updateApplication("ab", "cd", request, UUID.randomUUID().toString());
        assertNotNull(response);
    }

    @Test(expected = Test.None.class /* no exception expected */)
    public void updateStatus() throws ServiceException {
        UpdateStatusRequest request = getPodamFactoryWithLovs().manufacturePojo(UpdateStatusRequest.class);
        partyApplicationService.updateStatus("abc", "APPLKEY", request, UUID.randomUUID().toString());
    }

    @Test(expected = Test.None.class /* no exception expected */)
    public void getList() throws ServiceException {
        List<ApplicationDTO> res = partyApplicationService.getList("123", UUID.randomUUID().toString());
        assertNotNull(res);
    }
}