<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2017 (http://www.altova.com) by <PERSON> (Raiffeisenbank a.s.) -->
<xs:schema xmlns="http://rb.cz/cdm/dds/crm/cpa/P0001543/1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="http://rb.cz/cdm/dds/crm/cpa/P0001543/1.0" elementFormDefault="qualified" attributeFormDefault="unqualified" version="1.0.0">
    <xs:element name="P0001543">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="agent" minOccurs="0">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="repre" minOccurs="0">
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="repreName" type="xs:string" minOccurs="0"/>
                                        <xs:element name="repreSurname" type="xs:string" minOccurs="0"/>
                                        <xs:element name="address" minOccurs="0">
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="street" type="xs:string" minOccurs="0"/>
                                                    <xs:element name="number" type="xs:string" minOccurs="0"/>
                                                    <xs:element name="postalCode" type="xs:string" minOccurs="0"/>
                                                    <xs:element name="city" type="xs:string" minOccurs="0"/>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                        <xs:element name="repreReqNo" type="xs:string" minOccurs="0"/>
                                        <xs:element name="reprePhoneNo" type="xs:string" minOccurs="0"/>
                                        <xs:element name="repreEmail" type="xs:string" minOccurs="0"/>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="agentName" type="xs:string" minOccurs="0"/>
                            <xs:element name="agentReqNo" type="xs:string" minOccurs="0"/>
                            <xs:element name="agentPhoneNo" type="xs:string" minOccurs="0"/>
                            <xs:element name="agentEmail" type="xs:string" minOccurs="0"/>
                            <xs:element name="address">
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="street" type="xs:string"/>
                                        <xs:element name="number" type="xs:string"/>
                                        <xs:element name="postalCode" type="xs:string"/>
                                        <xs:element name="city" type="xs:string"/>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="commission" type="xs:decimal" minOccurs="0"/>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element name="role" type="xs:string" minOccurs="0"/>
                <xs:element name="applDetails">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="reportDate" type="xs:date"/>
                            <xs:element name="finaAmt" type="xs:decimal"/>
                            <xs:element name="currency" type="xs:string"/>
                            <xs:element name="rbAccount" type="xs:string"/>
                            <xs:element name="InsurSelFlg" type="xs:string"/>
                            <xs:element name="finaAmtAdjustment" type="xs:decimal" minOccurs="0"/>
                            <xs:element name="dueDate" type="xs:date"/>
                            <xs:element name="fixPeriod" type="xs:integer"/>
                            <xs:element name="totalIRate" type="xs:decimal"/>
                            <xs:element name="toBePaidAmt" type="xs:decimal"/>
                            <xs:element name="costToBePaid" type="xs:decimal"/>
                            <xs:element name="rPSN" type="xs:string"/>
                            <xs:element name="finaIR" type="xs:decimal"/>
                            <xs:element name="savingsContractFeeAmt" type="xs:decimal"/>
                            <xs:element name="loanContractFeeAmt" type="xs:decimal"/>
                            <xs:element name="savingsMaintenanceFeeAmt" type="xs:decimal"/>
                            <xs:element name="savingsStatementFeeAmt" type="xs:decimal"/>
                            <xs:element name="finaLoanLength" type="xs:int"/>
                            <xs:element name="loanMaintenanceFeeAmt" type="xs:decimal"/>
                            <xs:element name="loanStatementFeeAmt" type="xs:decimal"/>
                            <xs:element name="insFeeAmt" type="xs:decimal" minOccurs="0"/>
                            <xs:element name="estimDisbursDate" type="xs:date"/>
                            <xs:element name="firstPaymentDate" type="xs:date"/>
                            <xs:element name="addRSPN" type="xs:string"/>
                            <xs:element name="finaInstCnt" type="xs:int"/>
                            <xs:element name="finaInsAmt" type="xs:decimal" minOccurs="0"/>
                            <xs:element name="deposit" type="xs:decimal"/>
                            <xs:element name="finaInsAmtAdjustment" type="xs:decimal" minOccurs="0"/>
                            <xs:element name="insIRAdd" type="xs:decimal" minOccurs="0"/>
                            <xs:element name="loanPenaltyFeeAmt" type="xs:decimal"/>
                            <xs:element name="rstsBuildingSavingsNumber" type="xs:string"/>
                            <xs:element name="rstsRegistrationLoanNumber" type="xs:string"/>
                            <xs:element name="repaymentSchedule">
                                <xs:complexType>
                                    <xs:sequence>
                                        <xs:element name="mscheduleNoOfRows" type="xs:int"/>
                                        <xs:element name="repaymentScheduleItem" minOccurs="0" maxOccurs="unbounded">
                                            <xs:complexType>
                                                <xs:sequence>
                                                    <xs:element name="mSavings" type="xs:string"/>
                                                    <xs:element name="mPeriod" type="xs:string"/>
                                                    <xs:element name="mItem" type="xs:string"/>
                                                    <xs:element name="mFinaAmt" type="xs:decimal"/>
                                                    <xs:element name="mFinalIR" type="xs:decimal"/>
                                                    <xs:element name="mFinaInsAmt" type="xs:decimal"/>
                                                    <xs:element name="mRepaidInterest" type="xs:decimal"/>
                                                    <xs:element name="mOtherCosts" type="xs:decimal"/>
                                                    <xs:element name="mRepaidPrincipal" type="xs:decimal"/>
                                                    <xs:element name="mOtherCostsExluded" type="xs:decimal"/>
                                                    <xs:element name="mSavingsIncome" type="xs:decimal"/>
                                                    <xs:element name="mRemainingPrincipal" type="xs:decimal"/>
                                                    <xs:element name="mRemainingInterest" type="xs:decimal"/>
                                                </xs:sequence>
                                            </xs:complexType>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element name="party">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="firstName" type="xs:string"/>
                            <xs:element name="middleName" type="xs:string" minOccurs="0"/>
                            <xs:element name="lastName" type="xs:string"/>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
