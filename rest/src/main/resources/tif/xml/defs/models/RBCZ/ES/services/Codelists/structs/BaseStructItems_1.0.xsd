<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2011 sp1 (x64) (http://www.altova.com) by <PERSON><PERSON> (Trask solutions a.s.) -->
<!--
Xml Schema generated by NetLT EAExtensionManager plugin 'XSD Composer ver. 2.1.34 [24.5.2012 15:14' ]

Important!!!

Automatically generated from EA model, don't update manually!!!

Document revision: 1.0.0.3

-->
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/services/codelists/structs/BaseStructItems/1.0"
    version="1.0.0"
    xmlns:basestructitems="http://rb.cz/services/codelists/structs/BaseStructItems/1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="baseStructItems">
        <xs:complexType>
            <xs:sequence>
                <xs:element maxOccurs="unbounded" minOccurs="0" name="baseStructItem">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="1" minOccurs="1"
                                name="code" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Code of item in the codelist</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="1"
                                name="descriptionCode" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Description code of item in the codelist</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
