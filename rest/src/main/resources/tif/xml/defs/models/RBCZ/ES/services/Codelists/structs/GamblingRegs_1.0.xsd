<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/services/codelists/structs/GamblingRegs/1.0"
    version="1.0.0"
    xmlns:gamblingregs="http://rb.cz/services/codelists/structs/GamblingRegs/1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <!-- <xs:element name="REQUEST"/>  -->
    <xs:element name="gamblingRegs">
        <xs:complexType>
            <xs:sequence>
                <xs:element maxOccurs="unbounded" minOccurs="1" name="gamblingReg">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="1" minOccurs="0" name="IBAN">
                                <xs:annotation>
                                    <xs:documentation>International Bank Account Number. Optional, pokud je uvedeno <PERSON> a BIC, nevyplňuje se </xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="34"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="BIC">
                                <xs:annotation>
                                    <xs:documentation>Business Identification Code. Optional, pokud je uveden IBAN, nevyplňuje se </xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="11"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="accountNumber">
                                <xs:annotation>
                                    <xs:documentation>Gambler's account number. Optional, pokud je uveden IBAN, nevyplňuje se</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="35"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="1" name="bankName">
                                <xs:annotation>
                                    <xs:documentation>Name of the bank.</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="100"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="1" name="accountOwnerName">
                                <xs:annotation>
                                    <xs:documentation>Majitel účtu/Obchodní jméno </xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="100"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="1"
                                name="validFrom" type="xs:date">
                                <xs:annotation>
                                    <xs:documentation>Item valid from - udává časovou platnost</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="1"
                                name="validTo" type="xs:date">
                                <xs:annotation>
                                    <xs:documentation>Item valid to</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="1"
                                name="valid" type="xs:boolean">
                                <xs:annotation>
                                    <xs:documentation>Hodnota Valid udává platnost celého záznamu. Nabývá hodnot 'Y' nebo 'N'.  Aby byl záznam platný, musí být platný přes Valid i časově přes ValidFrom a ValidTo. Používá se např. pro vypnutí celého řádku, kdežto validFrom + validTo se používá pro časovou platnost.</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
