openapi: 3.0.3
info:
  title: HAD Partner Services
  termsOfService: urn:tos
  contact: {}
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0
  version: "1.0"
servers:
  - url: https://dev-api.rb.cz/rbapl/had
    description: PreSIT
  - url: https://tfx1-api.rb.cz/rbapl/had
    description: TFIX1
  - url: https://preprod-api.rb.cz/rbapl/had
    description: PrePROD
  - url: https://api.rb.cz/rbapl/had
    description: PROD
tags:
  - name: had-partners
paths:
  /partners/v1/dealers/{vendorCode}:
    get:
      tags:
        - had-partners
      summary: getDealerDetail
      operationId: had-partners-v1-getDealerDetail
      description: 'Calls DB procedure: eo_overit_prodejce.overit_prodejce'
      parameters:
        - name: vendorCode
          in: path
          required: true
          schema:
            type: string
        # rb headers
        - $ref: '#/components/parameters/X-Api-Name'
        - $ref: '#/components/parameters/X-Request-Id'
        - $ref: '#/components/parameters/X-Correlation-Id'
        - $ref: '#/components/parameters/X-Request-App'
        - $ref: '#/components/parameters/X-Frontend-App'
        - $ref: '#/components/parameters/X-Frontend-Service'
        - $ref: '#/components/parameters/X-User-Id'
        - $ref: '#/components/parameters/X-User-Type'
        - $ref: '#/components/parameters/X-Channel-Code'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetDealerDetailRes'
        default:
          description: 'Error response'
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  /partners/v1/shops/{vendorCode}:
    get:
      tags:
        - had-partners
      summary: getShopDetail
      operationId: had-partners-v1-getShopDetail
      description: 'Calls DB procedure: eo_odlozena_platba_01.overit_prodejce'
      parameters:
        - name: vendorCode
          in: path
          required: true
          schema:
            type: string
        - name: vendorUrl
          in: query
          required: false
          schema:
            type: string
        - name: vendorName
          in: query
          required: false
          schema:
            type: string
        # rb headers
        - $ref: '#/components/parameters/X-Api-Name'
        - $ref: '#/components/parameters/X-Request-Id'
        - $ref: '#/components/parameters/X-Correlation-Id'
        - $ref: '#/components/parameters/X-Request-App'
        - $ref: '#/components/parameters/X-Frontend-App'
        - $ref: '#/components/parameters/X-Frontend-Service'
        - $ref: '#/components/parameters/X-User-Id'
        - $ref: '#/components/parameters/X-User-Type'
        - $ref: '#/components/parameters/X-Channel-Code'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetShopDetailRes'
        default:
          description: 'Error response'
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  /partners/v1/dealers/{loginName}/bonus:
    get:
      tags:
        - had-partners
      summary: getDealerBonus
      operationId: had-partners-v1-getDealerBonus
      description: 'Calls DB procedure: eo_brokerportal.nacist_informace'
      parameters:
        - name: loginName
          in: path
          required: true
          schema:
            type: string
        # rb headers
        - $ref: '#/components/parameters/X-Api-Name'
        - $ref: '#/components/parameters/X-Request-Id'
        - $ref: '#/components/parameters/X-Correlation-Id'
        - $ref: '#/components/parameters/X-Request-App'
        - $ref: '#/components/parameters/X-Frontend-App'
        - $ref: '#/components/parameters/X-Frontend-Service'
        - $ref: '#/components/parameters/X-User-Id'
        - $ref: '#/components/parameters/X-User-Type'
        - $ref: '#/components/parameters/X-Channel-Code'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetDealerBonusRes'
        default:
          description: 'Error response'
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
components:
  parameters:
    # rb standard headers
    X-Api-Name:
      name: X-Api-Name
      in: header
      description: ID of the called service, defined by Integration platform RBCZ (Service IDs are managed in [kip.rb.cz](http://kip.rb.cz))
      required: true
      example: 'had-partners-v1-getDealerDetail'
      schema:
        type: string
    X-Request-Id:
      name: X-Request-Id
      in: header
      description: |
        ID of the request, unique to the call, as determined by the initiating (consumer) application. UUID format or RBCZ Message ID convention: application code + 21 numbers.
        (i.e. <SYS>001207641782552682875)
      required: true
      example: 'MCH001207641782552682875'
      schema:
        type: string
    X-Correlation-Id:
      name: X-Correlation-Id
      in: header
      description: |
        X-Correlation-ID will be used to track requests through all involved services to be able to graph
        request flows, map how services contribute to response times, identify bottle necks, etc.
        The X-Correlation-ID must be propagated unchanged in any and all downstream service calls (e.g. RBCZ services).
      required: true
      example: '99391c7e-ad88-49ec-a2ad-99ddcb1f7721'
      schema:
        type: string
        format: uuid
    X-Idempotency-Key:
      name: X-Idempotency-Key
      in: header
      description: |
        Unique request identifier to support idempotency.
      required: true
      example: 'd25ea706-5f78-11ed-9b6a-0242ac120002'
      schema:
        type: string
        format: uuid
    X-Request-App:
      name: X-Request-App
      in: header
      description: ID of the service consumer application that generates the request (RBCZ application code).
      example: 'MCH'
      required: true
      schema:
        type: string
        maxLength: 3
    X-Frontend-App:
      name: X-Frontend-App
      in: header
      description: ID of the service consumer system which originally generated the request. Used only when the service is mediated by another system (RBCZ application code).
      example: 'GIB'
      schema:
        type: string
        maxLength: 3
    X-Frontend-Service:
      name: X-Frontend-Service
      in: header
      description: ID of the previous level service. Used only when the service is called from another service.
      example: 'serviceId-operation'
      schema:
        type: string
    X-User-Id:
      name: X-User-Id
      in: header
      description: Unique identification of the user, who initiates the request (cza or technical user).
      example: "cza12345"
      schema:
        type: string
    X-User-Type:
      name: X-User-Type
      in: header
      description: Additional information to distinguish internal user (cza...) or external user (technical accounts, 3rd parties, clients...) - INT | EXT.
      example: "INT"
      schema:
        type: string
        enum:
          - INT
          - EXT
    X-Channel-Code:
      name: X-Channel-Code
      in: header
      description: Identification of the channel in which the request was sent.
      example: "BFO"
      schema:
        type: string
  schemas:
    # error schemas (from RB standard)
    Error:
      type: object
      description: |
        # Error Reference Model
        Error model to hold data which hepls to identify error cause and provide additional tracking informations.
      required:
        - requestId
        - correlationId
        - status
      properties:
        requestId:
          type: string
          description: A unique UUID of a specific request. A value shoud be obtained from a X-Request-Id header.
          example: '06f31981-c15d-48fb-86c6-53bfae940802'
        correlationId:
          type: string
          description: A unique UUID of identification entire communication. A value shoud be obtained from a X-Correlation-Id header.
          example: '2725ab36-4608-4abc-ba1c-6e929ef539b1'
        status:
          type: integer
          format: int32
          description: |
            A HTTP status code. If there is in use different protocol than HTTP, we should map an error to this protocol.
          minimum: 100
          maximum: 600
          exclusiveMaximum: true
          example: 400
        reasons:
          type: array
          items:
            $ref: "#/components/schemas/ResultItem"
    ResultItem:
      type: object
      description: Additional info about caused error or warning.
      required:
        - code
        - message
        - severity
      properties:
        code:
          type: string
          description: |
            Error code, Backend error code or Warnig code returned from the backend service.
          example: "XYZ8000"
        severity:
          type: string
          description: Shows if the reason for an unexpected situation is critical or just information.
          enum:
            - WARN
            - ERROR
            - FATAL
          example: "ERROR"
        message:
          type: string
          description: Human-readable message in user-requested language.
          example: "Payment rejected. Missing creditor iban."
        path:
          type: string
          description: The path of the problematic field which causes the error.
          example: "creditorAccount.iban"
        moreInfo:
          type: string
          description: More detailed description and error slolution, e.g. https://some.url
    # regular components
    GetDealerDetailRes:
      type: object
      required:
        - resultCode
        - huFlag
        - clFlag
        - deFlag
        - inFlag
        - broker
        - budgetList
      properties:
        resultCode:
          type: integer
          format: int32
          description: 'DB OUT parameter: vysledek'
        errorMessage:
          type: string
          description: 'DB OUT parameter: chyba'
        firstName:
          type: string
          description: 'DB OUT parameter: jmeno'
        lastName:
          type: string
          description: 'DB OUT parameter: prijmeni'
        addressStreet:
          type: string
          description: 'DB OUT parameter: ulice_a_cp'
        addressNumber:
          type: string
          description: 'empty until HAD DB supports separate street and number data'
        addressCity:
          type: string
          description: 'DB OUT parameter: obec'
        addressZipCode:
          type: string
          description: 'DB OUT parameter: psc'
        phone:
          type: string
          description: 'DB OUT parameter: telefon'
        email:
          type: string
          description: 'DB OUT parameter: email'
        companyName:
          type: string
          description: 'DB OUT parameter: spolecnost_nazev'
        companyIco:
          type: string
          description: 'DB OUT parameter: spolecnost_ico'
        companyAddressStreet:
          type: string
          description: 'DB OUT parameter: spolecnost_ulice_a_cp'
        companyAddressNumber:
          type: string
          description: 'empty until HAD DB supports separate street and number data'
        companyAddressCity:
          type: string
          description: 'DB OUT parameter: spolecnost_obec'
        companyAddressZipCode:
          type: string
          description: 'DB OUT parameter: spolecnost_psc'
        companyPhone:
          type: string
          description: 'DB OUT parameter: spolecnost_telefon'
        companyEmail:
          type: string
          description: 'DB OUT parameter: spolecnost_email'
        huFlag:
          type: boolean
          description: 'DB OUT parameter: hypoteky'
        clFlag:
          type: boolean
          description: 'DB OUT parameter: pujcky'
        deFlag:
          type: boolean
          description: 'DB OUT parameter: depozita'
        inFlag:
          type: boolean
          description: 'DB OUT parameter: investice'
        broker:
          type: boolean
          description: 'DB OUT parameter: zprostredkovatel'
        clKamFirstName:
          type: string
          description: 'DB OUT parameter: kam_cl_jmeno'
        clKamLastName:
          type: string
          description: 'DB OUT parameter: kam_cl_prijmeni'
        clKamEmail:
          type: string
          description: 'DB OUT parameter: kam_cl_email'
        iconDmsId:
          type: string
          description: 'DB OUT parameter: id_ikony_dms'
        cnlId:
          type: string
          description: 'DB OUT parameter: CNL_ID'
        budgetList:
          type: array
          description: 'DB OUT parameter: seznam_rozpoctu'
          items:
            type: object
            properties:
              id:
                type: string
                description: 'DB col: id_rozpoctu'
              name:
                type: string
                description: 'DB col: nazev'
              order:
                type: integer
                format: int32
                description: 'DB col: poradi'
    GetShopDetailRes: # updated
      type: object
      description: 'package EO_ODLOZENA_PLATBA_01'
      required:
        - resultCode
      properties:
        resultCode:
          type: integer
          format: int32
          description: 'DB OUT parameter: vysledek'
        errorMessage:
          type: string
          description: 'DB OUT parameter: chyba'
        verifiedBrokerCode:
          type: string
          description: 'DB OUT parameter: overeny_kod_prodejce'
        businessName:
          type: string
          description: 'DB OUT parameter: obchodni_nazev'
        userName:
          type: string
          description: 'DB OUT parameter: uzivatelske_jmeno'
        companyName:
          type: string
          description: 'DB OUT parameter: nazev_spolecnosti'
        addressStreet:
          type: string
          description: 'DB OUT parameter: ulice'
        addressNumber:
          type: string
          description: 'DB OUT parameter: cislo_popisne'
        addressCity:
          type: string
          description: 'DB OUT parameter: obec'
        addressZipCode:
          type: string
          description: 'DB OUT parameter: psc'
        phone:
          type: string
          description: 'DB OUT parameter: telefon'
        email:
          type: string
          description: 'DB OUT parameter: email'
        url:
          type: string
          description: 'DB OUT parameter: url'
        accountNumberPrefix:
          type: string
          description: 'DB OUT parameter: predcisli_uctu'
        accountNumberBase:
          type: string
          description: 'DB OUT parameter: cislo_uctu'
        bankCode:
          type: string
          description: 'DB OUT parameter: kod_banky'
        numberOfDaysUntilMaturity:
          type: integer
          format: int32
          description: 'DB OUT parameter: pocet_dni_do_splatnosti'
        icon:
          type: string
          description: 'DB OUT parameter: ikona'
    GetDealerBonusRes:
      type: object
      description: 'package EO_BROKERPORTAL'
      required:
        - resultCode
        - messageList
      properties:
        resultCode:
          type: integer
          format: int32
          description: 'DB OUT parameter: vysledek'
        errorMessage:
          type: string
          description: 'DB OUT parameter: chyba'
        accountBalance:
          type: number
          format: double
          description: 'DB OUT parameter: zustatek'
        balanceDate:
          type: string
          format: date-time
          description: 'DB OUT parameter: datum_zustatku'
        validUntil:
          type: string
          format: date-time
          description: 'DB OUT parameter: platnost_do'
        messageList:
          type: array
          description: 'DB OUT parameter: seznam_zprav'
          items:
            type: object
            properties:
              message:
                type: string
                description: 'DB col: zprava'
              order:
                type: integer
                format: int32
                description: 'DB col: poradi'
