openapi: 3.0.3
info:
  title: IFR - Card Processing
  version: 1.0.0
  description: |
    API to mange related information on a card that is issued by bank.
  contact:
    name: IP - Analysts_RBCZ
    email: <EMAIL>
  x-release-notes:
  - 202305REL - Initial Service Group, domain-service-id-vX-operationName
servers:
  - url: http://localhost:8080/rbepi
    description: Generated server url
  - url: https://dev-api.rb.cz/rbepi
    description: PreSit
  - url: https://tfx1-api.rb.cz/rbepi
    description: TestFix
  - url: https://preprod-api.rb.cz/rbepi
    description: PreProd
  - url: https://api.rb.cz/rbepi
    description: Prod
security:
  - BasicAuth: []
tags:
  - name: Card
    description: Card management
paths:
  /ifr/card-processing/v1/cards/address:
    put:
      summary: Modification of addresses related to card.
      description: |
        Updates address related to debit/credit card, identified by account quadruple.
        Several types of address can be related to single card.
        The resource enables to perform Add, Update, Delete, Copy and Replace operations - see operationTypes. 
      operationId: ifr-cardprocessing-v1-updateAddress
      tags:
        - Address
      parameters:
        # path parameters

        # query parameters

        # headers
        - in: header
          name: timeStamp
          required: true
          description: Date and time of the request
          schema:
            type: string
            format: date-time        
        - $ref: "#/components/parameters/X-Api-Name"
        - $ref: "#/components/parameters/X-Request-Id"
        - $ref: "#/components/parameters/X-Correlation-Id"
        - $ref: "#/components/parameters/X-Idempotency-Key"
        - $ref: "#/components/parameters/X-Request-App"
        - $ref: "#/components/parameters/X-Frontend-App"
        - $ref: "#/components/parameters/X-Frontend-Service"
      requestBody:
        description: Request body of the API
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateAddressReqResp"
      responses:
        "200":
          description: Successful response of operation.
          content:
            application/json:
              schema:
                type: object
                $ref: "#/components/schemas/UpdateAddressReqResp"                  
        default:
          description: Error response
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  /ifr/card-processing/v1/cards/persons/personalData:
    put:
      summary: Updates personal data related to a card.
      description: | 
        For given card, identified by account quadruple, it updates personal data related to the card.        
      operationId: ifr-cardprocessing-v1-updatePersonalData
      tags:
        - Personal data
      parameters:
        # path parameters

        # query parameters

        # headers
        - in: header
          name: timeStamp
          required: true
          description: Date and time of the request
          schema:
            type: string
            format: date-time                 
        - $ref: "#/components/parameters/X-Api-Name"
        - $ref: "#/components/parameters/X-Request-Id"
        - $ref: "#/components/parameters/X-Correlation-Id"
        - $ref: "#/components/parameters/X-Idempotency-Key"
        - $ref: "#/components/parameters/X-Request-App"
        - $ref: "#/components/parameters/X-Frontend-App"
        - $ref: "#/components/parameters/X-Frontend-Service"
      requestBody:
        description: A request body desc.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdatePersonalDataReqResp"
      responses:
        "200":
          description: Successful response of operation.
          content:
            application/json:
              schema:
                type: object
                $ref: "#/components/schemas/UpdatePersonalDataReqResp"                  
        default:
          description: Error response
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"  
components:
  parameters:
    X-Api-Name: 
      name: X-Api-Name
      in: header
      description: ID of the called service, definded by Integration platform RBCZ and is equal to 'operationId:' for this operations (see this .yaml or [kip.rb.cz](http://kip.rb.cz)).
      required: true
      example: 'serviceId-operation'
      schema:
        type: string
    X-Request-Id:
      name: X-Request-Id
      in: header
      description: |
        ID of the request, unique to the call, as determined by the initiating (consumer) application. UUID format or RBCZ Message ID convention: application code + 21 numbers.
        (e.g. 'SYS'001207641782552682875)
      required: true
      example: '99391c7e-ad88-49ec-a2ad-99ddcb1f7721'
      schema:
        type: string
        format: uuid
    X-Correlation-Id:
      name: X-Correlation-Id
      in: header
      description: |
        X-Correlation-ID will be used to track requests through all involved services to be able to graph
        request flows, map how services contribute to response times, identify bottle necks, etc.
        The X-Correlation-ID must be propagated unchanged in any and all downstream service calls (e.g. RBCZ services).
      required: true
      example: '99391c7e-ad88-49ec-a2ad-99ddcb1f7721'
      schema:
        type: string
        format: uuid
    X-Idempotency-Key:
      name: X-Idempotency-Key
      in: header
      description: Unique request identifier to support idempotency.
      example: '99391c7e-ad88-49ec-a2ad-99ddcb1f7721'
      schema:
        type: string
        maxLength: 40
    X-Request-App:
      name: X-Request-App
      in: header
      description: ID of the service consumer application that generates the request (RBCZ application code).
      example: 'ABC'
      required: true
      schema:
        type: string
        maxLength: 3
    X-Frontend-App:
      name: X-Frontend-App
      in: header
      description: ID of the service consumer system which originally generated the request. Used only when the service is mediated by another system (RBCZ application code).
      example: 'XYZ'
      schema:
        type: string
        maxLength: 3
    X-Frontend-Service:
      name: X-Frontend-Service
      in: header
      description: ID of the previous level service. Used only when the service is called from another servise.  
      example: 'serviceId-operation'
      schema:
        type: string
    X-User-Id:
      name: X-User-Id
      in: header
      description: Unique identification of the user, who initiates the request.
      example: "cza12345"
      required: true
      schema:
        type: string
    X-User-Type:
      name: X-User-Type
      in: header
      description: Additional info for identification internal user (cza...) or external user (technical accounts, 3rd parties, clients...) - INT | EXT.
      example: "INT"
      required: true
      schema:
        type: string
        enum:
        - INT
        - EXT
        - TECHNICAL
    X-Channel-Code:
      name: X-Channel-Code
      in: header
      description: Identification of the channel which has been request sent.
      example: "IBS"
      required: true
      schema:
        type: string
    Content-Language:
      name: Content-Language
      in: header
      description: Defines the language(s) of the intended audience. It allows a user to identify and differentiate according to the users' own preferred language. 
      example: 'cs-CZ'
      schema:
        type: string
  schemas:
    UpdateAddressReqResp:
      type: object
      required: [account, document, operationType, addressType, validFrom]
      properties:       
        account: 
          $ref: "#/components/schemas/Account"
        document:
          $ref: "#/components/schemas/Document"        
        operationType:
          type: string
          description: A - add, C-Copy, D-delete, M - modify/update , R-replace/update all address types for a person
          example: A
          enum:
            - A
            - C
            - M
            - D
            - R
        addressType:
          type: integer
          description: 1 = STATEMENT; 2 = WORK; 3 = HOME; 4 = MATERIALS; 5 = CARDS ADRESS
          example: 1
          minimum: 0
          maximum: 9
        validFrom:
          type: string
          format: date
          description: Date of validity from
          example: 2023-03-10
        validTo:
          type: string
          format: date
          description: Date of validity to
          example: 2999-12-31
        street:
          type: string
          example: Lipanská
          maxLength: 60
          minLength: 0
        doorNumber:
          type: string
          description: registration street number (before slash)
          example: 1278
          maxLength: 5
          minLength: 0
        neighborhood: 
          type: string
          description: Additional info for currier.
          example: byt číslo 6
          maxLength: 50
          minLength: 0
        city:
          type: string
          description: City
          example: Kuří
          maxLength: 50
          minLength: 0
        complement:
          type: string
          description: Additional information - a/ orientaion street number (after slash); b/ information about distribution type - post office or brach. Consult RPC for more info becuase the format very complicated.
          example: 8 S
          maxLength: 30
          minLength: 0
        country:
          type: integer
          description: in iso-3166-1 numerical format, e.g. 703 for Slovakia, 203 for Czechia
          example: 703
          minimum: 0
          maximum: 999
        zip:
          type: string
          example: 94505
          maxLength: 10
          minLength: 0
        mobile:
          type: string
          example: +420373390011
          maxLength: 20
          minLength: 0
        email:
          type: string
          maxLength: 128
          example: <EMAIL>
          minLength: 0
        phone:
          type: string
          example: +420373390011
          maxLength: 20
          minLength: 0
        extension:
          type: string
          maxLength: 10
          minLength: 0
        data1:
          type: string
          description: Only for debit cards - Firstname and lastname
          maxLength: 35
          minLength: 0
        data2:
          type: string
          description: Only for debit cards - Street and number1/number2
          maxLength: 35
          minLength: 0
        data3:
          type: string
          description: Only for debit cards - ZIP and City
          example: 43151 KLÁŠTEREC NAD OHŘÍ
          maxLength: 35
          minLength: 0
        data4:
          type: string
          description: Only for debit cards - Country
          example: CZECH_REPUBLIC
          maxLength: 35
          minLength: 0
    Account:
      type: object
      description: quadruple that uniqually identifies a card
      required: [issuer, branch, product, accountNumber]
      properties:
        issuer:
          type: integer
          description: Card issuer id. E.g. 5 for RBCZ
          example: 5
          minimum: 0
          maximum: 9999
        branch:
          type: integer
          description: Branch
          example: 98
          minimum: 0
          maximum: 99999
        product:
          type: integer
          description: According to LOV Issuers_Products - managed by Card Products division. E.g. 93 for EASY karta
          example: 93
          minimum: 0
          maximum: 99
        accountNumber:
          description: Account id related to Card. It has no relation to bank account number.
          type: integer
          example: 3457002
          minimum: 0
          maximum: *********
    Document: 
      type: object
      description: Document or association identified by type and id
      required: [documentType, document]
      properties:
        documentType:
          type: integer
          description: According to LOV Documents_types - managed by Card Products division. Type of identifier. E.g. 49 means SiebelId, 21 - is RČ, must be known for RPC when the card was created - see RPC operation 001 Create New Card
          example: 49
          minimum: 0
          maximum: 99
        document:
          type: string
          description: identification - see documentType
          example: **********
          maxLength: 15
          minLength: 1
    UpdatePersonalDataReqResp: 
      type: object
      description: Personal data
      required: [account, document, birthDate]
      properties:
        account: 
          $ref: "#/components/schemas/Account"
        document:
          $ref: "#/components/schemas/Document"
        birthDate:
          type: string
          format: date
          example: 2001-12-30
        city:
          type: string
          example: Košice
          maxLength: 50
          minLength: 0
        nationality:
          type: string
          example: ČESKÁ REPUBLIKA
          maxLength: 25
          minLength: 0
        maritalStatus:
          type: string
          description: 0 - WITHOUT INFORMATION, 1 - SINGLE, 2 - MARRIED, 3 - WIDOWER, 4 - CASUAL RELATION, 5 - DIVORCED, 6 - SEPARATED
          enum:
            - 0
            - 1
            - 2
            - 3
            - 4
            - 5
            - 6
          example: 0
        gender:
          type: string
          example: M
          enum:
            - M
            - F
    Error:
      type: object
      description: |
        # Error Reference Model

        Error model to hold data which hepls to identify error cause and provide additional tracking informations.        
      required:
        - requestId
        - correlationId
        - status
      properties:
        requestId:
          type: string
          description: A unique UUID of a specific request. A value shoud be obtained from a X-Request-Id header.
          example: '06f31981-c15d-48fb-86c6-53bfae940802'
        correlationId:
          type: string
          description: A unique UUID of identification entire communication. A value shoud be obtained from a X-Correlation-Id header.
          example: '2725ab36-4608-4abc-ba1c-6e929ef539b1'
        status:
          type: integer
          format: int32
          description: |
            A HTTP status code. If there is in use different protocol than HTTP, we should map an error to this protocol.
          minimum: 100
          maximum: 600
          exclusiveMaximum: true
          example: 400
        reasons:
          type: array
          items:
            $ref: "#/components/schemas/ResultItem"
    ResultItem:
      type: object
      description: Additional info about caused error or warning.
      required:
        - code
        - message
        - severity
      properties:
        code:
          type: string
          description: |
            Error code, Backend error code or Warnig code returned from the backend service.
          example: "XYZ8000"
        severity:
          type: string
          description: Shows if the reason for an unexpected situation is critical or just information.
          enum:
            - WARN
            - ERROR
            - FATAL
          example: "ERROR"
        message:
          type: string
          description: Human-readable message in user-requested language.
          example: "Payment rejected. Missing creditor iban."
        path:
          type: string
          description: The path of the problematic field which causes the error.
          example: "creditorAccount.iban"
        moreInfo:
          type: string
          description: More detailed description and error slolution, e.g. https://some.url
