openapi: 3.0.0
info:
  title: Party
  version: 1.0.4
  description: |
    The services manages operation connected with party/customer in RBCZ.
  contact:
    name: RBCZ - HO Integration & Core Banking Competence Center
  x-release-notes:
  - 202406REL - BN-16495 - added operation cus-party-v1-putPartyCategory
  - 202409REL - BN-16263 - added operation cus-party-v1-getContacts
  - 202409REL - BN-17415 - extended response of operation cus-party-v1-GetDetail
  - 202411REL - BN-17561 - rewrite of init channels to REST
  - 202411REL - BN-17561 - another 4 SBL elements added in RS mapping
  - 202502REL - BN-13532 - added operations cus-party-v1-insertIdentDocuments, cus-party-v1-updateIdentDocuments, cus-party-v1-getUnifeHeader
  - 202505REL - BN-18102 - commercial party creation made available
  
servers:
- url: https://dev-api.rb.cz/rbepi
  description: PreSit
- url: https://tfx1-api.rb.cz/rbepi
  description: TestFix
- url: https://preprod-api.rb.cz/rbepi
  description: PreProd
- url: https://api.rb.cz/rbepi
  description: Prod

paths:
  /cus/party/v1/parties/{partyId}/household-members:
    get:
      summary: GET-HOUSEHOLDMEMBERSLIST - Operation returns household members related to given party.
      description: |
        The ES operation returns the list of household members and the type of relation for a given party ID and optionally marital status entered on the input:
          -	If the input data are existing and valid, the service returns the subject party's party ID and the collection of his/her household members (their party ID and the type of relation).
          -	If no household members are found, then a valid response (result code 0) is returned with the subject party's party ID, but without the collection of household members.
          -	Maximum expected number of records in the collection in the response is around 10.
          -	Marital status and type of relation are values limited by backend codebooks.

      operationId: cus-party-v1-getHouseholdMembersList
         
      parameters:
      # path  
        - $ref: "#/components/parameters/partyId"
      # query  
        - $ref: "#/components/parameters/maritalStatus"      
      # header
        - $ref: "#/components/parameters/X-Api-Name"
        - $ref: "#/components/parameters/X-Request-Id"
        - $ref: "#/components/parameters/X-Correlation-Id"
        - $ref: "#/components/parameters/X-Request-App"
        - $ref: "#/components/parameters/X-Frontend-App"
        - $ref: "#/components/parameters/X-Frontend-Service"
       
        
      responses:
        "200":
          description: Successful response of getHouseholdMembersList
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetHouseholdMembersListResult"
        default:
          description: Error response
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"

  /cus/party/v1/parties/{partyId}:
    get:
      summary: GET-DETAIL - Operation returns Party detail.
      description: |
        Full dperation description #TODO: 
      operationId: cus-party-v1-getDetail
      parameters:
      # header
        - $ref: "#/components/parameters/X-Api-Name"
        - $ref: "#/components/parameters/X-Request-Id"
        - $ref: "#/components/parameters/X-Correlation-Id"
        - $ref: "#/components/parameters/X-Request-App"
        - $ref: "#/components/parameters/X-Frontend-App"
        - $ref: "#/components/parameters/X-Frontend-Service"
        - $ref: "#/components/parameters/X-User-ID"
        - $ref: "#/components/parameters/X-User-Type"
        - $ref: "#/components/parameters/X-Source-IP"
      # path  
        - $ref: "#/components/parameters/partyId"
      # query  
        - $ref: "#/components/parameters/identificationType"
        - $ref: "#/components/parameters/dataSource"
      responses:
        "200":
          description: Successful response of getDetail
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetDetailResult"
        default:
          description: Error response
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
    put:
      summary: UPDATE - Operation updates client's data.
      description: |
        Operation updates client's data. For 202303REL private party in RBCZ/SBL/ only.
        Update logic 
        - only nonempty atributes are updated in core system
          {key: newvalue}
        - only string atributes can be deleted (by sending empty string "") 
          {key: ""}
      operationId: cus-party-v1-update
      parameters:
      # header
        - $ref: "#/components/parameters/X-Api-Name"
        - $ref: "#/components/parameters/X-Request-Id"
        - $ref: "#/components/parameters/X-Correlation-Id"
        - $ref: "#/components/parameters/X-Request-App"
        - $ref: "#/components/parameters/X-Frontend-App"
        - $ref: "#/components/parameters/X-Frontend-Service"
        - $ref: "#/components/parameters/X-User-ID"    
        - $ref: "#/components/parameters/X-Channel-Code"   
      
      # path  
        - $ref: "#/components/parameters/partyId"
      
      requestBody:
        description: A request data for get client verification.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateRequest"
      responses:
        "200":
          description: Successful response of verification.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UpdateResult"
        default:
          description: Error response
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  
  /cus/party/v1/parties/{partyId}/documents:
    get:
      summary: GET-DOCUMENTS - Operation returns Party documents.
      description: |
        Operation returns all documents related to given PartyID. 
      operationId: cus-party-v1-getDocuments
      parameters:
      # path  
        - $ref: "#/components/parameters/partyId"
      # query
      # header
        - $ref: "#/components/parameters/X-Api-Name"
        - $ref: "#/components/parameters/X-Request-Id"
        - $ref: "#/components/parameters/X-Correlation-Id"
        - $ref: "#/components/parameters/X-Request-App"
        - $ref: "#/components/parameters/X-Frontend-App"
        - $ref: "#/components/parameters/X-Frontend-Service"
      responses:
        "200":
          description: Successful response of getDocuments
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetDocumentsResult"
        default:
          description: Error response
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
    post:
      summary: INSERT-IDENT-DOCUMENTS - Inserting indetification documents for a specific partyId
      description: |
        The service uploads documents for the specific partyId that is sent in the request. It is possible to insert multiple records in one call.
        It is possible to request a CBL document check for all uploaded documents by setting processCblCheck = true. Only for documents that are eligible for the CBL document check a result of check is provided. 
        The collection of documents always contains a complex for each uploaded record. SBL processes request one record after another. So it may happen that some records are processed and some not in case of error.
        The service processes either private or commercial parties in SBL.
        If partyType == 'PRIVATE' or missing, call A_SBL_UploadIdentificationDocument, otherwise, if partyType == 'COMMERCIAL', call A_SBL_UploadCommercialPartyIdentDoc.

      operationId: cus-party-v1-insertIdentDocuments 
      parameters:
      # path  
        - $ref: "#/components/parameters/partyId"
      # query
      # header
        - $ref: "#/components/parameters/X-Api-Name"
        - $ref: "#/components/parameters/X-Request-Id"
        - $ref: "#/components/parameters/X-Correlation-Id"
        - $ref: "#/components/parameters/X-Request-App"
        - $ref: "#/components/parameters/X-Frontend-App"
        - $ref: "#/components/parameters/X-Frontend-Service"
        - $ref: "#/components/parameters/X-User-Id"
        - $ref: "#/components/parameters/X-Channel-Code"
      requestBody:
            description: Inserts identification documents for the specific partyId
            content:
               application/json:
                schema:
                 $ref: "#/components/schemas/InsertIdentDocumentsReq"
      responses:
        "200":
          description: Successful response of inserted documents and cbl check if required.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/InsertIdentDocumentsRes"
        default:
          description: Error response
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  
    put:
      summary: UPDATE-IDENT-DOCUMENTS - Updating identification documents for a specific partyId
      description: | 
        The service updates documents for the specific partyId that is sent in the request. It is possible to update multiple records in one call. 
        It is possible to request a CBL document check for all uploaded documents by setting processCblCheck = true. Only for documents that are eligible for the CBL document check a result of check is provided. 
        The collection of documents always contains a complex for each uploaded record. SBL processes request one record after another. So it may happen that some records are processed and some not in case of error.
        The service processes either private or commercial parties in SBL. 
        If partyType == 'PRIVATE' or missing, call A_SBL_UploadIdentificationDocument, otherwise, if partyType == 'COMMERCIAL', call A_SBL_UploadCommercialPartyIdentDoc.

      operationId: cus-party-v1-updateIdentDocuments
      parameters:
      # path  
        - $ref: "#/components/parameters/partyId"
      # query
      # header
        - $ref: "#/components/parameters/X-Api-Name"
        - $ref: "#/components/parameters/X-Request-Id"
        - $ref: "#/components/parameters/X-Correlation-Id"
        - $ref: "#/components/parameters/X-Request-App"
        - $ref: "#/components/parameters/X-Frontend-App"
        - $ref: "#/components/parameters/X-Frontend-Service"
        - $ref: "#/components/parameters/X-User-Id"
        - $ref: "#/components/parameters/X-Channel-Code"
      requestBody:
            description: Inserts identification documents for the specific partyId
            content:
               application/json:
                schema:
                 $ref: "#/components/schemas/UpdateIdentDocumentsReq"
      responses:
        "200":
          description: Successful response of updated documents and cbl check if required.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UpdateIdentDocumentsRes"
        default:
          description: Error response
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
      
  /cus/party/v1/parties/verification:
    post:
      summary: VERIFICATION - Operation provides client verification.
      description: |
        Client verification based on personal data. 
      operationId: cus-party-v1-verification
      parameters:
        - $ref: "#/components/parameters/X-Api-Name"
        - $ref: "#/components/parameters/X-Request-Id"
        - $ref: "#/components/parameters/X-Correlation-Id"
        - $ref: "#/components/parameters/X-Request-App"
        - $ref: "#/components/parameters/X-Frontend-App"
        - $ref: "#/components/parameters/X-Frontend-Service"
        - $ref: "#/components/parameters/X-User-ID"
        - $ref: "#/components/parameters/X-User-Type"
      requestBody:
        description: A request data for get client verification.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/VerificationRequest"
      responses:
        "200":
          description: Successful response of verification.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/VerificationResult"
        default:
          description: Error response
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"      
  
  /cus/party/v1/parties/identification:
    post:
      summary: IDENTIFY - Identifies person through various personal data.
      description: |
        Operation tries to identify person through various personal data.

        <h2>Default scenario:</h2> 
       
        * <b><i>searchByPhone attribute in query is not filled OR == false</i></b><br>
          
        In the request, the person who we want to identify is specified by their name, birth date, birth number and phone number and various other personal data. Based on the information provided in the request, the operation tries to identify the person in MDC.
        The service gets as an input attributes of instance entity (one record), it runs a cleansing and matching plan, and returns a matched master record. If the record is identified to any existing record(s) in already processed and stored data, it returns the matched master record, otherwise it returns an empty response (except isIdentified flag).

          In the request element the following attributes must be specified:
          *	Party type (PRIVATE, COMMERCIAL)
          *	For party type == COMMERCIAL also legal status (FOP, PO)

          <b>At least one of the matching rules must be fulfilled for a record to be identified. Combination of the following attributes specifies the matching rules:</b>

          Private:          
          *	BIRTH NUMBER + FIRST NAME+ LAST NAME
          *	BIRTH NUMBER + FIRST NAME+ BIRTH DATE* + ADDRESS
          *	BIRTH NUMBER + FIRST NAME+ BIRTH DATE* + CONTACT
          *	BIRTH NUMBER + FIRST NAME+ BIRTH DATE* + DOCUMENT
          *	BIRTH NUMBER + FIRST NAME+ GENDER + BIRTH DATE 
          *	BIRTH NUMBER + FIRST NAME+ GENDER + CONTACT
          *	BIRTH NUMBER + FIRST NAME+ GENDER* + DOCUMENT
          *	FIRST NAME + LAST NAME + BIRTH DATE + ADDRESS
          *	FIRST NAME + LAST NAME + BIRTH DATE + CONTACT
          *	FIRST NAME + LAST NAME + BIRTH DATE + DOCUMENT

          Commercial:          
          *	IČ + REGISTRATION COUNTRY / COMPANY NAME + REGISTRATION COUNTRY (for CZ only)
          *	COMPANY NAME (without legal form) + ADDRESS (IČ doesn’t exist)

        <h2>Default scenario with extra identifiers:</h2>  
        * <b><i>searchByPhone attribute in query is not filled OR == false</i></b><br>
        * <b><i>otherIdentifiers == true <i/></b><br>

        Returns other identifiers (namely COCUNUT id)
        
        <h2>Extended scenario:</h2>  
        * <b><i>searchByPhone attribute in query is not filled OR == false</i></b><br>
        * <b><i>checkDigitalChannel == true <i/></b><br>

        Default behavior can be extended by calling Multichannel to check if party has a digital channel.
        (does not work with searchByPhone == true)
        
        <h2>Alternative scenario</h2>

        * <b>searchByPhone == true </b><br>

        Tries to identify party through tokentype tied to phonenumber. (Stored in AUS)

          Request
          * /searchPhone/phonenumber – mandatory
          * /searchPhone/tokenType – mandatory                 

      operationId: cus-party-v1-identify
      parameters:
        # path parameters
        
        # query parameters        
        - $ref: "#/components/parameters/SearchByPhone"
        - $ref: "#/components/parameters/CheckDigitalChannel" 
        - $ref: "#/components/parameters/OtherIdentifiers" 

        # headers
        - $ref: "#/components/parameters/X-Api-Name"
        - $ref: "#/components/parameters/X-Request-Id"
        - $ref: "#/components/parameters/X-Correlation-Id"
        - $ref: "#/components/parameters/X-Request-App"
        - $ref: "#/components/parameters/X-Frontend-App"
        - $ref: "#/components/parameters/X-Frontend-Service"        

      requestBody:
        description: A request data for get client verification.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/IdentifyRequest"
      responses:
        "200":
          description: Successful response of verification.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/IdentifyResult"
        default:
          description: Error response
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error" 

  /cus/party/v1/parties/{partyId}/categories:
        put:
          summary: CREATE/UPDATE party's category
          description: |
            The operation is used to create or update a party's (i.e a client's) category. The service is a request-response type and it can:

            Create Party Category - if the entity with the same category does not exist for the client.
            Update Party Category - if the entity with the same category exists for the client.

            Either categorization or value parameters have to be filled in the request. The provider sends the response to the consumer in order to inform them about the successful result or an error.
          operationId: cus-party-v1-assignCategory
          parameters:
             # header
             - $ref: "#/components/parameters/X-Api-Name"
             - $ref: "#/components/parameters/X-Request-Id"
             - $ref: "#/components/parameters/X-Correlation-Id"
             - $ref: "#/components/parameters/X-Request-App"
             - $ref: "#/components/parameters/X-Frontend-App"
             - $ref: "#/components/parameters/X-Frontend-Service"  
      
             # path  
             - $ref: "#/components/parameters/partyId"
      
          requestBody:
            description: A request data to CREATE/UPDATE party's category.
            content:
               application/json:
                schema:
                 $ref: "#/components/schemas/PartyCategoryRequest"
          responses:
            "200":
              description: Successful response.
            default:
              description: Error response
              content:
                application/json:
                 schema:
                  $ref: "#/components/schemas/Error"
   
  /cus/party/v1/parties/{personId}/detail-by-phone:
    get:
      summary: DETAIL-BY-PHONE - Gets the detail of a client based on telephone number or party ID.
      description: |
        Gets the detail of a client, list of his/her active and non-active applications and pre-approved offers (if they exist).<br><br>
        In the request, the person whose detail we want can be identified by his/her telehone number, Siebel party ID or an ID in another system (in that case, it is necessary to specify what system's ID is provided in the RQ). <br><br>
        For example, if we want to get the detail of a RSTS client, then we fill his/her rstsPartyId in the query parameter personId, we select personIdType == "SOURCEID" and in the query parameter sourceIdSystem we fill "CSA" (i.e. the three-letter code of CIBIS Sales, which is RSTS backend application). <br><br>
        Several application services are called subsequently from this REST IMS:
          - Firstly, the application msvc -getPersonInfo in LAS is called to get the details from ODE.
          - Secondly, Siebel is called for each party ID returned from LAS. 
            - If LAS returns no party ID, then we skip the "FOR EACH" cycle and go straight to the final response filling it with the data returned from -getPersonInfo.
            - If an error is returned from SBL, then the data for the final response are filled using the response from LAS operation -getPersonInfo.
          - Thirdly, if somewhat complex conditions described in the mapping xlsx are met, then AUS and GAS application services are called; also for each party ID returned from LAS.
      operationId: cus-party-v1-getDetailByPhone
      parameters:
      - $ref: '#/components/parameters/PersonId'
      - $ref: '#/components/parameters/PersonIdType'
      - $ref: '#/components/parameters/SourceIdSystem'
      - $ref: '#/components/parameters/ApplicationStatusSys'
      - $ref: '#/components/parameters/CustomerSegments'
      - $ref: '#/components/parameters/TenantId'
      - $ref: '#/components/parameters/X-Api-Name'
      - $ref: '#/components/parameters/X-Request-Id'
      - $ref: '#/components/parameters/X-Correlation-Id'
      - $ref: '#/components/parameters/X-Request-App'
      - $ref: '#/components/parameters/X-Frontend-App'
      - $ref: '#/components/parameters/X-User-ID'
      - $ref: '#/components/parameters/X-User-Type'
      - $ref: '#/components/parameters/X-Cl-Context-Id'
      responses:
        "200":
          description: OK; successful response from getDetailByPhone
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/GetDetailByPhoneResult'
        default:
          description: Error response in case any exception is thrown.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /cus/party/v1/parties/{lastName}/{firstName}/find-person:
    get:
      summary: FIND-PERSON - Identify a person, find their detail and security tokens.
      description: |
        Identify a person, find their detail and security tokens.<br><br>
        In the request, the person who we want to identify is specified by their name, birth date, birth number and phone number. Based on the information provided in the request, the operation tries to identify the person in MDC, get their detail in SBL and find their security tokens in AUS and GAS.<br><br>
        Following combinations of RQ data can be used to process the identification successfully:
         - FIRST NAME + LAST NAME + BIRTH CODE,
         - FIRST NAME + LAST NAME + BIRTH DATE + PHONE NUMBER,
         - FIRST NAME + LAST NAME + BIRTH DATE + PHONE NUMBER + BIRTH CODE.<br>
        <p>The REST IMS orchestrates several application services (see the orchestration diagram available in this operation's folder).
        High-level orchestration logic is the following:</p>
          <ol>
          <li>try to identify the person in MDC,</li>
          <li>if the person is identified, continue to call SBL,</li>
          <li>if the person is not identified, then create the final response,</li>
          <li>check whether the person is elligible* AND is not a client AND is not a disponent,</li>
          <li>if it is false, then create the final response,</li>
          <li>if it is true, then continue to call AUS and GAS; regarding GAS, based on configuration property "gaas_rewrite", either GAS TIF AS or REST API is called:
            <br>- IF "tifas" => the classic TIF AS A_GAS_00010_GetMobileTokenList_1_1 is called,
            <br>- IF "restapi" => the new REST API gas-integration-v1-getMobileTokens published on Kong API GW is called,</li>
          <li>create the final response.</li>
          </ol>
      operationId: cus-party-v1-findPerson
      parameters:
      - $ref: '#/components/parameters/LastName'
      - $ref: '#/components/parameters/FirstName'
      - $ref: '#/components/parameters/BirthCode'
      - $ref: '#/components/parameters/BirthDate'
      - $ref: '#/components/parameters/PhoneNumber'
      - $ref: '#/components/parameters/X-Api-Name'
      - $ref: '#/components/parameters/X-Request-Id'
      - $ref: '#/components/parameters/X-Correlation-Id'
      - $ref: '#/components/parameters/X-Request-App'
      - $ref: '#/components/parameters/X-Frontend-App'
      - $ref: '#/components/parameters/X-Frontend-Service'
      - $ref: '#/components/parameters/X-User-ID'
      - $ref: '#/components/parameters/X-User-Type'
      responses:
        "200":
          description: OK; successful response from findPerson
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/FindPersonResult'
        default:
          description: Error response in case any exception is thrown.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /cus/party/v1/parties:
    post:
      summary: CREATE - Create a party in CRM (SBL).
      description: |
        The operation can be used to create a party in SBL. 
        
        (To identify the party / to find whether the party already exists or not, consumers are expected to use another REST operation - e.g. cus-party-v1-findPerson or mdc-custom-v1-identityCribisExtended according to their specific use-case - prior to calling this create operation.)
        
        If no error occurs, the response returns the partyId of the newly created party. (After the successful creation of a new party, SBL informs MDC about it via CUS_00012_PartyUpdateNotification.)
        
        The operation allows to create commercial party as well. However, only few attributes are supported by Siebel at the moment. Therefore, in case of a commercial party, only the properties contained in the `basicInfo/commercialPartyDetail` object are mapped; other properties will be ignored (!).
        
        **Orchestration:**

        Based on the presence of `basicInfo/commercialPartyDetail` either A_SBL_00052_PartyCreate_1_0 or A_SBL_00072_CommercialPartyCreate_1_0 is invoked.

      operationId: cus-party-v1-create
      parameters:
      - $ref: '#/components/parameters/X-Api-Name'
      - $ref: '#/components/parameters/X-Request-Id'
      - $ref: '#/components/parameters/X-Correlation-Id'
      - $ref: '#/components/parameters/X-Request-App'
      - $ref: '#/components/parameters/X-Idempotency-Key'
      - $ref: '#/components/parameters/X-Frontend-App'
      - $ref: '#/components/parameters/X-Frontend-Service'
      - $ref: '#/components/parameters/X-User-ID'
      - $ref: '#/components/parameters/X-User-Type'
      - $ref: '#/components/parameters/X-Channel-Code'
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateRequest"
      responses:
        "200":
          description: OK; successful response from cus-party-v1-create.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateResult'
        default:
          description: Error response in case any exception is thrown.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'

  /cus/party/v1/parties/{partyId}/portfolio:
    get:
      summary: This service is used to retrieve the properties of asset management portfolios for a client based on their identifier.
      description: |
        This service is used to retrieve the properties of client's asset management portfolios based on their identifier. By using this service, you can obtain information about the portfolios owned by a client.

        Upon sending a request containing the client identifier, the service will return a list of portfolios associated with the client. Each portfolio is described by various properties.

        This service is useful for obtaining an overview of a client's portfolios and their properties. Based on this information, you can perform further analysis, make decisions, and provide relevant information to the client regarding their investments.
      operationId: cus-party-v1-getAmPortfolio
      parameters:
      - $ref: '#/components/parameters/partyId'
      - $ref: '#/components/parameters/X-Api-Name'
      - $ref: '#/components/parameters/X-Request-Id'
      - $ref: '#/components/parameters/X-Correlation-Id'
      - $ref: '#/components/parameters/X-Request-App'
      - $ref: '#/components/parameters/X-Frontend-App'
      - $ref: '#/components/parameters/X-User-ID'
      - $ref: '#/components/parameters/X-User-Type'
      responses:
        "200":
          description: OK; successful response from getDetailByPhone
          content:
            application/json:
              schema:
                  $ref: '#/components/schemas/GetAmPortfolioResult'
        default:
          description: Error response in case any exception is thrown.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'               

  /cus/party/v1/parties/{partyId}/contacts:
    get:
      summary: GET-CONTACTS - Operation returns Party contacts.
      description: |
        Operation returns all contacts related to given PartyID and given channel.
      operationId: cus-party-v1-getContacts
      parameters:
      # path  
      - $ref: "#/components/parameters/partyId"
      # query
      - $ref: "#/components/parameters/contactChannel"
      # header
      - $ref: "#/components/parameters/X-Api-Name"
      - $ref: "#/components/parameters/X-Request-Id"
      - $ref: "#/components/parameters/X-Correlation-Id"
      - $ref: "#/components/parameters/X-Request-App"
      - $ref: "#/components/parameters/X-Frontend-App"
      - $ref: "#/components/parameters/X-Frontend-Service"
      responses:
        "200":
          description: Successful response of getContacts
          content:
            application/json:
              schema:
            
                $ref: "#/components/schemas/GetContactsResult"
        default:
          description: Error response
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"

  /cus/party/v1/parties/{partyId}/care:
    get:
      summary: GET-CARE-LIST - Operation returns household members related to given party.
      description: |
        Get the data on an employee who cares about the client whose partyId is specified in the request.
        <br><br>Optionally, a few filtering conditions can be set (type of care and whether to return only the positions displayed or not displayed in e-banking).
        <br><br>Orchestration is very simple: this REST IMS is just a technical REST facade of a SBL TIF AS.

      operationId: cus-party-v1-getCareList
         
      parameters:
      # path
        - $ref: "#/components/parameters/partyId"
      # query
        - $ref: "#/components/parameters/CareType"
        - $ref: "#/components/parameters/CareDisplayedInEBanking"
      # header
        - $ref: "#/components/parameters/X-Api-Name"
        - $ref: "#/components/parameters/X-Request-Id"
        - $ref: "#/components/parameters/X-Correlation-Id"
        - $ref: "#/components/parameters/X-Request-App"
        - $ref: "#/components/parameters/X-Frontend-App"
        - $ref: "#/components/parameters/X-Frontend-Service"
        
      responses:
        "200":
          description: Successful response of getCareList
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetCareListResult"
        default:
          description: Error response
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"

  /cus/party/v1/parties/{partyId}/init-channels:
    put:
      summary: INIT-CHANNELS - Initiate processes in MCH regarding various settings in channels (GIB, SPB).
      description: |
        This operation initiates processes in MCH regarding various settings in channels (GIB, SPB), such as authorizations, accesses or preferred logging context.

        **Currently, two business activities are provided** - the import of a client (using A_MCH_00011_ClientImport) and the setting of preferred context for logging in GIB (using A_MCH_00013_SetPreferredContext_1_0). The selection between these activities is based on the CHOICE in the InitChannels operation's request.

        **A. The process of client import** is initialized by the import of customer data from BPM (SBL party). Flow in which MCH processes this request is following:
          1. Import the client from Siebel, create them in MCH.
          2. Channel access settings - internet banking, mobile banking, contact centre.
          3. Setting the client rights mask - clientRights (what rights the user can have at all - rights mask).
          4. Import accounts and set account rights.
          5. Set up signing rights and roles.
          6. User creation.
          7. Assign rights to the user profiles (what rights from the permitted rights of the client are checked/enabled).
          8. Set up the user's access to channels.
          9. Assigning a default security device to the client number (default is partyId from Siebel), based without security methods.

        Login name for the client account is generated automatically by ONB. Default value is client personal identification number (in Czech 'rodné číslo'). If the number is claimed by another client, the system automatically increases the last digit by one (until it reaches an untaken number).
        
        To get the information on the actual state of the process in MCH, the consumer needs to call another service called 'checkChannelStatus'.
        
        **B. The process of setting preferred context for GIB** is initialized by sending two Siebel IDs to MCH - one of them is the Siebel partyId of the GIB user for whom we want to set the preferred context; the other one is the Siebel partyId of the bank's client who will be set as the preferred context.

        The two above mentioned funcionalities might be illustrated by these example use cases:

        **Example use case 1 - Create an FOP (individual enterpreneur) user only with a business bank account:**
          1. In EntParty.InitChannels select complex clientImport in the CHOICE in the operation's RQ and fill in all other necessary details in order to create ONLY business bank account. Application service ClientImport will be called, and requestId (ID of the request for client import) will be returned.
          2. Repeatedly call EntParty.CheckChannelStatus with the requestId to get information on the status of the import process.
          3. When the client is successfully imported, the default setting of preferred GIB context is personnal bank account (which contains no products now), even though the only product which the client has is a business bank account in the business context - this is the default behaviour of MCH.
          4. That is why we need to change the preferred context of the FOP to the business bank account's context. To do so, call EntParty.InitChannels again, but now select complex preferredContext in the CHOICE in the operation's RQ and fill in the FOP's Siebel ID both in element partyId and in preferredPartyId. Application service SetPreferredContext will be called.
        
        **Example use case 2 - Change the preferred context for an already existing client:**
          1. Imagine an accountant who administers business bank accounts for her clients - client A, client B and client C. The accountant has got her own partyId in Siebel (which is linked to her GIB user account), each of clients A, B and C have also got their own partyId in Siebel.
          2. For many years, the accountant has preferred the context of client A after logging into GIB, but now she works much more with client C's business bank account, so she wants to have client C's context as the preferred one = i.e. she wants to see client C's account immediately after logging into GIB.
          3. That is why we need to call EntParty.InitChannels selecting complex preferredContext in the CHOICE in the operation's RQ. As partyId we use the accountants Siebel ID, as preferredPartyId we use client C's Siebel ID.

        **Orchestration** is very simple: based on query parameter "activity" one of two orchestrated MCH application services is called.

      operationId: cus-party-v1-initChannels

      parameters:
      # path
        - name: partyId
          in: path
          description: |
            - If .../QUERY/activity == CLIENT_IMPORT, then fill in Siebel partyId of the client to be imported.
            - If .../QUERY/activity == PREFERRED_CONTEXT, then fill in Siebel partyId of the GIB user for whom we want to set the preferred context.
          required: true
          schema:
            type: string
            maxLength: 15
            example: "********"
      #query
        - name: activity
          in: query
          description: |
            Specifies which business activity we want to initiate in MCH:
              - CLIENT_IMPORT - Create / activate an account in MCH.
              - PREFERRED_CONTEXT - Set the context for GIB which the user prefers.
          required: true
          schema:
            type: string
            enum:
              - CLIENT_IMPORT
              - PREFERRED_CONTEXT
            example: "PREFERRED_CONTEXT"
      # header
        - $ref: "#/components/parameters/X-Api-Name"
        - $ref: "#/components/parameters/X-Request-Id"
        - $ref: "#/components/parameters/X-Correlation-Id"
        - $ref: "#/components/parameters/X-Request-App"
        - $ref: "#/components/parameters/X-Frontend-App"
        - $ref: "#/components/parameters/X-Frontend-Service"
        - name: X-User-Id
          in: header
          description: |
            "cza" prefixed login of the operator (robot) responsible for the request.
          required: true
          example: "cza12345"
          schema:
            type: string
      
      requestBody:
        description: A request data for get client verification.
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/InitChannelsRequest"
      
      responses:
        "200":
          description: Successful response from initChannels.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/InitChannelsResult"
        default:
          description: Error response.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
  
  /cus/party/v1/parties/{partyId}/unife-header:
    get:
      summary: GET-UNIFE-HEADER - The simple operation to get basic and other information for the Unife header based on partyId and userId
      description: |
        The service returns a set of defined party data used in the Unife header (dashboard). 
        This service is produced specifically for the needs of one system, Unife, without any plans for reusability by other systems in the future.
      
      operationId: cus-party-v1-getUnifeHeader
      parameters:
      # path  
        - $ref: "#/components/parameters/PartyId"
      # query
        - $ref: "#/components/parameters/UserId" 
      # header
        - $ref: "#/components/parameters/X-Api-Name"
        - $ref: "#/components/parameters/X-Request-Id"
        - $ref: "#/components/parameters/X-Correlation-Id"
        - $ref: "#/components/parameters/X-Request-App"
        - $ref: "#/components/parameters/X-Frontend-App"
        - $ref: "#/components/parameters/X-Frontend-Service"
      responses:
        "200":
          description: Successful response of getUnifeHeader
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetUnifeHeaderRes"
        "404":
          description: Error response - partyId not found; 
                       In this case, the "code == 404" in the object "reasons".
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"
        default:
          description: Error response
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"

security:
  - BasicAuth: []

components:
  parameters:
    X-Api-Name: 
      name: X-Api-Name
      in: header
      description: ID of the called service, definded by Integration platform RBCZ and is equal to 'operationId:' for this operations (see this .yaml or [kip.rb.cz](http://kip.rb.cz))
      required: true
      example: 'serviceId-operation'
      schema:
        type: string
    X-Request-Id:
      name: X-Request-Id
      in: header
      description: |
        ID of the request, unique to the call, as determined by the initiating (consumer) application. UUID format or RBCZ Message ID convention: application code + 21 numbers.
        (e.g. 'SYS'001207641782552682875)
      required: true
      example: '99391c7e-ad88-49ec-a2ad-99ddcb1f7721'
      schema:
        type: string
        format: uuid
    X-Correlation-Id:
      name: X-Correlation-Id
      in: header
      description: |
        X-Correlation-ID will be used to track requests through all involved services to be able to graph request flows, map how services contribute to response times, identify bottle necks, etc.
        The X-Correlation-ID must be propagated unchanged in any and all downstream service calls (e.g. RBCZ services).
      required: true
      example: '99391c7e-ad88-49ec-a2ad-99ddcb1f7721'
      schema:
        type: string
        format: uuid
    X-Idempotency-Key:
      name: X-Idempotency-Key
      in: header
      description: Unique request identifier to support idempotency.
      example: '99391c7e-ad88-49ec-a2ad-99ddcb1f7721'
      required: true
      schema:
        type: string
        maxLength: 40
    X-Request-App:
      name: X-Request-App
      in: header
      description: ID of the service consumer application that generates the request (RBCZ application code).
      example: 'ABC'
      required: true
      schema:
        type: string
        maxLength: 3
    X-Frontend-App:
      name: X-Frontend-App
      in: header
      description: ID of the service consumer system which originally generated the request. Used only when the service is mediated by another system (RBCZ application code).
      example: 'XYZ'
      schema:
        type: string
        maxLength: 3
    X-Frontend-Service:
      name: X-Frontend-Service
      in: header
      description: ID of the previous level service. Used only when the service is called from another service.  
      example: 'serviceId-operation'
      schema:
        type: string
    X-User-ID:
      name: X-User-ID
      in: header
      description: Unique identification of the user who initiates the request (cza or technical user).
      required: true
      example: "cza12345"
      schema:
        type: string
    X-User-Id:
      name: X-User-Id
      in: header
      description: Unique identification of the user who initiates the request (cza or technical user).
      required: true
      example: "cza12345"
      schema:
        type: string
    X-User-Type:
      name: X-User-Type
      in: header
      description: Additional info for identification internal user (cza...) or external user (technical accounts, 3rd parties or clients) - INT | EXT.
      required: true
      example: "INT"
      schema:
        type: string
        maxLength: 3
        enum:
          - INT
          - EXT
    X-Source-IP:
      name: X-Source-IP
      in: header
      description: IP address if known
      example: *******
      schema:
        type: string
    X-Channel-Code:
      name: X-Channel-Code
      in: header
      description: Identification of the channel from which the request was sent. (If the request does not originate in a specific business channel, use constant "SYS".)
      example: "IBS"
      required: true
      schema:
        type: string
    X-Cl-Context-Id:
      name: X-Cl-Context-Id
      in: header
      description: Identification of the Cashloan context in which the process originates.
      example: "RSTS"
      required: false
      schema:
        type: string
        enum:
          - RSTS
          - RB
        default: "RB"
    partyId:
      name: partyId
      in: path
      description: SBL or other system Party ID identification.
      required: true
      schema:
        type: string   
        example: ********
    PersonId:
      name: personId
      in: path
      description: |
        The value of person identifier; these identifiers can be used:
        - phoneNum = telephone number,
        - partyId = Siebel party ID,
        - sourceId = if sourceId is used, then fill its value in personId and send the ID of the system from which the sourceId comes in sourceIdSystem query parameter; backend application should be able to check this commandatority.
      required: true
      schema:
        type: string
      example: "160999977"
    PersonIdType:
      name: personIdType
      in: query
      description: Type of person identifier.
      required: true
      schema:
        type: string
        enum:
        - PHONENUM
        - PARTYID
        - SOURCEID
      example: SOURCEID
    SourceIdSystem:
      name: sourceIdSystem
      in: query
      description: System from which the value of sourceId is used.Mandatory if personIdType == "SOURCEID".
      required: false
      schema:
        type: string
      example: CSA
    SearchByPhone:
      name: searchByPhone
      in: query
      description: Flag if to search by phone, connected to some authentication token. If false or is not filled, then search in MDC via party details
      required: false
      schema:
        type: boolean    
    contactChannel:
      name: contactChannel
      in: query
      description: Channel (use value "Email" or "Phone/Mobile/Fax") - only corresponding service contacts are returned in response if provided, otherwise all service contacts are returned in response.
      required: false 
      schema:
        type: string
        example: Email
    CheckDigitalChannel:
      name: checkDigitalChannel
      in: query
      description: Flag to call MCH to check whether the party has a digital channel, default behavior is "false"
      required: false
      schema:
        type: boolean
    OtherIdentifiers:
      name: otherIdentifiers
      in: query
      description: Flag for call to MDC to return other identifiers of the party (ie. COCUNUT id), default behavior is "false"
      required: false
      schema:
        type: boolean
    ApplicationStatusSys:
      name: applicationStatusSys
      in: query
      description: |
        It will allow to receive in response even requests in SYS status (by default, SYS requests do not go into response).
      required: false
      schema:
        type: boolean
      example: false
    CustomerSegments:
      name: customerSegments
      in: query
      description: |
        Flag indicating whether to retrieve VIP_CLIENT customer segments.
      required: false
      schema:
        type: boolean
        default: false
      example: false
    TenantId:
      name: tenantId
      in: query
      description: |
        Identification of the tenant (Cashloan context) from which we want to receive the party data.
        RSTS = Data related with building savings.
        RB = Data related with RB tenant.
        ALL = both of the above mentioned.
      required: false
      schema:
        type: string
        enum:
          - RSTS
          - RB
          - ALL
        default: RB
      example: "ALL"    
    LastName:
      name: lastName
      in: path
      required: true
      description: Person's last name.
      schema:
        $ref: "#/components/schemas/LastName"
      example: "Skřivánek"
    FirstName:
      name: firstName
      in: path
      required: true
      description: Person's first name.
      schema:
        $ref: "#/components/schemas/FirstName"
      example: "Rufin"
    BirthCode:
      name: birthCode
      in: query
      description: Person's birth code.
      schema:
        $ref: "#/components/schemas/BirthCode"
      example: "921015/1555"
    BirthDate:
      name: birthDate
      in: query
      description: Person's date of birth.
      schema:
        $ref: "#/components/schemas/BirthDate"
      example: "1992-10-15"
    PhoneNumber:
      name: phoneNumber
      in: query
      description: Person's telephone number.
      schema:
        $ref: "#/components/schemas/PhoneNumber"
      example: "+420606961424"
    maritalStatus:
      name: maritalStatus
      in: query
      description: Marital status of the person (subject party) whose household members we want to search for (backend codebook)
      required: false 
      schema:
        type: string
        example: Married  
    identificationType:
      name: identificationType
      in: query
      description: Type of the identifier.
      required: true
      schema:
        type: string
        enum:
          - PARTYID
    dataSource:
      name: dataSource
      in: query
      description: Data source specification.
      schema:
        type: string
        enum:
          - RBCZ
    CareType:
      name: type
      in: query
      description: Type of care. If the parameter is empty or not present, all care types are returned. SBL LOV RB_PRV_CARE_TYPE or RB_ACC_CARE_TYPE.
      schema:
        type: string
      example: GENBANK
    CareDisplayedInEBanking:
      name: displayedInEBanking
      in: query
      description: Tells whether to return only the care displayed or only the care not displayed in e-banking. If the parameter is empty or not present, then the results for both cases are returned.
      schema:
        type: boolean
    PartyId:
      name: partyId
      in: path
      description: SBL or other system Party ID identification.
      required: true
      example: "********"
      schema:
        type: string  
    UserId:
      name: userId
      in: query
      description: Identification of the user
      required: true
      example: "CZA98765"
      schema:
        type: string 

  securitySchemes:
    BasicAuth:
      type: http
      scheme: basic
  
  schemas:
    GetDocumentsResult:
      type: array
      description: List of party documents.
      items:
        $ref: '#/components/schemas/Documents'
    Documents:
      type: object
      required:
        - "srcReferenceId"
        - "documentId"
        - "documentType"
      properties:
        srcReferenceId:
          type: string
          description: Unique identification of the entity in source system.
        documentId:
          type: string
          description: Document identifier.
        documentType:
          type: string
          description: Identification Document type.
        validFrom:
          type: string
          format: date
          description: Valid from date.
        validTo:
          type: string
          format: date
          description: Valid to date.
        issuer:
          type: string
          description: Issuer.
        issuerCountry:
          type: string
          description: Country of issuer.
        cblCheckStatus:
          type: string
          description: CBL check status.
        cblCheckDate:
          type: string
          format: date
          description: CBL check date.
        unacceptable:
          type: boolean
          description: Unacceptable flag.
        primaryDocument:
          type: boolean
          description: Primary document flag.
        comment:
          type: string
          description: Comment.
        docPersonIdentification:
          type: string
          description: Type of document used for identification of User ( DOCSHOWNFACE2FACE / DOCSHOWNFACE2FACESCAN / DOCNOTSHOWNFACE2FACE ).

    DocumentsCR: #structure of documents used with operation cus-party-v1-create
      type: array
      items: 
        type: object
        properties:
          documentId:
              $ref: "#/components/schemas/DocumentId"
          documentType:
              $ref: "#/components/schemas/DocumentType"
          validity:
              $ref: "#/components/schemas/Validity"
          issuer:
              $ref: "#/components/schemas/Issuer"
          issuerCountry:
              $ref: "#/components/schemas/IssuerCountry"
          primaryDocument:
              $ref: "#/components/schemas/PrimaryDocument"
          comment:
              $ref: "#/components/schemas/Comment"
    
    DocumentsIdentifyRequest: #structure of documents used with operation cus-party-v1-identify
      type: array
      items: 
        type: object
        properties:
          documentId:
              $ref: "#/components/schemas/DocumentId"
          documentType:
              $ref: "#/components/schemas/DocumentType"
          validity:
              $ref: "#/components/schemas/Validity"
          issuer:
              $ref: "#/components/schemas/Issuer"
          issuerCountry:
              $ref: "#/components/schemas/IssuerCountry"
          primaryDocument:
              $ref: "#/components/schemas/PrimaryDocument"
          comment:
              $ref: "#/components/schemas/Comment"

    NetworkBank:
      type: object
      properties:
        nwbCustomerId:
          $ref: "#/components/schemas/NwbCustomerId"
        networkBankId:
          $ref: "#/components/schemas/NetworkBankId"
        segmentNwb:
          $ref: "#/components/schemas/SegmentNwb"
      required:
        - nwbCustomerId
        - networkBankId 

    UpdateRequest:
      type: object    
      required:
        - partyType
      properties:
        partyType:
          $ref: "#/components/schemas/PartyType"
        partyStatus:
          $ref: "#/components/schemas/PartyStatus"
        preferredLanguage:
          $ref: "#/components/schemas/PreferredLanguage"
        segment:
          $ref: "#/components/schemas/Segment"
        subsegment:
          $ref: "#/components/schemas/Subsegment"
        esa95:
          $ref: "#/components/schemas/Esa95"
        privateParty:
          $ref: "#/components/schemas/Person"
        taxation:
          $ref: "#/components/schemas/Taxation"
        aml:
          $ref: "#/components/schemas/Aml"
        sourceOfFunds:
          $ref: "#/components/schemas/SourceOfFunds"
    
    UpdateResult:
      type: object      
      properties:
        partyId:
          $ref: "#/components/schemas/PartyId"

    PartyCategoryRequest:
      type: object
      required:
        - type
        - validFrom
      properties:
        type:
          $ref: '#/components/schemas/PartyCategoryType'
        validFrom:
          $ref: '#/components/schemas/ValidFrom'
        validTo:
          $ref: '#/components/schemas/ValidTo'
        value:
          $ref: '#/components/schemas/CategoryValue'
        categorization:
          $ref: '#/components/schemas/Categorization'
        comment:
          $ref: '#/components/schemas/Comment'

    PartyId:
      type: string
      description: SBL or other system Party ID identification.   
      maxLength: 15
      example: "********"
    RstsPartyId:
      type: integer
      format: int64
      description: Identification of the party in RSTS (CIBIS).   
      example: 160999977
    TenantId:
      type: string
      description: Identification of the tenant (source) of the party detail.
      example: "RSTS"
    MdcId:
      type: string
      description: Identification of the tenant (source) of the party detail.
      example: "********9"
    PartyStatus:
      type: string
      description: Status of the party (ACTIVE, MERGED, DELETED ...).
      example: ACTIVE
    IsClient:
      type: boolean
      description: Is partyid client? (product owner)
      example: "false"
    IsVIP:
      type: boolean
      description: Is partyid VIP?
    IsDisponent:
      type: boolean
      description: Is partyid disponent?
    HasLoan:
      type: boolean
      description: Has partyid got an ACTIVE loan product?
    UsesAuthMethod:
      type: boolean
      description: |
        - TRUE - party has got at least one active authentication method,
        - FALSE - party has got no authentication method which is active or used.
    ScreenSegments:
      type: array
      description: List of screen segments which will be displayed to the client.
      items:
        type: string
    CustomerSegments:
      type: array
      description: List of screen segments which will be displayed to the client.
      items:
        type: string

    Aml:
      type: object      
      properties:
        pepCheck:
          $ref: "#/components/schemas/PepCheck"
        plannedTransactionType:
          $ref: "#/components/schemas/PlannedTransactionType"  

    SourceOfFunds:
      type: string
      description: Type of income - main source of income for client. (SBL application codebook RB_PRV_FUNDSOUR or RB_ACC_FUNDSOUR.)
      example: "BUSINESS"

    PepCheck:
      type: boolean
      description: |
        PepCheck flag; true = the party claimed that they are a politically exposed person, false = the party claimed not to be a politically exposed person.
        (SBL LOV RB_AML_PEP_CHECK; transalted in IMS.)
    PlannedTransactionType:
      type: string
      description: "aml section SBL codebook RB_PLANNED_TRANSACTIONS"    
    Segment:
      type: string
      description: Segment of client UNI_SEGM
      example: PI
    Subsegment:
      type: string
      description: Subsegment of client UNI_SUBSEGM
      example: MCNU
    Esa95:
      type: string
      description: ESA95/ESA2010 (SNA) -  area of client's business focus    
    IsEligible:
      type: boolean
      description: Is partyid eligible?
      example: "true" 
    MarketingConsent:
      type: boolean
      description: has partyid marketing consent?       
    MaritalStatus:
      type: string
      description: Marital status of the person (subject party) whose household members we want to search for (backend codebook)
      example: Married   
    CustomerSince:
      type: string
      format: date
      description: Relationship Start Date; Date when the Customer Party relationship was created.
    CustomerTill:
      type: string
      format: date
      description: Relationship End Date
    PreferredLanguage:
      type: string
      description: Preferred language, CZ|EN. (lovTranslate Language)
      example: "CZ"

    AdditionalInfoPrivateParty:
      type: object
      properties:
        taxResidency:
          $ref: "#/components/schemas/TaxResidency"
        productCustomersWantsUse:
          type: string
          description: Product the customer plans to use.
        pepFlag:
          $ref: "#/components/schemas/PepFlag"
        riskRate:
          type: string
          description: AML Risk rating;  SBL LOV RB_AMLRRATE - for current values please contact AS SBL business owner.
          example: "HIGH"
        iblFlag:
          type: boolean
          description: IBL registered.
          example: false
        fisaFlag:
          type: boolean
          description: AML - FISA.
          example: true
        cblCheckDate:
          type: string
          format: date-time
          description: Date and time of last CBL check.

    AdditionalInfoCompany:
      type: object
      properties:
        taxResidency:
          $ref: "#/components/schemas/TaxResidency"
        pepFlag:
          $ref: "#/components/schemas/PepFlag"

    IdentifyRequest:
      type: object
      properties:        
        searchParty:
          $ref: "#/components/schemas/SearchParty"
        searchPhone:
            $ref: "#/components/schemas/SearchPhone" 
    
    SearchParty:
      type: object      
      properties:
        party:
          $ref: "#/components/schemas/IdentifyParty"
        contacts:
          $ref: "#/components/schemas/ContactsIdentifyRequest" 
        documents:  
          $ref: "#/components/schemas/DocumentsIdentifyRequest"      
    
    SearchPhone:
      description: Phone number of the token (SMSPIN, SMS) (format '+' international format without spaces e.g. +420603808000)
      type: object
      required:
        - phoneNumber
        - tokenType        
      properties:
        phoneNumber:
          $ref: "#/components/schemas/PhoneNumber"
        tokenType:
          $ref: "#/components/schemas/TokenType"         

    TokenType:
      type: string     
      description: type of token

    SecurityRealm:
      type: string 
      description: Security domain  

    CheckDigitalChannel:
      type: boolean
      description: Check if digital channel (true/false) 
    
    IdentifyParty:
      type: object
      properties:
        partyName:        
          $ref: "#/components/schemas/PartyName"
        partyType:
          $ref: "#/components/schemas/PartyType"
        isClient:
          $ref: "#/components/schemas/IsClient"
        isVIP:
          $ref: "#/components/schemas/IsVIP"
        marketingConsent:
          $ref: "#/components/schemas/MarketingConsent"
        customerSince:
          $ref: "#/components/schemas/CustomerSince"
        preferredLanguage:
          $ref: "#/components/schemas/PreferredLanguage"        
        privateParty:
          $ref: "#/components/schemas/PrivatePartyDetail"
        commercialParty:
          $ref: "#/components/schemas/CommercialPartyDetail"  

    IdentifyPartyResult:
      type: object
      properties:
        partyId:        
          $ref: "#/components/schemas/PartyId"
        partyType:
          $ref: "#/components/schemas/PartyType"
        partyStatus:  
          $ref: "#/components/schemas/PartyStatus"
        isClient:
          $ref: "#/components/schemas/IsClient"
        isDisponent:
          $ref: "#/components/schemas/IsDisponent"
        isEligible:
          $ref: "#/components/schemas/IsEligible"
        customerSince:
          $ref: "#/components/schemas/CustomerSince"
        customerTill:
          $ref: "#/components/schemas/CustomerTill"
        digitalChannel:
          $ref: "#/components/schemas/DigitalChannel"  
        identifiers:
          $ref: "#/components/schemas/Identifiers" 
        privateParty:
          $ref: "#/components/schemas/PrivatePartyDetailIdentifyResponse"
        commercialParty:
          $ref: "#/components/schemas/CommercialPartyDetailIdentifyResponse"        

    DigitalChannel: 
      type: boolean
      description: Digital channel (true/false) 

    Identifiers:
      type: array
      items:
       $ref: "#/components/schemas/Identifier"

    Identifier:
      type: object
      properties:
        key:
          $ref: "#/components/schemas/IdKey"
        value:  
          $ref: "#/components/schemas/IdValue"

    IdKey:
      type: string
      example: "COCUNUT"

    IdValue:
      type: string  

    CocunutId:
      type: string
      description: "RB coconut id"
      example: 2843334

    CommercialPartyDetail:
      type: object
      properties:
        companyName:
          $ref: "#/components/schemas/CompanyName"
        companyNameSuffix:
          $ref: "#/components/schemas/CompanyNameSuffix"
        taxId:
          $ref: "#/components/schemas/TaxId"
        registrationNumber:
          $ref: "#/components/schemas/RegistrationNumber"
        birthCode:
          $ref: "#/components/schemas/BirthCode"
        legalForm:
          $ref: "#/components/schemas/LegalForm"
        legalStatus:
          $ref: "#/components/schemas/LegalStatus"
        registeredCompanyName:
          $ref: "#/components/schemas/RegisteredCompanyName"
        registeredCountry:
          $ref: "#/components/schemas/Country"

    CommercialPartyDetailCR:
      type: object
      required:
        - companyName
      properties:
        companyName:
          $ref: "#/components/schemas/CompanyName"
        companyNameSuffix:
          $ref: "#/components/schemas/CompanyNameSuffix"
        taxId:
          $ref: "#/components/schemas/TaxId"
        registrationNumber:
          $ref: "#/components/schemas/RegistrationNumber"
        phoneyRegistrationNumber:
          $ref: "#/components/schemas/PhoneyRegistrationNumber"
        legalForm:
          $ref: "#/components/schemas/LegalForm"
        legalStatus:
          $ref: "#/components/schemas/LegalStatus"
        registeredCompanyName:
          $ref: "#/components/schemas/RegisteredCompanyName"
        registeredCountry:
          $ref: "#/components/schemas/Country"
        birthCode:
          $ref: "#/components/schemas/BirthCode"
        phoneyBirthCode:
          $ref: "#/components/schemas/BirthCodePhoney"
        registryCheck:
          $ref: "#/components/schemas/RegistryCheck"


    CommercialPartyDetailIdentifyResponse:
      type: object
      properties:
        companyName:
          $ref: "#/components/schemas/CompanyName"
        taxId:
          $ref: "#/components/schemas/TaxId"
        registrationNumber:
          $ref: "#/components/schemas/RegistrationNumber"
        legalForm:
          $ref: "#/components/schemas/LegalForm"
        legalStatus:
          $ref: "#/components/schemas/LegalStatus"
        registeredCountry:
          $ref: "#/components/schemas/Country"      
        birthCode:
          $ref: "#/components/schemas/BirthCode"

    LegalForm:  
      type: string
      description: legal form - originaly codebook ACC_LEGFORM
      example: "121"

    LegalStatus:
      type: string  
      description: Basic legal form	FOP/PO (ACC_BLEGFORM)
      example: "PO"

    RegisteredCompanyName:
      type: string  
      description: Company name registered - received from Albertina system
      maxLength: 255
      example: "Obklady a dlažby Klatovy"

    CompanyNameSuffix:
      type: string
      description: Used for additional text printed in the contract after the company name and residence    
      maxLength: 255
      example: "spisová značka: C/74883, vedeno: Krajśký śoud v Ostravě, ze dne: 18.06.2018"

    PartyName:
      type: string
      description: Calculated - (Last name First name) for Comercial same as name
      example: Pepa Štěrba
      maxLength: 255

    VerificationRequest:
      type: object
      required:
        - sourceSystem
        - partyId
        - sessionId
        - document
      properties:
        sourceSystem:
          type: string
          description: PartyID source system.
          example: SBL
        partyId:
          type: string
          description: Party ID from source system.
        sessionId:
          type: string
          description: Verification session identification.
        firstName:
          type: string
          description: First name.
        lastName:
          type: string
          description: Last name
        birthplace:
          type: string
          description: Country and city of birth.
          example: CZ Liberec
        birthCode:
          type: string
          description: Czech Birth Code - only for Czech citizens.
        birthDate:
          type: string
          format: date
          description: Date of birth.
        gender:
          type: string
          description: Gender - M as MALE and F as FEMALE.
        document:
          $ref: "#/components/schemas/Document"

    GetDetailResult:
      type: object
      properties:
        private:
          $ref: "#/components/schemas/Person"
        company:
          $ref: "#/components/schemas/Company"
        contacts:
          $ref: "#/components/schemas/Contacts"

    GetHouseholdMembersListResult:
      type: object
      properties:
        subjectParty:
          $ref: "#/components/schemas/SubjectParty"
        householdMembers:
          $ref: "#/components/schemas/HouseholdMembers"
    
    IdentifyResult:
      type: object
      properties:
        isIdentified:
          $ref: "#/components/schemas/IsIdentified"
        basicInfo:  
          $ref: "#/components/schemas/IdentifyPartyResult"
        additionalInfo:
          $ref: "#/components/schemas/AdditionalInfoResult"
        users:
          $ref: "#/components/schemas/Users"

    AdditionalInfoResult:   
      type: object
      properties:
        segment:
          $ref: "#/components/schemas/Segment"
        subSegment:
          $ref: "#/components/schemas/Subsegment"
        taxResidency:
          $ref: "#/components/schemas/TaxResidency"
        cblCheckDate:
          $ref: "#/components/schemas/CblCheckDate"
        cblCheckResult:
          $ref: "#/components/schemas/CblCheckResult"

    Users:
      type: array
      items: 
        $ref: "#/components/schemas/User"

    User:
      type: object
      properties:
        clientId:
          $ref: "#/components/schemas/ClientId"
        securityRealm:
          $ref: "#/components/schemas/SecurityRealm"
        partyId:
          $ref: "#/components/schemas/PartyId"

    ClientId:
      type: string
      description:  Client username (login) which is used for authentication in Authentication server     

    CblCheckDate:
      type: string
      format: date-time
      description: Date and time of last CBL check; 
       For the operation:|
        - 'insertIdentDocuments' fill this asttribute if partyType == 'PRIVATE' or not exists.
        - 'updateIdentDocuments' fill this asttribute if partyType == 'PRIVATE' or not exists. 
       Is not retured in the response of the operation:|
        - 'insertIdentDocuments' if requested partyType == 'COMMERCIAL'.
        - 'updateIdentDocuments' if requested partyType == 'COMMERCIAL'.
    CblCheckResult:
      type: string
      description: CBL check result

    IsIdentified:
      type: boolean
      description: Is party identified?

    VerificationResult:
      type: object
      required:
        - result
        - scoredFirstName
        - scoredLastName
        - scoredBirthplace
        - scoredBirthCode
        - scoredBirthDate
        - scoredGender
        - scoredDocument
      properties:
        result:
          type: string
          description: Verification result.
          example: OK
        scoredFirstName:
          type: integer
          description: First name verification score.
          example: 100
        scoredLastName:
          type: integer
          description: Last name verification score.
          example: 100
        scoredBirthplace:
          type: integer
          description: Birth place verification score.
          example: 100
        scoredBirthCode:
          type: integer
          description: Birth Code verification score.
          example: 100
        scoredBirthDate:
          type: integer
          description: Date of birth verification score.
          example: 100
        scoredGender:
          type: integer
          description: Gender verification score.
          example: 100
        scoredDocument:
          type: integer
          description: Document verification score.
          example: 100

    GetDetailByPhoneResult:
      type: object
      description: Response body of getDeatilByphone.
      required:
        - firstName
        - lastName
      properties:
        partyId:
          $ref: '#/components/schemas/PartyId'
        rstsPartyId:
          $ref: '#/components/schemas/RstsPartyId'
        tenantId:
          $ref: '#/components/schemas/TenantId'
        mdcId:
          $ref: '#/components/schemas/MdcId'
        birthCode:
          $ref: '#/components/schemas/BirthCode'
        pseudoBirthCode:
          $ref: '#/components/schemas/BirthCodePhoney'
        birthDate:
          $ref: '#/components/schemas/BirthDate'
        partyStatus:
          $ref: '#/components/schemas/PartyStatus'
        firstName:
          $ref: '#/components/schemas/FirstName'
        lastName:
          $ref: '#/components/schemas/LastName'
        gender:
          $ref: '#/components/schemas/Gender'
        citizenship:
          $ref: '#/components/schemas/Citizenship'
        isClient:
          $ref: '#/components/schemas/IsClient'
        isDisponent:
          $ref: '#/components/schemas/IsDisponent'
        isEligible:
          $ref: '#/components/schemas/IsEligible'
        hasLoan:
          $ref: '#/components/schemas/HasLoan'
        usesAuthMethod:
          $ref: '#/components/schemas/UsesAuthMethod'
        screenSegments:
          $ref: '#/components/schemas/ScreenSegments'
        customerSegments:
          $ref: '#/components/schemas/CustomerSegments'
        applications:
            $ref: '#/components/schemas/ApplicationsForCLInfoList'
        authPhoneNum:
          $ref: '#/components/schemas/PhoneNumber'
        email:
          $ref: '#/components/schemas/EmailAddress'

    FindPersonResult:
      type: object
      description: Response body of findPerson.
      properties:
        group:
          $ref: '#/components/schemas/Group'
    Group:
      type: object
      properties:
        person:
          $ref: '#/components/schemas/PersonFP'
    
    PersonFP:
      type: object
      properties:
        partyId:
          $ref: '#/components/schemas/PartyId'
        firstName:
          $ref: '#/components/schemas/FirstName'
        lastName:
          $ref: '#/components/schemas/LastName'
        birthCode:
          $ref: '#/components/schemas/BirthCode'
        birthDate:
          $ref: '#/components/schemas/BirthDate'
        gender:
          $ref: '#/components/schemas/Gender'
        companyName:
          $ref: '#/components/schemas/CompanyName'
        registrationNumber:          
          $ref: '#/components/schemas/RegistrationNumber'
        isClient:
          $ref: '#/components/schemas/IsClient'
        isDisponent:
          $ref: '#/components/schemas/IsDisponent'
        isEligible:
          $ref: '#/components/schemas/IsEligible'
        phoneNumber:
          $ref: '#/components/schemas/PhoneNumber'
        usesAuthMethod:
          $ref: '#/components/schemas/UsesAuthMethod'

    CreateRequest:
      type: object
      required:
        - basicInfo
      properties:
        basicInfo:
          $ref: '#/components/schemas/BasicInfoCR'
        contacts:
          $ref: '#/components/schemas/ContactsCR'
        documents:
          $ref: '#/components/schemas/DocumentsCR'
        networkBank:
          $ref: '#/components/schemas/NetworkBank'

    BasicInfoCR:
      type: object
      description: |
        **Either `privatePartyDetail` XOR `commercialPartyDetail` MUST be sent in the request.**
      properties:
        customerSince:
          $ref: "#/components/schemas/CustomerSince"
        preferredLanguage:
          $ref: "#/components/schemas/PreferredLanguage"
        privatePartyDetail:
          $ref: "#/components/schemas/PrivatePartyDetail"
        commercialPartyDetail:
          $ref: "#/components/schemas/CommercialPartyDetailCR"

    PrivatePartyDetail:
      type: object
      properties:
        titlesBefore:
          $ref: "#/components/schemas/TitlesBefore"
        firstName:
          $ref: "#/components/schemas/FirstName"
        middleNames:
          $ref: "#/components/schemas/MiddleNames"
        lastName:
          $ref: "#/components/schemas/LastName"
        titlesAfter:
          $ref: "#/components/schemas/TitlesBehind"
        gender:
          $ref: "#/components/schemas/Gender"
        birthInfo:
          $ref: "#/components/schemas/BirthInfo"
        citizenship:
          $ref: "#/components/schemas/Citizenship"
        secondaryCitizenship:
          $ref: "#/components/schemas/SecondaryCitizenship"
        pepFlag:
          $ref: "#/components/schemas/PepFlag"
        taxResidency:
          $ref: "#/components/schemas/TaxResidency"
      required:
        - firstName
        - lastName
        - gender

    PrivatePartyDetailIdentifyResponse:
      type: object
      properties:
        titlesBefore:
          $ref: "#/components/schemas/TitlesBefore"
        firstName:
          $ref: "#/components/schemas/FirstName"
        middleNames:
          $ref: "#/components/schemas/MiddleNames"
        lastName:
          $ref: "#/components/schemas/LastName"
        birthLastName:
          $ref: "#/components/schemas/LastName"  
        titlesAfter:
          $ref: "#/components/schemas/TitlesBehind"
        isStaff:
          $ref: "#/components/schemas/IsStaff"  
        citizenship:
          $ref: "#/components/schemas/Citizenship"  
        gender:
          $ref: "#/components/schemas/Gender"
        birthInfo:
          $ref: "#/components/schemas/BirthInfoIdentifyResult"
    
    BirthInfo:
      type: object
      properties:
        birthCode:
          $ref: "#/components/schemas/BirthCode"
        phoneyBirthCode:
          $ref: "#/components/schemas/BirthCodePhoney"
        countryOfBirth:
          $ref: "#/components/schemas/BirthCountry"
        cityOfBirth:
          $ref: "#/components/schemas/BirthCity"
        birthDate:
          $ref: "#/components/schemas/BirthDate"

    BirthInfoIdentifyResult:
      type: object
      properties:
        birthDate:
          $ref: "#/components/schemas/BirthDate"  
        birthCode:
          $ref: "#/components/schemas/BirthCode"
        countryOfBirth:
          $ref: "#/components/schemas/BirthCountry"
        cityOfBirth:
          $ref: "#/components/schemas/BirthCity"

    CreateResult:
      type: object
      description: Response body of create.
      properties:
        partyId:
          $ref: '#/components/schemas/PartyId'
      required:
        - partyId

    ApplicationsForCLInfoList:
      type: array
      description: List of client's applications.
      items:
        $ref: '#/components/schemas/ApplicationsForCLInfoItem'
    
    ApplicationsForCLInfoItem:
      type: object
      properties:
        applicationStatus:
          type: string
          description: Application status, backend codebook APPL_STAT.
        busApplId:
          type: string
          description: Business ID of the application.
        applicationKey:
          type: integer
          description: ID (primary key) of the application.
          format: int64
        applicationType:
          type: string
          description: Backend codebook APPL_TP.
        applicationHash:
          type: string
          description: Hash (in case of using in links).
        businessProductType:
          type: string
          description: Application Bussines product type identification, backend codebook BUS_PROD_SUB_TP.
          example: RML
        businessProductSubtype:
          type: string
          description: Identification of the application product type, backend codebook BUS_PROD_SUB_TP.
        validTo:
          type: string
          description: Specifies the end of application validity.
          format: date-time
        firstTouchPoint:
          type: string
          description: Application First Touch CUS_00012_PartyUpdateNotification
          example: "18000000"
        firstTouchPointOwner:
          type: string
          description: Application First Touch Point Owner.
        distributionChannel:
          type: string
          description: Application Distribution Channel Identifier, backend codebook CNL.
          example: WEB

    Document:
      type: object
      description: Document for verification.
      properties:
        documentType:
          type: string
          description: Document type.
        documentId:
          type: string
          description: Document ID.

    TaxResidency:
      type: string
      description: Country of tax residency. ISO 3166-1 alpha-2 standard codes. (lovTranslate GENERAL_CNTR)
    TaxId:
      type: string
      description: Tax identification number (DIČ)
      example: "CZ********"
    BeneficialStatement:
      type: boolean
      description: The value will be true for Party who presented "Beneficial owner statement" document and has Tax domicile <> Czech Republic.  
    TaxJurisdiction:
      type: string
      description: tax jurisdiction 
    Documentation:    
      type: string
      description: Type of FATCA form document e.g. W8BEN.
    FatcaDocProvidedOn:
      type: string
      format: date
      description: CRM has to fill in date when documentation has been provided by the client      
    FatcaActionDone:
      type: string
      format: date
      description: FATCA data - "Datum FATCA dokončeno"/"FATCA done date"
    FatcaActionDoneFlag:
      type: boolean      
    SourceCode:
      type: string      
      description: tax data   
    Tin:
      type: string
      description: tin           
    TinType:
      type: string
      description: type of tin 
    CrsSignatureDate:
      type: string
      format: date
    FutureProductOwner:
      type: boolean            
    ActivityDue:
      type: string
      format: date      
    CureFlag:
      type: boolean      
      description: This flag overrules all indicias except the tax residences defined by self-certification.   
    TaxIdNumber:
      type: string
      description: DIČ / TIN          
    Comment:
      type: string
      maxLength: 255
      description: Free text note.
    EntityBusinessStatusCode:
      type: string
      description: LOV CrsBusinessEntityStatus

    Taxation:
      type: object
      description: taxation data
      properties:
        taxResidency:
          $ref: "#/components/schemas/TaxResidency"
        taxId:
          $ref: "#/components/schemas/TaxId"
        beneficialStatement:
          $ref: "#/components/schemas/BeneficialStatement"
        taxJurisdiction:
          $ref: "#/components/schemas/TaxJurisdiction"    
        fatca:
          $ref: "#/components/schemas/Fatca"
        crs:
          $ref: "#/components/schemas/Crs"

    Fatca:
      type: object
      description: fatca data
      properties:
        documentation:
          $ref: "#/components/schemas/Documentation"
        docProvidedOn:
          $ref: "#/components/schemas/FatcaDocProvidedOn"
        actionDone:
          $ref: "#/components/schemas/FatcaActionDone"
        actionDoneFlag:
          $ref: "#/components/schemas/FatcaActionDoneFlag"
        sourceCode:
          $ref: "#/components/schemas/SourceCode"
        tin: 
          $ref: "#/components/schemas/Tin"
        tinType: 
          $ref: "#/components/schemas/TinType"

    Crs:
      type: object
      description: crs data
      properties:
        signatureDate:
          $ref: "#/components/schemas/CrsSignatureDate"
        futureProductOwner:
          $ref: "#/components/schemas/FutureProductOwner"
        activityDue:
          $ref: "#/components/schemas/ActivityDue"
        cureFlag:
          $ref: "#/components/schemas/CureFlag"
        taxResidencyCountry:
          $ref: "#/components/schemas/Country"
        sourceCode:
          $ref: "#/components/schemas/SourceCode"
        taxIdentificationNumber: 
          $ref: "#/components/schemas/Tin"
        entityBusinessStatustinType: 
          $ref: "#/components/schemas/TinType"

    Person:
      type: object
      description: "Detail data about private customer."
      properties:
        titlesBefore:
          $ref: "#/components/schemas/TitlesBefore"
        firstName:
          $ref: "#/components/schemas/FirstName"
        middleNames:
          $ref: "#/components/schemas/MiddleNames"
        lastName:
          $ref: "#/components/schemas/LastName"
        titlesBehind:
          $ref: "#/components/schemas/TitlesBehind"
        birthDate:
          $ref: "#/components/schemas/BirthDate"
        birthCode:
          $ref: "#/components/schemas/BirthCode"
        birthCodePhoney:
          $ref: "#/components/schemas/BirthCodePhoney"
        birthCountry:
          $ref: "#/components/schemas/BirthCountry"
        birthCity:
          $ref: "#/components/schemas/BirthCity"
        birthplace:
          $ref: "#/components/schemas/Birthplace"
        birthLastName:
          $ref: "#/components/schemas/BirthLastName"
        gender:
          $ref: "#/components/schemas/Gender"
        isClient:
          $ref: "#/components/schemas/IsClient"
        isEligible:
          $ref: "#/components/schemas/IsEligible"
        isStaff:
          $ref: "#/components/schemas/IsStaff"
        citizenship:
          $ref: "#/components/schemas/Citizenship"
        preferredLanguage:
          $ref: "#/components/schemas/PreferredLanguage"
        additionalInfo:
          $ref: "#/components/schemas/AdditionalInfoPrivateParty"
    
    Company:
      type: object
      description: "Detail data about company (business) customer."
      properties:
        partyShortName:
          $ref: "#/components/schemas/PartyShortName"
        partyStatus:
          $ref: "#/components/schemas/PartyStatus"
        statusDate:
          $ref: "#/components/schemas/StatusDate"
        isClient:
          $ref: "#/components/schemas/IsClient"
        isVIP:
          $ref: "#/components/schemas/IsVIP"
        isEligible:
          $ref: "#/components/schemas/IsEligible"
        customerSince:
          $ref: "#/components/schemas/CustomerSince"
        customerTill:
          $ref: "#/components/schemas/CustomerTill"
        leavingReason:
          $ref: "#/components/schemas/LeavingReason"
        preferredLanguage:
          $ref: "#/components/schemas/PreferredLanguage"
        companyName:
          $ref: "#/components/schemas/CompanyName"
        companyNameSuffix:
          $ref: "#/components/schemas/CompanyNameSuffix"
        taxId:
          $ref: "#/components/schemas/TaxId"
        registrationNumber:
          $ref: "#/components/schemas/RegistrationNumber"
        phoneyRegistrationNumber:
          $ref: "#/components/schemas/PhoneyRegistrationNumber"
        birthCode:
          $ref: "#/components/schemas/BirthCode"
        legalForm:
          $ref: "#/components/schemas/LegalForm"
        legalStatus:
          $ref: "#/components/schemas/LegalStatus"
        registeredCompanyName:
          $ref: "#/components/schemas/RegisteredCompanyName"
        registeredCountry:
          $ref: "#/components/schemas/RegisteredCountry"
        plannedTransactions:
          $ref: "#/components/schemas/PlannedTransactions"
        additionalInfo:
          $ref: "#/components/schemas/AdditionalInfoCompany"
    
    Contacts:
      type: object
      description: List of customer contacts like e-mail address, phone numbers etc.
      properties:
        address:
          type: array
          items:
            $ref: "#/components/schemas/Address"
        phone:
          type: array
          items:
            $ref: "#/components/schemas/Phone"
        email:
          type: array
          items:
            $ref: "#/components/schemas/Email"
    
    ContactsCR: #structure of contacts used with operation cus-party-v1-create
      type: object
      description: List of contacts - mail addresses, telephone numbers and e-mail addresses.
      properties:
        addresses:
          type: array
          items:
            $ref: "#/components/schemas/AddressCR"
        phones:
          type: array
          items:
            $ref: "#/components/schemas/PhoneCR"
        emails:
          type: array
          items:
            $ref: "#/components/schemas/EmailCR"

    ContactsIdentifyRequest: #structure of contacts used with operation cus-party-v1-identify
      type: object
      description: List of contacts - mail addresses, telephone numbers and e-mail addresses.
      properties:
        addresses:
          type: array
          items:
            $ref: "#/components/schemas/AddressCR"
        phones:
          type: array
          items:
            $ref: "#/components/schemas/PhoneCR"
        emails:
          type: array
          items:
            $ref: "#/components/schemas/EmailCR"        
        urls:
          type: array
          items:
            $ref: "#/components/schemas/Url"         
    
    Address:
      type: object
      properties:
        addressName:
          $ref: "#/components/schemas/AddressName"
        addressType:
          $ref: "#/components/schemas/AddressType"
        addressId:
          $ref: "#/components/schemas/AddressId"
        addressee:
          $ref: "#/components/schemas/Addressee"
        additionalInfo:
          $ref: "#/components/schemas/AdditionalInfoAddr"
        street:
          $ref: "#/components/schemas/Street"
        streetNumber:
          $ref: "#/components/schemas/StreetNumber"
        city:
          $ref: "#/components/schemas/City"
        postalCode:
          $ref: "#/components/schemas/PostalCode"
        country:
          $ref: "#/components/schemas/Country"
        isResidency:
          $ref: "#/components/schemas/IsResidency"
        isPreferred:
          $ref: "#/components/schemas/IsPreferred"
        undeliverableFlag:
          $ref: "#/components/schemas/UndeliverableFlag"
    
    AddressCR: #structure of address used with operation cus-party-v1-create
      type: object
      properties:
        addressName:
          $ref: "#/components/schemas/AddressName"
        addressType:
          $ref: "#/components/schemas/AddressType"
        addressee:
          $ref: "#/components/schemas/Addressee"
        additionalInfo:
          $ref: "#/components/schemas/AdditionalInfoAddr"
        street:
          $ref: "#/components/schemas/Street"
        number:
          $ref: "#/components/schemas/StreetNumber"
        city:
          $ref: "#/components/schemas/City"
        postalCode:
          $ref: "#/components/schemas/PostalCode"
        country:
          $ref: "#/components/schemas/Country"
        preferred:
          $ref: "#/components/schemas/IsPreferred"
        classification:
          $ref: "#/components/schemas/ContactClassification"
        comment:
          $ref: "#/components/schemas/Comment"
      required:
        - addressType
        - addressee
        - number
        - city
        - postalCode
        - country
        - preferred

    SimpleAddress:
      type: string 
      description: Simple address in one string
      example: Hvezdova 1716/2B, 140 00, Praha 4     

    Phone:
      type: object
      properties:
        contactId:
          $ref: "#/components/schemas/ContactId"
        mediumType:
          $ref: "#/components/schemas/ContactType"
        phone:
          $ref: "#/components/schemas/PhoneNumber"
        phoneNumberType:
          $ref: "#/components/schemas/PhoneNumberType"
        preferred:
          $ref: "#/components/schemas/Preferred"
        undeliverableFlag:
          $ref: "#/components/schemas/UndeliverableFlag"

    PhoneCR: #structure of phone used with operation cus-party-v1-create
      type: object
      properties:
        contactName:
          $ref: "#/components/schemas/ContactName"
        contactType:
          $ref: "#/components/schemas/ContactType"
        number:
          $ref: "#/components/schemas/PhoneNumber"
        preferred:
          $ref: "#/components/schemas/Preferred"
        classification:
          $ref: "#/components/schemas/ContactClassification"
        comment:
          $ref: "#/components/schemas/Comment"
      required:
        - preferred
    
    Email:
      type: object
      properties:
        contactId:
          $ref: "#/components/schemas/ContactId"
        email:
          $ref: "#/components/schemas/EmailAddress"
        emailAddressType:
          $ref: "#/components/schemas/EmailAddressType"
        preferred:
          $ref: "#/components/schemas/Preferred"
        undeliverableFlag:
          $ref: "#/components/schemas/UndeliverableFlag"
    
    EmailCR: #structure of phone used with operation cus-party-v1-create
      type: object
      properties:
        contactName:
          $ref: "#/components/schemas/ContactName"
        contactType:
          $ref: "#/components/schemas/ContactType"
        address:
          $ref: "#/components/schemas/EmailAddress"
        preferred:
          $ref: "#/components/schemas/Preferred"
        classification:
          $ref: "#/components/schemas/ContactClassification"
        comment:
          $ref: "#/components/schemas/Comment"
      required:
        - preferred

    Url: 
      type: object
      properties:
        srcReferenceId:
          $ref: "#/components/schemas/SrcReferenceId"
        contactName:
          $ref: "#/components/schemas/ContactName"
        contactType:
          $ref: "#/components/schemas/ContactType"
        address:
          type: string
          description: A contact address (email, URL ..)
          maxLength: 100
        preferred:
          $ref: "#/components/schemas/IsPreferred"
        classification:
          $ref: "#/components/schemas/Classification"
        comment:  
          $ref: "#/components/schemas/Comment"
      
    Classification:
          type: string
          description: A classification - home/work (PRV_CONTCLASSIF)     

    SrcReferenceId: 
      type: string
      description: Unique identification of the entity in source system.
      example: "1-GULAZ"

    SubjectParty:
      type: object
      properties:

        partyId:
          $ref: "#/components/schemas/PartyId"
        lastName:
          $ref: "#/components/schemas/LastName"
        gender:
          $ref: "#/components/schemas/Gender"
        birthDate:
          $ref: "#/components/schemas/BirthDate"
        address:
          $ref: "#/components/schemas/SimpleAddress"

    HouseholdMembers:
      type: array
      items:
       $ref: "#/components/schemas/HouseholdMember"

    HouseholdMember:
      type: object
      properties:
        relationType:
          $ref: "#/components/schemas/RelationType"
        partyId:
          $ref: "#/components/schemas/PartyId"
        lastName:
          $ref: "#/components/schemas/LastName"
        gender:
          $ref: "#/components/schemas/Gender"
        birthDate:
          $ref: "#/components/schemas/BirthDate"
        address:
          $ref: "#/components/schemas/SimpleAddress"      

    PartyShortName:
      type: string
      description: A Shortcut for the Party, calculated by Siebel.
    StatusDate:
      type: string
      format: date-time
      description: Date and time of the last update of Party Status.
    LeavingReason:
      type: string
      description: Reason for leaving the bank
    PhoneyRegistrationNumber:
      type: string
      description: Pseudo registration number.
    RegisteredCountry:
      type: string
      description: Company name registered - received from Albertina system.
      example: "CZ"
    PlannedTransactions:
      type: string
      description: Planned transactions.
    RelationType:
      type: string
      description: Type of relationship between the subject party's and the household member
      example: Parent
    AddressId:
      type: string
      description: Unique identification of the address.
    AddressName:
      type: string
      maxLength: 30
      description: |
        User definable mail address name.
        Czech description: Pro účely TRS, kde je ulice a číslo v jednom atributu, může být mapováno sem.
      example: "Vinohradská 420/5"
    AddressType:
      type: string
      maxLength: 30
      description: Type of mail address. RESIDENCY|MAIL1|MAIL2|...|MAIL9|ACCOUNT_OWNER|CARD_DELIVERY
      example: "RESIDENCY"
    Addressee:
      type: string
      maxLength: 255
      description: Mail addressee full name.
      example: "Josef Novák"
    AdditionalInfoAddr:
      type: string
      maxLength: 255
      description: Additional address information.
      example: "U paní Svobodové"
    Street:
      type: string
      maxLength: 200
      description: Street name. If the customer has got a P.O.Box, it is supposed to be sent to SBL through this element.
      example: "Vlnitá"
    PartyType:
      type: string
      description: Type of party - PRIVATE or COMMERCIAL; 
      example: "PRIVATE"
    StreetNumber:
      type: string
      maxLength: 15
      description: Building number/street number
      example: "327/25A"
    City:
      type: string
      maxLength: 50
      description: City name
      example: "Slaný"
    PostalCode:
      type: string
      maxLength: 30
      description: ZIP code
      example: "27401"
    Country:
      type: string
      maxLength: 30
      description: Country, ISO 3166-1 alpha-2 standard codes (lovTranslate GENERAL_CNTR)
      example: "CZ"
    IsResidency:
      type: boolean
      description: Is residency address, values True/False.
    IsPreferred:
      type: boolean
      description: Preferred address, vaslues true/false
      example: "true"
    ContactClassification:
      type: string
      maxLength: 30
      description: Distinction of the contact home/work. HOME|WORK.
      example: "HOME"
    UndeliverableFlag:
      type: boolean
      description: Undeliverable flag. If the flag is set to "Y" the address is marked as undeliverable. Otherwise the address is ok.
    ContactId:
      type: string
      description: Unique identification of the contact (email, address or phone number)
    ContactName:
      type: string
      maxLength: 30
      description: User definable contact name (for email or address)
      example: "tel1"
    ContactType:
      type: string
      maxLength: 30
      description: Siebel technical field differentiating records in the same physical database table. EMAIL_ADDRESS|FAX_NUMBER|FOUREYESCHECK_NUMBER|LANDLINE_NUMBER|MOBILE_NUMBER.
    PhoneNumber:
      type: string
      description: Phone number.
      example: "+420606961424"
    PhoneNumberType:
      type: string
      description: Contact number type (FAX1.., MOBILE1.., PHONE1..)
      example: "MOBILE1"
    EmailAddressType:
      type: string
      description: Email address type (EMAIL1, EMAIL2,...).
    Preferred:
      type: boolean
      description: Flag if the contact type is preferred by customer.
    EmailAddress:
      type: string
      maxLength: 100
      description: E-mail address
      example: <EMAIL>
    TitlesBefore:
      type: string
      description: Titles in front of the name, e.g. prof., Ing., MUDr. etc.
      example: "Ing."
    FirstName:
      type: string
      maxLength: 50
      description: First name
      example: "Josef"
    MiddleNames:
      type: string
      description: Set of Middle names
      example: "Antonín František"
    LastName:
      type: string
      maxLength: 50
      description: Last name
      example: "Novák"
    TitlesBehind:
      type: string
      maxLength: 30
      description: Titles after the name, e.g. Ph.D., CSc. etc.
      example: "CSc."
    BirthDate:
      type: string
      format: date
      description: Date of birth
      example: "1992-10-15"
    BirthCode:
      type: string
      description: Standard Czech birth code; only for Czech or Slovak citizens.
      example: "921015/1555"
    BirthCodePhoney:
      type: string
      description: Phoney birth code. Used instead of a standard Czech or Slovak birth code if it is not available.
      example: "971231"
    BirthCountry:
      type: string
      description: Country of birth. ISO 3166-1 alpha-2 standard codes. (lovTranslate GENERAL_CNTR)
    BirthCity:
      type: string
      description: City of birth.
    Birthplace:
      type: string
      description: Country and city of birth.
      example: CZ Liberec
    BirthLastName:
      type: string
      description: Birth last name
    Gender:
      type: string
      description: Gender, "M" for male, "F" for female.
      example: "M"
    IsStaff:
      type: boolean
      description: Flag if private party record is RBCZ employee.
    Citizenship:
      type: string
      description: Citizenship. ISO 3166-1 alpha-2 standard codes. (lovTranslate GENERAL_CNTR)
    SecondaryCitizenship:
      type: string
      description: Secondary citizenship. ISO 3166-1 alpha-2 standard codes. (lovTranslate GENERAL_CNTR)
    PepFlag:
      type: boolean
      description: Indication whether the party is a politically exposed person.
      example: false
    CompanyName:
      type: string
      description: Name of the company.
      example: "Obklady a dlažby Klatovy"
    RegistrationNumber:
      type: string
      description: Commercial identification number of the company (IČO)
      example: "CZ********"
    DocumentId:
      type: string
      maxLength: 30
      description: Document identifier
      example: "RB455678"
    DocumentType:
      type: string
      maxLength: 30
      description: Type of identification document. SBL codebooks RB_ACC_PIDOCT and RB_PRV_PIDOCT. (lovTranslate UNI_PIDOCT)
      example: "P01"
    ValidFrom:
      type: string
      format: date
      description: Valid from date
    ValidTo:
      type: string
      format: date
      description: Valid to date
    Issuer:
      type: string
      maxLength: 100
      description: Identification document issuer.
      example: "Praha"
    IssuerCountry:
      type: string
      maxLength: 30
      description: Country of the identification document issuer. ISO 3166-1 alpha-2 standard codes. (lovTranslate GENERAL_CNTR)
      example: "CZ"
    PrimaryDocument:
      type: boolean
      description: Primary document flag (preffered document).
      example: "true"
    NwbCustomerId:
      type: string
      minLength: 1
      maxLength: 15
      description: |
        NWB_ID of party.
        Czech description: Unikátne ID klienta z NWB - pouzije sa externy identifikator.
    NetworkBankId:
      type: string
      minLength: 1
      maxLength: 50
      description: |
        "TBSK" ... etc.
        Czech description: networkBankId	string[35]	Skratka NWB (TBSK, ,…) vznikne nove ciselnikove pole.
    SegmentNwb:
      type: string
      minLength: 1
      maxLength: 50
      description: Segment NWB.

    Validity:
      type: object
      properties:
        validFrom:
          $ref: "#/components/schemas/ValidFrom"
        validTo:
          $ref: "#/components/schemas/ValidTo"
    
    GetAmPortfolioResult:
      type: object
      required:
        - "partyId"
      properties:
        partyId:
          $ref: "#/components/schemas/PartyId"
        portfolio:
          type: array
          items:
            $ref: "#/components/schemas/Portfolio"

    Portfolio:
      type: object
      properties:
        externalId:
          type: string
          description: ExternalId of portfolio
        name:
          type: string
          description: Name of portfolio
        contractTypeExternalId:
          type: string
          description: ExternalId of contract type
        portfolioStatus:
          type: string
          description: Status of portfolio
        createDate:
          type: string
          format: date
          description: Portfolio create date
          example: 2024-03-03
        validFrom:
          type: string
          format: date
          description: Portfolio valid from date
          example: 2024-03-04
        investmentRiskStrategyId:
          type: string
          description: Investment risk strategy identification number
        minimalTimeHorizon:
          type: number
          description: Minimal time horizon for portfolio
        strategyName:
          type: string
          description: Strategy name for portfolio
        mifidSuitabilities:
          type: string
          description: What type of investor is the strategy suitable for
        description:
          type: string
          description: The description of the strategy
        sfdr:  
          type: integer
          description: The SFDR (Sustainable Finance Disclosure Regulation) is a European regulation that defines three levels of sustainability according to Articles 6, 8 and 9. The data can take on one of the following values, 0 / 6 / 8 / 9, meaning
        esgSfdr:
          type: number
          description: Minimum level of sustainability (SFDR) that investments should contain (in percentage).
        esgSfdrCategory:
          type: string
          description: It will be possible to enter an enumeration of the values E, S, G, while the value or combination of values represents the area/areas of sustainability on which the given investment instrument = portfolio AM is focused

    PartyCategoryType:
      type: string
      maxLength: 30
      description: Type of the category. Selectable from LOV. (UNI_PCATT)
      example: PRODUCT_PAYLATER

    CategoryValue:
      type: string
      maxLength: 100
      description: Value of category - free text

    Categorization:
      type: string
      maxLength: 30
      description: A value of the category. Selectable from LOV (UNI_CTGZATION)
      example: PRODUCT_PAYLATER_Y

    GetContactsResult:
      type: object
      description: Response body of getContacts.
      properties:
        contacts:
          $ref: "#/components/schemas/ContactsArray"    
          
    ContactsArray:
      type: array
      description: Collection of the contacts.
      items:
        $ref: "#/components/schemas/ContactsItem"

    ContactsItem:
      type: object
      required:
        - recordId
        - lastUpdated
        - modifiedBy
        - contact
        - contactChannel
        - contactType
        - purpose
      properties:
        recordId:
          type: string
          maxLength: 15
          description: Internal unique Id of the record
          example: 1-H19CI4
        lastUpdated:
          type: string
          format: date-time
          description: Last updated date
          example: 05/14/2024 13:22:25
        isActive:
          type: boolean  
          description: To be verified flag
        isPrimary:
          type: boolean  
          description: Preffered flag
        modifiedBy:
          type: string
          maxLength: 50
          description: Last modified by
          example: CZA4037E
        contact:
          type: string
          maxLength: 255
          description: Based on Channel Mobile phone Number, Telephone Number, Fax, Email or Residential Address
          example: <EMAIL>
        contactChannel:
          type: string
          maxLength: 50
          description: Channel (email/fax/phone/mobile/address)
          example: Email
        comment:
          type: string
          maxLength: 255
          description: Comment
          example: Poznámka
        contactType:
          type: string
          maxLength: 100
          description: Based on Channel. Detail  Type of the channel (mobile1/mobile2/..)
          example: EMAIL1
        purpose:
          type: string
          maxLength: 50
          description: Purpose of a contact
          example: ContactPurpose_02
        relatedAccountPartyId:
          type: string
          maxLength: 50
          description: Unique Commercial Party Identifier
          example: 1109850
        contactFirstName:
          type: string
          maxLength: 50
          description: First Name
          example: Dřímal
        relatedContactPartyId:
          type: string
          maxLength: 100
          description: Unique Private Party Identifier
          example: ********
        contactLastName:
          type: string
          maxLength: 50
          description: Last Name
          example: Trpaslík 

    GetCareListResult:
      type: object
      required:
        - partyId
        - care
      properties:
        partyId:
          $ref: "#/components/schemas/PartyId"
        care:
          $ref: "#/components/schemas/CareList"

    CareList:
      type: array
      items:
        $ref: "#/components/schemas/CareItem"

    CareItem:
      type: object
      required:
        - careType
        - division
        - position
      properties:
        careType:
          type: string
          maxLength: 30
          description: Type of care. (SBL LOV RB_PRV_CARE_TYPE or RB_ACC_CARE_TYPE.)
          example: GENBANK
        primaryCare:
          type: boolean
          description: Flag stating which party care is primary / profitability relevant.
        displayedInEBanking:
          type: boolean
          description: Tells whether the care is displayed in e-banking or not.
        comment:
          type: string
          maxLength: 255
          description: Comment.
        firstName:
          type: string
          maxLength: 50
          description: First name of the bank's employee.
        lastName:
          type: string
          maxLength: 50
          description: Last name of the bank's employee.
        userId:
          type: string
          maxLength: 30
          description: User ID of the bank's employee (begining with "cza").
        division:
          $ref: "#/components/schemas/CareDivision"
        position:
          $ref: "#/components/schemas/CarePosition"
        contacts:
          $ref: "#/components/schemas/CareContacts"
        
    CareDivision:
      type: object
      required:
        - divisionId
      properties:
        divisionId:
          type: string
          maxLength: 15
          description: Organization unit ID. (SBL foreign key to the record of Organization Unit entity to which the position belongs.)
          example: 1-A1234
        divisionName:
          type: string
          maxLength: 100
          description: Organization unit name.
        divisionSol:
          type: string
          maxLength: 30
          description: Organization unit SOL.

    CarePosition:
      type: object
      required:
        - positionId
        - positionType
        - positionNumber
      properties:
        positionId:
          type: string
          maxLength: 15
          description: Position ID.
          example: 1-A1234
        positionType:
          type: string
          maxLength: 30
          description: Position type name. (SBL LOV RB_POSITION_TYPE.)
          example: HYTEAM
        positionNumber:
          type: string
          maxLength: 30
          description: Position serial number. (SBL LOV RB_POSITION_SER_NUM.)
          example: "55"
        parentPositionId:
          type: string
          maxLength: 15
          description: Parent position ID. (SBL foreign key to Parent Position.)
          example: 1-A1234

    CareContacts:
      type: object
      properties:
        phoneNumber:
          $ref: "#/components/schemas/PhoneNumber"
        emailAddress:
          $ref: "#/components/schemas/EmailAddress"

    InitChannelsRequest:
      type: object
      properties:
        clientImport:
          $ref: "#/components/schemas/ClientImport"
        preferredContext:
          $ref: "#/components/schemas/PreferredContext"

    ClientImport:
      type: object
      description: Mandatory if .../QUERY/activity == CLIENT_IMPORT.
      required:
        - basicInfo
      properties:
        basicInfo:
          $ref: "#/components/schemas/BasicInfoClImport"
        bankAccounts:
          $ref: "#/components/schemas/BankAccountsArray"
        authMethods:
          $ref: "#/components/schemas/AuthMethodsArray"

    BasicInfoClImport:
      type: object
      required:
        - disponentId
        - authorizationType
        - partyType
      properties:
        disponentId:
          type: string
          description: Siebel partyId of the user to be imported; usually the same as the same as .../PATH/partyId, differs ONLY IF we import a disponent to the client.
          example: "********"
        authorizationType:
          type: string
          enum:
            - OWNER
            - DISPONENT
            - MICRO_OWNER
          description: Authorization code to use default configuration from.
          example: "OWNER"
        partyType:
          type: string
          enum:
            - CORPORATE
            - RETAIL
          description: Specifies whether we import corporate or retail client (for now use only RETAIL).
          example: "RETAIL"

    BankAccountsArray:
      type: array
      items:
        $ref: "#/components/schemas/AccountNumber"

    AccountNumber:
      type: object
      required:
        - numberPart2
        - bankCode
      properties:
        numberPart1:
          type: string
          maxLength: 6
          description: Prefix part of the bank account number.
          example: "19"
        numberPart2:
          type: string
          maxLength: 10
          description: Base part of the bank account number.
          example: "**********"
        bankCode:
          type: string
          description: Part of the bank account number which identifies the bank.
          example: "5500"

    AuthMethodsArray:
      type: array
      items:
        $ref: "#/components/schemas/AuthMethod"

    AuthMethod:
      type: string
      description: Authentication method which will be enabled for imported client - currently not used.
      enum:
        - MEK_SMS
        - MEK_SIM
        - OEK
        - SPEK
      example: "OEK"

    PreferredContext:
      type: object
      description: Mandatory if .../QUERY/activity == PREFERRED_CONTEXT.
      required:
        - preferredPartyId
      properties:
        preferredPartyId:
          type: string
          description: Siebel partyId of the bank's client who will be set as the preferred context.
          example: "********"

    InitChannelsResult:
      type: object
      description: If .../QUERY/activity == PREFERRED_CONTEXT, the the successful response has got an empty body.
      properties:
        requestId:
          type: integer
          format: int64
          description: ID of client import request, which is asynchronous. Can be used to verify status of import.
          example: 741

    InsertIdentDocumentsReq:
      type: object
      required:
        - insertDocuments
      properties:
        partyType:
          $ref: "#/components/schemas/PartyType"
        allowDuplicateEntry:
          $ref: "#/components/schemas/AllowDuplicateEntry"
        insertDocuments:
          $ref: "#/components/schemas/InsertDocuments"
    InsertDocuments:
      description: A list of inserted documents
      type: array
      items:  
        $ref: "#/components/schemas/InsertDocument"
    InsertDocument:
      type: object  
      required:
        - srcReferenceId
        - documentId
        - documentType
        - validity
        - issuerCountry
      properties:
        srcReferenceId:
          $ref: "#/components/schemas/SrcReferenceId"
        documentId:
          $ref: "#/components/schemas/DocumentId"
        documentType:
          $ref: "#/components/schemas/DocumentType"
        validity:
          $ref: "#/components/schemas/Validity"
        issuer:
          $ref: "#/components/schemas/Issuer"
        issuerCountry:
          $ref: "#/components/schemas/IssuerCountry"
        cblCheckStatus:
          $ref: "#/components/schemas/CblCheckStatus"
        cblCheckDate:
          $ref: "#/components/schemas/CblCheckDate"
        unacceptable:
          $ref: "#/components/schemas/Unacceptable"
        primaryDocument:
          $ref: "#/components/schemas/PrimaryDocument"
        comment:
          $ref: "#/components/schemas/Comment"
        processCblCheck:
          $ref: "#/components/schemas/ProcessCblCheck"
        docPersonIdentification:
          $ref: "#/components/schemas/DocPersonIdentification"
        source:
          $ref: "#/components/schemas/Source"
    
    InsertIdentDocumentsRes:
      type: object
      properties:
        insertedDocuments:
          $ref: "#/components/schemas/InsertedDocuments"
    InsertedDocuments:
      description: List of returned documents
      type: array
      items:
        $ref: "#/components/schemas/InsertedDocument"
    InsertedDocument:
      type: object  
      required:
        - srcReferenceId
        - trgReferenceId
        - documentId
        - documentType
      properties:
        srcReferenceId:
          $ref: "#/components/schemas/SrcReferenceId"
        trgReferenceId:
          $ref: "#/components/schemas/TrgReferenceId"
        documentId:
          $ref: "#/components/schemas/DocumentId"
        documentType:
          $ref: "#/components/schemas/DocumentType"
        cblCheckStatus:
          $ref: "#/components/schemas/CblCheckStatus"
        cblCheckDate:
          $ref: "#/components/schemas/CblCheckDate"
        unacceptable:
          $ref: "#/components/schemas/Unacceptable"
        duplicateDocPartyList:
          $ref: "#/components/schemas/DuplicateDocPartyList"

    UpdateIdentDocumentsReq:
      type: object
      required:
        - updateDocuments
      properties:
        partyType:
          $ref: "#/components/schemas/PartyType"
        allowDuplicateEntry:
          $ref: "#/components/schemas/AllowDuplicateEntry"
        updateDocuments:
          $ref: "#/components/schemas/UpdateDocuments"
    UpdateDocuments:
      description: List of inserted documents
      type: array
      items:  
        $ref: "#/components/schemas/UpdateDocument"
    UpdateDocument:
      type: object  
      required:
        - srcReferenceId
        - trgReferenceId
      properties:
        srcReferenceId:
          $ref: "#/components/schemas/SrcReferenceId"
        trgReferenceId:
          $ref: "#/components/schemas/TrgReferenceId"
        documentId:
          $ref: "#/components/schemas/DocumentId"
        documentType:
          $ref: "#/components/schemas/DocumentType"
        validity:
          $ref: "#/components/schemas/Validity"
        issuer:
          $ref: "#/components/schemas/Issuer"
        issuerCountry:
          $ref: "#/components/schemas/IssuerCountry"
        cblCheckStatus:
          $ref: "#/components/schemas/CblCheckStatus"
        cblCheckDate:
          $ref: "#/components/schemas/CblCheckDate"
        unacceptable:
          $ref: "#/components/schemas/Unacceptable"
        primaryDocument:
          $ref: "#/components/schemas/PrimaryDocument"
        comment:
          $ref: "#/components/schemas/Comment"
        processCblCheck:
          $ref: "#/components/schemas/ProcessCblCheck"
        docPersonIdentification:
          $ref: "#/components/schemas/DocPersonIdentification"
        source:
          $ref: "#/components/schemas/Source"
  
    UpdateIdentDocumentsRes:
        type: object
        properties:
          updatedDocuments:
            $ref: "#/components/schemas/UpdatedDocuments"
    UpdatedDocuments:
      description: List of updated documents
      type: array
      items:
        $ref: "#/components/schemas/UpdatedDocument"
    UpdatedDocument:
      type: object  
      required:
        - srcReferenceId
        - trgReferenceId
        - documentId
        - documentType
      properties:
        srcReferenceId:
          $ref: "#/components/schemas/SrcReferenceId"
        trgReferenceId:
          $ref: "#/components/schemas/TrgReferenceId"
        documentId:
          $ref: "#/components/schemas/DocumentId"
        documentType:
          $ref: "#/components/schemas/DocumentType"
        cblCheckStatus:
          $ref: "#/components/schemas/CblCheckStatus"
        cblCheckDate:
          $ref: "#/components/schemas/CblCheckDate"
        unacceptable:
          $ref: "#/components/schemas/Unacceptable"
        duplicateDocPartyList:
          $ref: "#/components/schemas/DuplicateDocPartyList"

    AllowDuplicateEntry:
      description: Flag indicating to store records even in case of duplicate document is found.
        For the operation:|
       - 'insertIdentDocuments' fill this asttribute if partyType == 'PRIVATE' or not exists.
       - 'updateIdentDocuments' fill this asttribute if partyType == 'PRIVATE' or not exists.
      type: boolean
      example: "true"
    CblCheckStatus:
      description: Status of CBL Check (lovTranslate GENERAL_CBLSTAT). 
        For the operation:|
        - 'insertIdentDocuments' fill this asttribute if partyType == 'PRIVATE' or not exists.
        - 'updateIdentDocuments' fill this asttribute if partyType == 'PRIVATE' or not exists. 
        Is not retured in the response of the operation:|
        - 'insertIdentDocuments' if requested partyType == 'COMMERCIAL'.
        - 'updateIdentDocuments' if requested partyType == 'COMMERCIAL'.
      type: string
      example: "Done"
      maxLength: 30
    Unacceptable:
      description: When 'true' = unacceptable, if 'false' = acceptable;
        Is not retured in the response of the operation:|
        - 'insertIdentDocuments' if requested partyType == 'COMMERCIAL'.
        - 'updateIdentDocuments' if requested partyType == 'COMMERCIAL'.
      type: boolean
      example: "false"
    ProcessCblCheck:
      description: Flag indicating if CBL check should be done for documents created. 
         For the operation:|
       - 'insertIdentDocuments' fill this asttribute if partyType == 'PRIVATE' or not exists.
       - 'updateIdentDocuments' fill this asttribute if partyType == 'PRIVATE' or not exists.
      type: boolean
      example: "true"
    DocPersonIdentification:
      description: Type of document used for identification of User (DOCSHOWNFACE2FACE / DOCSHOWNFACE2FACESCAN / DOCNOTSHOWNFACE2FACE); 
        For the operation:|
       - 'insertIdentDocuments' fill this asttribute if partyType == 'PRIVATE' or not exists.
       - 'updateIdentDocuments' fill this asttribute if partyType == 'PRIVATE' or not exists.
      type: string
      example: "DOCSHOWNFACE2FACE"
      maxLength: 30
    Source:
      description: Source of the Document. Values - backend codebook ('RB_PRV_PIDOCSOURCE')
        Fill it if partyType == 'PRIVATE' or not exists 
        For the operation 'insertIdentDocuments', the attribute 'Source' must be filled. 
        For the operation 'updateIdentDocuments', the attribute 'Source' must be filled if any of the following attributes were changed - 'documentType', 'documentId', 'issuerCountry', 'validFrom', 'validTo'. 
        (If any other attribute than the specified ones is changed, the source remains the same - it is not filled in.)  
      type: string 
      example: "BankID"
      maxLength: 30
    TrgReferenceId:
      description: Unique identification of the entity in target system
      type: string
      example: "1-ROFLMAO"
      maxLength: 15
    DuplicateDocPartyList:
      description: List of parties with the same document; 
       For the operation:|
       - 'insertIdentDocuments' is returned if requested partyType == 'PRIVATE' or not exists.
       - 'updateIdentDocuments' is returned if requested partyType == 'PRIVATE' or not exists.
      type: array
      items:
        $ref: "#/components/schemas/DuplicateDocParty" 
    DuplicateDocParty:
      description: Parties with the same document
      type: string
      example: "43229"
    
    GetUnifeHeaderRes:
      type: object
      required:
      - basicInfo
      - cares
      properties:
        basicInfo:
          $ref: "#/components/schemas/BasicInfoHeader"
        additionalInfo:
          $ref: "#/components/schemas/AdditionalInfo"
        consents:
          $ref: "#/components/schemas/Consents"
        contacts:
          $ref: "#/components/schemas/ContactsHeader"
        cares:
          $ref: "#/components/schemas/Cares"
        categories:
          $ref: "#/components/schemas/Categories"
        documents:
          $ref: "#/components/schemas/HeaderDocuments"
        notes:
          $ref: "#/components/schemas/Notes"
    
    BasicInfoHeader:
      type: object
      required:
      - partyId
      - partyType
      properties:
        partyId:
          $ref: "#/components/schemas/PartyId"
        partyType:
          $ref: "#/components/schemas/PartyType"
        isClient:
          $ref: "#/components/schemas/IsClient"
        isEligible:
          $ref: "#/components/schemas/IsEligible"
        preferredLanguage:
          $ref: "#/components/schemas/PreferredLanguage"
        privateParty:
          $ref: "#/components/schemas/PrivateParty"
        commercialParty:
          $ref: "#/components/schemas/CommercialParty"
    PrivateParty:
      type: object
      required:
        - lastName
      properties:
        titlesBefore:
          $ref: "#/components/schemas/TitlesBefore"
        firstName:
          $ref: "#/components/schemas/FirstName"
        middleNames:
          $ref: "#/components/schemas/MiddleNames"
        lastName:
          $ref: "#/components/schemas/LastName"
        titlesBehind:
          $ref: "#/components/schemas/TitlesBehind"
        gender:
          $ref: "#/components/schemas/Gender"
        birthInfo:
          $ref: "#/components/schemas/BirthInfo"
        citizenship:
          $ref: "#/components/schemas/Citizenship"
        secondaryCitizenships:
          $ref: "#/components/schemas/SecondaryCitizenships"
    SecondaryCitizenships:
      type: array
      items:
        $ref: "#/components/schemas/Citizenship"
    CommercialParty:
      type: object
      required:
        - companyName
      properties:
        companyName:
          $ref: "#/components/schemas/CompanyName"
        registrationNumber:
          $ref: "#/components/schemas/RegistrationNumber" 
    
    AdditionalInfo:
      type: object
      properties:
        taxation:
          $ref: "#/components/schemas/TaxationHeader"
        aml:
          $ref: "#/components/schemas/AmlHeader"
        cbl:
          $ref: "#/components/schemas/Cbl"
        subSegment:
          $ref: "#/components/schemas/SubSegment"
        marketingConsentsStatus:
          $ref: "#/components/schemas/MarketingConsentsStatus"
    TaxationHeader:
      type: object
      description: Taxation data
      properties:
        taxResidency:
          $ref: "#/components/schemas/TaxResidency"
        taxId:
          $ref: "#/components/schemas/TaxId"
    AmlHeader:
      type: object
      properties:
        pepCheck:
          $ref: "#/components/schemas/PepCheckHeader"
    Cbl:
      type: object
      properties:
        cblCheckDate:
          $ref: "#/components/schemas/CblCheckDate"        
        cblCheckResult:
          $ref: "#/components/schemas/CblCheckResult"
        unacceptableFlag:
          $ref: "#/components/schemas/UnacceptableHeader"
        iblFound:
          $ref: "#/components/schemas/IblFound"
        isirFound:
          $ref: "#/components/schemas/IsirFound"
        riskRating:
          $ref: "#/components/schemas/RiskRating"
        fisaFound:
          $ref: "#/components/schemas/FisaFound"
        pepFound:
          $ref: "#/components/schemas/PepFound"
        cblResultUrl:
          $ref: "#/components/schemas/CblResultUrl"
    
    Consents:
      description: List of marketing consents
      type: array
      items:
        $ref: "#/components/schemas/Consent"
    Consent:
      description: Marketing consent
      type: object
      properties:
        consentId:
          $ref: "#/components/schemas/ConsentId"
        consentType:
          $ref: "#/components/schemas/ConsentType"
        consentStatus:
          $ref: "#/components/schemas/ConsentStatus"

    ContactsHeader:
      type: object
      description: List of customer contacts like e-mail address, phone numbers etc.
      properties:
        addresses:
          type: array
          items:
            $ref: "#/components/schemas/AddressHeader"
        phones:
          type: array
          items:
            $ref: "#/components/schemas/PhoneHeader"
        emails:
          type: array
          items:
            $ref: "#/components/schemas/EmailHeader"
    AddressHeader:
      type: object
      required:
        - addressId
      properties:
        addressId:
          $ref: "#/components/schemas/AddressId"
        sblAddressSlot:
          $ref: "#/components/schemas/SblAddressSlot"
        street:
          $ref: "#/components/schemas/Street"
        number:
          $ref: "#/components/schemas/StreetNumber"
        city:
          $ref: "#/components/schemas/City"
        postalCode:
          $ref: "#/components/schemas/PostalCode"
        country:
          $ref: "#/components/schemas/Country"
        isPreferred:
          $ref: "#/components/schemas/IsPreferred"
    PhoneHeader:
      type: object
      properties:
        contactId:
          $ref: "#/components/schemas/ContactId"
        contactName:
          $ref: "#/components/schemas/ContactName"
        number:
          $ref: "#/components/schemas/PhoneNumber"
        numberType:
          $ref: "#/components/schemas/PhoneNumberType"
    EmailHeader:
      type: object
      properties:
        contactId:
          $ref: "#/components/schemas/ContactId"
        contactName:
          $ref: "#/components/schemas/ContactName"
        address:
          $ref: "#/components/schemas/EmailAddress"
        addressType:
          $ref: "#/components/schemas/EmailAddressType"     

    Cares:
      description: List of cares
      type: array
      items:
        $ref: "#/components/schemas/Care"
    Care:
      type: object
      required:
        - primaryCare
      properties:
        primaryCare:
          $ref: "#/components/schemas/PrimaryCare"
        careType:
          $ref: "#/components/schemas/CareType"
        firstName:
          $ref: "#/components/schemas/FirstName"
        lastName:
          $ref: "#/components/schemas/LastName"
        division:
          $ref: "#/components/schemas/Division"
        position:
          $ref: "#/components/schemas/Position"
        userId:
          $ref: "#/components/schemas/UserId" 
    Division:
      type: object
      properties:
        divisionName:
          $ref: "#/components/schemas/DivisionName"
    Position:
      type: object
      properties:
        positionType:
          $ref: "#/components/schemas/PositionType"

    Categories:
      description: List of categories
      type: array
      items:
        $ref: "#/components/schemas/Category"
    Category:
      type: object
      required:
        - type
      properties:
        categoryId:
          $ref: "#/components/schemas/CategoryId"
        type:
          $ref: "#/components/schemas/CategoryType"
        validity:
          $ref: "#/components/schemas/Validity"
        value:
          $ref: "#/components/schemas/CategoryValue"
        categorization:
          $ref: "#/components/schemas/Categorization"
        comment:
          $ref: "#/components/schemas/Comment"
        rowType:
          $ref: "#/components/schemas/RowType"

    HeaderDocuments:
      description: Identification document
      type: array
      items:
        $ref: "#/components/schemas/HeaderDocument"
    HeaderDocument:
      type: object
      required:
        - documentId
        - documentType
      properties:
        srcReferenceId:
          $ref: "#/components/schemas/SrcReferenceId"
        siebelReferenceId:
          $ref: "#/components/schemas/SiebelReferenceId"
        documentId:
          $ref: "#/components/schemas/DocumentId"
        documentType:
         $ref: "#/components/schemas/DocumentType"
        validity:
         $ref: "#/components/schemas/Validity"
        issuer:
          $ref: "#/components/schemas/Issuer"
        issuerCountry:
          $ref: "#/components/schemas/IssuerCountry"
        cblCheckStatus:
          $ref: "#/components/schemas/CblCheckStatusHeader"
        unacceptable:
          $ref: "#/components/schemas/UnacceptableHeader"
        duplicateDocPartyList:
          description: List of parties with the same document;
          type: array
          items:
            $ref: "#/components/schemas/DuplicateDocParty"     

    Notes:
      description: List of notes
      type: array
      items:
        $ref: "#/components/schemas/Note"
    Note:
      type: object
      required:
       - noteId
      properties:
        noteId:
          $ref: "#/components/schemas/NoteId"
        note:
          $ref: "#/components/schemas/NoteHeader"
        type:
          $ref: "#/components/schemas/TypeHeader"
        isPrivate:
          $ref: "#/components/schemas/IsPrivate"
        created:
          $ref: "#/components/schemas/Created"
        
    PepCheckHeader:
      type: string
      description: Pep check confirmed/not corfirmed
      example: "Confirmed"
    SubSegment:
      type: string
      description: Subsegment of a client (lovTranslate UNI_SUBSEGM)
      example: "CS01"
    MarketingConsentsStatus:
      type: string
      description: Marketing Consents Status
    IblFound:
      type: boolean
      description: Registered in IBL
      example: "true"
    IsirFound:
      type: boolean
      description: Registered in ISIR
      example: "true"
    RiskRating:
      type: string
      description: AML Risk rating
      example: "HIGH"
    FisaFound:
      type: boolean
      description: AML - FISA
      example: "true"
    PepFound:
      type: boolean
      description: AMP - PEP
      example: "false"
    CblResultUrl:
      type: string
      description: CBL check result-link
      example: "http://www.seznam.cz"
    ConsentId:
      type: string
      description: Primary Id (SBL row id)
      maxLength: 15
      example: "1-874FW"
    ConsentType:
      type: string
      description: Consent
      maxLength: 30
      example: "CARE_MKTGRSRCH"
    ConsentStatus:
      type: string
      description: State
      maxLength: 30
      example: "CANCELLED"
    SblAddressSlot:
      type: string
      description: Address type (RESIDENCY or MAIL1 - MAIL9)
      example: "MAIL1"
    PrimaryCare:
      type: boolean
      description: Flag if the care is primary or not.
      example: "true"
    CareType:
      type: string
      description: Type of care (lovTranslate UNI_PCARET)
      example: "RISKMANAG"
    DivisionName:
      type: string
      description: Organization unit name
      example: "Pobočka Praha - City Tower (Hvězdova)"
    PositionType:
      type: string
      description: Position type name
      example: "COMSALMAN"
    UserId:
      type: string
      description: user Id
      example: "CZA59485"
    CategoryId:
      type: string
      description: Primary Id (SBL row id)
      example: "1-874FW"
    CategoryType:
      type: string
      description: Type (lovTranslate UNI_PCATT)
      example: "Broker"
    RowType:
      type: string
      description: Type of data record
      example: "CATEGORY"
    SiebelReferenceId:  
      type: string
      description: Primary Id (SBL row id)
      example: "1-874FW"
    CblCheckStatusHeader:
      type: string
      description: CBL check status (lovTranslate GENERAL_CBLSTAT)
      example: "PENDING"
    UnacceptableHeader:
      type: boolean
      description: Unacceptable flag - When 'true' = unacceptable, if 'false' = acceptable.
      example: "false"
    NoteId:
      type: string
      description: Primary Id (SBL row id)
      maxLength: 15
      example: "1-874FW"
    NoteHeader:
      type: string
      description: Note to customer
      maxLength: 5000
      example: "Poorly dressed customer"
    TypeHeader:
      type: string
      description: Type of note - value from picklist
      maxLength: 30
      example: "General Contact"
    IsPrivate:
      type: boolean
      description: If the note is marked as private then "true".
      maxLength: 30
      example: "true"
    Created:
      type: string
      format: date
      description: Date of record created ("YYYY-MM-DD")
      example: "2024-10-31"
    RegistryCheck:
      type: boolean
      description: Simulates pressing the button "Registers" to pass the eligibility check if requested.
      example: false

    Error:
      type: object
      description: |
        # Error Reference Model
        Error model to hold data which help to identify error cause and provide additional tracking information.        
      required:
        - requestId
        - correlationId
        - status
      properties:
        requestId:
          type: string
          description: A unique UUID of a specific request. A value shoud be obtained from a X-Request-Id header.
          example: '06f31981-c15d-48fb-86c6-53bfae940802'
        correlationId:
          type: string
          description: A unique UUID of identification entire communication. A value shoud be obtained from a X-Correlation-Id header.
          example: '2725ab36-4608-4abc-ba1c-6e929ef539b1'
        status:
          type: integer
          format: int32
          description: |
            A HTTP status code. If there is in use different protocol than HTTP, we should map an error to this protocol.
          minimum: 100
          maximum: 600
          exclusiveMaximum: true
          example: 400
        reasons:
          type: array
          items:
            $ref: "#/components/schemas/ErrorReason"

    ErrorReason:
      type: object
      description: Additional info about caused error.
      required:
        - code
        - message
        - severity
      properties:
        code:
          type: string
          description: |
            Error code or Backend error code returned from backend service.
          example: "XYZ8000"
        severity:
          type: string
          description: Shows if the reason for an unexpected situation is critical or just information.
          enum:
            - WARN
            - ERROR
            - FATAL
          example: "ERROR"
        message:
          type: string
          description: Human-readable message in user-requested language.
          example: "Payment rejected. Missing creditor iban."
        path:
          type: string
          description: The path of the problematic field which causes the error.
          example: "creditorAccount.iban"
        moreInfo:
          type: string
          description: More detailed description and error slolution, e.g. https://some.url