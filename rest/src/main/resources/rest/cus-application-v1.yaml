openapi: 3.0.0

info:
  title: Application
  version: 1.0.0
  description: |
    API which helps to serve the matters related to applications for specific products. <br>
    (Therefore, here the focus is more on the application instance in ADB or ODE; while API cus-party-application-v1 emphasises the relation between a party and their application).
  contact:
    name: IP - Analysts_RBCZ
    email: <EMAIL>

servers:
  - url: https://dev-api.rb.cz/rbepi
    description: PreSIT
  - url: https://tfx1-api.rb.cz/rbepi
    description: TFX1
  - url: https://preprod-api.rb.cz/rbepi
    description: PrePROD
  - url: https://api.rb.cz/rbepi
    description: PROD

security:
  - BasicAuth: []

tags:
  - name: Cashloan applications
    description: Resources related with the cashloan applications in ADB or ODE.

paths:
  /cus/application/v1/applications/{applKey}/client-action:
    put:
      tags:
        - Cashloan applications
      summary: Log a client-initiated action/event in ADB and ODE.
      description: |
        Log a client-initiated action/event (such as viewing terms and services or signing) on a cashloan application in ADB and ODE.<br><br>
        The application is specified by its applKey in the request.<br><br>
        Technically, this REST integration microservice mediates (via the orchestrated LAS application microservices) the communication with ADB's and ODE's DB procedures which are named SET_APPL_EVE in both of the systems.
      operationId: cus-application-v1-setClientAction

      #RQ parameters declarations
      parameters:
        # path parameters
        - $ref: "#/components/parameters/ApplKey"
        # query parameters
        # headers
        - $ref: "#/components/parameters/X-Api-Name"
        - $ref: "#/components/parameters/X-Request-Id"
        - $ref: "#/components/parameters/X-Correlation-Id"
        - $ref: "#/components/parameters/X-Request-App"
        - $ref: "#/components/parameters/X-Frontend-App"
        - $ref: "#/components/parameters/X-Frontend-Service"
        - $ref: "#/components/parameters/X-Channel-Code"
        - $ref: "#/components/parameters/X-User-Id"
        - $ref: "#/components/parameters/X-User-Type"
        - $ref: "#/components/parameters/X-Cl-Context-Id"

      #RQ body
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SetClientActionRequest"

      #RS
      responses:
        "200":
          description: OK; successful response from setClientAction; has got an empty body.
        default:
          description: Error response.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"

  /cus/application/v1/applications/{applicationId}/interest-change:
    put:
      tags:
        - Cashloan applications
      summary: Set the changed interest rate of a standard loan application variant or refinancing application in ADB.
      description: |
        Set the changed interest rate of a standard loan application variant or refinancing application in ADB. Typically, discount is given to the client, nevertheless, higher interest rate can also be used in some buseness cases.<br><br>
        In the request the consumer provides only an identifier of the application (using path and query parameters; the operation has got no RQ body).<br><br>
        Several REST application microservices are called subsequently from this REST IMS:
          - Get the basic detail of the application = las-application-v1-getBasicDetail.
          - Get the information on the interest change which was used on the application = had-interest-change-v1-getUsedInterestChange.
          - Get the detail on variants and variant parameters of a loan application = las-application-v1-getVariants.
          - Check what loan product the requested application is for.
          - If the application is for a standard cashloan, then set the new interest rate to its specific application variant = las-application-v1-setVariantParamsChange.
          - If the application is for refinancing, then set the new interest rate to the specific party's obligations = las-application-v1-setObligationInterestRate.
      operationId: cus-application-v1-setInterestChange

      #RQ parameters declarations
      parameters:
        # path parameters
        - $ref: "#/components/parameters/ApplicationId"
        # query parameters
        - $ref: "#/components/parameters/ApplicationIdType"
        # headers
        - $ref: "#/components/parameters/X-Api-Name"
        - $ref: "#/components/parameters/X-Request-Id"
        - $ref: "#/components/parameters/X-Correlation-Id"
        - $ref: "#/components/parameters/X-Request-App"
        - $ref: "#/components/parameters/X-Frontend-App"
        - $ref: "#/components/parameters/X-Frontend-Service"
        - $ref: "#/components/parameters/X-Channel-Code"
        - $ref: "#/components/parameters/X-User-Id"
        - $ref: "#/components/parameters/X-User-Type"

      #RQ body

      #RS
      responses:
        "200":
          description: OK; successful response from setInterestChange; has got an empty body.
        default:
          description: Error response.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/Error"

components:
  securitySchemes:
    BasicAuth:
      type: http
      scheme: basic

  parameters:
    #Header parameters definitions
    X-Api-Name:
      name: X-Api-Name
      in: header
      description: ID of the called service, definded by Integration platform RBCZ and is equal to 'operationId:' of this operation (see this .yaml or [kip.rb.cz](http://kip.rb.cz)).
      required: true
      example: 'cus-application-v1-setClientAction'
      schema:
        type: string
    X-Request-Id:
      name: X-Request-Id
      in: header
      description: |
        ID of the request, unique to the call, as determined by the initiating (consumer) application. UUID format or RBCZ Message ID convention: application code + 21 numbers
        (i.e. <SYS>001207641782552682875).
      required: true
      example: '99391c7e-ad88-49ec-a2ad-99ddcb1f7721'
      schema:
        type: string
    X-Correlation-Id:
      name: X-Correlation-Id
      in: header
      description: |
        X-Correlation-ID will be used to track requests through all involved services to be able to graph
        request flows, map how services contribute to response times, identify bottle necks, etc.
        The X-Correlation-ID must be propagated unchanged in any and all downstream service calls (e.g. RBCZ services).
      required: true
      example: 'ea1ba73c-f7f4-4ccd-80b5-4237582198ec'
      schema:
        type: string
        format: uuid
    X-Request-App:
      name: X-Request-App
      in: header
      description: ID of the service consumer application that generates the request (RBCZ application code).
      example: 'CLF'
      required: true
      schema:
        type: string
        maxLength: 3
    X-Frontend-App:
      name: X-Frontend-App
      in: header
      description: ID of the service consumer system which originally generated the request. Used only when the service is mediated by another system (RBCZ application code).
      example: 'GIB'
      schema:
        type: string
        maxLength: 3
    X-Frontend-Service:
      name: X-Frontend-Service
      in: header
      description: ID of the previous level service. Used only when the service is called from another service.
      example: 'serviceId-operation'
      schema:
        type: string
    X-Channel-Code:
      name: X-Channel-Code
      in: header
      description: Identification of the (business) channel in which the request was sent.
      example: "BFO"
      schema:
        type: string
    X-User-Id:
      name: X-User-Id
      in: header
      description: Unique identification of the user, who initiates the request (cza or technical user).
      example: "cza12345"
      schema:
        type: string
    X-User-Type:
      name: X-User-Type
      in: header
      description: Additional information to destinguish internal user (cza...) or external user (technical accounts, 3rd parties, clients...) - INT | EXT.
      example: "INT"
      schema:
        type: string
        enum:
          - INT
          - EXT
    X-Cl-Context-Id:
      name: X-Cl-Context-Id
      in: header
      description: Identification of the Cashloan context in which the process originates.
      example: "RSTS"
      required: false
      schema:
        type: string
        enum:
          - RSTS
          - RB
        default: "RB"

    #Path parameters definitions
    ApplKey:
      name: applKey
      in: path
      required: true
      description: ID (primary key in ADB) of the application.
      schema:
        type: string
      example: 999
    ApplicationId:
      name: applicationId
      in: path
      description: Value of the application identifier.
      required: true
      schema:
        type: string
      example: ************

    #Query parameters definitions
    ApplicationIdType:
      name: applicationIdType
      in: query
      description: |
        Specifies which kind of application identifier will be used:
          - busApplId = Business ID of the application. String.
          - applKey = ID (primary key in ADB) of the application. Integer (int64).
      required: true
      schema:
        type: string
        enum:
          - BUSAPPLID
          - APPLKEY

  schemas:
    #Objects and arrays definitions
    SetClientActionRequest:
      type: object
      required:
        - eventType
      properties:
        eventType:
          $ref: "#/components/schemas/EventType"
        description:
          $ref: "#/components/schemas/EventDescription"
        documentType:
          $ref: "#/components/schemas/DocumentType"
        ipAddress:
          $ref: "#/components/schemas/IpAddress"

    Error:
      type: object
      description: |
        ## Error Reference model
        Error model to hold the data which help to identify error cause and provide additional tracking information.
      required:
        - requestId
        - correlationId
        - status
      properties:
        requestId:
          $ref: "#/components/schemas/RequestId"
        correlationId:
          $ref: "#/components/schemas/CorrelationId"
        status:
          $ref: "#/components/schemas/ErrorStatus"
        reasons:
          $ref: "#/components/schemas/ErrorReasons"

    ErrorReasons:
      type: array
      items:
        $ref: "#/components/schemas/ErrorReason"

    ErrorReason:
      type: object
      description: Additional information about caused error.
      required:
        - code
        - severity
      properties:
        code:
          $ref: "#/components/schemas/ErrorCode"
        severity:
          $ref: "#/components/schemas/ErrorSeverity"
        message:
          $ref: "#/components/schemas/ErrorMessage"

    #Primitive types definitions
    EventType:
      type: string
      description: Identifier of the client-initiated action/event on the application. Backend uses LOV APPL_EVE_TP.
      example: PENNY_VERIF_PROMISE
    EventDescription:
      type: string
      description: Further detail of the action/event (identification of the banker or agent who approved it).
      example: abcd1234
    DocumentType:
      type: string
      description: Type of the document related to the action/event. Backend uses LOV DOC_TP.
      example: L002
    IpAddress:
      type: string
      description: IP address of the user who logs the action/event.
      example: '************'
    RequestId:
      type: string
      description: A unique UUID of a specific request or RBCZ Message ID convention (application code + 21 numbers). The value shoud be obtained from X-Request-Id header.
      example: '99391c7e-ad88-49ec-a2ad-99ddcb1f7721'
    CorrelationId:
      type: string
      description: A unique UUID of the entire communication identification. The value shoud be obtained from X-Correlation-Id header.
      example: 'ea1ba73c-f7f4-4ccd-80b5-4237582198ec'
    ErrorStatus:
      type: integer
      format: int32
      description: An HTTP status code. If a different protocol than HTTP is used, we should map and transform the error to HTTP protocol style.
      minimum: 100
      maximum: 600
      exclusiveMaximum: true
      example: 400
    ErrorCode:
      type: string
      description: Error code or Backend error code returned from backend service.
      example: "XYZ8000"
    ErrorSeverity:
      type: string
      description: Shows if the reason for an unexpected situation is critical or just information.
      enum:
        - WARN
        - ERROR
        - FATAL
      example: "ERROR"
    ErrorMessage:
      type: string
      description: Human-readable message in user-requested language.
      example: "Payment rejected. Missing creditor iban."