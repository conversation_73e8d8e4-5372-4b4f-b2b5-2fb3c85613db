package cz.equa.camapp.rest.util;

import org.apache.hc.client5.http.classic.HttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClientBuilder;
import org.apache.hc.client5.http.impl.routing.DefaultProxyRoutePlanner;
import org.apache.hc.core5.http.HttpException;
import org.apache.hc.core5.http.HttpHost;
import org.apache.hc.core5.http.protocol.HttpContext;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.boot.web.client.RestTemplateCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

@Configuration
public class ProxyConfig {
    
    @Value("${http.proxy.hostname:mwg.rb.cz}")
	private String httpProxyHostname;
    
    @Value("${http.proxy.port:8079}")
	private int httpProxyPort;
    
    @Value("${http.proxy.scheme:https}")
	private String httpProxyScheme;
    
    @Value("${http.proxy.user-agent:CMN}")
	private String httpProxyUserAgent;

    @Bean(name = "proxyRestTemplate")
    public RestTemplate restTemplate() {
        ClientHttpRequestInterceptor interceptor = (request, body, execution) -> {
            request.getHeaders().add("user-agent", httpProxyUserAgent);
            return execution.execute(request, body);
        };        
        return new RestTemplateBuilder(new ProxyCustomizer()).additionalInterceptors(interceptor).build();
    }
    
    class ProxyCustomizer implements RestTemplateCustomizer {

        @Override
        public void customize(RestTemplate restTemplate) {
            HttpHost proxy = new HttpHost(httpProxyScheme, httpProxyHostname, httpProxyPort);
            HttpClient httpClient = HttpClientBuilder.create()
                .setUserAgent(httpProxyUserAgent)
                .setRoutePlanner(new DefaultProxyRoutePlanner(proxy) {
                    @Override
                    public HttpHost determineProxy(HttpHost target, HttpContext context) throws HttpException {
                        return super.determineProxy(target,context);
                    }
                })
                .build();
            restTemplate.setRequestFactory(new HttpComponentsClientHttpRequestFactory(httpClient));
        }
    }
}
