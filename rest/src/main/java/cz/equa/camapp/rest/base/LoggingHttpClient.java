package cz.equa.camapp.rest.base;

import cz.equa.camapp.utils.MDCFields;
import cz.equa.camapp.utils.MDCHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLParameters;
import java.io.*;
import java.lang.reflect.Field;
import java.net.Authenticator;
import java.net.CookieHandler;
import java.net.ProxySelector;
import java.net.http.HttpClient;
import java.net.http.HttpHeaders;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Flow;
import java.util.stream.Collectors;

@Slf4j
public class LoggingHttpClient extends HttpClient {
    private static final long MAX_LOGGABLE_BODY_SIZE = 100_000;
    private final HttpClient client;

    public LoggingHttpClient(HttpClient client) {
        this.client = client;
    }

    @Override
    public <T> HttpResponse<T> send(HttpRequest req, HttpResponse.BodyHandler<T> responseBodyHandler) throws IOException, InterruptedException {
        MDCHelper.put(MDCFields.API_NAME_EXTERNAL, getServiceName(req));
        long startTime = System.currentTimeMillis();
        if (!log.isInfoEnabled()) {
            return client.send(req, responseBodyHandler);
        }

        var requestInfo = new StringBuilder()
                .append("\nRequest: ")
                .append(req.method())
                .append(" ")
                .append(req.uri());

        buildHeadersEntry(requestInfo, req.headers());

        if (req.bodyPublisher().isPresent()) {
            resolveBodyFromRequest(req, requestInfo);
        }

        if (req instanceof LoggingHttpRequest loggingHttpRequest && loggingHttpRequest.getBody() != null) {
            requestInfo
                    .append("\nBody: ")
                    .append(new String(loggingHttpRequest.getBody(), StandardCharsets.UTF_8));
        }
        logInfo(requestInfo.toString());

        var response = client.send(req, responseBodyHandler);

        var responseBody = new BufferedReader(
                new InputStreamReader(
                        (InputStream) response.body(),
                        StandardCharsets.UTF_8
                )
        )
                .lines()
                .collect(Collectors.joining("\n"));
        requestInfo = new StringBuilder();
        requestInfo.append("\nResponse: ").append(response.statusCode()).append(" ");
        buildHeadersEntry(requestInfo, response.headers());
        requestInfo.append("\nBody: ").append(responseBody);

        response.headers().firstValue("x-kong-upstream-latency").ifPresent(latency -> {
            MDCHelper.put(MDCFields.LATENCY_EXTERNAL, latency);
        });
        logInfo(requestInfo.toString());
        log.info("Call {} duration {}", getServiceName(req), System.currentTimeMillis() - startTime);

        return new HttpResponseImpl<>(response, (T) new ByteArrayInputStream(responseBody.getBytes(StandardCharsets.UTF_8)));
    }

    protected String getServiceName(HttpRequest req) {
        String name = "";
        if (req.headers() != null && req.headers().map() != null && req.headers().map().get("X-Api-Name") != null) {
            name = String.join(" ", req.headers().map().get("X-Api-Name"));
        }
        if (StringUtils.isEmpty(name) && req.uri() != null) {
            name = req.uri().toString();
        }
        return name;
    }

    protected void resolveBodyFromRequest(HttpRequest req, StringBuilder requestInfo) {
        req.bodyPublisher().ifPresent(bodyPublisher -> {
            long contentLength = bodyPublisher.contentLength();

            if (contentLength > MAX_LOGGABLE_BODY_SIZE) {
                requestInfo.append("\nBody: [data stream of ")
                        .append(contentLength)
                        .append(" bytes]");
                return;
            }


            try {
                ByteArrayOutputStream data = new ByteArrayOutputStream();
                CompletableFuture<Void> future = new CompletableFuture<>();

                bodyPublisher.subscribe(new Flow.Subscriber<>() {
                    private Flow.Subscription subscription;

                    @Override
                    public void onSubscribe(Flow.Subscription subscription) {
                        this.subscription = subscription;
                        subscription.request(Long.MAX_VALUE);
                    }

                    @Override
                    public void onNext(ByteBuffer item) {
                        byte[] bytes = new byte[item.remaining()];
                        item.get(bytes);
                        data.write(bytes, 0, bytes.length);
                    }

                    @Override
                    public void onError(Throwable t) {
                        future.completeExceptionally(t);
                    }

                    @Override
                    public void onComplete() {
                        future.complete(null);
                    }
                });

                future.join();
                requestInfo.append("\nBody: ")
                        .append(data.toString(StandardCharsets.UTF_8));

            } catch (Exception e) {
                log.warn("Could not log request body", e);
            }
        });
    }

    private void buildHeadersEntry(StringBuilder headersForLog, HttpHeaders headers) {
        headersForLog.append("\nHeaders: [\n");
        headers.map().forEach((name, values) ->
        {
            headersForLog.append(name).append(":");
            if ("Authorization".equals(name)) {
                values.forEach(value -> headersForLog
                        .append(value, 0, 10)
                        .append("****")
                        .append(value, value.length() - 4, value.length())
                        .append("\n")
                );
            } else {
                values.forEach(value -> headersForLog
                        .append(value)
                        .append("\n")
                );
            }
        });
        headersForLog.append("]");
    }

    @Override
    public Optional<CookieHandler> cookieHandler() {
        return client.cookieHandler();
    }

    @Override
    public Optional<Duration> connectTimeout() {
        return client.connectTimeout();
    }

    @Override
    public Redirect followRedirects() {
        return client.followRedirects();
    }

    @Override
    public Optional<ProxySelector> proxy() {
        return client.proxy();
    }

    @Override
    public SSLContext sslContext() {
        return client.sslContext();
    }

    @Override
    public SSLParameters sslParameters() {
        return client.sslParameters();
    }

    @Override
    public Optional<Authenticator> authenticator() {
        return client.authenticator();
    }

    @Override
    public Version version() {
        return client.version();
    }

    @Override
    public Optional<Executor> executor() {
        return client.executor();
    }

    @Override
    public <T> CompletableFuture<HttpResponse<T>> sendAsync(HttpRequest request, HttpResponse.BodyHandler<T> responseBodyHandler) {
        return client.sendAsync(request, responseBodyHandler);
    }

    @Override
    public <T> CompletableFuture<HttpResponse<T>> sendAsync(HttpRequest request, HttpResponse.BodyHandler<T> responseBodyHandler, HttpResponse.PushPromiseHandler<T> pushPromiseHandler) {
        return client.sendAsync(request, responseBodyHandler, pushPromiseHandler);
    }

    private void logInfo(String input) {
        if (StringUtils.isBlank(input)) {
            log.warn("String for log is blank");
            return;
        }

        int maxLength = 15000;
        int startIndex = 0;
        while (startIndex < input.length()) {
            int endIndex = Math.min(startIndex + maxLength, input.length());
            log.info(input.substring(startIndex, endIndex));
            startIndex = endIndex;
        }
    }
}
