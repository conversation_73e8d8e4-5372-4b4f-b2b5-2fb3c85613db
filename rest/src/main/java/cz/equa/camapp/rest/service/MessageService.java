package cz.equa.camapp.rest.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import cz.equa.camapp.service.ServiceException;
import cz.rb.ifr.message.handler.ApiException;
import cz.rb.ifr.message.handler.MessageApi;
import cz.rb.ifr.message.model.Error;
import cz.rb.ifr.message.model.ErrorReason;
import cz.rb.ifr.message.model.SendSMSRequest;
import cz.rb.ifr.message.model.SendSMSResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.UUID;

@Component
@RequiredArgsConstructor
@Slf4j
public class MessageService extends CommonRestService {
    private static final String SEND_SMS_XAPI = "ifr-message-v1-sendSms";

    private MessageApi messageApi;

    @Autowired
    public MessageService(MessageApi messageApi) {
        this.messageApi = messageApi;
    }

    public void sendSms(String smsNumber, String smsText, String correlationId) throws ServiceException {
        SendSMSRequest sendSMSRequest = new SendSMSRequest();
        sendSMSRequest.setSmsNumber(smsNumber);
        sendSMSRequest.setSmsText(smsText);
        sendSMSRequest.setSsrAppId(60); //CMN
        sendSMSRequest.setPriority(1);
        sendSMSRequest.setDeliveryReport(false);

        try {
            SendSMSResult sendSmsResponse = messageApi.ifrMessageV1SendSms(
                    SEND_SMS_XAPI,
                    UUID.randomUUID(),
                    UUID.fromString(correlationId),
                    X_REQUEST_APP,
                    xFrontendApp,
                    xFrontendService,
                    sendSMSRequest);
        } catch (ApiException ex) {
            String responseBody = ex.getResponseBody();
            try {
                Error error = getErrorResponseBody(responseBody);
                if (error.getReasons().stream().anyMatch(r -> "IFR8004".equals(r.getCode()))) {
                    String message = error.getReasons().stream()
                            .filter(r -> "IFR8004".equals(r.getCode()))
                            .map(ErrorReason::getMessage)
                            .findFirst()
                            .orElse("");
                    log.error("SMS was not sent: {}", message);
                    return;
                }

                for (ErrorReason reason : error.getReasons()) {
                    if ("SSR0001".equals(reason.getCode())) {
                        log.error("SMS was not sent: {}", reason.getMessage());
                    } else {
                        throw new ServiceException(ex.getMessage(), ex);
                    }
                }
            } catch (JsonProcessingException e) {
                throw new ServiceException(e.getMessage(), e);
            }
        }
    }

    private Error getErrorResponseBody(String jsonString) throws JsonProcessingException {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        return objectMapper.readValue(jsonString, Error.class);
    }
}
