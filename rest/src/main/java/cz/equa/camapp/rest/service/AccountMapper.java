package cz.equa.camapp.rest.service;

import cz.equa.camapp.lovs.LovFactory;
import cz.equa.camapp.rest.model.bai.account.GetListResDTO;
import cz.rb.bai.account.model.GetListRes;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(uses = {LovFactory.class})
public interface AccountMapper {
    AccountMapper INSTANCE = Mappers.getMapper(AccountMapper.class);


    GetListResDTO esbToDto(GetListRes response);

    GetListRes dtoToEsb(GetListResDTO response);
}
