package cz.equa.camapp.rest.base;

import cz.equa.camapp.rest.model.KongConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.Base64;

@Configuration
@Slf4j
@ComponentScan({
        "cz.rb.bai",
        "cz.rb.cus",
        "cz.rb.dms.document",
        "cz.rb.had",
        "cz.rb.ifr",
        "cz.rb.las",
        "cz.rb.mut.external",
        "cz.rb.ocr",
        "cz.rb.mdc"
})
public class WebClientConfiguration {
    private final KongConfig kongConfig;

    public WebClientConfiguration(@Qualifier("kongConfig") KongConfig kongConfig) {
        this.kongConfig = kongConfig;
    }

    @Bean("lasHttpClient")
    public HttpClient lasHttpClient() {
        log.info("Kong configuration {}", kongConfig);
        var defaultConfig = kongConfig.getConfigForHost("default");
        return httpClient(defaultConfig.getConnectionTimeout());
    }

    private HttpClient httpClient(int timeout) {
        return new LoggingHttpClient(
                HttpClient
                        .newBuilder()
                        .connectTimeout(Duration.ofMillis(timeout))
                        .build()
        ) {
            private HttpRequest addAuth(HttpRequest req) {
                String hostname = null;
                try {
                    hostname = req.uri().toString();
                    var config = kongConfig.getConfigForHost(hostname);

                    String auth = config.getUsername() + ":" + config.getPassword();
                    return HttpRequest.newBuilder(req, (name, value) -> true)
                            .header("Authorization", "Basic " +
                                    Base64.getEncoder().encodeToString(auth.getBytes()))
                            .build();
                } catch (Exception e) {
                    log.warn("Failed to get configuration for hostname: {}, error: {}",
                            hostname, e.getMessage());
                    throw new RuntimeException("Failed to configure request", e);
                }
            }

            @Override
            public <T> HttpResponse<T> send(HttpRequest req, HttpResponse.BodyHandler<T> responseBodyHandler) throws IOException, InterruptedException {
                return super.send(addAuth(req), responseBodyHandler);
            }
        };
    }
}
