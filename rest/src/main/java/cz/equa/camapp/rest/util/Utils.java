package cz.equa.camapp.rest.util;

import java.time.OffsetDateTime;

public class Utils {

	public static String getApplicationId(Long applKey, String busAppId) {
		return applKey != null ? applKey.toString() : busAppId;
	}

	public static String getApplicationIdType(Long applKey) {
		return applKey != null ? "APPLKEY" : "BUSAPPLID";
	}
    
    public static OffsetDateTime getNowWithoutNanoseconds() {
        OffsetDateTime now = OffsetDateTime.now();
        int nanos = now.getNano()/1000000;
        return now.withNano(1000000 * nanos);
	}
}
