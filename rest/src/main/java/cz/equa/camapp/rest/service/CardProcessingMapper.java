package cz.equa.camapp.rest.service;

import cz.equa.camapp.lovs.LovFactory;
import cz.equa.camapp.model.card_processing.UpdateAddressDTO;
import cz.equa.camapp.model.card_processing.UpdatePersonalDataDTO;
import cz.rb.ifr.card.processing.model.UpdateAddressReqResp;
import cz.rb.ifr.card.processing.model.UpdatePersonalDataReqResp;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(uses = {LovFactory.class})
public interface CardProcessingMapper { //NOSONAR
    CardProcessingMapper INSTANCE = Mappers.getMapper(CardProcessingMapper.class);

    UpdatePersonalDataReqResp dtoToEsb(UpdatePersonalDataDTO entity);
    UpdateAddressReqResp dtoToEsb(UpdateAddressDTO entity);
}

