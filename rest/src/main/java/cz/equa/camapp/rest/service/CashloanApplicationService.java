package cz.equa.camapp.rest.service;

import cz.equa.camapp.rest.util.Utils;
import cz.equa.camapp.service.ServiceException;
import cz.rb.cus.application.handler.ApiException;
import cz.rb.cus.application.handler.CashloanApplicationsApi;
import cz.rb.cus.application.model.SetClientActionRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.UUID;

@Component
@RequiredArgsConstructor
public class CashloanApplicationService extends CommonRestService {
    private static final String SET_CLIENT_ACTION = "cus-application-v1-setClientAction";
    private static final String SET_INTEREST_CHANGE = "cus-application-v1-setInterestChange";

    private CashloanApplicationsApi cashloanApplicationsApi;

    @Autowired
    public CashloanApplicationService(CashloanApplicationsApi cashloanApplicationsApi) {
        this.cashloanApplicationsApi = cashloanApplicationsApi;
    }
    public void setClientAction(Long applKey, String eventType, String correlationId) throws ServiceException {
       setClientAction(applKey, eventType, correlationId, null);
    }

    public void setClientAction(Long applKey, String eventType, String correlationId, String clContextId) throws ServiceException {
        SetClientActionRequest request = new SetClientActionRequest();
        request.setEventType(eventType);
        
        try {
            cashloanApplicationsApi.cusApplicationV1SetClientAction(Long.toString(applKey),
                SET_CLIENT_ACTION,
                UUID.randomUUID().toString(),
                UUID.fromString(correlationId),
                X_REQUEST_APP,
                xFrontendApp,
                xFrontendService,
                "IBS",
                CommonRestService.CAM_USER,
                CommonRestService.EXT,
                clContextId,
                request);
        } catch (ApiException ex) {
            throw new ServiceException(ex.getMessage(), ex);
        }
    }

    public void setApplIntrsChng(Long applKey, String busApplId, String correlationId) throws ServiceException {
        try {
            cashloanApplicationsApi.cusApplicationV1SetInterestChange(Utils.getApplicationId(applKey, busApplId),
                Utils.getApplicationIdType(applKey),
                SET_INTEREST_CHANGE,
                UUID.randomUUID().toString(),
                UUID.fromString(correlationId),
                X_REQUEST_APP,
                xFrontendApp,
                xFrontendService,
                "IBS",
                CommonRestService.CAM_USER,
                CommonRestService.EXT);
        } catch (ApiException ex) {
            throw new ServiceException(ex.getMessage(), ex);
        }
    }
}
