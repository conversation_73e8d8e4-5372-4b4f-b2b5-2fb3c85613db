package cz.equa.camapp.rest.service;

import cz.equa.camapp.service.ServiceException;
import cz.rb.ocr.mut.handler.ApiException;
import cz.rb.ocr.mut.handler.V3Api;
import cz.rb.ocr.mut.model.MultiApplicantDmsDocumentRequestModel;
import cz.rb.ocr.mut.model.MultiDmsDocumentRequestModel;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.UUID;

@Component
public class MutOcrService extends CommonRestService {

    private static final String VALIDATE_STATEMENTS_ONLINE = "mut-ocr-v3-validateStatements";

    private final V3Api v3Api;

    public MutOcrService(V3Api v3Api) {
        this.v3Api = v3Api;
    }

    public void validateStatements(String applKey, List<MultiDmsDocumentRequestModel> applicants, boolean camNotificationRequired, String correlationId) throws ServiceException {
        try {
            MultiApplicantDmsDocumentRequestModel payload = new MultiApplicantDmsDocumentRequestModel();
            payload.setApplKey(applKey);
            payload.setApplicants(applicants);
            payload.setCamNotificationRequired(camNotificationRequired);
            v3Api.mutOcrV3ValidateStatements(
                    VALIDATE_STATEMENTS_ONLINE,
                    UUID.randomUUID().toString(),
                    correlationId,
                    X_REQUEST_APP,
                    payload,
                    xFrontendApp,
                    xFrontendService
            );
        } catch (ApiException ex) {
            throw new ServiceException(ex.getMessage());
        }
    }
}
