package cz.equa.camapp.rest.model;

import lombok.Getter;

import static cz.equa.camapp.rest.model.Document.*;

@Getter
public enum DocumentType {

    // ===== DOCUMENTS =====
    // Cashloan
    P0001206("<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", DocumentType.PDF, DELIVERY_CHANNEL_ARCHIVE, DocumentType.STORE, DOCUMENT_CLCFD_CL_DEFAULT),
    P0001200("Smlouva o úvěru", DocumentType.PDF, DELIVERY_CHANNEL_ARCHIVE, DocumentType.PRINTANDSTORE, DOCUMENT_CLCFD_CL_DEFAULT),
    P0001201("Smlouva o běžném účtu", DocumentType.PDF, DELIVERY_CHANNEL_ARCHIVE, DocumentType.PRINTANDSTORE, DOCUMENT_CLCFD_CL_DEFAULT),
    P0001217("<PERSON>k<PERSON><PERSON><PERSON><PERSON><PERSON> dopis k běžnému účtu", DocumentType.PDF, DELIVERY_CHANNEL_ARCHIVE, DocumentType.STORE, null),
    P0001218("Akceptační dokument k produktu - úvěr", DocumentType.PDF, DELIVERY_CHANNEL_ARCHIVE, DocumentType.PRINTANDSTORE, null),

    P0001240("Výpovědní dopis - půjčka", DocumentType.PDF, DELIVERY_CHANNEL_ARCHIVE, DocumentType.STORE, DOCUMENT_CLCFD_CL_DEFAULT),
    P0001241("Výpovědní dopis - kontokorent", DocumentType.PDF, DELIVERY_CHANNEL_ARCHIVE, DocumentType.STORE, DOCUMENT_CLNTF_PREPUBLISH),
    P0001242("Výpovědní dopis - půjčka", DocumentType.PDF, DELIVERY_CHANNEL_ARCHIVE, DocumentType.STORE, DOCUMENT_CLNTF_PREPUBLISH),
    P0001243("Výpovědní dopis - kreditní karta", DocumentType.PDF, DELIVERY_CHANNEL_ARCHIVE, DocumentType.STORE, DOCUMENT_CLNTF_PREPUBLISH),
    P0001238("Návrh na uzavření smlouvy o RePůjčce", DocumentType.PDF, DELIVERY_CHANNEL_ARCHIVE, DocumentType.STORE, DOCUMENT_CLCFD_CL_DEFAULT),
    P0001239("Akceptace návrhu na uzavření Smlouvy o půjčce na konsolidaci", DocumentType.PDF, DELIVERY_CHANNEL_ARCHIVE, DocumentType.STORE, null),
    P0001387("Předsmluvní informace", DocumentType.PDF, DELIVERY_CHANNEL_ARCHIVE, DocumentType.STORE, DOCUMENT_CLCFD_CL_DEFAULT),
    P0001388("Návrh na uzavření smlouvy o vydání kreditní karty", DocumentType.PDF, DELIVERY_CHANNEL_ARCHIVE, DocumentType.STORE, DOCUMENT_CLCFD_CL_DEFAULT),
    P0001389("Akceptace Návrhu na uzavření Smlouvy o vydání kreditní karty", DocumentType.PDF, DELIVERY_CHANNEL_ARCHIVE, DocumentType.STORE, null),
    P0001432("Předsmluvní informace", DocumentType.PDF, DELIVERY_CHANNEL_ARCHIVE, DocumentType.STORE, DOCUMENT_CLCFD_CL_DEFAULT),
    P0001433("Návrh na uzavření smlouvy o BR", DocumentType.PDF, DELIVERY_CHANNEL_ARCHIVE, DocumentType.STORE, DOCUMENT_CLCFD_CL_DEFAULT),
    P0001434("Návrh na uzavření smlouvy o PD", DocumentType.PDF, DELIVERY_CHANNEL_ARCHIVE, DocumentType.STORE, DOCUMENT_CLCFD_CL_DEFAULT),
    P0001435("Akceptace návrhu smlouvy", DocumentType.PDF, DELIVERY_CHANNEL_ARCHIVE, DocumentType.STORE, null),
    P0001460("Návrh na uzavření Dohody o navýšení Úvěrového limitu úvěru čerpaného kreditní kartou.", DocumentType.PDF, DELIVERY_CHANNEL_ARCHIVE, DocumentType.STORE, DOCUMENT_CLCFD_CL_DEFAULT),
    P0001461("Akceptace Návrhu na uzavření Dohody o navýšení Úvěrového limitu úvěru čerpaného kreditní kartou", DocumentType.PDF, DELIVERY_CHANNEL_ARCHIVE, DocumentType.STORE, null),
    P0001488("Návrh na uzavření Smlouvy o Optimální splátce", DocumentType.PDF, DELIVERY_CHANNEL_ARCHIVE, DocumentType.STORE, DOCUMENT_CLCFD_CL_DEFAULT),
    P0001489("Akceptace návrhu na uzavření Smlouvy o Optimální splátce", DocumentType.PDF, DELIVERY_CHANNEL_ARCHIVE, DocumentType.STORE, null),

    //Mortgage
    P0001427("Nabídka hypotečního úvěru.pdf", DocumentType.PDF, DELIVERY_CHANNEL_ARCHIVE, DocumentType.STORE, null),
    P0001533("Potvrzení o zrušení žádosti.pdf", DocumentType.PDF, DELIVERY_CHANNEL_ARCHIVE, DocumentType.STORE, null),

    // Deferred payment
    P0001231("Smlouva PlatímPak", DocumentType.PDF, DELIVERY_CHANNEL_ARCHIVE, DocumentType.STORE, null),

    // ===== EMAILS =====
    // Cashloan
    X0000005("Předsmluvní podmínky k účtu", DocumentType.PDF, DELIVERY_CHANNEL_EMAIL, DocumentType.NOURL, DocumentType.EML, DOCUMENT_CLCFD_CL_DEFAULT),
    X0000006(null, DocumentType.PDF, DELIVERY_CHANNEL_EMAIL, DocumentType.NOURL, DocumentType.EML, DOCUMENT_CLCFD_CL_DEFAULT),
    X0000007(null, DocumentType.PDF, DELIVERY_CHANNEL_EMAIL, DocumentType.NOURL, DocumentType.EML, DOCUMENT_CLCFD_CL_DEFAULT),
    X0000008(null, DocumentType.PDF, DELIVERY_CHANNEL_EMAIL, DocumentType.NOURL, DocumentType.EML, DOCUMENT_CLCFD_CL_DEFAULT),
    X0000009(null, DocumentType.PDF, DELIVERY_CHANNEL_EMAIL, DocumentType.NOURL, DocumentType.EML, DOCUMENT_CLCFD_CL_DEFAULT),
    X0000015("Dodejte prosím úvěrovou smlouvu k Vaší Repůjčce", DocumentType.PDF, DELIVERY_CHANNEL_EMAIL, DocumentType.NOURL, DocumentType.EML, null),
    X0000017("Dokumentace k Vaší kreditní kartě", DocumentType.PDF, DELIVERY_CHANNEL_EMAIL, DocumentType.NOURL, DocumentType.EML, null),
    X0000018("Akceptace Návrhu na uzavření Smlouvy o vydání kreditní karty", DocumentType.PDF, DELIVERY_CHANNEL_EMAIL, DocumentType.NOURL, DocumentType.EML, null),
    X0000029("Akceptace Návrhu na uzavření Dohody o navýšení Úvěrového limitu úvěru čerpaného kreditní kartou", DocumentType.PDF, DELIVERY_CHANNEL_EMAIL, DocumentType.NOURL, DocumentType.EML, null),

    // Deferred payment
    X0000012("PlatímPak: Možnosti ověření", DocumentType.PDF, DELIVERY_CHANNEL_EMAIL, DocumentType.NOURL, DocumentType.PDF, null),
    X0000013("PlatímPak: Hotovo, máte zaplaceno", DocumentType.PDF, DELIVERY_CHANNEL_EMAIL, DocumentType.NOURL, DocumentType.PDF, null),
    X0000014("PlatímPak: Vyhodnocení Vaší žádosti", DocumentType.PDF, DELIVERY_CHANNEL_EMAIL, DocumentType.NOURL, DocumentType.PDF, null),

    // Mortgage
    X0000026("Garantovaná nabídka", DocumentType.PDF, DELIVERY_CHANNEL_EMAIL, DocumentType.NOURL, DocumentType.PDF, null),
    X0000030("Zamítnutí hypoteční nabídky", DocumentType.PDF, DELIVERY_CHANNEL_EMAIL, DocumentType.NOURL, DocumentType.PDF, null),
    X0000042("Žádost o hypotéku – ověření příjmů bylo dokončeno", DocumentType.PDF, DELIVERY_CHANNEL_EMAIL, DocumentType.NOURL, DocumentType.PDF, null),
    X0000044("Potvrzení o zrušení žádosti o hypotéku", DocumentType.PDF, DELIVERY_CHANNEL_EMAIL, DocumentType.NOURL, DocumentType.PDF, null),
    X0000056("Nová nabídka úrokové sazby", DocumentType.PDF, DELIVERY_CHANNEL_EMAIL, DocumentType.NOURL, DocumentType.EML, null),
    P0001616("Oznámení o nové výši splátky", DocumentType.PDF, DELIVERY_CHANNEL_EMAIL, DocumentType.STORE, DocumentType.PDF, null),

    // REKO
    X0000045("Reko zamítnuto", DocumentType.EMAIL, DELIVERY_CHANNEL_EMAIL, DocumentType.NOURL, DocumentType.PDF, null),
    X0000046("Reko ověřování", DocumentType.EMAIL, DELIVERY_CHANNEL_EMAIL, DocumentType.NOURL, DocumentType.PDF, null),
    X0000047("Reko schváleno", DocumentType.EMAIL, DELIVERY_CHANNEL_EMAIL, DocumentType.NOURL, DocumentType.PDF, null),
    X0000048("Reko akceptováno", DocumentType.EMAIL, DELIVERY_CHANNEL_EMAIL, DocumentType.NOURL, DocumentType.PDF, null),
    P0001540("Návrh Smlouvy o stavebním spoření, Smlouvy o úvěru a Zástavní smlouvy", DocumentType.PDF, DELIVERY_CHANNEL_ARCHIVE, DocumentType.STORE, DocumentType.PDF, null),
    P0001541("Akceptace návrhu Smlouvy o stavebním spoření, Smlouvy o úvěru, Smlouvy o zřízení zástavního práva", DocumentType.PDF, DELIVERY_CHANNEL_ARCHIVE, DocumentType.STORE, DocumentType.PDF, null, CLNTF_CANCEL),
    P0001543("Předsmluvní informace", DocumentType.PDF, DELIVERY_CHANNEL_ARCHIVE, DocumentType.STORE, DocumentType.PDF, null, CLNTF_CANCEL),
    P0001614("Potvrzení o zrušení žádosti", DocumentType.PDF, DELIVERY_CHANNEL_ARCHIVE, DocumentType.STORE, DocumentType.PDF, null),
    P0001617("Potvrzení o zrušení žádosti", DocumentType.PDF, DELIVERY_CHANNEL_ARCHIVE, DocumentType.STORE, DocumentType.PDF, null),
    P0001644("Záznam z jednání", DocumentType.PDF, DELIVERY_CHANNEL_ARCHIVE, DocumentType.STORE, DocumentType.PDF, null);



    private static final String NOURL = "nourl";
    private static final String STORE = "store";
    private static final String EML = "eml";
    private static final String PDF = "pdf";
    private static final String EMAIL = "email";
    private static final String PRINTANDSTORE = "printandstore";

    private final String documentName;
    private final String format;
    private final String deliveryChannel;
    private final String processing;
    private final String contentFormat;
    private String documentSmState;
    private final String cancelSmState;

    DocumentType(String documentName, String format, String deliveryChannel, String processing, String documentSmState) {
        this(documentName, format, deliveryChannel, processing, "NONE", documentSmState, null);
    }

    DocumentType(String documentName, String format, String deliveryChannel, String processing, String contentFormat, String documentSmState) {
        this(documentName, format, deliveryChannel, processing, contentFormat, documentSmState, null);
    }

    DocumentType(String documentName, String format, String deliveryChannel, String processing, String contentFormat, String documentSmState, String cancelSmState) {
        this.documentName = documentName;
        this.format = format;
        this.deliveryChannel = deliveryChannel;
        this.processing = processing;
        this.contentFormat = contentFormat;
        this.documentSmState = documentSmState;
        this.cancelSmState = cancelSmState;
    }


    public String getDocumentName() {
        return documentName;
    }

    public String getFormat() {
        return format;
    }

    public String getDeliveryChannel() {
        return deliveryChannel;
    }

    public String getProcessing() {
        return processing;
    }

    public String getContentFormat() {
        return contentFormat;
    }

    public String getDocumentSmState() {
        return documentSmState;
    }

    public static DocumentType getDocumentType(String templateId, boolean isUpdate) {
        DocumentType documentType = valueOf(templateId);
        switch (documentType) {
            case P0001241, P0001242, P0001243 -> documentType.documentSmState = isUpdate ? documentType.documentSmState : null;
        }
        return documentType;
    }
}
