package cz.equa.camapp.rest.service;

import cz.equa.camapp.service.ServiceException;
import cz.rb.las.sms.handler.ApiException;
import cz.rb.las.sms.handler.DefaultApi;
import cz.rb.las.sms.model.GenerateSmsCodeRequest;
import cz.rb.las.sms.model.GenerateSmsCodeResponse;
import cz.rb.las.sms.model.VerifySmsCodeRequest;
import cz.rb.las.sms.model.VerifySmsCodeResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.UUID;

@Component
@RequiredArgsConstructor
public class SmsService extends CommonRestService {

	private static final String VERIFY_XAPI = "las-sms-v1-verifyCode";
	private static final String REGENERATE_XAPI = "las-sms-v1-regenerateCode";
	private static final String GENERATE_XAPI = "las-sms-v1-generateCode";


	private DefaultApi defaultApi;

	@Autowired
	public SmsService(DefaultApi defaultApi) {
		this.defaultApi = defaultApi;
	}

	public VerifySmsCodeResponse verifyCode(String processId, VerifySmsCodeRequest request, String correlationId) throws ServiceException {
		try {
			return defaultApi.lasSmsV1VerifyCode(processId,
					VERIFY_XAPI,
					UUID.randomUUID().toString(),
					correlationId,
					X_REQUEST_APP,
					request,
					UUID.randomUUID().toString(),
					xFrontendApp,
					xFrontendService,
					null,
					null,
					null);
		} catch (ApiException ex) {
			throw new ServiceException(ex.getMessage(), ex);
		}
	}

	public void regenerateCode(String processId, String correlationId) throws ServiceException {
		try {
			defaultApi.lasSmsV1RegenerateCode(processId,
					REGENERATE_XAPI,
					UUID.randomUUID().toString(),
					correlationId,
					X_REQUEST_APP,
					UUID.randomUUID().toString(),
					xFrontendApp,
					xFrontendService,
					null,
					null,
					null);
		} catch (ApiException ex) {
			throw new ServiceException(ex.getMessage(), ex);
		}
	}

	public GenerateSmsCodeResponse generateCode(GenerateSmsCodeRequest request, String correlationId) throws ServiceException {
		try {
			return defaultApi.lasSmsV1GenerateCode(
					GENERATE_XAPI,
					UUID.randomUUID().toString(),
					correlationId,
					X_REQUEST_APP,
					request,
					UUID.randomUUID().toString(),
					xFrontendApp,
					xFrontendService,
					null,
					null,
					null);
		} catch (ApiException ex) {
			throw new ServiceException(ex.getMessage(), ex);
		}
	}
}
