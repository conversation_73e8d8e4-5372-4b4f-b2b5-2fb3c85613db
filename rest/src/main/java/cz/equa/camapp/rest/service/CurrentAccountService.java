package cz.equa.camapp.rest.service;

import cz.equa.camapp.model.account_number.AccountNumberDto;
import cz.equa.camapp.service.ServiceException;
import cz.rb.bai.currentaccount.handler.ApiException;
import cz.rb.bai.currentaccount.handler.DefaultApi;
import cz.rb.bai.currentaccount.model.BaiCurrentAccountV1CreateNumber200Response;
import cz.rb.bai.currentaccount.model.CreateNumberReq;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.UUID;


@Component(value = "restCurrentAccountService")
@RequiredArgsConstructor
@Slf4j
public class CurrentAccountService extends CommonRestService {

    private static final String CREATE_NUMBER = "bai-current-account-v1-createNumber";

    private final DefaultApi defaultApi;

    public AccountNumberDto createNumber(String partyId, String correlationId) throws ServiceException {
        try {
            CreateNumberReq createNumberReq = new CreateNumberReq();
            createNumberReq.setPartyId(partyId);
            BaiCurrentAccountV1CreateNumber200Response response = defaultApi.baiCurrentAccountV1CreateNumber(
                    CREATE_NUMBER,
                    UUID.randomUUID(),
                    UUID.fromString(correlationId),
                    X_REQUEST_APP,
                    CAM_USER,
                    CommonRestService.SYS,
                    xFrontendApp,
                    xFrontendService,
                    createNumberReq
            );
            if (response == null) {
                return null;
            }
            return CurrentAccountMapper.INSTANCE.esbToDto(response);
        } catch (ApiException e) {
            throw new ServiceException(e.getMessage(), e);
        }
    }
}
