package cz.equa.camapp.rest.model;

public class Document {
    public static final String DOCUMENT_CLCFD_CL_DEFAULT = "CLCFD_CL_DEFAULT";
    public static final String DOCUMENT_CLNTF_PREPUBLISH = "CLNTF_PREPUBLISH";
    public static final String DOCUMENT_CLCFD_CANCEL = "CLCFD_CANCEL";
    public static final String DOCUMENT_CLCFD_DECLINED = "CLCFD_DECLINED";

    public static final String DELIVERY_CHANNEL_ARCHIVE = "ARCHIVE";
    public static final String DELIVERY_CHANNEL_EMAIL = "EMAIL";
    public static final String RENDERING_CONTENT_PROCESSING_PRINTANDSTORE = "printandstore";
    public static final String RENDERING_CONTENT_PROCESSING_STORE = "store";
    public static final String MINOR_FLAG = "MINOR";
    public static final String NO_VERSION_FLAG = "NO_VERSION";
    public static final String STR_ZDZAM = "STR / ZDZAM";


    public static final String REKO_DRAFT = "REKO_DRAFT";
    public static final String CLNTF_DRAFT = "CLNTF_DRAFT";
    public static final String CLNTF_DEFAULT = "CLNTF_DEFAULT";
    public static final String CLNTF_CANCEL = "CLNTF_CANCEL";
}
