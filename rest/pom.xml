<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cz.equa.camapp</groupId>
        <artifactId>camapp</artifactId>
        <version>70.0.07-SNAPSHOT</version>
    </parent>

    <artifactId>rest</artifactId>
    <packaging>jar</packaging>
    <name>rest</name>

    <dependencies>
        <dependency>
            <groupId>cz.equa.camapp</groupId>
            <artifactId>common</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-log4j2</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
        </dependency>
        <dependency>
            <groupId>org.openapitools</groupId>
            <artifactId>jackson-databind-nullable</artifactId>
            <version>0.2.4</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>${mapstruct.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mock-server</groupId>
            <artifactId>mockserver-netty-no-dependencies</artifactId>
            <version>5.15.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>uk.co.jemos.podam</groupId>
            <artifactId>podam</artifactId>
            <version>${podam.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.unitils</groupId>
            <artifactId>unitils-core</artifactId>
            <version>3.4.6</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents.client5</groupId>
            <artifactId>httpclient5</artifactId>
            <version>${httpclient.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.jaxrs</groupId>
            <artifactId>jackson-jaxrs-json-provider</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.annotation</groupId>
            <artifactId>javax.annotation-api</artifactId>
            <version>1.3.2</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.sun.xml.ws</groupId>
            <artifactId>jaxws-ri</artifactId>
            <type>pom</type>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-oxm</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.openapitools</groupId>
                    <artifactId>openapi-generator-maven-plugin</artifactId>
                    <version>7.5.0</version>
                    <configuration>
                        <generatorName>java</generatorName>
                        <generateApiDocumentation>false</generateApiDocumentation>
                        <generateApiTests>false</generateApiTests>
                        <generateModelTests>false</generateModelTests>
                        <generateSupportingFiles>true</generateSupportingFiles>
                        <generateApis>true</generateApis>
                        <httpUserAgent>Camunda-rest-client</httpUserAgent>
                        <templateDirectory>${project.basedir}/src/main/resources/templates</templateDirectory>
                        <configOptions>
                            <sourceFolder>/</sourceFolder>
                            <useJakartaEe>true</useJakartaEe>
                        </configOptions>
                        <library>native</library>
<!--                        <typeMappings>-->
<!--                            <typeMapping>Double=java.math.BigDecimal</typeMapping>-->
<!--                            <typeMapping>Float=java.math.BigDecimal</typeMapping>-->
<!--                            <typeMapping>double=java.math.BigDecimal</typeMapping>-->
<!--                            <typeMapping>float=java.math.BigDecimal</typeMapping>-->
<!--                        </typeMappings>-->
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.openapitools</groupId>
                <artifactId>openapi-generator-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>las-sms-v1</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.basedir}/src/main/resources/rest/las-sms-v1.yaml</inputSpec>
                            <apiPackage>cz.rb.las.sms.handler</apiPackage>
                            <invokerPackage>cz.rb.las.sms.handler</invokerPackage>
                            <additionalProperties>configName=las.cz.rb.las.sms.handler,httpClientType=las,parseResponseBody=true</additionalProperties>
                            <modelPackage>cz.rb.las.sms.model</modelPackage>
                        </configuration>
                    </execution>
                    <execution>
                        <id>las-operation-v1</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.basedir}/src/main/resources/rest/las-operation-v1.yaml</inputSpec>
                            <apiPackage>cz.rb.las.operation.handler</apiPackage>
                            <invokerPackage>cz.rb.las.operation.handler</invokerPackage>
                            <additionalProperties>configName=las.cz.rb.las.operation.handler,httpClientType=las,parseResponseBody=true</additionalProperties>
                            <modelPackage>cz.rb.las.operation.model</modelPackage>
                        </configuration>
                    </execution>
                    <execution>
                        <id>las-application-v1</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.basedir}/src/main/resources/rest/las-application-v1.yaml</inputSpec>
                            <apiPackage>cz.rb.las.application.handler</apiPackage>
                            <invokerPackage>cz.rb.las.application.handler</invokerPackage>
                            <additionalProperties>configName=las.cz.rb.las.application.handler,httpClientType=las,parseResponseBody=true</additionalProperties>
                            <modelPackage>cz.rb.las.application.model</modelPackage>
                        </configuration>
                    </execution>
                    <execution>
                        <id>las-approval-v1</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.basedir}/src/main/resources/rest/las-approval-v1.yaml</inputSpec>
                            <apiPackage>cz.rb.las.approval.handler</apiPackage>
                            <invokerPackage>cz.rb.las.approval.handler</invokerPackage>
                            <additionalProperties>configName=las.cz.rb.las.approval.handler,httpClientType=las,parseResponseBody=true</additionalProperties>
                            <modelPackage>cz.rb.las.approval.model</modelPackage>
                        </configuration>
                    </execution>
                    <execution>
                        <id>las-parametrization-app-v1</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.basedir}/src/main/resources/rest/las-parametrization-app-v1.yaml</inputSpec>
                            <apiPackage>cz.rb.las.parametrization.handler</apiPackage>
                            <invokerPackage>cz.rb.las.parametrization.handler</invokerPackage>
                            <additionalProperties>configName=las.cz.rb.las.parametrization.handler,httpClientType=las,parseResponseBody=true</additionalProperties>
                            <modelPackage>cz.rb.las.parametrization.model</modelPackage>
                        </configuration>
                    </execution>
                    <execution>
                        <id>cus-application-v1.yaml</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.basedir}/src/main/resources/rest/cus-application-v1.yaml</inputSpec>
                            <apiPackage>cz.rb.cus.application.handler</apiPackage>
                            <invokerPackage>cz.rb.cus.application.handler</invokerPackage>
                            <additionalProperties>configName=las.cz.rb.cus.application.handler,httpClientType=las,parseResponseBody=true</additionalProperties>
                            <modelPackage>cz.rb.cus.application.model</modelPackage>
                        </configuration>
                    </execution>
                    <execution>
                        <id>had-partners-v1</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.basedir}/src/main/resources/rest/had-partners-v1.yaml</inputSpec>
                            <apiPackage>cz.rb.had.partners.handler</apiPackage>
                            <invokerPackage>cz.rb.had.partners.handler</invokerPackage>
                            <additionalProperties>configName=las.cz.rb.had.partners.handler,httpClientType=las,parseResponseBody=true</additionalProperties>
                            <modelPackage>cz.rb.had.partners.model</modelPackage>
                        </configuration>
                    </execution>
                    <execution>
                        <id>cus-party-application-v1</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.basedir}/src/main/resources/rest/cus-party-application-v1.yaml</inputSpec>
                            <apiPackage>cz.rb.cus.party.application.handler</apiPackage>
                            <invokerPackage>cz.rb.cus.party.application.handler</invokerPackage>
                            <additionalProperties>configName=las.cz.rb.cus.party.application.handler,httpClientType=las,parseResponseBody=true</additionalProperties>
                            <modelPackage>cz.rb.cus.party.application.model</modelPackage>
                        </configuration>
                    </execution>
                    <execution>
                        <id>had-interest-change-v1</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.basedir}/src/main/resources/rest/had-interest-change-v1.yaml</inputSpec>
                            <apiPackage>cz.rb.had.interestchange.handler</apiPackage>
                            <invokerPackage>cz.rb.had.interestchange.handler</invokerPackage>
                            <additionalProperties>configName=las.cz.rb.had.interestchange.handler,httpClientType=las,parseResponseBody=true</additionalProperties>
                            <modelPackage>cz.rb.had.interestchange.model</modelPackage>
                        </configuration>
                    </execution>
                    <execution>
                        <id>cus-party-v1</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.basedir}/src/main/resources/rest/cus-party-v1.yaml</inputSpec>
                            <apiPackage>cz.rb.cus.party.handler</apiPackage>
                            <invokerPackage>cz.rb.cus.party.handler</invokerPackage>
                            <additionalProperties>configName=las.cz.rb.cus.party.handler,httpClientType=las,parseResponseBody=true</additionalProperties>
                            <modelPackage>cz.rb.cus.party.model</modelPackage>
                        </configuration>
                    </execution>
                    <execution>
                        <id>bai-loan-approval-v1</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.basedir}/src/main/resources/rest/bai-loan-approval-v1.yaml</inputSpec>
                            <apiPackage>cz.rb.bai.loan.approval.handler</apiPackage>
                            <invokerPackage>cz.rb.bai.loan.approval.handler</invokerPackage>
                            <additionalProperties>configName=las.cz.rb.bai.loan.approval.handler,httpClientType=las,parseResponseBody=true</additionalProperties>
                            <modelPackage>cz.rb.bai.loan.approval.model</modelPackage>
                        </configuration>
                    </execution>
                    <execution>
                        <id>ifr-card-processing-v1</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.basedir}/src/main/resources/rest/ifr-card-processing-v1.yaml</inputSpec>
                            <apiPackage>cz.rb.ifr.card.processing.handler</apiPackage>
                            <invokerPackage>cz.rb.ifr.card.processing.handler</invokerPackage>
                            <additionalProperties>configName=las.cz.rb.ifr.card.processing.handler,httpClientType=las,parseResponseBody=true</additionalProperties>
                            <modelPackage>cz.rb.ifr.card.processing.model</modelPackage>
                        </configuration>
                    </execution>
                    <execution>
                        <id>ifr-message-v1</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.basedir}/src/main/resources/rest/ifr-message-v1.yaml</inputSpec>
                            <apiPackage>cz.rb.ifr.message.handler</apiPackage>
                            <invokerPackage>cz.rb.ifr.message.handler</invokerPackage>
                            <additionalProperties>configName=las.cz.rb.ifr.message.handler,httpClientType=las,parseResponseBody=true</additionalProperties>
                            <modelPackage>cz.rb.ifr.message.model</modelPackage>
                        </configuration>
                    </execution>
                    <execution>
                        <id>dms-document-v1</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.basedir}/src/main/resources/rest/dms-document-v1.yaml</inputSpec>
                            <apiPackage>cz.rb.dms.document.handler</apiPackage>
                            <invokerPackage>cz.rb.dms.document.handler</invokerPackage>
                            <additionalProperties>configName=las.cz.rb.dms.document.handler,httpClientType=las,parseResponseBody=true</additionalProperties>
                            <modelPackage>cz.rb.dms.document.model</modelPackage>
                            <templateDirectory>${project.basedir}/src/main/resources/templates/libraries/native/dmstemplates</templateDirectory>
                        </configuration>
                    </execution>
                    <execution>
                        <id>dms-document-v2</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.basedir}/src/main/resources/rest/dms-document-v2.yaml</inputSpec>
                            <apiPackage>cz.rb.dms.document.v2.handler</apiPackage>
                            <invokerPackage>cz.rb.dms.document.v2.handler</invokerPackage>
                            <additionalProperties>configName=las.cz.rb.dms.document.v2.handler,httpClientType=las,parseResponseBody=true</additionalProperties>
                            <modelPackage>cz.rb.dms.document.v2.model</modelPackage>
                            <templateDirectory>${project.basedir}/src/main/resources/templates/libraries/native/dmstemplates</templateDirectory>
                        </configuration>
                    </execution>
                    <execution>
                        <id>cus-activity-v1</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.basedir}/src/main/resources/rest/cus-activity-v1.yaml</inputSpec>
                            <apiPackage>cz.rb.cus.activity.handler</apiPackage>
                            <invokerPackage>cz.rb.cus.activity.handler</invokerPackage>
                            <additionalProperties>configName=las.cz.rb.cus.activity.handler,httpClientType=las,parseResponseBody=true</additionalProperties>
                            <modelPackage>cz.rb.cus.activity.model</modelPackage>
                        </configuration>
                    </execution>
                    <execution>
                        <id>bai-loan-v1</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.basedir}/src/main/resources/rest/bai-loan-v1.yaml</inputSpec>
                            <apiPackage>cz.rb.bai.loan.handler</apiPackage>
                            <invokerPackage>cz.rb.bai.loan.handler</invokerPackage>
                            <additionalProperties>configName=las.cz.rb.bai.loan.approval.handler,httpClientType=las,parseResponseBody=true</additionalProperties>
                            <modelPackage>cz.rb.bai.loan.model</modelPackage>
                        </configuration>
                    </execution>
                    <execution>
                        <id>bai-account-v1</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.basedir}/src/main/resources/rest/bai-account-v1.yaml</inputSpec>
                            <apiPackage>cz.rb.bai.account.handler</apiPackage>
                            <invokerPackage>cz.rb.bai.account.handler</invokerPackage>
                            <additionalProperties>configName=las.cz.rb.bai.account.handler,httpClientType=las,parseResponseBody=true</additionalProperties>
                            <modelPackage>cz.rb.bai.account.model</modelPackage>
                        </configuration>
                    </execution>
                    <execution>
                        <id>mut-external-v1</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.basedir}/src/main/resources/rest/mut-external-v1.yaml</inputSpec>
                            <apiPackage>cz.rb.mut.external.handler</apiPackage>
                            <invokerPackage>cz.rb.mut.external.handler</invokerPackage>
                            <additionalProperties>configName=las.cz.rb.mut.external.handler,httpClientType=las,parseResponseBody=true</additionalProperties>
                            <modelPackage>cz.rb.mut.external.model</modelPackage>
                        </configuration>
                    </execution>
                    <execution>
                        <id>cut-ocr-v2</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.basedir}/src/main/resources/rest/cut-ocr-v2.yaml</inputSpec>
                            <apiPackage>cz.rb.ocr.cut.handler</apiPackage>
                            <invokerPackage>cz.rb.ocr.cut.handler</invokerPackage>
                            <additionalProperties>configName=ocr.cz.rb.v2.handler,httpClientType=las,parseResponseBody=true</additionalProperties>
                            <modelPackage>cz.rb.ocr.cut.model</modelPackage>
                        </configuration>
                    </execution>
                    <execution>
                        <id>mdc-custom-v1</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.basedir}/src/main/resources/rest/mdc-custom-v1.yaml</inputSpec>
                            <apiPackage>cz.rb.mdc.custom.handler</apiPackage>
                            <invokerPackage>cz.rb.mdc.custom.handler</invokerPackage>
                            <additionalProperties>configName=las.cz.rb.mdc.custom.handler,httpClientType=las,parseResponseBody=true</additionalProperties>
                            <modelPackage>cz.rb.mdc.custom.model</modelPackage>
                        </configuration>
                    </execution>
                    <execution>
                        <id>mut-ocr-v3</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.basedir}/src/main/resources/rest/mut-ocr-v3.yaml</inputSpec>
                            <apiPackage>cz.rb.ocr.mut.handler</apiPackage>
                            <invokerPackage>cz.rb.ocr.mut.handler</invokerPackage>
                            <additionalProperties>configName=mut.cz.rb.ocr.v3.handler,httpClientType=las,parseResponseBody=true</additionalProperties>
                            <modelPackage>cz.rb.ocr.mut.model</modelPackage>
                        </configuration>
                    </execution>
                    <execution>
                        <id>bai-current-account-v1</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.basedir}/src/main/resources/rest/bai-current-account-v1.yaml</inputSpec>
                            <apiPackage>cz.rb.bai.currentaccount.handler</apiPackage>
                            <invokerPackage>cz.rb.bai.currentaccount.handler</invokerPackage>
                            <additionalProperties>configName=las.cz.rb.bai.currentaccount.handler,httpClientType=las,parseResponseBody=true</additionalProperties>
                            <modelPackage>cz.rb.bai.currentaccount.model</modelPackage>
                        </configuration>
                    </execution>
                    <execution>
                        <id>rest-tif-ims-v2</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>${project.basedir}/src/main/resources/rest/rest-tif-ims-v2.yaml</inputSpec>
                            <apiPackage>cz.rb.tif.ims.handler</apiPackage>
                            <invokerPackage>cz.rb.tif.ims.handler</invokerPackage>
                            <additionalProperties>
                                configName=las.cz.rb.tif.ims.handler,httpClientType=las,parseResponseBody=false
                            </additionalProperties>
                            <modelPackage>cz.rb.tif.ims.model</modelPackage>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.jvnet.jaxb</groupId>
                <artifactId>jaxb-maven-plugin</artifactId>
                <version>3.0.0</version>
                <dependencies>
                    <dependency>
                        <groupId>org.glassfish.jaxb</groupId>
                        <artifactId>jaxb-runtime</artifactId>
                        <version>${jaxb-runtime.version}</version>
                    </dependency>
                </dependencies>
                <executions>
                    <execution>
                        <id>scriptura-xsd-schema-generate</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <generateDirectory>${project.build.directory}/generated-sources/scriptura</generateDirectory>
                            <bindingDirectory>src/main/resources/binding</bindingDirectory>
                            <bindingIncludes>
                                <include>dateBinding.xjb</include>
                            </bindingIncludes>
                            <schemaDirectory>src/main/resources/scriptura</schemaDirectory>
                            <schemaIncludes>
                                <include>*.xsd</include>
                            </schemaIncludes>
                            <episodeFile>${project.build.directory}/generated-sources/xsd-generate.episode</episodeFile>
                            <removeOldOutput>true</removeOldOutput>
                            <args>
                                <arg>-XautoNameResolution</arg>
                                <arg>-Xfluent</arg>
                                <arg>-Xnonnull</arg>
                            </args>
                            <plugins>
                                <dependency>
                                    <groupId>cz.rb.camapp.jaxb2</groupId>
                                    <artifactId>fluent</artifactId>
                                    <version>${project.version}</version>
                                </dependency>
                                <dependency>
                                    <groupId>cz.rb.camapp.jaxb2</groupId>
                                    <artifactId>nonnull</artifactId>
                                    <version>${project.version}</version>
                                </dependency>
                            </plugins>
                        </configuration>
                    </execution>
                    <execution>
                        <id>xsd-generate</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <generateDirectory>${project.build.directory}/generated-sources/xsd</generateDirectory>
                            <bindingDirectory>src/main/resources/binding</bindingDirectory>
                            <bindingIncludes>*.xjb</bindingIncludes>
                            <schemaDirectory>src/main/resources/tif/xml</schemaDirectory>
                            <schemaIncludes>
                                <include>**/*.xsd</include>
                            </schemaIncludes>
                            <episodeFile>${project.build.directory}/generated-sources/xsd-generate.episode</episodeFile>
                            <removeOldOutput>true</removeOldOutput>
                            <args>
                                <arg>-XautoNameResolution</arg>
                                <arg>-Xfluent</arg>
                                <arg>-Xnonnull</arg>
                            </args>
                            <plugins>
                                <dependency>
                                    <groupId>cz.rb.camapp.jaxb2</groupId>
                                    <artifactId>fluent</artifactId>
                                    <version>${project.version}</version>
                                </dependency>
                                <dependency>
                                    <groupId>cz.rb.camapp.jaxb2</groupId>
                                    <artifactId>nonnull</artifactId>
                                    <version>${project.version}</version>
                                </dependency>
                            </plugins>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <profiles>
        <profile>
            <!-- local dev groovy scripts generations (download-yaml.groovy) -->
            <id>groovy-generate</id>
            <dependencies>
                <dependency>
                    <groupId>org.codehaus.groovy</groupId>
                    <artifactId>groovy-all</artifactId>
                    <version>3.0.21</version>
                </dependency>
                <dependency>
                    <groupId>org.apache.ivy</groupId>
                    <artifactId>ivy</artifactId>
                    <version>2.5.2</version>
                </dependency>
            </dependencies>
        </profile>
    </profiles>
</project>
