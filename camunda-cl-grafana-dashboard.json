{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "description": "Dashboard for Camunda CL and Spring Boot Statistics", "editable": true, "fiscalYearStartMonth": 0, "gnetId": 6756, "graphTooltip": 0, "id": 4, "links": [], "liveNow": false, "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 102, "panels": [], "title": "Document generator", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 1}, "id": 104, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "editorMode": "code", "expr": "previewController_generate{instance=\"$instance\", application=\"$application\"}", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Generate document count", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 1}, "id": 106, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "editorMode": "code", "expr": "generateDocument_duration{instance=\"$instance\", application=\"$application\"}", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Generate document duration", "type": "timeseries"}, {"collapsed": false, "datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 9}, "id": 54, "panels": [], "targets": [{"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "refId": "A"}], "title": "Basic Statistics", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 1, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 0, "y": 10}, "id": 52, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.2.4", "targets": [{"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "expr": "process_uptime_seconds{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "", "metric": "", "refId": "A", "step": 14400}], "title": "Uptime", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 1, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(50, 172, 45, 0.97)", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 70}, {"color": "rgba(245, 54, 54, 0.9)", "value": 90}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 6, "w": 5, "x": 6, "y": 10}, "id": 58, "links": [], "maxDataPoints": 100, "options": {"orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "9.2.4", "targets": [{"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "editorMode": "code", "expr": "sum(jvm_memory_used_bytes{application=\"$application\", instance=\"$instance\", area=\"heap\"})*100/sum(jvm_memory_max_bytes{application=\"$application\",instance=\"$instance\", area=\"heap\"})", "format": "time_series", "intervalFactor": 1, "legendFormat": "", "range": true, "refId": "A", "step": 14400}], "title": "Heap Used", "type": "gauge"}, {"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "decimals": 1, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}, {"options": {"from": -1e+32, "result": {"text": "N/A"}, "to": 0}, "type": "range"}], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(50, 172, 45, 0.97)", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 70}, {"color": "rgba(245, 54, 54, 0.9)", "value": 90}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 6, "w": 5, "x": 11, "y": 10}, "id": 60, "links": [], "maxDataPoints": 100, "options": {"orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true}, "pluginVersion": "9.2.4", "targets": [{"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "expr": "sum(jvm_memory_used_bytes{application=\"$application\", instance=\"$instance\", area=\"nonheap\"})*100/sum(jvm_memory_max_bytes{application=\"$application\",instance=\"$instance\", area=\"nonheap\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "", "refId": "A", "step": 14400}], "title": "Non-Heap Used", "type": "gauge"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 16, "y": 10}, "hiddenSeries": false, "id": 66, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "editorMode": "code", "expr": "process_files_open{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Open Files", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "expr": "process_files_max{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Max Files", "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "Process Open Files", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "locale", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "dateTimeAsIso"}, "overrides": []}, "gridPos": {"h": 3, "w": 6, "x": 0, "y": 13}, "id": 56, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {"valueSize": 26}, "textMode": "auto"}, "pluginVersion": "9.2.4", "targets": [{"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "expr": "process_start_time_seconds{application=\"$application\", instance=\"$instance\"}*1000", "format": "time_series", "intervalFactor": 2, "legendFormat": "", "metric": "", "refId": "A", "step": 14400}], "title": "Start time", "type": "stat"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 16}, "hiddenSeries": false, "id": 95, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "expr": "system_cpu_usage{instance=\"$instance\", application=\"$application\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "System CPU Usage", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "expr": "process_cpu_usage{instance=\"$instance\", application=\"$application\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Process CPU Usage", "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "CPU Usage", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 16}, "hiddenSeries": false, "id": 96, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "expr": "system_load_average_1m{instance=\"$instance\", application=\"$application\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Load Average [1m]", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "expr": "system_cpu_count{instance=\"$instance\", application=\"$application\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "CPU Core Size", "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "Load Average", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"collapsed": true, "datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 23}, "id": 48, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 34}, "hiddenSeries": false, "id": 85, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "repeat": "memory_pool_heap", "repeatDirection": "h", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "expr": "jvm_memory_used_bytes{instance=\"$instance\", application=\"$application\", id=\"$memory_pool_heap\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Used", "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "expr": "jvm_memory_committed_bytes{instance=\"$instance\", application=\"$application\", id=\"$memory_pool_heap\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Commited", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "expr": "jvm_memory_max_bytes{instance=\"$instance\", application=\"$application\", id=\"$memory_pool_heap\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Max", "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "$memory_pool_heap (heap)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "bytes", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 42}, "hiddenSeries": false, "id": 88, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "repeat": "memory_pool_nonheap", "repeatDirection": "h", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "expr": "jvm_memory_used_bytes{instance=\"$instance\", application=\"$application\", id=\"$memory_pool_nonheap\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Used", "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "expr": "jvm_memory_committed_bytes{instance=\"$instance\", application=\"$application\", id=\"$memory_pool_nonheap\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Commited", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "expr": "jvm_memory_max_bytes{instance=\"$instance\", application=\"$application\", id=\"$memory_pool_nonheap\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Max", "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "$memory_pool_nonheap (non-heap)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "bytes", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "decimals": 0, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 50}, "hiddenSeries": false, "id": 50, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "expr": "jvm_classes_loaded{instance=\"$instance\", application=\"$application\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Classes Loaded", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "Classes Loaded", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"decimals": 0, "format": "locale", "label": "", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 50}, "hiddenSeries": false, "id": 80, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "expr": "irate(jvm_classes_unloaded_total{instance=\"$instance\", application=\"$application\"}[5m])", "format": "time_series", "intervalFactor": 1, "legendFormat": "Classes Unloaded", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "Classes Unloaded", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 58}, "hiddenSeries": false, "id": 82, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "expr": "jvm_buffer_memory_used_bytes{instance=\"$instance\", application=\"$application\", id=\"direct\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Used Bytes", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "expr": "jvm_buffer_total_capacity_bytes{instance=\"$instance\", application=\"$application\", id=\"direct\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Capacity Bytes", "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "Direct Buffers", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 58}, "hiddenSeries": false, "id": 83, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "expr": "jvm_buffer_memory_used_bytes{instance=\"$instance\", application=\"$application\", id=\"mapped\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Used Bytes", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "expr": "jvm_buffer_total_capacity_bytes{instance=\"$instance\", application=\"$application\", id=\"mapped\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Capacity Bytes", "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "Mapped Buffers", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 65}, "hiddenSeries": false, "id": 68, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "expr": "jvm_threads_daemon{instance=\"$instance\", application=\"$application\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Daemon", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "expr": "jvm_threads_live{instance=\"$instance\", application=\"$application\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Live", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "expr": "jvm_threads_peak{instance=\"$instance\", application=\"$application\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Peak", "refId": "C"}], "thresholds": [], "timeRegions": [], "title": "Threads", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 65}, "hiddenSeries": false, "id": 78, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "expr": "irate(jvm_gc_memory_allocated_bytes_total{instance=\"$instance\", application=\"$application\"}[5m])", "format": "time_series", "intervalFactor": 1, "legendFormat": "allocated", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "expr": "irate(jvm_gc_memory_promoted_bytes_total{instance=\"$instance\", application=\"$application\"}[5m])", "format": "time_series", "intervalFactor": 1, "legendFormat": "promoted", "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "Memory Allocate/Promote", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "bytes", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}], "targets": [{"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "refId": "A"}], "title": "JVM Statistics - Memory", "type": "row"}, {"collapsed": true, "datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 24}, "id": 72, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 87}, "hiddenSeries": false, "id": 74, "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": true, "hideZero": true, "max": true, "min": true, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "expr": "irate(jvm_gc_pause_seconds_count{instance=\"$instance\", application=\"$application\"}[5m])", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{action}} [{{cause}}]", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "GC Count", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "locale", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 87}, "hiddenSeries": false, "id": 76, "legend": {"alignAsTable": true, "avg": true, "current": false, "hideEmpty": true, "hideZero": true, "max": true, "min": true, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "expr": "irate(jvm_gc_pause_seconds_sum{instance=\"$instance\", application=\"$application\"}[5m])", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{action}} [{{cause}}]", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "GC Stop the World Duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "s", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}], "targets": [{"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "refId": "A"}], "title": "JVM Statistics - GC", "type": "row"}, {"collapsed": true, "datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 25}, "id": 34, "panels": [{"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 4, "x": 0, "y": 98}, "id": 44, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.2.4", "targets": [{"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "expr": "hikaricp_connections{instance=\"$instance\", application=\"$application\", pool=\"$hikaricp\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Connections Size", "type": "stat"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 20, "x": 4, "y": 98}, "hiddenSeries": false, "id": 36, "legend": {"alignAsTable": true, "avg": true, "current": true, "hideEmpty": true, "hideZero": false, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "expr": "hikaricp_connections_active{instance=\"$instance\", application=\"$application\", pool=\"$hikaricp\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Active", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "expr": "hikaricp_connections_idle{instance=\"$instance\", application=\"$application\", pool=\"$hikaricp\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Idle", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "expr": "hikaricp_connections_pending{instance=\"$instance\", application=\"$application\", pool=\"$hikaricp\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Pending", "refId": "C"}], "thresholds": [], "timeRegions": [], "title": "Connections", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 4, "x": 0, "y": 102}, "id": 46, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.2.4", "targets": [{"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "expr": "hikaricp_connections_timeout_total{instance=\"$instance\", application=\"$application\", pool=\"$hikaricp\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Connection Timeout Count", "type": "stat"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 0, "y": 106}, "hiddenSeries": false, "id": 38, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "expr": "hikaricp_connections_creation_seconds_sum{instance=\"$instance\", application=\"$application\", pool=\"$hikaricp\"} / hikaricp_connections_creation_seconds_count{instance=\"$instance\", application=\"$application\", pool=\"$hikaricp\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Creation Time", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "Connection Creation Time", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "s", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 8, "y": 106}, "hiddenSeries": false, "id": 42, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "expr": "hikaricp_connections_usage_seconds_sum{instance=\"$instance\", application=\"$application\", pool=\"$hikaricp\"} / hikaricp_connections_usage_seconds_count{instance=\"$instance\", application=\"$application\", pool=\"$hikaricp\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Usage Time", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "Connection Usage Time", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "s", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 16, "y": 106}, "hiddenSeries": false, "id": 40, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "expr": "hikaricp_connections_acquire_seconds_sum{instance=\"$instance\", application=\"$application\", pool=\"$hikaricp\"} / hikaricp_connections_acquire_seconds_count{instance=\"$instance\", application=\"$application\", pool=\"$hikaricp\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Acquire Time", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "Connection Acquire Time", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "s", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}], "targets": [{"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "refId": "A"}], "title": "HikariCP Statistics", "type": "row"}, {"collapsed": true, "datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 26}, "id": 18, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 113}, "hiddenSeries": false, "id": 4, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "expr": "irate(http_server_requests_seconds_count{instance=\"$instance\", application=\"$application\", uri!~\".*actuator.*\"}[5m])", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{method}} [{{status}}] - {{uri}}", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "Request Count", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "none", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 120}, "hiddenSeries": false, "id": 2, "legend": {"alignAsTable": true, "avg": true, "current": false, "max": true, "min": true, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "expr": "irate(http_server_requests_seconds_sum{instance=\"$instance\", application=\"$application\", exception=\"None\", uri!~\".*actuator.*\"}[5m]) / irate(http_server_requests_seconds_count{instance=\"$instance\", application=\"$application\", exception=\"None\", uri!~\".*actuator.*\"}[5m])", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{method}} [{{status}}] - {{uri}}", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "Response Time", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "s", "label": "", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}], "targets": [{"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "refId": "A"}], "title": "HTTP Statistics", "type": "row"}, {"collapsed": true, "datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 27}, "id": 22, "panels": [{"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "locale"}, "overrides": []}, "gridPos": {"h": 4, "w": 4, "x": 0, "y": 128}, "id": 28, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.2.4", "targets": [{"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "expr": "tomcat_global_error_total{instance=\"$instance\", application=\"$application\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Total Error Count", "type": "stat"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "decimals": 0, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 9, "x": 4, "y": 128}, "hiddenSeries": false, "id": 24, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "expr": "tomcat_sessions_active_current{instance=\"$instance\", application=\"$application\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "active sessions", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "Active Sessions", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "none", "label": "", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 11, "x": 13, "y": 128}, "hiddenSeries": false, "id": 26, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "expr": "irate(tomcat_global_sent_bytes_total{instance=\"$instance\", application=\"$application\"}[5m])", "format": "time_series", "intervalFactor": 1, "legendFormat": "<PERSON><PERSON>", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "expr": "irate(tomcat_global_received_bytes_total{instance=\"$instance\", application=\"$application\"}[5m])", "format": "time_series", "intervalFactor": 1, "legendFormat": "Recieved Bytes", "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "Sent & Recieved Bytes", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "bytes", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "locale"}, "overrides": []}, "gridPos": {"h": 3, "w": 4, "x": 0, "y": 132}, "id": 32, "links": [], "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.2.4", "targets": [{"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "expr": "tomcat_threads_config_max{instance=\"$instance\", application=\"$application\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "<PERSON><PERSON><PERSON> Config <PERSON>", "type": "stat"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 13, "x": 0, "y": 135}, "hiddenSeries": false, "id": 30, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "expr": "tomcat_threads_current{instance=\"$instance\", application=\"$application\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Current thread", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "expr": "tomcat_threads_busy{instance=\"$instance\", application=\"$application\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "Current thread busy", "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "Threads", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}], "targets": [{"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "refId": "A"}], "title": "Tomcat Statistics", "type": "row"}, {"collapsed": true, "datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 28}, "id": 8, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 129}, "hiddenSeries": false, "id": 6, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"alias": "", "datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "editorMode": "code", "expr": "irate(log4j2_events_total{instance=\"$instance\", application=\"$application\", level=\"info\"}[5m])", "format": "time_series", "intervalFactor": 1, "legendFormat": "info", "range": true, "rawSql": "SELECT\n  $__time(time_column),\n  value1\nFROM\n  metric_table\nWHERE\n  $__timeFilter(time_column)\n", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "INFO logs", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "none", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 129}, "hiddenSeries": false, "id": 10, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"alias": "", "datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "editorMode": "code", "expr": "irate(log4j2_events_total{instance=\"$instance\", application=\"$application\", level=\"error\"}[5m])", "format": "time_series", "intervalFactor": 1, "legendFormat": "error", "range": true, "rawSql": "SELECT\n  $__time(time_column),\n  value1\nFROM\n  metric_table\nWHERE\n  $__timeFilter(time_column)\n", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "ERROR logs", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "none", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 136}, "hiddenSeries": false, "id": 14, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"alias": "", "datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "editorMode": "code", "expr": "irate(log4j2_events_total{instance=\"$instance\", application=\"$application\", level=\"warn\"}[5m])", "format": "time_series", "intervalFactor": 1, "legendFormat": "warn", "range": true, "rawSql": "SELECT\n  $__time(time_column),\n  value1\nFROM\n  metric_table\nWHERE\n  $__timeFilter(time_column)\n", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "WARN logs", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "none", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 136}, "hiddenSeries": false, "id": 16, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"alias": "", "datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "editorMode": "code", "expr": "irate(log4j2_events_total{instance=\"$instance\", application=\"$application\", level=\"debug\"}[5m])", "format": "time_series", "intervalFactor": 1, "legendFormat": "debug", "range": true, "rawSql": "SELECT\n  $__time(time_column),\n  value1\nFROM\n  metric_table\nWHERE\n  $__timeFilter(time_column)\n", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "DEBUG logs", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "none", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 136}, "hiddenSeries": false, "id": 20, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.2.4", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"alias": "", "datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "editorMode": "code", "expr": "irate(log4j2_events_total{instance=\"$instance\", application=\"$application\", level=\"trace\"}[5m])", "format": "time_series", "intervalFactor": 1, "legendFormat": "trace", "range": true, "rawSql": "SELECT\n  $__time(time_column),\n  value1\nFROM\n  metric_table\nWHERE\n  $__timeFilter(time_column)\n", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "TRACE logs", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "none", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}], "targets": [{"datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "refId": "A"}], "title": "log4j2 Statistics", "type": "row"}], "refresh": false, "schemaVersion": 37, "style": "dark", "tags": [], "camunda": {"list": [{"current": {"selected": true, "text": "localhost", "value": "localhost"}, "datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "definition": "label_values(application_started_time_seconds, instance)", "hide": 0, "includeAll": false, "label": "Instance", "multi": false, "name": "instance", "options": [], "query": {"query": "label_values(application_started_time_seconds, instance)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"current": {"selected": false, "text": "Camunda", "value": "Camunda"}, "datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "definition": "label_values(application_started_time_seconds{instance=\"$instance\"}, application)", "hide": 0, "includeAll": false, "label": "Application", "multi": false, "name": "application", "options": [], "query": {"query": "label_values(application_started_time_seconds{instance=\"$instance\"}, application)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"current": {"selected": true, "text": "HikariPool-1", "value": "HikariPool-1"}, "datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "definition": "", "hide": 0, "includeAll": false, "label": "HikariCP-Pool", "multi": false, "name": "hikaricp", "options": [], "query": {"query": "label_values(hikaricp_connections{instance=\"$instance\", application=\"$application\"}, pool)", "refId": "Prometheus-hikaricp-Variable-Query"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "definition": "", "hide": 0, "includeAll": true, "label": "Memory Pool (heap)", "multi": false, "name": "memory_pool_heap", "options": [], "query": {"query": "label_values(jvm_memory_used_bytes{application=\"$application\", instance=\"$instance\", area=\"heap\"},id)", "refId": "Prometheus-memory_pool_heap-Variable-Query"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}, {"current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": {"type": "prometheus", "uid": "ETjgbjv4z"}, "definition": "", "hide": 0, "includeAll": true, "label": "Memory Pool (nonheap)", "multi": false, "name": "memory_pool_nonheap", "options": [], "query": {"query": "label_values(jvm_memory_used_bytes{application=\"$application\", instance=\"$instance\", area=\"nonheap\"},id)", "refId": "Prometheus-memory_pool_nonheap-Variable-Query"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "Camunda application", "uid": "wHFsZROVk", "version": 7, "weekStart": ""}