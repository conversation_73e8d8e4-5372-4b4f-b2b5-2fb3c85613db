package cz.equa.camapp;

import cz.equa.camapp.lovs.LovApplPtRoleTp;
import cz.equa.camapp.lovs.LovBusProdSubTp;
import cz.equa.camapp.lovs.LovCcy;
import cz.equa.camapp.model.RepaidContractNumListDTO;
import cz.equa.camapp.process.ProcessVariable;
import cz.equa.camapp.process.ProcessVariableConstants;
import cz.equa.camapp.rest.model.cus.party.*;
import cz.equa.camapp.service.applicationservice.model.*;
import cz.equa.camapp.utils.Constants;
import cz.equa.camapp.utils.RecorderExecutionListener;
import cz.equa.camapp.rest.model.cus.partyApplication.ApplicationDTO;
import cz.equa.camapp.rest.model.cus.partyApplication.ProductDetailDTO;
import org.camunda.bpm.engine.ProcessEngine;
import org.camunda.bpm.engine.RuntimeService;
import org.camunda.bpm.engine.impl.persistence.entity.TimerEntity;
import org.camunda.bpm.engine.runtime.EventSubscription;
import org.camunda.bpm.engine.runtime.Job;
import org.camunda.bpm.engine.task.Task;
import org.camunda.bpm.engine.test.junit5.ProcessEngineExtension;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.*;

import static cz.equa.camapp.model.contact.AddressToInsertDTO.SBL_ADDRESS_SLOT_MAIL;
import static cz.equa.camapp.model.contact.EmailToInsertDTO.SBL_EMAIL_SLOT;
import static cz.equa.camapp.model.contact.PhoneToInsertDTO.SBL_PHONE_SLOT_MOBILE;
import static cz.rb.tif.mapping.IdCardTransformer.TP_KEY_C00;
import static cz.rb.tif.mapping.IdCardTransformer.TP_KEY_C19;

@ExtendWith({ProcessEngineExtension.class, SpringExtension.class, MockitoExtension.class})
public abstract class AbstractCoverageTest {

    protected static final String BUSINESS_KEY = "BusinessKey";

    protected static final String PARTY_PREFILLED_STR = "PARTY PRE-FILLED";
    protected static final String PARTY_PREFILLED_PREFERRED_STR = "PARTY PRE-FILLED PREFERRED";
    protected static final LocalDate PARTY_PREFILLED_DATE = LocalDate.of(2100, 1, 1);
    protected static final LocalDate PARTY_PREFILLED_PREFERRED_DATE = LocalDate.of(2200, 1, 1);

    @Autowired
    protected ProcessEngine processEngine;

    @MockBean
    protected JavaMailSender javaMailSender;

    public Map<String, Object> getStartProcessVariables() throws Exception {
        final Map<String, Object> variables = new HashMap<>();
        variables.put(ProcessVariable.APPLICATION_KEY.getKey(), **********L);
        variables.put(ProcessVariable.BUS_APPL_ID.getKey(), "7643");
        variables.put(ProcessVariable.PARTY_ID.getKey(), "CLIENT_ID");
        variables.put(ProcessVariable.SYSTEM_ID.getKey(), "SYSTEM_ID");
        variables.put(ProcessVariable.REQUEST_TYPE.getKey(), "REQUEST_TYPE");
        variables.put(ProcessVariable.SPSS_RESULT.getKey(), "OK");
        variables.put(ProcessVariable.UUID.getKey(), "UUID");
        variables.put(ProcessVariable.NOTIFY_TEMPLATE.getKey(), "L001");
        variables.put(ProcessVariable.UPDATE_CLIENT.getKey(), "TRUE");
        variables.put(ProcessVariable.COURIER_DATA_GENERATED.getKey(), false);
        variables.put(ProcessVariable.MANUAL_APPROVAL_STARTED.getKey(), false);
        variables.put(ProcessVariable.LOAN_APPLICATION_ACCOUNT_OPENING_CALLED.getKey(), false);
        variables.put(ProcessVariable.REGISTRY_CALLED.getKey(), false);
        variables.put(ProcessVariable.ACCOUNT_OPENING_STARTED.getKey(), false);
        variables.put(ProcessVariable.AML_IN_PROGRESS.getKey(), false);
        variables.put(ProcessVariable.REPEATABLE_ERROR.getKey(), false);
        variables.put(ProcessVariable.TASK_KEY.getKey(), ProcessVariableConstants.TASK_KEY__NO_TASK_KEY);
        variables.put(ProcessVariable.LOAN_APPLICATION_DECLARED_PURPOSE.getKey(), ProcessVariableConstants.LOAN_APPLICATION_DECLARED_PURPOSE__NO_DECLARED_PURPOSE);
        variables.put(ProcessVariable.CALL_PERSON_VERIFICATION_BY_IDCARDS_RESULT.getKey(), "");
        variables.put(ProcessVariable.ACTIVATION_LIST_EMPTY.getKey(), true);
        variables.put(ProcessVariable.LOAN_APPLICATION_REPAID_CONTRACT_NUMS.getKey(), new RepaidContractNumListDTO());
        variables.put(ProcessVariable.LOAN_APPLICATION_DRAW_FAILED_REPAID_CONTRACT_NUMS.getKey(), new RepaidContractNumListDTO());
        variables.put(ProcessVariable.NEED_UPDATE_ACCOUNT_NUMBER.getKey(), false);
        variables.put(ProcessVariable.RESERVED_ACCOUNT.getKey(), null);

        return variables;
    }

    public void waitUntilNoActiveJobs(final ProcessEngine processEngine, final long wait) throws InterruptedException {
        waitUntilNoActiveJobs(processEngine, wait, null, null, false);
    }

    public void waitUntilNoActiveJobs(final ProcessEngine processEngine,
                                      final long wait,
                                      final EventSubscriber eventSubscriber,
                                      final TaskSubscriber taskSubscriber) throws InterruptedException {
        waitUntilNoActiveJobs(processEngine, wait, eventSubscriber, taskSubscriber, false);
    }

    public void waitUntilNoActiveJobs(final ProcessEngine processEngine,
                                      final long wait,
                                      final EventSubscriber eventSubscriber,
                                      final TaskSubscriber taskSubscriber,
                                      boolean processTimersImmediately) throws InterruptedException {
        long timeout = System.currentTimeMillis() + wait;
        List<Job> waitingJobs = null;
        List<EventSubscription> eventSubscriptions = null;
        List<Task> tasks = null;
        while (System.currentTimeMillis() < timeout) {
            waitingJobs = processEngine.getManagementService().createJobQuery().list();
            eventSubscriptions = processEngine.getRuntimeService().createEventSubscriptionQuery().list();
            tasks = processEngine.getTaskService().createTaskQuery().list();
            if (waitingJobs.isEmpty() && eventSubscriptions.isEmpty() && tasks.isEmpty()) {
                break;
            }
            System.out.println("There are " + waitingJobs.size() + " jobs and " + eventSubscriptions.size() + " event subscriptions waiting to be executed");
            for (final Job job : waitingJobs) {
                boolean timerEntity = job instanceof TimerEntity;
                if (!timerEntity || processTimersImmediately || ((job.getDuedate() != null) && job.getDuedate().before(new Date()))) {
                    try {
                        processEngine.getManagementService().executeJob(job.getId());
                    } catch (final Exception ex) {
                        System.out.println("Job " + job.getId() + " thrown an exception: " + ex.getMessage());
                    }
                }
            }
            if (eventSubscriber != null) {
                eventSubscriber.processEvents(eventSubscriptions);
            }
            if (taskSubscriber != null) {
                taskSubscriber.processTasks(tasks);
            }
        }
        if (!eventSubscriptions.isEmpty()) {

        }
        if (!waitingJobs.isEmpty()) {

        }
        if (!tasks.isEmpty()) {

        }
        if (System.currentTimeMillis() > timeout) {
            throw new InterruptedException("Timeout");
        }
    }

    protected LoanApplicationDTO getLoanApplication() {
        LoanApplicationDTO application = new LoanApplicationDTO();
        application.setBusProdSubTp(LovBusProdSubTp.RCL_STANDARD.getCode());
        application.setPosId("1234");
        application.setContrSignDate(LocalDate.now());
        application.setApplDate(OffsetDateTime.now());
        application.setAccNum("**********");
        application.setAccBankCode("5500");
        application.setValidFromDate(OffsetDateTime.now());
        application.setValidToDate(OffsetDateTime.now().plusDays(10));
        application.setApplTpId("id");
        application.setFirstTouchPoint("touch point");
        application.setCcyId(LovCcy.CZECH_KORUNA.getCode());
        application.setOrderNr("num1");
        application.setUrl("url");
        application.setMoneyTransferDate(OffsetDateTime.now().plusDays(1));
        return application;
    }

    protected GetMortApplResponseDTO getMtgApplication() {
        GetMortApplResponseDTO application = new GetMortApplResponseDTO();
        ApplIdKeyIdDTO applIdKeyIdDTO = new ApplIdKeyIdDTO();
        applIdKeyIdDTO.setApplKey(123L);
        applIdKeyIdDTO.setBusApplId("123");
        application.setApplId(applIdKeyIdDTO);
        List<PersonMortGetDTO> persons = new ArrayList<>();
        PersonMortGetDTO main = new PersonMortGetDTO();
        main.setAdbInstPtKey(123L);
        main.setFirstName("Hlavni");
        main.setFamilyName("Hypotekar");
        persons.add(main);
        PersonMortGetDTO other = new PersonMortGetDTO();
        other.setAdbInstPtKey(20L);
        other.setFirstName("Vedlejsi");
        other.setFamilyName("Hypotekar");
        persons.add(other);
        application.setPersons(persons);
        List<GetMortApplDTO> appls = new ArrayList<>();
        GetMortApplDTO appl = new GetMortApplDTO();
        appl.setApplId(applIdKeyIdDTO);
        List<GetMortApplPersDTO> pers = new ArrayList<>();
        GetMortApplPersDTO p = new GetMortApplPersDTO();
        p.setAdbInstPtKey(123L);
        p.setApplPtRoleTpId(LovApplPtRoleTp.MAIN.getCode());
        pers.add(p);
        appl.setPersons(pers);
        appl.setHash("hash");
        appl.setApplDate(OffsetDateTime.now());
        appl.setApplDateTo(OffsetDateTime.now());
        appls.add(appl);
        application.setMortgageAppls(appls);
        List<MortApplVariantDTO> vars = new LinkedList<>();
        MortApplVariantDTO var = new MortApplVariantDTO();
        vars.add(var);
        appl.setApplVariants(vars);
        return application;
    }

    protected List<ObligationDTO> getObligationsWithPrimaryObligation() {
        List<ObligationDTO> obligations = new ArrayList<>();
        ObligationDTO obligation = new ObligationDTO();
        obligation.setAdbInstPtKey(BigInteger.ONE);
        obligation.setPrimaryOblgtn(Boolean.TRUE);
        obligation.setFinInstnGrpCode("JINABANK");
        obligation.setClFinInstnId("KB");
        obligation.setOfFinInstnId("KB");
        obligation.setScOblgtnProdTpId("consumerLoan");
        obligation.setInstOblgtnKey(BigInteger.ONE);
        obligation.setScTotLoanAmt(BigDecimal.ONE);
        obligation.setScAmt(BigDecimal.ONE);
        obligation.setOfAmt(BigDecimal.TEN);
        obligation.setScInstl(BigDecimal.ONE);
        obligation.setCcyId(LovCcy.CZECH_KORUNA.getCode());
        obligation.setScIntSourceSystem("SourceSystem");
        obligation.setScIntSourceSystemId(Constants.TRS_SYSTEM_ID);
        obligation.setScFinInstnCode("RB");
        obligation.setOblgtnSelected(true);
        obligation.setOfTotLoanAmt(BigDecimal.ONE);
        obligation.setOfBankId("0100");
        obligations.add(obligation);
        return obligations;
    }

    protected List<ObligationDTO> getObligationsWithPrimaryObligationMidas() {
        List<ObligationDTO> obligations = new ArrayList<>();
        ObligationDTO obligation = new ObligationDTO();
        obligation.setAdbInstPtKey(BigInteger.ONE);
        obligation.setPrimaryOblgtn(Boolean.TRUE);
        obligation.setFinInstnGrpCode("JINABANK");
        obligation.setClFinInstnId("KB");
        obligation.setOfFinInstnId("KB");
        obligation.setScOblgtnProdTpId("consumerLoan");
        obligation.setInstOblgtnKey(BigInteger.ONE);
        obligation.setScTotLoanAmt(BigDecimal.ONE);
        obligation.setScAmt(BigDecimal.ONE);
        obligation.setScInstl(BigDecimal.ONE);
        obligation.setCcyId(LovCcy.CZECH_KORUNA.getCode());
        obligation.setScIntSourceSystem("SourceSystem");
        obligation.setScIntSourceSystemId(Constants.MIDAS_SYSTEM_ID);
        obligation.setScFinInstnCode("RB");
        obligation.setOblgtnSelected(true);
        obligation.setOfTotLoanAmt(BigDecimal.ONE);
        obligations.add(obligation);
        return obligations;
    }

    protected List<DocumentsDto> prepareDocumentsDtoList() {
        List<DocumentsDto> docList = new LinkedList<>();

        // doc 1 - primary
        DocumentsDto doc1 = new DocumentsDto();
        docList.add(doc1);
        doc1.setPrimaryDocument(true); // primary
        doc1.setUnacceptable(false);
        doc1.setDocumentType(TP_KEY_C00.getSblValue());
        doc1.setDocumentId("PARTY_IDDOCUMENT_ID");
        doc1.setIssuer(PARTY_PREFILLED_PREFERRED_STR);
        doc1.setIssuerCountry(PARTY_PREFILLED_PREFERRED_STR);
        doc1.setValidFrom(PARTY_PREFILLED_PREFERRED_DATE);
        doc1.setValidTo(PARTY_PREFILLED_PREFERRED_DATE);

        // doc 2 - not primary
        DocumentsDto doc2 = new DocumentsDto();
        docList.add(doc2);
        doc2.setPrimaryDocument(false); // not primary
        doc2.setUnacceptable(false);
        doc2.setDocumentType(TP_KEY_C19.getSblValue());
        doc2.setDocumentId(PARTY_PREFILLED_STR);
        doc2.setIssuer(PARTY_PREFILLED_STR);
        doc2.setIssuerCountry(PARTY_PREFILLED_STR);
        doc2.setValidFrom(PARTY_PREFILLED_PREFERRED_DATE);
        doc2.setValidTo(PARTY_PREFILLED_PREFERRED_DATE);

        // doc 3 - unacceptable
        DocumentsDto doc3 = new DocumentsDto();
        docList.add(doc3);
        doc3.setPrimaryDocument(true);
        doc3.setUnacceptable(true);  // unacceptable
        doc3.setDocumentType(TP_KEY_C00.getSblValue());
        doc3.setDocumentId(PARTY_PREFILLED_STR);
        doc3.setIssuer(PARTY_PREFILLED_STR);
        doc3.setIssuerCountry(PARTY_PREFILLED_STR);
        doc3.setValidFrom(PARTY_PREFILLED_PREFERRED_DATE);
        doc3.setValidTo(PARTY_PREFILLED_PREFERRED_DATE);

        return docList;
    }

    protected static GetDetailResultDto createGetDetailResultDto() {
        return createGetDetailResultDto(true, true, true, true, true);
    }

    protected static GetDetailResultDto createGetDetailResultDto(boolean fillBasicInfo, boolean fillBirthInfo, boolean fillTaxation,
                                                                 boolean fillContacts, boolean fillIdCards) {
        GetDetailResultDto getDetailResultDto = new GetDetailResultDto();
        PersonDto privateDto = new PersonDto();
        privateDto.setFirstName(PARTY_PREFILLED_STR);
        privateDto.setMiddleNames(PARTY_PREFILLED_STR);
        privateDto.setLastName(PARTY_PREFILLED_STR);
        privateDto.setBirthDate(PARTY_PREFILLED_DATE);
        privateDto.setBirthCode(PARTY_PREFILLED_STR);
        privateDto.setBirthCodePhoney(PARTY_PREFILLED_STR);
        privateDto.setBirthCountry(PARTY_PREFILLED_STR);
        privateDto.setBirthCity(PARTY_PREFILLED_STR);
        privateDto.setBirthplace(PARTY_PREFILLED_STR);
        privateDto.setBirthLastName(PARTY_PREFILLED_STR);
        privateDto.setGender(PARTY_PREFILLED_STR);
        privateDto.setIsClient(false);
        privateDto.setIsEligible(true);
        privateDto.setIsStaff(false);
        privateDto.setCitizenship(PARTY_PREFILLED_STR);
        AdditionalInfoPrivatePartyDto additionalInfo = new AdditionalInfoPrivatePartyDto();
        additionalInfo.setProductCustomersWantsUse("product");
        additionalInfo.setPepFlag(false);
        privateDto.setAdditionalInfo(additionalInfo);
        privateDto.setPreferredLanguage("CZ");

        getDetailResultDto.setPerson(privateDto);

        CompanyDto company = new CompanyDto();
        company.setPartyShortName("COS");
        company.setPartyStatus("staus2");
        company.setStatusDate(OffsetDateTime.now());
        company.setIsClient(false);
        company.setIsVIP(false);
        company.setIsEligible(false);
        company.setCustomerSince(LocalDate.now());
        company.setCustomerTill(LocalDate.now());
        company.setLeavingReason("Reason");
        company.setPreferredLanguage("CZ");
        company.setCompanyName("Company");
        company.setCompanyNameSuffix("XY");
        company.setTaxId("TaxId123");
        company.setRegistrationNumber("12334");
        company.setPhoneyRegistrationNumber("");
        company.setBirthCode(PARTY_PREFILLED_STR);
        company.setLegalForm("LegalForm");
        company.setLegalStatus("LegalStatus");
        company.setRegisteredCompanyName("Company s.r.o.");
        company.setRegisteredCountry("CZ");
        company.setPlannedTransactions("PlannedTransactions");

        getDetailResultDto.setCompany(company);

        if (fillContacts) {
            ContactsDto contacts = new ContactsDto();
            List<AddressDto> address = new LinkedList<>();

            // permanent address 1 - preffered
            AddressDto permAddr1 = new AddressDto();
            permAddr1.setAddressType("RESIDENCY");
            permAddr1.setIsResidency(true);
            permAddr1.setIsPreferred(true);   // preffered
            permAddr1.setUndeliverableFlag(false);
            permAddr1.setStreet(PARTY_PREFILLED_PREFERRED_STR);
            permAddr1.setStreetNumber(PARTY_PREFILLED_PREFERRED_STR);
            permAddr1.setCity(PARTY_PREFILLED_PREFERRED_STR);
            permAddr1.setPostalCode(PARTY_PREFILLED_PREFERRED_STR);
            permAddr1.setCountry(PARTY_PREFILLED_PREFERRED_STR);
            address.add(permAddr1);

            // permanent address 2 - not preffered
            AddressDto permAddr2 = new AddressDto();
            permAddr2.setAddressId("123");
            permAddr2.setAddressType("RESIDENCY");
            permAddr2.setIsResidency(true);
            permAddr2.setIsPreferred(false);  // not preffered
            permAddr2.setUndeliverableFlag(false);
            permAddr2.setStreet(PARTY_PREFILLED_STR);
            permAddr2.setStreetNumber(PARTY_PREFILLED_STR);
            permAddr2.setCity(PARTY_PREFILLED_STR);
            permAddr2.setPostalCode(PARTY_PREFILLED_STR);
            permAddr2.setCountry(PARTY_PREFILLED_STR);
            address.add(permAddr2);

            // permanent address 3 - undeliverable
            AddressDto permAddr3 = new AddressDto();
            permAddr3.setAddressType("RESIDENCY");
            permAddr3.setIsResidency(true);
            permAddr3.setIsPreferred(true);
            permAddr3.setUndeliverableFlag(true);  // undeliverable
            permAddr3.setStreet(PARTY_PREFILLED_STR);
            permAddr3.setStreetNumber(PARTY_PREFILLED_STR);
            permAddr3.setCity(PARTY_PREFILLED_STR);
            permAddr3.setPostalCode(PARTY_PREFILLED_STR);
            permAddr3.setCountry(PARTY_PREFILLED_STR);
            address.add(permAddr3);

            // postal address 1 - preffered
            AddressDto postalAddr1 = new AddressDto();
            postalAddr1.setAddressId(SBL_ADDRESS_SLOT_MAIL + 1);
            postalAddr1.setAddressType(SBL_ADDRESS_SLOT_MAIL + 1);
            postalAddr1.setIsResidency(false);
            postalAddr1.setIsPreferred(true);   // preffered
            postalAddr1.setUndeliverableFlag(false);
            postalAddr1.setStreet(PARTY_PREFILLED_PREFERRED_STR);
            postalAddr1.setStreetNumber(PARTY_PREFILLED_PREFERRED_STR);
            postalAddr1.setCity(PARTY_PREFILLED_PREFERRED_STR);
            postalAddr1.setPostalCode(PARTY_PREFILLED_PREFERRED_STR);
            postalAddr1.setCountry(PARTY_PREFILLED_PREFERRED_STR);
            address.add(postalAddr1);

            // postal address 2 - not preffered
            AddressDto postalAddr2 = new AddressDto();
            postalAddr2.setAddressType(SBL_ADDRESS_SLOT_MAIL + 2);
            postalAddr2.setIsResidency(false);
            postalAddr2.setIsPreferred(false);  // not preffered
            postalAddr2.setUndeliverableFlag(false);
            postalAddr2.setStreet(PARTY_PREFILLED_STR);
            postalAddr2.setStreetNumber(PARTY_PREFILLED_STR);
            postalAddr2.setCity(PARTY_PREFILLED_STR);
            postalAddr2.setPostalCode(PARTY_PREFILLED_STR);
            postalAddr2.setCountry(PARTY_PREFILLED_STR);
            address.add(postalAddr2);

            // postal address 3 - undeliverable
            AddressDto postalAddr3 = new AddressDto();
            postalAddr3.setAddressType(SBL_ADDRESS_SLOT_MAIL + 3);
            postalAddr3.setIsResidency(false);
            postalAddr3.setIsPreferred(true);
            postalAddr3.setUndeliverableFlag(true);  // undeliverable
            postalAddr3.setStreet(PARTY_PREFILLED_STR);
            postalAddr3.setStreetNumber(PARTY_PREFILLED_STR);
            postalAddr3.setCity(PARTY_PREFILLED_STR);
            postalAddr3.setPostalCode(PARTY_PREFILLED_STR);
            postalAddr3.setCountry(PARTY_PREFILLED_STR);
            address.add(postalAddr3);

            contacts.setAddress(address);

            List<EmailDto> emails = new LinkedList<>();
            // email 1 - preferred
            EmailDto email = new EmailDto();
            emails.add(email);
            email.setEmail(PARTY_PREFILLED_PREFERRED_STR);
            email.setEmailAddressType(SBL_EMAIL_SLOT + 1);
            email.setContactId("12390");
            email.setPreferred(true);  // preffered
            email.setUndeliverableFlag(false);

            // email 2 - not preffered
            EmailDto email2 = new EmailDto();
            emails.add(email2);
            email2.setEmail(PARTY_PREFILLED_STR);
            email2.setEmailAddressType(SBL_EMAIL_SLOT + 2);
            email2.setPreferred(false);  // not preffered
            email2.setUndeliverableFlag(false);

            // email 3 - undeliverable
            EmailDto email3 = new EmailDto();
            emails.add(email3);
            email3.setEmail(PARTY_PREFILLED_STR);
            email3.setEmailAddressType(SBL_EMAIL_SLOT + 3);
            email3.setPreferred(true);
            email3.setUndeliverableFlag(true);  // undeliverable

            contacts.setEmail(emails);

            List<PhoneDto> phones = new LinkedList<>();

            // phone 1 - preffered
            PhoneDto phone1 = new PhoneDto();
            phone1.setContactId("phone1");
            phone1.setPhone(PARTY_PREFILLED_PREFERRED_STR);
            phone1.setPreferred(true);  // preffered
            phone1.setUndeliverableFlag(false);
            phone1.setMediumType("MOBILE_NUMBER");
            phone1.setPhoneNumberType(SBL_PHONE_SLOT_MOBILE + 1);
            phones.add(phone1);

            // phone 2 - not preffered
            PhoneDto phone2 = new PhoneDto();
            phone2.setContactId("phone2");
            phone2.setPhone(PARTY_PREFILLED_STR);
            phone2.setPreferred(false);  // not preffered
            phone2.setMediumType("MOBILE_NUMBER");
            phone2.setUndeliverableFlag(false);
            phone2.setPhoneNumberType(SBL_PHONE_SLOT_MOBILE + 2);
            phones.add(phone2);

            // phone 3 - undeliverable
            PhoneDto phone3 = new PhoneDto();
            phone3.setContactId("phone3");
            phone3.setPhone(PARTY_PREFILLED_STR);
            phone3.setPreferred(true);
            phone3.setMediumType("MOBILE_NUMBER");
            phone3.setUndeliverableFlag(true);  // undeliverable
            phone3.setPhoneNumberType(SBL_PHONE_SLOT_MOBILE + 3);
            phones.add(phone3);

            contacts.setPhone(phones);
            getDetailResultDto.setContacts(contacts);
        }
        return getDetailResultDto;
    }

    protected ApplicationDTO getApplicationDto() {
        ApplicationDTO applicationDTO = new ApplicationDTO();
        ProductDetailDTO productDetail = new ProductDetailDTO();
        productDetail.setProductCode("012345");
        productDetail.setProductName("productName");
        productDetail.setProductType("productType");
        productDetail.setProductCategory("productCategory");
        applicationDTO.setProductDetail(productDetail);
        return applicationDTO;
    }

    public class TestCheckpoint {

        final RuntimeService runtimeService;

        final List<Checkpoint> checkpoints = new ArrayList<>();

        public TestCheckpoint(final RuntimeService runtimeService) {
            this.runtimeService = runtimeService;
            RecorderExecutionListener.clear();
        }

        public TestCheckpoint addOnEndOfActivity(String activityName) {
            checkpoints.add(new Checkpoint(activityName, false));
            runtimeService.createEventSubscriptionQuery().eventType("end").eventName(activityName);
            return this;
        }

        public TestCheckpoint addOnStartOfActivity(String activityName) {
            checkpoints.add(new Checkpoint(activityName, false));
            runtimeService.createEventSubscriptionQuery().eventType("start").eventName(activityName);
            return this;
        }

        public TestCheckpoint addTransitionCheck(String transitionId) {
            checkpoints.add(new Checkpoint(transitionId, true));
            runtimeService.createEventSubscriptionQuery().eventType("take").eventName(transitionId);
            return this;
        }

        public boolean checkpointPassed(int checkPointIndex) {
            final Checkpoint checkpoint = checkpoints.get(checkPointIndex);
            return RecorderExecutionListener.getRecordedEvents().stream().anyMatch(event ->
                    (checkpoint.isTransition && checkpoint.checkpointName.equals(event.getTransitionId())) ||
                            (!checkpoint.isTransition && checkpoint.checkpointName.equals(event.getActivityName())));
        }

        private class Checkpoint {
            String checkpointName;
            boolean isTransition;

            public Checkpoint(String checkpointName, boolean isTransition) {
                this.checkpointName = checkpointName;
                this.isTransition = isTransition;
            }
        }
    }
}
