package cz.equa.camapp.config;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.AbstractResource;
import org.springframework.core.io.Resource;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.web.SpringJUnitWebConfig;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.Properties;

import static org.junit.jupiter.api.Assertions.assertEquals;

@SpringJUnitWebConfig(classes = {RetryConfiguration.class})
@TestPropertySource(properties = {"camunda.bpm.job-execution.failed-job-retry-time-cycle=XXX"})
public class RetryConfigurationTest {

    @Autowired
    RetryConfiguration underTest;

    @Test
    public void testRetryElement() throws Exception {
        underTest.retryConfigurationFile = getTestProperties();
        underTest.initConfiguration();
        assertEquals("F", underTest.getRetryConfiguration(Arrays.asList("Ecommerce-EZZA", "getApplication")).getValue());
        assertEquals("XXX", underTest.getRetryConfiguration(Arrays.asList("UnknownProcess-willUseDefaultConfiguration")).getValue());
        assertEquals("A", underTest.getRetryConfiguration(Arrays.asList("Ecommerce-EZZA")).getValue());
        assertEquals("B", underTest.getRetryConfiguration(Arrays.asList("Ecommerce-EZZA", "SolveApplicant")).getValue());
        assertEquals("C", underTest.getRetryConfiguration(Arrays.asList("Ecommerce-EZZA", "SolveApplicant", "findParty")).getValue());
        assertEquals("D", underTest.getRetryConfiguration(Arrays.asList("Ecommerce-EZZA", "findParty")).getValue());
        assertEquals("E", underTest.getRetryConfiguration(Arrays.asList("Ecommerce-ESAL", "findParty")).getValue());
        assertEquals("F", underTest.getRetryConfiguration(Arrays.asList("Ecommerce-EZZA", "SolveApplicant", "getApplication")).getValue());
    }

    private Resource getTestProperties() throws Exception {
        Properties properties = new Properties();
        final String propertiesString = "Ecommerce-EZZA.*=A\n"
                + "Ecommerce-EZZA.SolveApplicant.*=B\n"
                + "Ecommerce-EZZA.SolveApplicant.findParty=C\n"
                + "Ecommerce-EZZA.findParty=D\n"
                + "Ecommerce-ESAL.*=E\n"
                + "Ecommerce-EZZA.SolveApplicant.getApplication=F\n";
        properties.load(new ByteArrayInputStream(propertiesString.getBytes()));
        return new AbstractResource() {
            @Override
            public String getDescription() {
                return null;
            }

            @Override
            public InputStream getInputStream() throws IOException {
                final ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                properties.store(outputStream, null);
                return new ByteArrayInputStream(outputStream.toByteArray());
            }
        };
    }
}