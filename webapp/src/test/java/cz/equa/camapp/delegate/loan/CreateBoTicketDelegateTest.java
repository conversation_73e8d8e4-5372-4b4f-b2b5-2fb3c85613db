package cz.equa.camapp.delegate.loan;

import cz.equa.camapp.AbstractCoverageTest;
import cz.equa.camapp.InMemProcessEngineConfiguration;
import cz.equa.camapp.config.*;
import cz.equa.camapp.delegate.*;
import cz.equa.camapp.delegate.deffered_payment.*;
import cz.equa.camapp.delegate.loan.card.UpdateAdditionalDocumentDelegate;
import cz.equa.camapp.lovs.LovFinInstn;
import cz.equa.camapp.manager.LovManager;
import cz.equa.camapp.manager.RccManager;
import cz.equa.camapp.process.ProcessVariable;
import cz.equa.camapp.process.TcherRestartEvent;
import cz.equa.camapp.rest.service.*;
import cz.equa.camapp.rest.util.ProxyConfig;
import cz.equa.camapp.service.WebServiceConfig;
import cz.equa.camapp.service.applicationservice.model.ObligationsDTO;
import cz.equa.camapp.task.TaskManager;
import cz.equa.camapp.tif.TifTestClient;
import cz.equa.camapp.utils.FileUtilsWrapper;
import cz.rb.camapp.kafka.KafkaConsumerConfig;
import cz.rb.camapp.kafka.KafkaConsumerConfigDmsRbox;
import cz.rb.tif.bai_00001_loan_process_consolidation.Bai00001LoanProcessConsolidation;
import cz.rb.tif.bai_00015_current_account.Bai00015CurrentAccount;
import cz.rb.tif.bai_00018_remote_cash_account.Bai00018RemoteCashAccount;
import cz.rb.tif.bai_00029_consumer_loan.Bai00029ConsumerLoan;
import cz.rb.tif.crd_00008_ent_payment_card.Crd00008EntPaymentCard;
import cz.rb.tif.cus_00041_ent_party.Cus00041EntParty;
import cz.rb.tif.cus_00046_questionnaire.Cus00046Questionnaire;
import cz.rb.tif.cus_00050_contact.Cus00050Contact;
import cz.rb.tif.evn_00015_payment_card.Evn00015PaymentCard;
import cz.rb.tif.ifr_00014_card_processing.Ifr00014CardProcessing;
import cz.rb.tif.ifr_00016_start_service.Ifr00016StartService;
import org.camunda.bpm.engine.RuntimeService;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.community.rest.client.api.ProcessDefinitionApi;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit.jupiter.web.SpringJUnitWebConfig;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;

@SpringJUnitWebConfig(classes = {InMemProcessEngineConfiguration.class, CamundaConfiguration.class, ProcessConfiguration.class,
        TifTestClient.class, cz.equa.camapp.process.ProcessManager.class, org.springframework.boot.info.BuildProperties.class,
        cz.equa.camapp.config.IncidentHandlingConfiguration.class, cz.equa.camapp.config.FailedTaskIncidentHandler.class,
        cz.equa.camapp.config.RetryConfigurationParseListener.class, cz.equa.camapp.config.RetryConfiguration.class, KafkaConsumerConfig.class,
        WebServiceConfig.class, CamundaBpmRestJerseyAutoConfiguration.class, UserTaskNameExpressionParseListener.class, CamundaConfiguration.class, ProxyConfig.class,
        KafkaConsumerConfigDmsRbox.class,
        ProcessDefinitionApi.class,
        UpdateApplicationDelegate.class,
        CheckApplUserRightsDelegate.class,
        SetPersonVerificationDelegate.class,
        SetLoanApplDelegate.class,
        GetApplDelegate.class,
        FindCustomerTypeDelegate.class,
        CheckStornoAllowedDelegate.class,
        SetSigningChannelDelegate.class,
        StartManualApprovingDelegate.class,
        CancelManualApprovingDelegate.class,
        CheckAccountOpeningStatusDelegate.class,
        SetContractSignedDelegate.class,
        SetApplicationEventDelegate.class,
        CheckFTPCodeDelegate.class,
        GetLovFinInstnDelegate.class,
        PrepareDocumentConditionsDelegate.class,
        GetSBLPartyDetailDelegate.class,
        UploadContactsFromApplDelegate.class,
        CallApprovalDelegate.class,
        GetAmlDetailDelegate.class,
        GetAccountListDelegate.class,
        SetAmlQuestDelegate.class,
        SetPartyFromApplDelegate.class,
        SetApplFromPartyDelegate.class,
        CheckPersonalDataDelegate.class,
        SetAMLDataOnSBLPersonDelegate.class,
        CheckAccNumFromGetLoanApplDelegate.class,
        GetDealerInfoDelegate.class,
        BookAccNrDelegate.class,
        GetParametrizationDelegate.class,
        GenerateDocumentDelegate.class,
        CreateConsumerLoanDelegate.class,
        GetCalcValuesDelegate.class,
        ConsumerLoanDrawingDelegate.class,
        CreateCurrentAccountDelegate.class,
        UpdateOpportunityDelegate.class,
        DeleteDocumentsDelegate.class,
        GenerateEmailDelegate.class,
        CancelManualTaskDelegate.class,
        CancelDocumentsDelegate.class,
        SetVariablesRejectionCancellationDelegate.class,
        CreateBoTicketDelegate.class,
        SetAccNrIntoApplDelegate.class,
        ClearPsd2DataDelegate.class,
        SetIdCardToApplicationDelegate.class,
        InitChannelsDelegate.class,
        ProcessConsolidationDelegate.class,
        CreateSSTDelegate.class,
        GetProductDetailDelegate.class,
        CheckRepaiedOblgtnDelegate.class,
        CreateDebitCardDelegate.class,
        ConsumerLoanDrawingWithRepaymentsDelegate.class,
        DmsService.class,
        GetIntrsChngUsageDelegate.class,
        GetIntrsChngUsageFromVariantDelegate.class,
        CheckConditionsDelegate.class,
        SetUsageStateDelegate.class,
        GetCalcValuesSelOfrDelegate.class,
        SetLoanApplUpdateInterestDelegate.class,
        SetApplicationIntrsChangeDelegate.class,
        AddClientApprovalDelegate.class,
        GetDocumentUrlDelegate.class,
        SendEmailToBODelegate.class,
        DownloadDocumentDelegate.class,
        CheckRejectionAllowedDelegate.class,
        RccManager.class,
        SetCreditCardApplDelegate.class,
        PushNewCardDelegate.class,
        SetCardInOdsDelegate.class,
        UpdateCardLimitsRpcDelegate.class,
        PersonalDataUpdatingDelegate.class,
        AddressesDataUpdatingDelegate.class,
        UpdateOptionalDocumentDelegate.class,
        UpdateAdditionalDocumentDelegate.class,
        NewServiceDelegate.class,
        SetPlasticCardStatusDelegate.class,
        GetApplStateDelegate.class,
        UpdateSigningChannelDelegate.class,
        SendConfirmationSmsDelegate.class,
        UpdateSignChannelInLoanAppDelegate.class,
        GetRccCalcValuesDelegate.class,
        NotifyMerchantDelegate.class,
        FindPartyDelegate.class,
        CreatePartyDelegate.class,
        AssignCategoryDelegate.class,
        CheckEcommerceApplRightsDelegate.class,
        GetShopInfoDelegate.class,
        DeferredGenerateDocumentDelegate.class,
        CreateConsumerDeferredPaymentDelegate.class,
        DeferredDrawingDelegate.class,
        CreatePaymentOrderDelegate.class,
        UpdateApplicationWithPennyTransactionDelegate.class,
        FailedExternalWorkerIncidentHandler.class,
        FileUtilsWrapper.class,
        CurrentAccountService.class})
public class CreateBoTicketDelegateTest extends AbstractCoverageTest {

    @Autowired
    private CreateBoTicketDelegate createBoTicketDelegate;

    @Autowired
    private TifTestClient tifClient;

    @SuppressWarnings("unused")
    @MockBean
    private LovManager lovManager;

    @Mock
    protected LovFinInstn lovFinInstn;

    @MockBean
    public ApplicationService applicationService;

    @MockBean
    public CurrentAccountService currentAccountService;

    @MockBean
    public PartyApplicationService partyApplicationService;

    @MockBean
    public AccountService accountService;

    @MockBean
    private OperationService operationService;

    @MockBean
    private LoanApprovalService loanService;

    @MockBean
    private Cus00041EntParty cus00041EntParty;

    @MockBean
    private Cus00050Contact cus00050Contact;

    @MockBean
    private Cus00046Questionnaire cus00046Questionnaire;

    @MockBean
    private HadPartnersService hadPartnersService;

    @MockBean
    private Bai00015CurrentAccount bai00015CurrentAccount;

    @MockBean
    private ParametrizationService parametrizationService;

    @MockBean
    private TaskManager taskManager;

    @MockBean
    private DmsService dmsService;

    @MockBean
    private Ifr00016StartService ifr00016StartService;

    @MockBean
    private Bai00018RemoteCashAccount bai00018RemoteCashAccount;

    @MockBean
    private Bai00001LoanProcessConsolidation bai00001LoanProcessConsolidation;

    @MockBean
    private Crd00008EntPaymentCard crd00008EntPaymentCard;

    @MockBean
    private HadInterestChangeService hadInterestChangeService;

    @MockBean
    private Bai00029ConsumerLoan bai00029ConsumerLoan;

    @MockBean
    private CashloanApplicationService cashloanApplicationService;

    @MockBean
    private Ifr00014CardProcessing ifr00014CardProcessing;

    @MockBean
    private Evn00015PaymentCard evn00015PaymentCard;

    @MockBean
    private CardProcessingService cardProcessingService;

    @MockBean
    private MessageService messageService;

    @MockBean
    private PartyService partyService;

    @MockBean
    protected RuntimeService runtimeService;

    @Mock
    private DelegateExecution delegateExecution;

    @BeforeEach
    public void setUp() {
        when(lovManager.getLov(any(LovFinInstn.class), any())).thenReturn(lovFinInstn);
        lenient().when(delegateExecution.getVariable(ProcessVariable.LOAN_APPLICATION_OBLIGATIONS.getKey())).thenReturn(new ObligationsDTO(getObligationsWithPrimaryObligation()));
        lenient().when(delegateExecution.getVariable(ProcessVariable.LOAN_APPLICATION.getKey())).thenReturn(getLoanApplication());
        lenient().when(delegateExecution.getVariable(ProcessVariable.GET_DETAIL_RESULT.getKey())).thenReturn(createGetDetailResultDto(true, true, true, true, true));
        when(delegateExecution.getVariable(ProcessVariable.TCHER_EVENT.getKey())).thenReturn(new TcherRestartEvent("lastActivity", "PZSTAB"));
    }

    @Test
    public void partyUpdate() throws Exception {
        assertDoesNotThrow(() -> createBoTicketDelegate.execute(delegateExecution));
    }
}
