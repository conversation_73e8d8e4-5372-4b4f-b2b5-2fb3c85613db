package cz.equa.camapp.delegate.loan;

import cz.equa.camapp.model.GeneratedDocumentInfoDTO;
import cz.equa.camapp.model.GeneratedDocumentsDTO;
import cz.equa.camapp.model.document.GetDocumentUrlDto;
import cz.equa.camapp.process.ProcessVariable;
import cz.equa.camapp.rest.model.DocumentType;
import cz.equa.camapp.rest.model.GetLoanApplicationResult;
import cz.equa.camapp.rest.service.ApplicationService;
import cz.equa.camapp.rest.service.DmsService;
import cz.equa.camapp.rest.service.OperationService;
import cz.equa.camapp.service.applicationservice.model.ObligationDTO;
import cz.equa.camapp.utils.Constants;
import cz.equa.camapp.utils.FileUtilsWrapper;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.engine.test.junit5.ProcessEngineExtension;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.web.SpringJUnitWebConfig;

import java.io.File;
import java.net.URI;
import java.net.URL;
import java.nio.file.Path;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith({ProcessEngineExtension.class})
@SpringJUnitWebConfig(classes = {RefiPrepareDocumentsForEmailDelegate.class})
@TestPropertySource(properties = {
        "refi.file.location=camunda/********/"
})
class RefiPrepareDocumentsForEmailDelegateTest {

    @Autowired
    RefiPrepareDocumentsForEmailDelegate refiPrepareDocumentsForEmailDelegate;
    @Mock
    private DelegateExecution delegateExecution;

    @MockBean
    private JavaMailSender javaMailSender;
    @MockBean
    private ApplicationService applicationService;
    @MockBean
    private OperationService operationService;
    @MockBean
    private DmsService dmsService;
    @TempDir
    static Path tempDir;

    @DynamicPropertySource
    static void dynamicProperties(DynamicPropertyRegistry registry) {
        registry.add("refi.file.location", () -> tempDir.toString());
    }

    @Test
    public void testGetDocument() throws Exception {

        String url = "https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf";
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(Constants.REFI_FOLDER_DATE_FORMAT);
        String formattedCurrentDate = LocalDate.now().format(formatter);
        String finInstnId = "FIOBANK";
        Long applKey = 12334L;

        GetLoanApplicationResult responseApplWithProp = new GetLoanApplicationResult();
        List<ObligationDTO> obligations = new LinkedList<>();
        ObligationDTO obl = new ObligationDTO();
        obl.setPrimaryOblgtn(true);
        obl.setOfFinInstnId(finInstnId);
        obligations.add(obl);
        responseApplWithProp.setObligations(obligations);

        File destinationFile = new File(tempDir + "/" + formattedCurrentDate + "/" + finInstnId + "/********_Zadost_o_splaceni_pujcky_primarni_zavazek_" + applKey + ".pdf");

        when(applicationService.getApplicationWithProperties(anyLong(), anyBoolean(), anyBoolean(), any(), anyBoolean(), anyString())).thenReturn(responseApplWithProp);
        when(delegateExecution.getVariable(ProcessVariable.APPLICATION_KEY.getKey())).thenReturn(applKey);
        var response = new GeneratedDocumentsDTO();
        Map<String, GeneratedDocumentInfoDTO> mapOfDoc = new HashMap<>();
        GeneratedDocumentInfoDTO documentInfo = new GeneratedDocumentInfoDTO();
        documentInfo.setDocumentId("Vypoved_123_PAN_NOVAK");
        mapOfDoc.put(DocumentType.********.name(), documentInfo);
        response.setGeneratedObjectIds(mapOfDoc);
        when(delegateExecution.getVariable(ProcessVariable.GENERATED_DOCUMENTS.getKey())).thenReturn(response);

        GetDocumentUrlDto responseDocument = new GetDocumentUrlDto();
        responseDocument.setDocumentUrl(url);
        responseDocument.setFilename("dummy.pdf");
        responseDocument.setFormat("pdf");
        when(dmsService.getDocumentUrl(anyString(), anyString())).thenReturn(responseDocument);

        try (MockedStatic<FileUtilsWrapper> utilities = Mockito.mockStatic(FileUtilsWrapper.class)) {
            utilities.when(() -> FileUtilsWrapper.createURL(anyString()))
                    .thenReturn(URI.create(url).toURL());
            utilities.when(() -> FileUtilsWrapper.copyURLToFile(any(URL.class), any(File.class), anyInt(), anyInt()))
                    .thenAnswer(invocation -> null);

            assertDoesNotThrow(() -> refiPrepareDocumentsForEmailDelegate.execute(delegateExecution));
        }
    }
}