package cz.equa.camapp.delegate.loan;

import com.fasterxml.jackson.core.JsonProcessingException;
import cz.equa.camapp.processes.loan.LoanApprovalAbstractTest;
import cz.equa.camapp.service.ServiceException;
import cz.equa.camapp.service.applicationservice.model.ObligationDTO;
import cz.equa.camapp.service.applicationservice.model.ObligationsDTO;
import cz.equa.camapp.service.product_service.ProductsDTO;
import cz.rb.tif.ifr_00016_start_service.model.StartServiceDTO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.junit.jupiter.web.SpringJUnitWebConfig;

import java.math.BigDecimal;
import java.util.List;

import static cz.equa.camapp.utils.ApplicationUtils.convertJsonToType;
import static org.assertj.core.api.Assertions.assertThat;

@SpringJUnitWebConfig(classes = {CreateSSTClOptiDelegate.class})
class CreateSSTClOptiDelegateTest extends LoanApprovalAbstractTest {

    @Autowired
    private CreateSSTClOptiDelegate underTest;

    @Test
    void createRequest_Opti() throws ServiceException, JsonProcessingException {
        ObligationsDTO obligations = new ObligationsDTO();
        var obligationsList = getOptiObligations();
        ObligationDTO obl = obligationsList.get(0);
        obl.setScAmt(BigDecimal.valueOf(123.45));
        obl.setScIntSourceSystem("OPTI_AAA");
        obligations.setObligations(obligationsList);

        ProductsDTO productDto = getOptiProducts();
        String partyId = "22030959";
        StartServiceDTO a = underTest.createRequest("1",
                partyId,
                productDto,
                obligations
        );
        System.out.println(a.getParams());
        assertThat(a.getParams()).contains("\"obligationKey\" : \"385098\"");
        assertThat(a.getParams()).contains("\"contractNumber\" : \"OPTI_AAA\"");
        assertThat(a.getParams()).contains("\"obligationAmount\" : \"123.45\"");
    }

    @Test
    void createRequest_Rcc() throws ServiceException, JsonProcessingException {
        ObligationsDTO obligations = new ObligationsDTO();
        var obligationsList = getObligations();
        obligationsList.get(0).setScIntSourceSystem("BBB");
        obligationsList.get(0).setOfOblgtnProdTpId("RCC");
        obligations.setObligations(obligationsList);

        ProductsDTO productDto = new ProductsDTO(getProducts());
        String partyId = "123";
        StartServiceDTO a = underTest.createRequest("1",
                partyId,
                productDto,
                obligations
        );
        System.out.println(a.getParams());
        assertThat(a.getParams()).contains("\"obligationKey\" : \"344\"");
        assertThat(a.getParams()).contains("\"obligationAmount\" : \"1\"");
    }

    private List<ObligationDTO> getOptiObligations() throws JsonProcessingException {
        String json = "{\n" +
                "\t\"obligations\": [\n" +
                "\t\t{\n" +
                "\t\t\t\"instOblgtnKey\": 385098,\n" +
                "\t\t\t\"virtOblgtnId\": null,\n" +
                "\t\t\t\"adbInstPtKey\": 890040,\n" +
                "\t\t\t\"ofFirstInstlDate\": null,\n" +
                "\t\t\t\"ofFinInstnId\": null,\n" +
                "\t\t\t\"ofContrNum\": null,\n" +
                "\t\t\t\"ofTotLoanAmt\": null,\n" +
                "\t\t\t\"scTotLoanAmt\": 276000,\n" +
                "\t\t\t\"ofTotInstlCnt\": null,\n" +
                "\t\t\t\"ofIntrsRx\": null,\n" +
                "\t\t\t\"scIntrsRx\": 16,\n" +
                "\t\t\t\"ofInstl\": null,\n" +
                "\t\t\t\"scInstl\": 5031,\n" +
                "\t\t\t\"ofOblgtnProdTpId\": null,\n" +
                "\t\t\t\"scOblgtnProdTpId\": \"CRL\",\n" +
                "\t\t\t\"ofMaturityDate\": null,\n" +
                "\t\t\t\"ofAmt\": null,\n" +
                "\t\t\t\"scAmt\": 274978,\n" +
                "\t\t\t\"finInstnGrpCode\": \"1\",\n" +
                "\t\t\t\"ofrIntrsRx\": 0.16,\n" +
                "\t\t\t\"origContractRequired\": false,\n" +
                "\t\t\t\"scPrimaryOblgtn\": true,\n" +
                "\t\t\t\"ofAccNum\": null,\n" +
                "\t\t\t\"ofAccNumPrefix\": null,\n" +
                "\t\t\t\"ccyId\": \"CZK\",\n" +
                "\t\t\t\"ofCrntAccFeeAmt\": null,\n" +
                "\t\t\t\"ofContrFeeAmt\": null,\n" +
                "\t\t\t\"ofPrinc\": null,\n" +
                "\t\t\t\"ofInstlCnt\": null,\n" +
                "\t\t\t\"ofOblgtnLoanPurpId\": null,\n" +
                "\t\t\t\"ofBankId\": null,\n" +
                "\t\t\t\"ofVarSymbol\": null,\n" +
                "\t\t\t\"ofConstSymbol\": null,\n" +
                "\t\t\t\"ofSpecSymbol\": null,\n" +
                "\t\t\t\"ofOblgtnDocTpId\": null,\n" +
                "\t\t\t\"ofComment\": null,\n" +
                "\t\t\t\"scCcbContractId\": null,\n" +
                "\t\t\t\"clFinInstnId\": \"RB\",\n" +
                "\t\t\t\"scFinInstnCode\": \"RB\",\n" +
                "\t\t\t\"clOrder\": 1,\n" +
                "\t\t\t\"scOrder\": 1,\n" +
                "\t\t\t\"oblgtnSelected\": true,\n" +
                "\t\t\t\"scIsApplicable\": true,\n" +
                "\t\t\t\"scIsMandatory\": false,\n" +
                "\t\t\t\"scIntContrNum\": null,\n" +
                "\t\t\t\"scIntSourceSystem\": \"L.********\",\n" +
                "\t\t\t\"scIntSourceSystemId\": \"TRS\",\n" +
                "\t\t\t\"primaryOblgtn\": null,\n" +
                "\t\t\t\"finRepayedAmt\": null,\n" +
                "\t\t\t\"totRepaymentFlag\": null,\n" +
                "\t\t\t\"oblgtnAmtAfterRepayment\": null,\n" +
                "\t\t\t\"delFlag\": null\n" +
                "\t\t}\n" +
                "\t]\n" +
                "}";
        ObligationsDTO obligations = convertJsonToType(json, ObligationsDTO.class);
        return obligations.getObligations();
    }

    private ProductsDTO getOptiProducts() throws JsonProcessingException {
        String json = "{\n" +
                "\t\"products\": [\n" +
                "\t\t{\n" +
                "\t\t\t\"deposit\": null,\n" +
                "\t\t\t\"termDeposit\": null,\n" +
                "\t\t\t\"card\": null,\n" +
                "\t\t\t\"loan\": {\n" +
                "\t\t\t\t\"prodSrcId\": {\n" +
                "\t\t\t\t\t\"value\": \"L.********\",\n" +
                "\t\t\t\t\t\"srcSystemId\": \"TRS\"\n" +
                "\t\t\t\t},\n" +
                "\t\t\t\t\"status\": \"ACTIVE\",\n" +
                "\t\t\t\t\"contrBusId\": \"2393936\",\n" +
                "\t\t\t\t\"prodClass\": null,\n" +
                "\t\t\t\t\"prodTp\": \"RCL\",\n" +
                "\t\t\t\t\"busProdSubTp\": \"RCL_REFI\",\n" +
                "\t\t\t\t\"firstTouchPoint\": null,\n" +
                "\t\t\t\t\"fullfilmentCnl\": null,\n" +
                "\t\t\t\t\"createDate\": \"2023-11-15\",\n" +
                "\t\t\t\t\"closeDate\": null,\n" +
                "\t\t\t\t\"descr\": \"RePůjčka 2311624366\",\n" +
                "\t\t\t\t\"accPrefix\": \"27\",\n" +
                "\t\t\t\t\"accNum\": \"4156193003\",\n" +
                "\t\t\t\t\"amountLoan\": 276000,\n" +
                "\t\t\t\t\"ccy\": \"CZK\",\n" +
                "\t\t\t\t\"principalBalance\": 276000,\n" +
                "\t\t\t\t\"maturityLoan\": \"2033-11-15\",\n" +
                "\t\t\t\t\"interestRate\": 16,\n" +
                "\t\t\t\t\"repaymentNextDate\": null,\n" +
                "\t\t\t\t\"rpmtLastDate\": null,\n" +
                "\t\t\t\t\"nextPaymentAmount\": null,\n" +
                "\t\t\t\t\"overdueAmount\": null,\n" +
                "\t\t\t\t\"statement\": null,\n" +
                "\t\t\t\t\"paymentType\": \"A\",\n" +
                "\t\t\t\t\"rpmtOrigCnt\": null,\n" +
                "\t\t\t\t\"rpmtCnt\": null,\n" +
                "\t\t\t\t\"rpmtRegRem\": null,\n" +
                "\t\t\t\t\"rpmtRemCnt\": null,\n" +
                "\t\t\t\t\"insurType\": null,\n" +
                "\t\t\t\t\"applicationId\": null,\n" +
                "\t\t\t\t\"allowedExtraRepayment\": null,\n" +
                "\t\t\t\t\"userRepPlan\": null,\n" +
                "\t\t\t\t\"repaymentAmount\": 276000,\n" +
                "\t\t\t\t\"totalRepaymentAmount\": 0,\n" +
                "\t\t\t\t\"earlyRepayment\": false,\n" +
                "\t\t\t\t\"earlyRepaymentDate\": null,\n" +
                "\t\t\t\t\"customerNo\": \"1125634688\",\n" +
                "\t\t\t\t\"siebelPtId\": \"22030959\",\n" +
                "\t\t\t\t\"loanId\": \"TRS_LOA_2393936\",\n" +
                "\t\t\t\t\"contractNo\": \"2311624366\"\n" +
                "\t\t\t},\n" +
                "\t\t\t\"insurance\": null,\n" +
                "\t\t\t\"mortgage\": null,\n" +
                "\t\t\t\"overdraft\": null,\n" +
                "\t\t\t\"investment\": null\n" +
                "\t\t}\n" +
                "\t]\n" +
                "}";
        return convertJsonToType(json, ProductsDTO.class);
    }

}