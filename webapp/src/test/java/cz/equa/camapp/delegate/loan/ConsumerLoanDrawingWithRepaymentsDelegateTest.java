package cz.equa.camapp.delegate.loan;

import cz.equa.camapp.lovs.LovBusProdSubTp;
import cz.equa.camapp.model.consumer_loan.ProcessDrawingConsumerLoanDTO;
import cz.equa.camapp.process.ProcessVariable;
import cz.equa.camapp.processes.loan.LoanApprovalAbstractTest;
import cz.equa.camapp.service.ServiceException;
import cz.equa.camapp.service.applicationservice.model.ObligationsDTO;
import cz.equa.camapp.utils.Constants;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.engine.test.junit5.ProcessEngineExtension;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.junit.jupiter.web.SpringJUnitWebConfig;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.verify;

@ExtendWith({ProcessEngineExtension.class})
@SpringJUnitWebConfig(classes = {ConsumerLoanDrawingWithRepaymentsDelegate.class})
class ConsumerLoanDrawingWithRepaymentsDelegateTest extends LoanApprovalAbstractTest {

    @Autowired
    private ConsumerLoanDrawingWithRepaymentsDelegate underTest;

    @Mock
    private DelegateExecution delegateExecution;

    @Test
    void loanDrawingRclRefi() throws ServiceException {
        lenient().when(delegateExecution.getVariable(ProcessVariable.PRODUCT_ID.getKey())).thenReturn(LovBusProdSubTp.RCL_REFI.getCode());
        lenient().when(delegateExecution.getVariable(ProcessVariable.LOAN_APPLICATION_OBLIGATIONS.getKey())).thenReturn(new ObligationsDTO(getObligationsWithPrimaryObligation()));
        lenient().when(delegateExecution.getVariable(ProcessVariable.LOAN_APPLICATION.getKey())).thenReturn(getLoanApplication());
        lenient().when(delegateExecution.getVariable(ProcessVariable.TCHER_EVENT.getKey())).thenReturn(null);
        lenient().when(delegateExecution.getVariable(ProcessVariable.APPLICATION_VARIANTS.getKey())).thenReturn(getApplVariantsWithREQandAPR());

        assertDoesNotThrow(() -> underTest.execute(delegateExecution));

        ArgumentCaptor<ProcessDrawingConsumerLoanDTO> argument = ArgumentCaptor.forClass(ProcessDrawingConsumerLoanDTO.class);
        verify(bai00029ConsumerLoan).processDrawing(argument.capture());
        assertTrue(argument.getValue().getListOfRepayments().getRepayment().stream()
                .allMatch(i -> "0100".equals(i.getBankCode()) && Constants.DEFAULT_CURRENCY.equals(i.getCurrencyCode()) && BigDecimal.ONE.compareTo(i.getAmount()) == 0));
    }

    @Test
    void loanDrawingRclOpti() throws ServiceException {
        lenient().when(delegateExecution.getVariable(ProcessVariable.PRODUCT_ID.getKey())).thenReturn("RCL_OPTI");
        lenient().when(delegateExecution.getVariable(ProcessVariable.LOAN_APPLICATION_OBLIGATIONS.getKey())).thenReturn(new ObligationsDTO(getObligationsWithPrimaryObligation()));
        lenient().when(delegateExecution.getVariable(ProcessVariable.LOAN_APPLICATION.getKey())).thenReturn(getLoanApplication());
        lenient().when(delegateExecution.getVariable(ProcessVariable.TCHER_EVENT.getKey())).thenReturn(null);
        lenient().when(delegateExecution.getVariable(ProcessVariable.APPLICATION_VARIANTS.getKey())).thenReturn(getApplVariantsWithREQandAPR());

        assertDoesNotThrow(() -> underTest.execute(delegateExecution));

        ArgumentCaptor<ProcessDrawingConsumerLoanDTO> argument = ArgumentCaptor.forClass(ProcessDrawingConsumerLoanDTO.class);
        verify(bai00029ConsumerLoan).processDrawing(argument.capture());
        assertNotNull(argument.getValue().getListOfRepayments());
        assertNotNull(argument.getValue().getListOfRepayments().getRepayment());
        assertTrue(argument.getValue().getListOfRepayments().getRepayment().stream()
                .allMatch(i -> Constants.ACCOUNT_BANK_CODE.equals(i.getBankCode()) && Constants.DEFAULT_CURRENCY.equals(i.getCurrencyCode()) && BigDecimal.TEN.compareTo(i.getAmount()) == 0));
    }
}