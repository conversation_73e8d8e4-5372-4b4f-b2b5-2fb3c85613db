package cz.equa.camapp.delegate.loan;

import cz.equa.camapp.delegate.CheckStornoAllowedDelegate;
import cz.equa.camapp.process.ProcessVariable;
import cz.equa.camapp.rest.service.ApplicationService;
import cz.equa.camapp.rest.service.OperationService;
import cz.rb.las.application.model.StateLog;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.test.context.junit.jupiter.web.SpringJUnitWebConfig;

import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@SpringJUnitWebConfig(classes = {CheckStornoAllowedDelegate.class})
public class CheckStornoAllowedDelegateTest {

    @Autowired
    private CheckStornoAllowedDelegate underTest;

    @Mock
    private DelegateExecution delegateExecution;

    @MockBean
    public ApplicationService applicationService;

    @MockBean
    public OperationService operationService;

    @MockBean
    protected JavaMailSender javaMailSender;

    @BeforeEach
    public void setup() {
        when(delegateExecution.getBusinessKey()).thenReturn("LoanApproval-123456-123456789");
    }

    @Test
    public void test_MSCH1_TCHER() throws Exception {
        when(applicationService.getApplStatusHistory(any(), any(), anyString())).thenReturn(createSortedStateWithTcherLog("MSCH1"));

        underTest.execute(delegateExecution);

        verify(delegateExecution).setVariable(ProcessVariable.STORNO_ALLOWED.getKey(), true);
    }

    @Test
    public void test_SMLAKC_TCHER() throws Exception {

        when(applicationService.getApplStatusHistory(any(), any(), anyString())).thenReturn(createSortedStateWithTcherLog("SMLAKC"));

        underTest.execute(delegateExecution);

        verify(delegateExecution).setVariable(ProcessVariable.STORNO_ALLOWED.getKey(), false);
    }

    @Test
    public void test_MSCH1() throws Exception {

        when(applicationService.getApplStatusHistory(any(), any(), anyString())).thenReturn(createSortedStateLog("MSCH1"));

        underTest.execute(delegateExecution);

        verify(delegateExecution).setVariable(ProcessVariable.STORNO_ALLOWED.getKey(), true);
    }

    @Test
    public void test_SMLAKC() throws Exception {

        when(applicationService.getApplStatusHistory(any(), any(), anyString())).thenReturn(createSortedStateLog("SMLAKC"));

        underTest.execute(delegateExecution);

        verify(delegateExecution).setVariable(ProcessVariable.STORNO_ALLOWED.getKey(), false);
    }

    @Test
    public void testUnsorted_MSCH1_TCHER() throws Exception {

        when(applicationService.getApplStatusHistory(any(), any(), anyString())).thenReturn(createUnSortedStateWithTcherLog("MSCH1"));

        underTest.execute(delegateExecution);

        verify(delegateExecution).setVariable(ProcessVariable.STORNO_ALLOWED.getKey(), true);
    }

    private List<StateLog> createUnSortedStateWithTcherLog(String stateBeforeTcher) {
        List<StateLog> stateLog = new ArrayList<>();
        stateLog.add(createStateLogEntry(5, stateBeforeTcher));
        stateLog.add(createStateLogEntry(2, "PZLEAD"));
        stateLog.add(createStateLogEntry(6, "TCHER"));
        stateLog.add(createStateLogEntry(3, "PZSTAP"));
        stateLog.add(createStateLogEntry(1, "PZ"));
        stateLog.add(createStateLogEntry(4, "SMLAKC"));
        return stateLog;
    }

    private List<StateLog> createSortedStateWithTcherLog(String stateBeforeTcher) {
        List<StateLog> stateLog = createSortedStateLog(stateBeforeTcher);
        stateLog.add(createStateLogEntry(6, "TCHER"));
        return stateLog;
    }

    private List<StateLog> createSortedStateLog(String lastState) {
        List<StateLog> stateLog = new ArrayList<>();
        stateLog.add(createStateLogEntry(1, "PZ"));
        stateLog.add(createStateLogEntry(2, "PZLEAD"));
        stateLog.add(createStateLogEntry(3, "PZSTAP"));
        stateLog.add(createStateLogEntry(4, "ZDPDP"));
        stateLog.add(createStateLogEntry(5, lastState));
        return stateLog;
    }

    private StateLog createStateLogEntry(int order, String state) {
        StateLog logEntry = new StateLog();
        logEntry.setState(state);
        logEntry.setChangeDateTime(OffsetDateTime.of(2020, order, 2, 12, 12, 12, 12, ZoneOffset.UTC));
        return logEntry;
    }
}
