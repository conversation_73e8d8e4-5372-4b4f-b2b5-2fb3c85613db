package cz.equa.camapp.loadtest;

import cz.equa.camapp.rest.service.ApplicationService;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.test.context.junit.jupiter.web.SpringJUnitWebConfig;

import java.util.concurrent.*;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.mockito.Mockito.mock;

@SpringJUnitWebConfig(classes = {StuckDelegate.class})
public class StuckDelegateTest {

    @Autowired
    private StuckDelegate stuckDelegate;

    @MockBean
	public ApplicationService applicationService;
    
    @MockBean
    private JavaMailSender javaMail;
        
    @Test
    public void testExecute() throws Exception {
        final DelegateExecution execution = mock(DelegateExecution.class);
        final ExecutorService executor = Executors.newSingleThreadExecutor();
        final Future<String> future = executor.submit((Callable) () -> {
            stuckDelegate.execute(execution);
            return "FINISHED";
        });

        boolean finished = false;
        try {
            future.get(1, TimeUnit.SECONDS);
            finished = true;
        } catch (TimeoutException e) {
            stuckDelegate.interrupt();
        }
        assertFalse(finished);
    }

}