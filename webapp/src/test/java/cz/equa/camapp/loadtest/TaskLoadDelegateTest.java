package cz.equa.camapp.loadtest;

import cz.equa.camapp.rest.service.ApplicationService;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.test.context.junit.jupiter.web.SpringJUnitWebConfig;

import static org.mockito.Mockito.*;

@SpringJUnitWebConfig(classes = {TaskLoadDelegate.class})
public class TaskLoadDelegateTest {

    @Autowired
    private TaskLoadDelegate taskLoadDelegate;

    @MockBean
	public ApplicationService applicationService;
    
    @MockBean
    private JavaMailSender javaMail;

    @Test
    public void testExecute() throws Exception {
        final DelegateExecution execution = mock(DelegateExecution.class);
        doReturn(1).when(execution).getVariable("ITERATIONS");

        taskLoadDelegate.execute(execution);
        verify(execution, times(1)).setVariable(eq("ITERATIONS"), eq(0));
    }

}