package cz.equa.camapp.processes.loan;

import cz.equa.camapp.EventSubscriber;
import cz.equa.camapp.lovs.LovBusProdSubTp;
import cz.equa.camapp.lovs.LovDsnTp;
import cz.equa.camapp.process.ProcessVariable;
import cz.equa.camapp.rest.model.cus.party.GetDetailResultDto;
import cz.equa.camapp.service.applicationservice.model.LoanApplicationDTO;
import cz.equa.camapp.service.applicationservice.model.ObligationDTO;
import cz.equa.camunda.task.CompletionType;
import org.camunda.bpm.engine.RuntimeService;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Map;

import static cz.equa.camapp.process.Message.*;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

public class LoanApprovalESALTest extends LoanApprovalAbstractTest {
    
    private static final String TASK_ID = "1231";
    
    @Test
	public void clPersonVerifiedPath() throws Exception {
        prepareFindPartyResponse();
        when(applicationApprovalService.callApproval(any(), eq(3), eq("KO_PRE_CONTRACT_GEN"), anyString())).thenReturn(LovDsnTp.APPROVE);
        when(operationService.checkApplUserRights(any(), any(), any(), any(LoanApplicationDTO.class), anyString())).thenReturn(true);
        when(applicationService.getApplState(any(), eq("7643"), anyString())).thenReturn("FNLEND");

        when(partyService.getDetail(any(), any())).thenReturn(createGetDetailResultDto(true,true,true,true,true));

		final RuntimeService runtimeService = processEngine.getRuntimeService();
        
        TestCheckpoint testCheckPoints = new TestCheckpoint(runtimeService)
                .addTransitionCheck("Flow_16k6suw")
                .addOnEndOfActivity("EndProcess");

		Map<String, Object> startProcessVariables = getStartProcessVariables();
		startProcessVariables.put(ProcessVariable.NO_NACES_FOUND.getKey(), false);
		startProcessVariables.put(ProcessVariable.GET_DETAIL_RESULT.getKey(), new GetDetailResultDto());
        startProcessVariables.put(ProcessVariable.LOAN_APPLICATION_RETAIL_CLOSED_END_LOAN.getKey(), true);
        startProcessVariables.put(ProcessVariable.LOAN_APPLICATION_RETAIL_OVERDRAFT.getKey(), true);
        startProcessVariables.put(ProcessVariable.LOAN_APPLICATION_CREDIT_CARD_MIDAS_OBLIGATION.getKey(), getObligations().get(0));
		runtimeService.startProcessInstanceByKey("LoanApproval", BUSINESS_KEY, startProcessVariables);
        
        final EventSubscriber eventSubscribers = new EventSubscriber(processManager, BUSINESS_KEY, true);
		eventSubscribers.addMessage(CL_PERSONAL_DATA_COLLECTED);
        eventSubscribers.addMessage(CL_APPLICATION_FINALIZED);
        eventSubscribers.addMessage(CL_SST_CREATED);
        eventSubscribers.addMessage(CL_SST_FINISHED);
        eventSubscribers.addMessage(CL_CONTRACT_SIGNED_ONLINE);
        
		waitUntilNoActiveJobs(processEngine, WAIT_TO_JOBS, eventSubscribers, null);

        assertTrue(testCheckPoints.checkpointPassed(0));
        assertTrue(testCheckPoints.checkpointPassed(1));
	}

    @Override
    protected LoanApplicationDTO getLoanApplication() {
        LoanApplicationDTO application = super.getLoanApplication();
        application.setBusProdSubTp(LovBusProdSubTp.RCL_REFI.getCode());
        return application;
    }

    @Override
    protected List<ObligationDTO> getObligations() {
        return super.getObligationsWithPrimaryObligation();
    }

    @Test
	public void clPurposeDocumentedWithCreateSst() throws Exception {
        prepareFindPartyResponse();
        when(applicationApprovalService.callApproval(any(), eq(3), eq("KO_PRE_CONTRACT_GEN"), anyString())).thenReturn(LovDsnTp.APPROVE);
        when(operationService.checkApplUserRights(any(), any(), any(), any(LoanApplicationDTO.class), anyString())).thenReturn(true);
        when(taskManager.completeTask(eq(TASK_ID), eq(CompletionType.CANCELED), eq(null), eq(BUSINESS_KEY))).thenReturn(true);
        when(applicationService.getApplState(any(), eq("7643"), anyString())).thenReturn("FNLEND");
        when(partyService.getDetail(any(), any())).thenReturn(createGetDetailResultDto(true,true,true,true,true));

		final RuntimeService runtimeService = processEngine.getRuntimeService();
        
        TestCheckpoint testCheckPoints = new TestCheckpoint(runtimeService)
                .addTransitionCheck("Flow_0q8m88p")
                .addOnEndOfActivity("EndProcess");

        Map<String, Object> startProcessVariables = getStartProcessVariables();
		startProcessVariables.put(ProcessVariable.NO_NACES_FOUND.getKey(), false);
        startProcessVariables.put(ProcessVariable.LOAN_APPLICATION_CL_REFI_DOCUMENTS_CURRENT_TASK_ID.getKey(), TASK_ID);
        startProcessVariables.put(ProcessVariable.LOAN_APPLICATION_RETAIL_CLOSED_END_LOAN.getKey(), true);
        startProcessVariables.put(ProcessVariable.LOAN_APPLICATION_RETAIL_OVERDRAFT.getKey(), true);
        startProcessVariables.put(ProcessVariable.GET_DETAIL_RESULT.getKey(), new GetDetailResultDto());
		runtimeService.startProcessInstanceByKey("LoanApproval", BUSINESS_KEY, startProcessVariables);
        
        final EventSubscriber eventSubscribers = new EventSubscriber(processManager, BUSINESS_KEY, true);
        eventSubscribers.addMessage(CL_PERSONAL_DATA_COLLECTED);
        eventSubscribers.addMessage(CL_APPLICATION_FINALIZED);
        eventSubscribers.addMessage(CL_PURPOSE_DOCUMENTED);
        eventSubscribers.addMessage(CL_SST_FINISHED);
        eventSubscribers.addMessage(CL_CONTRACT_SIGNED_ONLINE);

        waitUntilNoActiveJobs(processEngine, WAIT_TO_JOBS, eventSubscribers, null);

        assertTrue(testCheckPoints.checkpointPassed(0));
        assertTrue(testCheckPoints.checkpointPassed(1));
    }

    @Test
	public void clPersonVerifiedPathApprovalDecline() throws Exception {
        prepareFindPartyResponse();
        when(applicationApprovalService.callApproval(any(), eq(3), eq("KO_PRE_CONTRACT_GEN"), anyString())).thenReturn(LovDsnTp.APPROVE);
        when(applicationApprovalService.callApproval(any(), eq(2), eq("2ND_SCORING"), anyString())).thenReturn(LovDsnTp.DECLINE);
        when(operationService.checkApplUserRights(any(), any(), any(), any(LoanApplicationDTO.class), anyString())).thenReturn(true);
        when(applicationService.getApplState(any(), eq("7643"), anyString())).thenReturn("FNLEND");
        when(partyService.getDetail(any(), any())).thenReturn(createGetDetailResultDto(true,true,true,true,true));

		final RuntimeService runtimeService = processEngine.getRuntimeService();
        
        TestCheckpoint testCheckPoints = new TestCheckpoint(runtimeService)
                .addOnEndOfActivity("final ZDZA");

		Map<String, Object> startProcessVariables = getStartProcessVariables();
		startProcessVariables.put(ProcessVariable.NO_NACES_FOUND.getKey(), false);
        startProcessVariables.put(ProcessVariable.APPLICATION_VARIANTS.getKey(), getApplVariantsWithREQandAPR());
		startProcessVariables.put(ProcessVariable.REJECTION_ALLOWED.getKey(), true);
        startProcessVariables.put(ProcessVariable.GET_DETAIL_RESULT.getKey(), new GetDetailResultDto());
        runtimeService.startProcessInstanceByKey("LoanApproval", BUSINESS_KEY, startProcessVariables);
        
        final EventSubscriber eventSubscribers = new EventSubscriber(processManager, BUSINESS_KEY, true);
		eventSubscribers.addMessage(CL_PERSONAL_DATA_COLLECTED);
        eventSubscribers.addMessage(CL_APPLICATION_FINALIZED);
        eventSubscribers.addMessage(CL_SST_CREATED);
        eventSubscribers.addMessage(CL_SST_FINISHED);
        eventSubscribers.addMessage(CL_ACCOUNT_OPENING_DONE, getAccountOpenningDoneParameters(true));
        eventSubscribers.addMessage(CL_CONTRACT_SIGNED_ONLINE);
        eventSubscribers.addMessage(CL_ACCOUNT_ACTIVATION_DONE);
        eventSubscribers.addMessage(CL_LOAN_DRAWN);

		waitUntilNoActiveJobs(processEngine, WAIT_TO_JOBS, eventSubscribers, null);

        assertTrue(testCheckPoints.checkpointPassed(0));
	}
    
    @Test
	public void clPersonVerifiedPathApprovalManual() throws Exception {
        prepareFindPartyResponse();
        when(applicationApprovalService.callApproval(any(), eq(3), eq("KO_PRE_CONTRACT_GEN"), anyString())).thenReturn(LovDsnTp.APPROVE);
        when(applicationApprovalService.callApproval(any(), eq(2), eq("2ND_SCORING"), anyString())).thenReturn(LovDsnTp.MANEVALREQ).thenReturn(LovDsnTp.APPROVE);
        when(operationService.checkApplUserRights(any(), any(), any(), any(LoanApplicationDTO.class), anyString())).thenReturn(true);
        when(applicationService.getApplState(any(), eq("7643"), anyString())).thenReturn("FNLEND");
        when(partyService.getDetail(any(), any())).thenReturn(createGetDetailResultDto(true,true,true,true,true));

		final RuntimeService runtimeService = processEngine.getRuntimeService();
        
        TestCheckpoint testCheckPoints = new TestCheckpoint(runtimeService)
                .addOnEndOfActivity("call ZDSCH from MSCH2")
                .addOnEndOfActivity("EndProcess");

        Map<String, Object> startProcessVariables = getStartProcessVariables();
		startProcessVariables.put(ProcessVariable.NO_NACES_FOUND.getKey(), false);
        startProcessVariables.put(ProcessVariable.LOAN_APPLICATION_RETAIL_CLOSED_END_LOAN.getKey(), true);
        startProcessVariables.put(ProcessVariable.LOAN_APPLICATION_RETAIL_OVERDRAFT.getKey(), true);
        startProcessVariables.put(ProcessVariable.GET_DETAIL_RESULT.getKey(), new GetDetailResultDto());
		runtimeService.startProcessInstanceByKey("LoanApproval", BUSINESS_KEY, startProcessVariables);
        
        final EventSubscriber eventSubscribers = new EventSubscriber(processManager, BUSINESS_KEY, true);
        eventSubscribers.addMessage(CL_PERSONAL_DATA_COLLECTED);
        eventSubscribers.addMessage(CL_APPLICATION_FINALIZED);
        eventSubscribers.addMessage(CL_SST_CREATED);
        eventSubscribers.addMessage(CL_SST_FINISHED);
        eventSubscribers.addMessage(CL_MANUAL_APPROVING_DONE);
        eventSubscribers.addMessage(CL_CONTRACT_SIGNED_ONLINE);

        waitUntilNoActiveJobs(processEngine, WAIT_TO_JOBS, eventSubscribers, null);

        assertTrue(testCheckPoints.checkpointPassed(0));
        assertTrue(testCheckPoints.checkpointPassed(1));
	}
}
