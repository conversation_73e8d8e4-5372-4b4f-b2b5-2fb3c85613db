package cz.equa.camapp.processes.loan;

import cz.equa.camapp.EventSubscriber;
import cz.equa.camapp.lovs.LovBusProdSubTp;
import cz.equa.camapp.process.ProcessVariable;
import org.camunda.bpm.engine.RuntimeService;
import org.junit.jupiter.api.Test;

import java.util.Map;

import static cz.equa.camapp.lovs.LovApplStat.*;
import static cz.equa.camapp.process.Message.CL_PERSONAL_DATA_COLLECTED;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class RejectCancelTest extends LoanApprovalAbstractTest {

    @Test
    public void testRejectionNotAllowed() throws Exception {
        final RuntimeService runtimeService = processEngine.getRuntimeService();

        Map<String, Object> startProcessVariables = getStartProcessVariables();
        startProcessVariables.put(ProcessVariable.APPLICATION_STATE.getKey(), THE_CONTRACT_ACCEPTED.getCode());
        startProcessVariables.put(ProcessVariable.UPDATE_OPPORTUNITY_ODS_STATE.getKey(), APPL_REJECTED.getCode());
        startProcessVariables.put(ProcessVariable.REJECTION_ALLOWED.getKey(), false);

        TestCheckpoint testCheckPoints = new TestCheckpoint(runtimeService)
                .addTransitionCheck("Flow_rejectionNotAllowed");

        runtimeService.startProcessInstanceByKey("Reject_cancel", BUSINESS_KEY, startProcessVariables);

        final EventSubscriber eventSubscribers = new EventSubscriber(processManager, BUSINESS_KEY, true);
        eventSubscribers.addMessage(CL_PERSONAL_DATA_COLLECTED);

        waitUntilNoActiveJobs(processEngine, WAIT_TO_JOBS, eventSubscribers, null);
        assertTrue(testCheckPoints.checkpointPassed(0));
    }

    @Test
    public void testRclOpti() throws Exception {
        application.setBusProdSubTp(LovBusProdSubTp.RCL_OPTI.getCode());
        final RuntimeService runtimeService = processEngine.getRuntimeService();

        TestCheckpoint testCheckPoints = new TestCheckpoint(runtimeService)
                .addTransitionCheck("Flow_rejectionAllowed")
                .addTransitionCheck("Flow_notNotifyMerchant")
                .addTransitionCheck("Flow_notGenerateEmail")
                .addTransitionCheck("Flow_notSetUsageState");

        Map<String, Object> startProcessVariables = getStartProcessVariables();
        startProcessVariables.put(ProcessVariable.APPLICATION_STATE.getKey(), PZSTAP.getCode());
        startProcessVariables.put(ProcessVariable.UPDATE_OPPORTUNITY_ODS_STATE.getKey(), APPL_REJECTED.getCode());
        startProcessVariables.put(ProcessVariable.CALL_REJECTION.getKey(), false);

        runtimeService.startProcessInstanceByKey("Reject_cancel", BUSINESS_KEY, startProcessVariables);

        final EventSubscriber eventSubscribers = new EventSubscriber(processManager, BUSINESS_KEY, true);
        eventSubscribers.addMessage(CL_PERSONAL_DATA_COLLECTED);

        waitUntilNoActiveJobs(processEngine, WAIT_TO_JOBS, eventSubscribers, null);
        assertTrue(testCheckPoints.checkpointPassed(0));
        assertTrue(testCheckPoints.checkpointPassed(1));
        assertTrue(testCheckPoints.checkpointPassed(2));
        assertTrue(testCheckPoints.checkpointPassed(3));
    }
}
