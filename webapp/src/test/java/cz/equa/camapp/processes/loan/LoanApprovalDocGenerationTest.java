package cz.equa.camapp.processes.loan;

import cz.equa.camapp.EventSubscriber;
import cz.equa.camapp.lovs.LovBusProdSubTp;
import cz.equa.camapp.lovs.LovCcy;
import cz.equa.camapp.lovs.LovDsnTp;
import cz.equa.camapp.lovs.LovIncTp;
import cz.equa.camapp.process.ProcessVariable;
import cz.equa.camapp.service.applicationservice.model.LoanApplicationDTO;
import cz.equa.camapp.service.applicationservice.model.NewPtIncomeDTO;
import cz.equa.camapp.service.applicationservice.model.ObligationDTO;
import cz.equa.camapp.service.applicationservice.model.PtIncomeListDTO;
import cz.equa.camapp.rest.model.cus.partyApplication.ApplicationDTO;
import cz.equa.camapp.rest.model.cus.partyApplication.ProductDetailDTO;
import org.camunda.bpm.engine.RuntimeService;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;

import static cz.equa.camapp.process.Message.*;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.*;

public class LoanApprovalDocGenerationTest extends LoanApprovalAbstractTest {

    @Test
    public void mainSuccessfulPath() throws Exception {
        when(applicationApprovalService.callApproval(any(), eq(1), eq("1ST_SCORING"), anyString())).thenReturn(LovDsnTp.APPROVE);
        when(operationService.checkApplUserRights(any(), any(), any(), any(LoanApplicationDTO.class), anyString())).thenReturn(true);

        final RuntimeService runtimeService = processEngine.getRuntimeService();
        TestCheckpoint testCheckPoints = new TestCheckpoint(runtimeService)
                .addOnEndOfActivity("call contract creation");

        Map<String, Object> startProcessVariables = getStartProcessVariables();
        startProcessVariables.put(ProcessVariable.PARTY_ID.getKey(), PARTY_ID);
        startProcessVariables.put(ProcessVariable.GET_DETAIL_RESULT.getKey(), createGetDetailResultDto());
        startProcessVariables.put(ProcessVariable.NO_NACES_FOUND.getKey(), false);
        startProcessVariables.put(ProcessVariable.ACCOUNT_OPENING_STATUS_IN_PROGRESS.getKey(), false);
        startProcessVariables.put(ProcessVariable.REJECTION_ALLOWED.getKey(), true);
        runtimeService.startProcessInstanceByKey("LoanApproval", BUSINESS_KEY, startProcessVariables);

        final EventSubscriber eventSubscribers = new EventSubscriber(processManager, BUSINESS_KEY, true);
        eventSubscribers.addMessage(CL_PERSONAL_DATA_COLLECTED);
        eventSubscribers.addMessage(CL_APPLICATION_FINALIZED);
        eventSubscribers.addMessage(CL_CONTRACT_SIGNED_ONLINE);

        waitUntilNoActiveJobs(processEngine, WAIT_TO_JOBS, eventSubscribers, null);
        verify(bai00029ConsumerLoan, times(2)).getOffers(any(), any(), any(), any(), any(), any(), any(), anyBoolean());

        assertTrue(testCheckPoints.checkpointPassed(0));
    }

    @Test
    public void mainSuccessfulPathRefi() throws Exception {
        when(applicationApprovalService.callApproval(any(), eq(1), eq("1ST_SCORING"), anyString())).thenReturn(LovDsnTp.APPROVE);
        when(operationService.checkApplUserRights(any(), any(), any(), any(LoanApplicationDTO.class), anyString())).thenReturn(true);
        application.setBusProdSubTp(LovBusProdSubTp.RCL_REFI.getCode());
        when(partyService.getDetail(any(),any())).thenReturn(prepareGetDetailResultDto());

        final RuntimeService runtimeService = processEngine.getRuntimeService();
        TestCheckpoint testCheckPoints = new TestCheckpoint(runtimeService)
                .addOnEndOfActivity("call contract creation");

        Map<String, Object> startProcessVariables = getStartProcessVariables();
        startProcessVariables.put(ProcessVariable.PRODUCT_ID.getKey(), LovBusProdSubTp.RCL_REFI.getCode());
        startProcessVariables.put(ProcessVariable.PARTY_ID.getKey(), PARTY_ID);
        startProcessVariables.put(ProcessVariable.NO_NACES_FOUND.getKey(), false);
        startProcessVariables.put(ProcessVariable.ACCOUNT_OPENING_STATUS_IN_PROGRESS.getKey(), false);
        startProcessVariables.put(ProcessVariable.REJECTION_ALLOWED.getKey(), true);
        startProcessVariables.put(ProcessVariable.GET_DETAIL_RESULT.getKey(), createGetDetailResultDto());
        runtimeService.startProcessInstanceByKey("LoanApproval", BUSINESS_KEY, startProcessVariables);

        final EventSubscriber eventSubscribers = new EventSubscriber(processManager, BUSINESS_KEY, true);
        eventSubscribers.addMessage(CL_PERSONAL_DATA_COLLECTED);
        eventSubscribers.addMessage(CL_APPLICATION_FINALIZED);
        eventSubscribers.addMessage(CL_CONTRACT_SIGNED_ONLINE);

        waitUntilNoActiveJobs(processEngine, WAIT_TO_JOBS, eventSubscribers, null);
        verify(bai00029ConsumerLoan, times(2)).getOffers(any(), any(), any(), any(), any(), any(), any(), anyBoolean());

        assertTrue(testCheckPoints.checkpointPassed(0));
    }

    protected List<ObligationDTO> getObligationsOpti() {
        List<ObligationDTO> obligations = new ArrayList<>();
        ObligationDTO obligation = new ObligationDTO();
        obligation.setClFinInstnId("KB");
        obligation.setOfFinInstnId("KB");
        obligation.setCcyId(LovCcy.CZECH_KORUNA.getCode());
        obligation.setScIntSourceSystem("SourceSystem");
        obligation.setScIntSourceSystemId("SourceSystemId");
        obligation.setScAmt(BigDecimal.ONE);
        obligation.setScFinInstnCode("RB");
        obligation.setAdbInstPtKey(BigInteger.ONE);
        obligation.setOblgtnSelected(true);
        obligation.setPrimaryOblgtn(true);
        obligation.setOfAmt(BigDecimal.ONE);
        obligation.setOfAccNum("1234567890");
        obligation.setOfTotLoanAmt(BigDecimal.ONE);
        obligation.setInstOblgtnKey(BigInteger.valueOf(364));
        obligation.setScIntSourceSystem("OPTI_PRODuct");
        obligation.setOfOblgtnProdTpId("RCC");
        obligation.setScTotLoanAmt(BigDecimal.valueOf(123));
        obligations.add(obligation);
        return obligations;
    }

    @Test
    public void mainSuccessfulPathOpti() throws Exception {
        application.setBusProdSubTp(LovBusProdSubTp.RCL_OPTI.getCode());
        when(applicationApprovalService.callApproval(any(), eq(1), eq("1ST_SCORING"), anyString())).thenReturn(LovDsnTp.APPROVE);
        when(operationService.checkApplUserRights(any(), any(), any(), any(LoanApplicationDTO.class), anyString())).thenReturn(true);
        when(getLoanApplicationResult.getApplVariants()).thenReturn(getApplVariantsWithSELWithoutAccountNumber());
        lenient().when(getLoanApplicationResult.getObligations()).thenReturn(getObligationsOpti());

        final RuntimeService runtimeService = processEngine.getRuntimeService();
        TestCheckpoint testCheckPoints = new TestCheckpoint(runtimeService)
                .addTransitionCheck("Flow_toSetReserveAcc")
                .addTransitionCheck("Flow_accNum_no")
                .addTransitionCheck("Flow_needUpdateAccountNumber_yes")
                .addTransitionCheck("Flow_skipGetParametrization")
                .addTransitionCheck("Flow_notGenerateX0000008")
                .addTransitionCheck("Flow_toCreateSstRclOpti")
                .addOnEndOfActivity("call contract creation");

        Map<String, Object> startProcessVariables = getStartProcessVariables();
        startProcessVariables.put(ProcessVariable.PRODUCT_ID.getKey(), LovBusProdSubTp.RCL_OPTI.getCode());
        startProcessVariables.put(ProcessVariable.PARTY_ID.getKey(), PARTY_ID);
        startProcessVariables.put(ProcessVariable.NO_NACES_FOUND.getKey(), false);
        startProcessVariables.put(ProcessVariable.ACCOUNT_OPENING_STATUS_IN_PROGRESS.getKey(), false);
        startProcessVariables.put(ProcessVariable.REJECTION_ALLOWED.getKey(), true);
        startProcessVariables.put(ProcessVariable.ACTUAL_PARAMETRIZATION.getKey(), prepareActualParametrization());

        NewPtIncomeDTO newPtIncomeDTO = new NewPtIncomeDTO();
        newPtIncomeDTO.setIncTpId(LovIncTp.ANY.getCode());
        NewPtIncomeDTO newPtIncomeDTO2 = new NewPtIncomeDTO();
        newPtIncomeDTO2.setIncTpId(LovIncTp.ANY.getCode());
        PtIncomeListDTO ptIncomeListDTO = new PtIncomeListDTO();
        ptIncomeListDTO.setIncomes(Arrays.asList(newPtIncomeDTO, newPtIncomeDTO2));
        startProcessVariables.put(ProcessVariable.LOAN_APPLICATION_INCOMES.getKey(), ptIncomeListDTO);
        runtimeService.startProcessInstanceByKey("LoanApproval", BUSINESS_KEY, startProcessVariables);

        final EventSubscriber eventSubscribers = new EventSubscriber(processManager, BUSINESS_KEY, true);
        eventSubscribers.addMessage(CL_PERSONAL_DATA_COLLECTED);
        eventSubscribers.addMessage(CL_APPLICATION_FINALIZED);
        eventSubscribers.addMessage(CL_CONTRACT_SIGNED_ONLINE);
        eventSubscribers.addMessage(CL_OPTI_SST_FINISHED);

        waitUntilNoActiveJobs(processEngine, WAIT_TO_JOBS, eventSubscribers, null);
        verify(bai00029ConsumerLoan, times(1)).getOffers(any(), any(), any(), any(), any(), any(), any(), anyBoolean());

        assertTrue(testCheckPoints.checkpointPassed(0));
        assertTrue(testCheckPoints.checkpointPassed(1));
        assertTrue(testCheckPoints.checkpointPassed(2));
        assertTrue(testCheckPoints.checkpointPassed(3));
        assertTrue(testCheckPoints.checkpointPassed(4));
        assertTrue(testCheckPoints.checkpointPassed(5));
        assertTrue(testCheckPoints.checkpointPassed(6));
    }

    @Test
    public void aoInProcess() throws Exception {
        when(applicationApprovalService.callApproval(any(), eq(1), eq("1ST_SCORING"), anyString())).thenReturn(LovDsnTp.APPROVE);
        when(operationService.checkApplUserRights(any(), any(), any(), any(LoanApplicationDTO.class), anyString())).thenReturn(true);
        when(partyApplicationService.getList(any(), anyString())).thenReturn(null).thenReturn(getAoListOfApplications());

        final RuntimeService runtimeService = processEngine.getRuntimeService();
        TestCheckpoint testCheckPoints = new TestCheckpoint(runtimeService)
                .addOnEndOfActivity("callZDST");

        Map<String, Object> startProcessVariables = getStartProcessVariables();
        startProcessVariables.put(ProcessVariable.PARTY_ID.getKey(), PARTY_ID);
        startProcessVariables.put(ProcessVariable.GET_DETAIL_RESULT.getKey(), createGetDetailResultDto());
        startProcessVariables.put(ProcessVariable.NO_NACES_FOUND.getKey(), false);
        startProcessVariables.put(ProcessVariable.ACCOUNT_OPENING_STATUS_IN_PROGRESS.getKey(), false);
        runtimeService.startProcessInstanceByKey("LoanApproval", BUSINESS_KEY, startProcessVariables);

        final EventSubscriber eventSubscribers = new EventSubscriber(processManager, BUSINESS_KEY, true);
        eventSubscribers.addMessage(CL_PERSONAL_DATA_COLLECTED);
        eventSubscribers.addMessage(CL_APPLICATION_FINALIZED);
        eventSubscribers.addMessage(CL_CONTRACT_SIGNED_ONLINE);

        waitUntilNoActiveJobs(processEngine, WAIT_TO_JOBS, eventSubscribers, null);

        assertFalse(testCheckPoints.checkpointPassed(0));
    }

    @Test
    public void applicationFinalizedCallApprovalReject() throws Exception {
        when(applicationApprovalService.callApproval(any(), eq(1), eq("1ST_SCORING"), anyString())).thenReturn(LovDsnTp.APPROVE);
        when(applicationApprovalService.callApproval(any(), eq(3), eq("KO_PRE_CONTRACT_GEN"), anyString())).thenReturn(LovDsnTp.APPROVE).thenReturn(LovDsnTp.DECLINE);
        when(operationService.checkApplUserRights(any(), any(), any(), any(LoanApplicationDTO.class), anyString())).thenReturn(true);

        final RuntimeService runtimeService = processEngine.getRuntimeService();
        TestCheckpoint testCheckPoints = new TestCheckpoint(runtimeService)
                .addOnEndOfActivity("call ZDZA");

        Map<String, Object> startProcessVariables = getStartProcessVariables();
        startProcessVariables.put(ProcessVariable.PARTY_ID.getKey(), PARTY_ID);
        startProcessVariables.put(ProcessVariable.GET_DETAIL_RESULT.getKey(), createGetDetailResultDto());
        startProcessVariables.put(ProcessVariable.NO_NACES_FOUND.getKey(), false);
        startProcessVariables.put(ProcessVariable.ACCOUNT_OPENING_STATUS_IN_PROGRESS.getKey(), false);
        runtimeService.startProcessInstanceByKey("LoanApproval", BUSINESS_KEY, startProcessVariables);

        final EventSubscriber eventSubscribers = new EventSubscriber(processManager, BUSINESS_KEY, true);
        eventSubscribers.addMessage(CL_PERSONAL_DATA_COLLECTED);
        eventSubscribers.addMessage(CL_APPLICATION_FINALIZED);
        eventSubscribers.addMessage(CL_APPLICATION_FINALIZED);

        waitUntilNoActiveJobs(processEngine, WAIT_TO_JOBS, eventSubscribers, null);

        assertTrue(testCheckPoints.checkpointPassed(0));
    }

    @Test
    public void applicationFinalizedCallApprovalApprove() throws Exception {
        when(applicationApprovalService.callApproval(any(), eq(1), eq("1ST_SCORING"), anyString())).thenReturn(LovDsnTp.APPROVE);
        when(applicationApprovalService.callApproval(any(), eq(3), eq("KO_PRE_CONTRACT_GEN"), anyString())).thenReturn(LovDsnTp.APPROVE).thenReturn(LovDsnTp.APPROVE);
        when(operationService.checkApplUserRights(any(), any(), any(), any(LoanApplicationDTO.class), anyString())).thenReturn(true);

        final RuntimeService runtimeService = processEngine.getRuntimeService();
        TestCheckpoint testCheckPoints = new TestCheckpoint(runtimeService)
                .addTransitionCheck("Flow_approved");

        Map<String, Object> startProcessVariables = getStartProcessVariables();
        startProcessVariables.put(ProcessVariable.PARTY_ID.getKey(), PARTY_ID);
        startProcessVariables.put(ProcessVariable.GET_DETAIL_RESULT.getKey(), createGetDetailResultDto());
        startProcessVariables.put(ProcessVariable.NO_NACES_FOUND.getKey(), false);
        startProcessVariables.put(ProcessVariable.ACCOUNT_OPENING_STATUS_IN_PROGRESS.getKey(), false);
        runtimeService.startProcessInstanceByKey("LoanApproval", BUSINESS_KEY, startProcessVariables);

        final EventSubscriber eventSubscribers = new EventSubscriber(processManager, BUSINESS_KEY, true);
        eventSubscribers.addMessage(CL_PERSONAL_DATA_COLLECTED);
        eventSubscribers.addMessage(CL_APPLICATION_FINALIZED);
        eventSubscribers.addMessage(CL_APPLICATION_FINALIZED);
        eventSubscribers.addMessage(CL_CONTRACT_SIGNED_ONLINE);

        waitUntilNoActiveJobs(processEngine, WAIT_TO_JOBS, eventSubscribers, null);

        assertTrue(testCheckPoints.checkpointPassed(0));
    }

    @Test
    public void accNumEquaNotFilled() throws Exception {
        when(applicationApprovalService.callApproval(any(), eq(1), eq("1ST_SCORING"), anyString())).thenReturn(LovDsnTp.APPROVE);
        when(operationService.checkApplUserRights(any(), any(), any(), any(LoanApplicationDTO.class), anyString())).thenReturn(true);
        when(getLoanApplicationResult.getApplVariants()).thenReturn(getApplVariantsWithSELWithoutAccountNumber());


        final RuntimeService runtimeService = processEngine.getRuntimeService();
        TestCheckpoint testCheckPoints = new TestCheckpoint(runtimeService)
                .addOnEndOfActivity("bookAccNr");

        Map<String, Object> startProcessVariables = getStartProcessVariables();
        startProcessVariables.put(ProcessVariable.PARTY_ID.getKey(), PARTY_ID);
        startProcessVariables.put(ProcessVariable.GET_DETAIL_RESULT.getKey(), createGetDetailResultDto());
        startProcessVariables.put(ProcessVariable.NO_NACES_FOUND.getKey(), false);
        startProcessVariables.put(ProcessVariable.ACCOUNT_OPENING_STATUS_IN_PROGRESS.getKey(), false);
        startProcessVariables.put(ProcessVariable.CALLED_BOOK_ACC_NUMBER.getKey(), false);
        runtimeService.startProcessInstanceByKey("LoanApproval", BUSINESS_KEY, startProcessVariables);
        application.setAccNum(null);
        final EventSubscriber eventSubscribers = new EventSubscriber(processManager, BUSINESS_KEY, true);
        eventSubscribers.addMessage(CL_PERSONAL_DATA_COLLECTED);
        eventSubscribers.addMessage(CL_APPLICATION_FINALIZED);
        eventSubscribers.addMessage(CL_CONTRACT_SIGNED_ONLINE);

        waitUntilNoActiveJobs(processEngine, WAIT_TO_JOBS, eventSubscribers, null);

        assertTrue(testCheckPoints.checkpointPassed(0));
    }

    @Test
    public void accNumEquaFilled() throws Exception {
        when(applicationApprovalService.callApproval(any(), eq(1), eq("1ST_SCORING"), anyString())).thenReturn(LovDsnTp.APPROVE);
        when(operationService.checkApplUserRights(any(), any(), any(), any(LoanApplicationDTO.class), anyString())).thenReturn(true);
        when(getLoanApplicationResult.getApplVariants()).thenReturn(getApplVariantsWithSELWithAccountNumber());


        final RuntimeService runtimeService = processEngine.getRuntimeService();
        TestCheckpoint testCheckPoints = new TestCheckpoint(runtimeService)
                .addTransitionCheck("Flow_accNum_yes");

        Map<String, Object> startProcessVariables = getStartProcessVariables();
        startProcessVariables.put(ProcessVariable.PARTY_ID.getKey(), PARTY_ID);
        startProcessVariables.put(ProcessVariable.GET_DETAIL_RESULT.getKey(), createGetDetailResultDto());
        startProcessVariables.put(ProcessVariable.NO_NACES_FOUND.getKey(), false);
        startProcessVariables.put(ProcessVariable.ACCOUNT_OPENING_STATUS_IN_PROGRESS.getKey(), false);
        runtimeService.startProcessInstanceByKey("LoanApproval", BUSINESS_KEY, startProcessVariables);

        final EventSubscriber eventSubscribers = new EventSubscriber(processManager, BUSINESS_KEY, true);
        eventSubscribers.addMessage(CL_PERSONAL_DATA_COLLECTED);
        eventSubscribers.addMessage(CL_APPLICATION_FINALIZED);
        eventSubscribers.addMessage(CL_CONTRACT_SIGNED_ONLINE);

        waitUntilNoActiveJobs(processEngine, WAIT_TO_JOBS, eventSubscribers, null);

        assertTrue(testCheckPoints.checkpointPassed(0));
    }

    @Test
    public void onlChannelChanged() throws Exception {
        when(applicationApprovalService.callApproval(any(), eq(1), eq("1ST_SCORING"), anyString())).thenReturn(LovDsnTp.APPROVE);
        when(operationService.checkApplUserRights(any(), any(), any(), any(LoanApplicationDTO.class), anyString())).thenReturn(true);
        when(getLoanApplicationResult.getApplVariants()).thenReturn(getApplVariantsWithSELWithAccountNumber());


        final RuntimeService runtimeService = processEngine.getRuntimeService();
        TestCheckpoint testCheckPoints = new TestCheckpoint(runtimeService)
                .addTransitionCheck("Flow_accNum_yes");

        Map<String, Object> startProcessVariables = getStartProcessVariables();
        startProcessVariables.put(ProcessVariable.PARTY_ID.getKey(), PARTY_ID);
        startProcessVariables.put(ProcessVariable.GET_DETAIL_RESULT.getKey(), createGetDetailResultDto());
        startProcessVariables.put(ProcessVariable.NO_NACES_FOUND.getKey(), false);
        startProcessVariables.put(ProcessVariable.ACCOUNT_OPENING_STATUS_IN_PROGRESS.getKey(), false);
        startProcessVariables.put(ProcessVariable.GET_DETAIL_RESULT.getKey(), createGetDetailResultDto());
        runtimeService.startProcessInstanceByKey("LoanApproval", BUSINESS_KEY, startProcessVariables);

        final EventSubscriber eventSubscribers = new EventSubscriber(processManager, BUSINESS_KEY, true);
        eventSubscribers.addMessage(CL_PERSONAL_DATA_COLLECTED);
        eventSubscribers.addMessage(CL_APPLICATION_FINALIZED);
        Map<String, Object> clOnbStatusChangeVariables = new HashMap<>();
        clOnbStatusChangeVariables.put("clONBChannelChange_channel", "BANKID");
        clOnbStatusChangeVariables.put("clONBChannelChange_posId", "18");
        eventSubscribers.addMessage(CL_ONB_CHANNEL_CHANGE, clOnbStatusChangeVariables);
        eventSubscribers.addMessage(CL_CONTRACT_SIGNED_ONLINE);

        waitUntilNoActiveJobs(processEngine, WAIT_TO_JOBS, eventSubscribers, null);

        assertTrue(testCheckPoints.checkpointPassed(0));
    }

    public void onlStatusChangedRejected() throws Exception {
        when(applicationApprovalService.callApproval(any(), eq(1), eq("1ST_SCORING"), anyString())).thenReturn(LovDsnTp.APPROVE);
        when(operationService.checkApplUserRights(any(), any(), any(), any(LoanApplicationDTO.class), anyString())).thenReturn(true);
        when(getLoanApplicationResult.getApplVariants()).thenReturn(getApplVariantsWithSELWithAccountNumber());


        final RuntimeService runtimeService = processEngine.getRuntimeService();
        TestCheckpoint testCheckPoints = new TestCheckpoint(runtimeService)
                .addTransitionCheck("Flow_accNum_yes");

        Map<String, Object> startProcessVariables = getStartProcessVariables();
        startProcessVariables.put(ProcessVariable.PARTY_ID.getKey(), PARTY_ID);
        startProcessVariables.put(ProcessVariable.GET_DETAIL_RESULT.getKey(), createGetDetailResultDto());
        startProcessVariables.put(ProcessVariable.NO_NACES_FOUND.getKey(), false);
        startProcessVariables.put(ProcessVariable.ACCOUNT_OPENING_STATUS_IN_PROGRESS.getKey(), false);
        runtimeService.startProcessInstanceByKey("LoanApproval", BUSINESS_KEY, startProcessVariables);

        final EventSubscriber eventSubscribers = new EventSubscriber(processManager, BUSINESS_KEY, true);
        eventSubscribers.addMessage(CL_PERSONAL_DATA_COLLECTED);
        eventSubscribers.addMessage(CL_APPLICATION_FINALIZED);
        Map<String, Object> clOnbStatusChangeVariables = new HashMap<>();
        clOnbStatusChangeVariables.put("clONBStatusChange_applicationState", "REJECTED");
        eventSubscribers.addMessage(CL_ONB_STATUS_CHANGE, clOnbStatusChangeVariables);
        eventSubscribers.addMessage(CL_CONTRACT_SIGNED_ONLINE);

        waitUntilNoActiveJobs(processEngine, WAIT_TO_JOBS, eventSubscribers, null);

        assertTrue(testCheckPoints.checkpointPassed(0));
    }

    @Test
    public void onlStatusChangedFinished() throws Exception {
        when(applicationApprovalService.callApproval(any(), eq(1), eq("1ST_SCORING"), anyString())).thenReturn(LovDsnTp.APPROVE);
        when(operationService.checkApplUserRights(any(), any(), any(), any(LoanApplicationDTO.class), anyString())).thenReturn(true);
        when(getLoanApplicationResult.getApplVariants()).thenReturn(getApplVariantsWithSELWithAccountNumber());


        final RuntimeService runtimeService = processEngine.getRuntimeService();
        TestCheckpoint testCheckPoints = new TestCheckpoint(runtimeService)
                .addTransitionCheck("Flow_accNum_yes");

        Map<String, Object> startProcessVariables = getStartProcessVariables();
        startProcessVariables.put(ProcessVariable.PARTY_ID.getKey(), PARTY_ID);
        startProcessVariables.put(ProcessVariable.GET_DETAIL_RESULT.getKey(), createGetDetailResultDto());
        startProcessVariables.put(ProcessVariable.NO_NACES_FOUND.getKey(), false);
        startProcessVariables.put(ProcessVariable.ACCOUNT_OPENING_STATUS_IN_PROGRESS.getKey(), false);
        runtimeService.startProcessInstanceByKey("LoanApproval", BUSINESS_KEY, startProcessVariables);

        final EventSubscriber eventSubscribers = new EventSubscriber(processManager, BUSINESS_KEY, true);
        eventSubscribers.addMessage(CL_PERSONAL_DATA_COLLECTED);
        eventSubscribers.addMessage(CL_APPLICATION_FINALIZED);
        Map<String, Object> clOnbStatusChangeVariables = new HashMap<>();
        clOnbStatusChangeVariables.put("clONBStatusChange_applicationState", "FINISHED");
        eventSubscribers.addMessage(CL_ONB_STATUS_CHANGE, clOnbStatusChangeVariables);
        eventSubscribers.addMessage(CL_CONTRACT_SIGNED_ONLINE);

        waitUntilNoActiveJobs(processEngine, WAIT_TO_JOBS, eventSubscribers, null);

        assertTrue(testCheckPoints.checkpointPassed(0));
    }

    public void stornoProcessAfterDocsAreGenerated() throws Exception {
        when(applicationApprovalService.callApproval(any(), eq(1), eq("1ST_SCORING"), anyString())).thenReturn(LovDsnTp.APPROVE);
        when(getLoanApplicationResult.getApplVariants()).thenReturn(getApplVariantsWithSELWithAccountNumber());


        final RuntimeService runtimeService = processEngine.getRuntimeService();
        TestCheckpoint testCheckPoints = new TestCheckpoint(runtimeService)
                .addTransitionCheck("Flow_accNum_yes");

        Map<String, Object> startProcessVariables = getStartProcessVariables();
        startProcessVariables.put(ProcessVariable.PARTY_ID.getKey(), PARTY_ID);
        startProcessVariables.put(ProcessVariable.GET_DETAIL_RESULT.getKey(), createGetDetailResultDto());
        startProcessVariables.put(ProcessVariable.NO_NACES_FOUND.getKey(), false);
        startProcessVariables.put(ProcessVariable.MOCK_APPLICATION.getKey(), false);
        startProcessVariables.put(ProcessVariable.ACCOUNT_OPENING_STATUS_IN_PROGRESS.getKey(), false);
        runtimeService.startProcessInstanceByKey("LoanApproval", BUSINESS_KEY, startProcessVariables);

        final EventSubscriber eventSubscribers = new EventSubscriber(processManager, BUSINESS_KEY, true);
        eventSubscribers.addMessage(CL_PERSONAL_DATA_COLLECTED);
        eventSubscribers.addMessage(CL_APPLICATION_FINALIZED);
        eventSubscribers.addMessage(STORNO_PROCESS);

        waitUntilNoActiveJobs(processEngine, WAIT_TO_JOBS, eventSubscribers, null);

        assertTrue(testCheckPoints.checkpointPassed(0));
    }

    private List<ApplicationDTO> getAoListOfApplications() {
        List<ApplicationDTO> listOfAOApplications = new ArrayList<>();
        ApplicationDTO aoApplication = new ApplicationDTO();

        ProductDetailDTO productDetail = new ProductDetailDTO();
        productDetail.setProductCategory("2");
        aoApplication.setProductDetail(productDetail);

        listOfAOApplications.add(aoApplication);
        return listOfAOApplications;
    }
}
