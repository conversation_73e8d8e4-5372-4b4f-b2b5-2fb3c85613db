package cz.equa.camapp.processes.loan.deffered_payment;

import cz.equa.camapp.lovs.LovBankCzCode;
import cz.equa.camapp.lovs.LovDsnTp;
import cz.equa.camapp.process.ProcessVariable;
import cz.equa.camapp.processes.loan.LoanApprovalAbstractTest;
import cz.equa.camapp.service.paymentorderserviceinternal.model.PaymentDTO;
import org.camunda.bpm.engine.RuntimeService;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.Map;

import static cz.equa.camapp.process.ProcessVariable.LOAN_APPLICATION_OWNER;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.*;

public class PennyPaymentTest extends LoanApprovalAbstractTest {
    private static final String PENNY_PAYMENT = "PennyPayment";

    @Test
    public void testPennyPayment() throws Exception {
        when(applicationApprovalService.callApproval(any(), eq(1), eq("1ST_SCORING"), anyString())).thenReturn(LovDsnTp.APPROVE);

        
        final RuntimeService runtimeService = processEngine.getRuntimeService();

        TestCheckpoint testCheckPoints = new TestCheckpoint(runtimeService)
                .addOnStartOfActivity("send penny to technical account")
                .addOnEndOfActivity("Successfully send");
        runtimeService.startProcessInstanceByKey(PENNY_PAYMENT, BUSINESS_KEY, getStartProcessVariables());
        waitUntilNoActiveJobs(processEngine, WAIT_TO_JOBS, getEventSubscribers(BUSINESS_KEY), null);

        assertTrue(testCheckPoints.checkpointPassed(0));
        assertTrue(testCheckPoints.checkpointPassed(1));
    }

    @Override
    public Map<String, Object> getStartProcessVariables() throws Exception {
        final Map<String, Object> variables = super.getStartProcessVariables();
        variables.put(LOAN_APPLICATION_OWNER.getKey(), getPrimaryOwner());

        PaymentDTO pennyTransaction = new PaymentDTO();
        pennyTransaction.setAmount(BigDecimal.ONE);
        pennyTransaction.setDebtorAccNum("1234567");
        pennyTransaction.setDebtorBankCode(new LovBankCzCode("5500"));
        pennyTransaction.setVariableSymbol(BUSINESS_KEY);

        variables.put(ProcessVariable.PENNY_TRANSACTION.getKey(), pennyTransaction);

        return variables;
    }
}
