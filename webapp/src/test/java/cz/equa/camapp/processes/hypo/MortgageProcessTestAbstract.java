package cz.equa.camapp.processes.hypo;

import cz.equa.camapp.AbstractWorker;
import cz.equa.camapp.InMemProcessEngineConfiguration;
import cz.equa.camapp.delegate.GetMtgApplPersonDelegate;
import cz.equa.camapp.delegate.SetMtgApplPersonDelegate;
import cz.equa.camapp.delegate.loan.*;
import cz.equa.camapp.lovs.LovApplPtRoleTp;
import cz.equa.camapp.lovs.LovDsnTp;
import cz.equa.camapp.lovs.LovProcessTp;
import cz.equa.camapp.mtg.ProcessVariables;
import cz.equa.camapp.mtg.worker.UpdateApplicationStatus;
import cz.equa.camapp.process.ProcessVariable;
import cz.equa.camapp.processes.TestExternalTaskService;
import cz.equa.camapp.rest.model.cus.party.ContactsDto;
import cz.equa.camapp.rest.model.cus.party.DocumentsDtoList;
import cz.equa.camapp.rest.model.cus.party.GetDetailResultDto;
import cz.equa.camapp.rest.model.cus.party.PersonDto;
import cz.equa.camapp.rest.service.*;
import cz.equa.camapp.service.ServiceException;
import cz.equa.camapp.service.applicationservice.model.*;
import cz.rb.tif.cus_00041_ent_party.Cus00041EntParty;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.client.spring.annotation.ExternalTaskSubscription;
import org.camunda.bpm.client.task.ExternalTask;
import org.camunda.bpm.engine.repository.ProcessDefinition;
import org.camunda.bpm.engine.runtime.ProcessInstance;
import org.camunda.bpm.engine.test.junit5.ProcessEngineExtension;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.Activity;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.springframework.beans.factory.annotation.AnnotatedBeanDefinition;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider;
import org.springframework.core.type.filter.AnnotationTypeFilter;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.junit.jupiter.web.SpringJUnitWebConfig;

import java.io.InputStream;
import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.time.OffsetDateTime;
import java.util.*;

import static org.camunda.bpm.engine.test.assertions.bpmn.BpmnAwareTests.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@SpringJUnitWebConfig(classes = {InMemProcessEngineConfiguration.class,
        GetSBLPartyDetailDelegate.class,
        CheckPersonalDataDelegate.class,
        SetVariablesRejectionCancellationDelegate.class,
        GetMtgApplPersonDelegate.class,
        SetMtgApplPersonDelegate.class,
        SetApplFromPartyDelegate.class,
        FindPartyInSBLDelegate.class,
        SetPartyFromApplDelegate.class,
        CreatePartyInSiebelDelegate.class})
@ExtendWith({ProcessEngineExtension.class, SpringExtension.class})
@Slf4j
public abstract class MortgageProcessTestAbstract {
    protected static final String BUSINESS_KEY = "BusinessKey";

    private static Set<BeanDefinition> beanDefs;
    private final Map<String, Collection<Activity>> activitiesForProcessInstance = new HashMap<>();

    @Mock
    protected ExternalTask externalTaskMock;

    protected final TestExternalTaskService testExternalTaskService = new TestExternalTaskService();

    @MockBean
    protected JavaMailSender javaMailSender;

    @MockBean
    protected Cus00041EntParty cus00041EntParty;

    @MockBean
    protected ApplicationService applicationService;

    @MockBean
    protected PartyApplicationService partyApplicationService;

    @MockBean
    protected OperationService operationService;

    @MockBean
    protected CashloanApplicationService cashloanApplicationService;

    @MockBean
    protected ApprovalService approvalService;

    @MockBean
    protected CusActivityService cusActivityService;

    @MockBean
    private PartyService partyService;

    @BeforeAll
    public static void setupClass() {
        ClassPathScanningCandidateComponentProvider provider = new ClassPathScanningCandidateComponentProvider(false);
        provider.addIncludeFilter(new AnnotationTypeFilter(ExternalTaskSubscription.class));

        beanDefs = provider.findCandidateComponents("cz.equa.camapp.mtg.worker");
    }

    @AfterEach
    public void cleanProcessEngine() {
        int cnt = processInstanceQuery().list().size();
        processInstanceQuery().list().forEach(inst -> runtimeService().deleteProcessInstance(inst.getId(), "deleteReason"));
        log.debug("Killed {} process instances", cnt);
    }

    protected GetMortApplResponseDTO getMtgApplication(boolean active) {
        GetMortApplResponseDTO application = new GetMortApplResponseDTO();
        ApplIdKeyIdDTO applIdKeyIdDTO = new ApplIdKeyIdDTO();
        applIdKeyIdDTO.setApplKey(1L);
        applIdKeyIdDTO.setBusApplId("1");
        application.setApplId(applIdKeyIdDTO);
        List<PersonMortGetDTO> persons = new ArrayList<>();
        PersonMortGetDTO main = new PersonMortGetDTO();
        main.setAdbInstPtKey(10L);
        main.setFirstName("Hlavni");
        main.setFamilyName("Hypotekar");
        persons.add(main);
        PersonMortGetDTO other = new PersonMortGetDTO();
        other.setAdbInstPtKey(20L);
        other.setFirstName("Vedlejsi");
        other.setFamilyName("Hypotekar");
        persons.add(other);
        application.setPersons(persons);
        List<GetMortApplDTO> appls = new ArrayList<>();

        GetMortApplDTO appl = new GetMortApplDTO();
        appl.setApplId(applIdKeyIdDTO);
        appl.setApplTpId("NEW_PROD");
        List<GetMortApplPersDTO> pers = new ArrayList<>();
        GetMortApplPersDTO p = new GetMortApplPersDTO();
        p.setAdbInstPtKey(10L);
        p.setApplPtRoleTpId(LovApplPtRoleTp.MAIN.getCode());
        pers.add(p);
        appl.setPersons(pers);
        appl.setApplStatId(active ? "GNV" : "STR");
        appl.setHash("hash");
        appl.setApplDate(OffsetDateTime.now());
        appl.setApplDateTo(OffsetDateTime.now());
        appls.add(appl);

        GetMortApplDTO appl2 = new GetMortApplDTO();
        ApplIdKeyIdDTO applIdKeyIdDTO2 = new ApplIdKeyIdDTO();
        applIdKeyIdDTO2.setApplKey(2L);
        applIdKeyIdDTO2.setBusApplId("2");
        appl2.setApplId(applIdKeyIdDTO2);
        appl2.setPersons(pers);
        appl2.setApplStatId(active ? "GNV" : "STR");
        appl2.setHash("hash");
        appl2.setApplDate(OffsetDateTime.now());
        appl2.setApplDateTo(OffsetDateTime.now());
        appls.add(appl2);

        application.setMortgageAppls(appls);
        List<MortApplVariantDTO> vars = new LinkedList<>();
        MortApplVariantDTO var = new MortApplVariantDTO();
        vars.add(var);
        appl.setApplVariants(vars);
        return application;
    }

    @BeforeEach
    public void setup() throws ServiceException {
        repositoryService().createDeployment()
                .addClasspathResource("processes/hypo/mortgage.bpmn")
                .addClasspathResource("processes/hypo/mortgage-guar-offer.bpmn")
                .addClasspathResource("processes/hypo/mortgage-application-check.bpmn")
                .addClasspathResource("processes/hypo/mortgage-income-verification.bpmn")
                .addClasspathResource("processes/loan/solve_applicant.bpmn")
                .addClasspathResource("processes/hypo/mortgage-storno.bpmn")
                .addClasspathResource("processes/hypo/mortgage-finalization.bpmn")
                .addClasspathResource("processes/hypo/mortgage-rollover.bpmn")
                .addClasspathResource("processes/hypo/mortgage-appraisal.bpmn")
                .deploy();

        when(externalTaskMock.getAllVariables()).thenReturn(getStartProcessVariables());
        when(externalTaskMock.getVariable(UpdateApplicationStatus.APPL_ID)).thenReturn("1");
        when(externalTaskMock.getVariable(UpdateApplicationStatus.PROCESS_STATE)).thenReturn("PZSTAP");
        when(externalTaskMock.getVariable("processTp")).thenReturn(LovProcessTp.ADD_MORTGAGE.getCode());
        when(externalTaskMock.getId()).thenReturn("1");
        when(externalTaskMock.getTopicName()).thenReturn("Topic");
        when(partyService.getDetail(any(), any())).thenReturn(getGetDetailResultDto());
        when(approvalService.callMortgageApproval(any(), any(), any(), any())).thenReturn(LovDsnTp.APPROVE);
        when(operationService.checkApplUserRights(any(), any(), any(), anyString(), any())).thenReturn(Boolean.TRUE);
        when(applicationService.getMortgageAppl(any(), any(), any(), any())).thenReturn(getMtgApplication(true));
    }

    protected <T extends AbstractWorker> void executeWorker(T worker) {
        try {
            worker.execute(externalTaskMock, testExternalTaskService);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    protected void executeWorker(String activityId, ProcessInstance processInstance) throws ServiceException {
        String topicName = getTopicNameFromActivity(activityId, processInstance);
        try {
            for (BeanDefinition bd : beanDefs) {
                if (bd instanceof AnnotatedBeanDefinition) {
                    Map<String, Object> annotAttributeMap = ((AnnotatedBeanDefinition) bd)
                            .getMetadata()
                            .getAnnotationAttributes(ExternalTaskSubscription.class.getCanonicalName());
                    String currentTopicName = annotAttributeMap.get("topicName").toString();
                    assert topicName != null;
                    if (topicName.equals(currentTopicName)) {
                        Constructor<?>[] ctors = Class.forName(bd.getBeanClassName()).getConstructors();
                        for (Constructor<?> ctor : ctors) {
                            Class<?>[] paramTypes = ctor.getParameterTypes();
                            Object[] params = new Object[paramTypes.length];
                            int paramCounter = 0;
                            for (Class<?> paramType : paramTypes) {
                                if (paramType.getCanonicalName().equals(ApprovalService.class.getCanonicalName())) {
                                    params[paramCounter++] = approvalService;
                                } else if (paramType.getCanonicalName().equals(PartyApplicationService.class.getCanonicalName())) {
                                    params[paramCounter++] = partyApplicationService;
                                } else if (paramType.getCanonicalName().equals(ApplicationService.class.getCanonicalName())) {
                                    params[paramCounter++] = applicationService;
                                } else if (paramType.getCanonicalName().equals(OperationService.class.getCanonicalName())) {
                                    params[paramCounter++] = operationService;
                                } else if (paramType.getCanonicalName().equals(CashloanApplicationService.class.getCanonicalName())) {
                                    params[paramCounter++] = cashloanApplicationService;
                                } else if (paramType.getCanonicalName().equals(CusActivityService.class.getCanonicalName())) {
                                    params[paramCounter++] = cusActivityService;
                                }
                            }
                            executeWorker((AbstractWorker) ctor.newInstance(params));
                        }
                    }
                }
            }
            log.info("activityId {}", activityId);
            log.info("processInstance {} {}", processInstance.getProcessDefinitionId(), processInstance.getId());
            org.camunda.bpm.engine.externaltask.ExternalTask externalTask = externalTask(activityId, processInstance);
            Map<String, Object> savedVariables = testExternalTaskService.getSavedVariables();
            log.info("savedVariables {}", savedVariables.toString());
            complete(externalTask, savedVariables);
        } catch (ClassNotFoundException | InstantiationException | IllegalAccessException | IllegalArgumentException |
                 InvocationTargetException ex) {
            log.error(ex.getMessage(), ex);
            throw new ServiceException(ex.getMessage(), ex);
        }
    }

    private String getTopicNameFromActivity(String activityId, ProcessInstance processInstance) {
        if (!activitiesForProcessInstance.containsKey(processInstance.getId())) {
            ProcessDefinition processDefinition = repositoryService().getProcessDefinition(processInstance.getProcessDefinitionId());
            InputStream resourceStream = repositoryService().getResourceAsStream(processDefinition.getDeploymentId(), processDefinition.getResourceName());

            BpmnModelInstance modelInstance = Bpmn.readModelFromStream(resourceStream);
            activitiesForProcessInstance.put(processInstance.getId(), modelInstance.getModelElementsByType(Activity.class));
        }
        for (Activity activity : activitiesForProcessInstance.get(processInstance.getId())) {
            if (activityId != null && activityId.equals(activity.getAttributeValue("id"))) {
                return activity.getAttributeValueNs("http://camunda.org/schema/1.0/bpmn", "topic");
            }
        }
        return null;
    }

    private NewPersonDTO getNewPersonDTO() {
        NewPersonDTO dto = new NewPersonDTO();
        dto.setPep(false);
        return dto;
    }

    private GetDetailResultDto getGetDetailResultDto() {
        GetDetailResultDto dto = new GetDetailResultDto();
        dto.setContacts(new ContactsDto());
        dto.setPerson(new PersonDto());
        return dto;
    }
    private DocumentsDtoList getDocumentsDtoList() {
        return new DocumentsDtoList();
    }


    protected Map<String, Object> getStartProcessVariables() {
        final Map<String, Object> variables = new HashMap<>();
        variables.put("applId", "1");
        variables.put(ProcessVariables.APPL_KEY, 11L);
        variables.put(ProcessVariables.BUS_APPL_ID, "1");
        variables.put(ProcessVariables.APPLICATION_STATE, "PZSTAP");
        variables.put(ProcessVariables.PROCESS_TP, LovProcessTp.ADD_MORTGAGE.getCode());
        variables.put(ProcessVariable.NEED_EDIT_SBL_PARTY.getKey(), false);
        variables.put(ProcessVariable.NEED_UPLOAD_CONTACTS.getKey(), false);
        variables.put(ProcessVariable.NEED_EDIT_ADB_PARTY.getKey(), false);
        variables.put(ProcessVariables.IS_FIRST_OFFER, true);
        variables.put(ProcessVariables.MAIN_APPL_EXPIRY_DATE, "PT99S");
        variables.put(ProcessVariables.SUB_APPL_EXPIRY_DATE, "PT99S");
        variables.put(ProcessVariables.IS_FAKE_CLIENT, false);
        variables.put(ProcessVariables.GUAR_OFFER_EMAIL_SENT, false);
        variables.put(ProcessVariables.CHECK_APPL_USER_RIGHTS_RESULT, false);
        variables.put(ProcessVariable.LOAN_APPLICATION_OWNER.getKey(), getNewPersonDTO());
        variables.put(ProcessVariable.GET_DETAIL_RESULT.getKey(), getGetDetailResultDto());
        variables.put(ProcessVariable.GET_PARTY_DOCUMENTS_RESULT.getKey(), getDocumentsDtoList());
        variables.put(ProcessVariables.CORRELATION_ID, "e0f4db7f-fe29-46d8-8595-4b8fa289e0ff");
        return variables;
    }
}


