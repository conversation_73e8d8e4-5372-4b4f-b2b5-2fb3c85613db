package cz.equa.camapp.processes.hypo;

import cz.equa.camapp.config.ProcessConfiguration;
import cz.equa.camapp.mtg.ProcessVariables;
import cz.equa.camapp.service.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.engine.runtime.ProcessInstance;
import org.camunda.bpm.engine.test.junit5.ProcessEngineExtension;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.test.context.ContextConfiguration;

import java.util.HashMap;
import java.util.Map;

import static cz.equa.camapp.process.Message.*;
import static org.camunda.bpm.engine.test.assertions.bpmn.BpmnAwareTests.*;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ContextConfiguration(classes = {ProcessConfiguration.class})
@ExtendWith({ProcessEngineExtension.class})
@Slf4j
public class IncomeVerificationTest extends MortgageProcessTestAbstract {

    @Test
    @Disabled
    public void mainStornoPath() throws ServiceException {
        final Map<String, Object> variables = new HashMap<>();
        variables.put("applId", "1");
        variables.put(ProcessVariables.APPL_KEY, 11L);
        variables.put(ProcessVariables.BUS_APPL_ID, "1");
        variables.put(ProcessVariables.BUS_SUB_APPL_ID, "11");
        variables.put(ProcessVariables.APPLICATION_STATE, "ZDZAM");
        variables.put(ProcessVariables.REJECTION_STATE, "ZDZAM");
        variables.put(ProcessVariables.CORRELATION_ID, "correlationId");

        ProcessInstance processInstance = runtimeService().startProcessInstanceByKey("MortgageIncomeVerification",
                BUSINESS_KEY, variables);
        assertThat(processInstance).isStarted()
                .hasPassed("Event_startZDOP")
                .isNotEnded();
        assertThat(processInstance).isActive().isWaitingAt("Activity_mortgageApplState_OP", "Activity_subApplState_OP");
        executeWorker("Activity_subApplState_OP", processInstance);
        executeWorker("Activity_mortgageApplState_OP", processInstance);

        runtimeService().correlateMessage(RML_INCOME_VERIFICATION_METHOD_SELECTED.getValue(), processInstance.getBusinessKey());

        assertThat(processInstance).isActive().isWaitingAt("Activity_mortgageApplState_OP1", "Activity_subApplState_OP1");
        executeWorker("Activity_subApplState_OP1", processInstance);
        executeWorker("Activity_mortgageApplState_OP1", processInstance);

        assertThat(processInstance).isActive().isWaitingAt("Activity_createManualTask");
        executeWorker("Activity_createManualTask", processInstance);

        runtimeService().correlateMessage(RML_MANUAL_APPROVING_STORNO.getValue(), processInstance.getBusinessKey());

        assertThat(processInstance).hasPassed("Event_call_STR");
        assertThat(processInstance).isEnded();
        assertTrue(processInstanceQuery().list().isEmpty());
    }

    @Test
    @Disabled
    public void mainSuccessfulPath() throws ServiceException {
        final Map<String, Object> variables = new HashMap<>();
        variables.put("applId", "1");
        variables.put(ProcessVariables.APPL_KEY, 11L);
        variables.put(ProcessVariables.BUS_APPL_ID, "1");
        variables.put(ProcessVariables.BUS_SUB_APPL_ID, "11");
        variables.put(ProcessVariables.APPLICATION_STATE, "ZDZAM");
        variables.put(ProcessVariables.REJECTION_STATE, "ZDZAM");
        variables.put(ProcessVariables.CORRELATION_ID, "correlationId");
        variables.put(ProcessVariables.CALL_APPROVAL_RESULT, "APPROVE");

        ProcessInstance processInstance = runtimeService().startProcessInstanceByKey("MortgageIncomeVerification",
                BUSINESS_KEY, variables);
        assertThat(processInstance).isStarted()
                .hasPassed("Event_startZDOP")
                .isNotEnded();
        assertThat(processInstance).isActive().isWaitingAt("Activity_mortgageApplState_OP", "Activity_subApplState_OP");
        executeWorker("Activity_subApplState_OP", processInstance);
        executeWorker("Activity_mortgageApplState_OP", processInstance);

        runtimeService().correlateMessage(RML_INCOME_VERIFICATION_METHOD_SELECTED.getValue(), processInstance.getBusinessKey());

        assertThat(processInstance).isActive().isWaitingAt("Activity_mortgageApplState_OP1", "Activity_subApplState_OP1");
        executeWorker("Activity_subApplState_OP1", processInstance);
        executeWorker("Activity_mortgageApplState_OP1", processInstance);

        assertThat(processInstance).isActive().isWaitingAt("Activity_createManualTask");
        executeWorker("Activity_createManualTask", processInstance);

        runtimeService().correlateMessage(RML_MANUAL_APPROVING_DONE.getValue(), processInstance.getBusinessKey());

        assertThat(processInstance).isActive().isWaitingAt("Activity_mortgageApplState_ZDPDP", "Activity_subApplState_ZDPDP");
        executeWorker("Activity_subApplState_ZDPDP", processInstance);
        executeWorker("Activity_mortgageApplState_ZDPDP", processInstance);

        assertThat(processInstance).isActive().isWaitingAt("Activity_callMortgageApproval");
        executeWorker("Activity_callMortgageApproval", processInstance);

        assertThat(processInstance).hasPassed("Event_callEZSO");
        assertThat(processInstance).isEnded();
    }

}
