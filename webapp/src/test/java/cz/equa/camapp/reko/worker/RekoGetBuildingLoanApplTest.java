package cz.equa.camapp.reko.worker;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import cz.equa.camapp.rest.service.ApplicationService;
import cz.equa.camapp.service.LocalDateCombinedSerializer;
import cz.equa.camapp.service.ServiceException;
import cz.equa.camapp.service.ZonedDateTimeCombinedSerializer;
import cz.equa.camapp.utils.ApplicationUtils;
import cz.rb.las.application.handler.RFC3339DateFormat;
import cz.rb.las.application.model.CtGetBuildingLoanAppl;
import cz.rb.las.application.model.GetBuildingLoanApplResponse;
import org.camunda.bpm.client.task.ExternalTask;
import org.camunda.bpm.client.task.ExternalTaskService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.openapitools.jackson.nullable.JsonNullableModule;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit.jupiter.web.SpringJUnitWebConfig;

import java.text.DateFormat;
import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.Map;
import java.util.TimeZone;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@SpringJUnitWebConfig(classes = {RekoGetBuildingLoanAppl.class})
class RekoGetBuildingLoanApplTest {

    @MockBean
    private ApplicationService applicationService;

    @Mock
    private ExternalTask externalTask;
    @Mock
    private ExternalTaskService externalTaskService;

    private RekoGetBuildingLoanAppl underTest;

    private Map<String, Object> getProcessVariables() throws JsonProcessingException {
        String jsonString = """
                {
                    "busApplId": "1",
                    "applKey": 1,
                    "isIncome": "1",
                    "isObligation": "1",
                    "isOwner": "1",
                    "requestedVariants": "NONE",
                    "X-Correlation-Id": "1"
                }""";
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        return objectMapper.readValue(jsonString, new TypeReference<>() {
        });
    }

    @BeforeEach
    public void setUp() throws ServiceException {
        underTest = new RekoGetBuildingLoanAppl(applicationService);
    }

    @Test
    void executeWorker() throws JsonProcessingException, ServiceException {
        GetBuildingLoanApplResponse buildingLoanApplResponse = new GetBuildingLoanApplResponse();
        buildingLoanApplResponse.setBuildingLoanAppl(new CtGetBuildingLoanAppl());

        when(applicationService.getBuildingLoanAppl(anyLong(), any(), anyBoolean(), anyBoolean(), any(), anyBoolean(), any(), any()))
                .thenReturn(buildingLoanApplResponse);
        doNothing().when(externalTaskService).complete(any(), any());
        when(externalTask.getAllVariables()).thenReturn(getProcessVariables());

        RekoGetBuildingLoanAppl underTest = new RekoGetBuildingLoanAppl(applicationService);
        assertDoesNotThrow(() -> underTest.execute(externalTask, externalTaskService));

        verify(applicationService, times(1)).getBuildingLoanAppl(anyLong(), any(), anyBoolean(), anyBoolean(), any(), anyBoolean(), any(), any());
    }

    @Test
    void convertJsonToObjectTest() {
        String json = """
                {
                   "applId":{
                      "applKey":853599101427,
                      "busApplId":"2482001127"
                   },
                   "persons":[
                      {
                         "siebelId":"3505145",
                         "genderId":null,
                         "eduStatId":null,
                         "housingStatId":null,
                         "citizenship":null,
                         "marStatId":null,
                         "rcNum":null,
                         "salutId":null,
                         "titleBefore":null,
                         "firstName":null,
                         "familyName":null,
                         "birthPlace":null,
                         "birthName":null,
                         "birthDate":null,
                         "birthCntryId":null,
                         "taxDomicile":"CZ",
                         "tin":null,
                         "pep":false,
                         "mortExpSum":9999,
                         "householdExpSum":9999,
                         "othExpSum":9999,
                         "mntdChildCnt":null,
                         "crntAddrValidDate":null,
                         "email":null,
                         "phoneNum":"+420776604774",
                         "permAddr":null,
                         "postalAddr":null,
                         "idCards":[
                           \s
                         ],
                         "adbInstPtKey":907185,
                         "ptTpId":"FO",
                         "cbId":null,
                         "verifications":null,
                         "incomeVerifications":null,
                         "ptCorpDetail":{
                            "ptAcntgTpId":null,
                            "entpInc":null,
                            "taxYear":null,
                            "dtiAmt":null,
                            "dprAmt":null,
                            "taxFlatRx":null,
                            "equityAmt":null,
                            "prftLossAmt":null,
                            "prevRvnAmt":null,
                            "rvnAmt":null,
                            "ast":null
                         },
                         "busName":null,
                         "applPtRoleTpId":"APPLICANT",
                         "rstsPartyId":"2720678",
                         "rstsPseudoBirthCode":null,
                         "residencyTpId":null,
                         "residencyFrom":null,
                         "residencyTo":null,
                         "numHouseholdPerson":null,
                         "numSupportedChildren":null,
                         "idCardsReq":null,
                         "segments":[
                           \s
                         ]
                      }
                   ],
                   "incomes":null,
                   "buildingLoanAppl":{
                      "applTpId":null,
                      "applStatId":"PZSTAP",
                      "applDate":"2024-09-26T00:00:00+02:00",
                      "posId":"18000000",
                      "firstTouchPoint":"18000000",
                      "applReason":null,
                      "hash":"ECEEPf62SsQs6XYPI9zWx0NJGYIOhoQe7/4M/9SaYorte8Q2FfM9mt6xv3lwKUk/Y2HdVkG+uXSoEH0EOhForWuxpjGjRkjaU1Sn1EiBUUCWCpf1C54uSj7x30h63zc55rOEYJUB86uHxeijtz03d9PtpN4oQa1xwmXD0n0J4wo=",
                      "preApprOfrId":null,
                      "opportunityId":"46101908",
                      "ccyId":"CZK",
                      "distCnlId":"18",
                      "fulfillmentCnl":null,
                      "wrkPlaceId":"89c2d5f3-3dd3-4c60-ba45-adafe263137b",
                      "busProdSubTp":"BSL_REA",
                      "browserInfo":null,
                      "telcoQueryAllowedFlag":false,
                      "authMobSearchFlag":null,
                      "firstTouchPointOwnr":"18000000",
                      "promoCode":null,
                      "applDsnKey":null,
                      "applComplPosId":null,
                      "contrNum":null,
                      "contrNumBs":null,
                      "contrNumReg":null,
                      "contrNumCr":null,
                      "contrSignDate":null,
                      "contrSignPosId":null,
                      "contrSignAdvisorId":null,
                      "contrSignAdvisorName":null,
                      "lastChangeAdvisorId":"18000000",
                      "validFromDate":"2024-09-26T00:00:00+02:00",
                      "validToDate":null,
                      "moneyTransferDate":null,
                      "rejectRsnId":null,
                      "rejectRsn":null,
                      "rejectRsnTp":null,
                      "personApplModelId":null,
                      "computedSalary":null,
                      "incVerifPSD2Flag":null,
                      "incVerifAccStmFlag":null,
                      "incVerifStmUploadFlag":null,
                      "incVerifCallFlag":null,
                      "incVerifSrcId":null,
                      "incVerifPSD2Discount":null,
                      "contrCond":null,
                      "registryResult":null,
                      "finalRiskClass":null,
                      "consents":[
                        \s
                      ]
                   },
                   "applVariants":[
                      {
                         "applVariantKey":2214874123857,
                         "applVariantTpId":"REQ",
                         "applVariantSignCnlFlag":null,
                         "busProdTpId":"BSL",
                         "busProdSubTpId":"BSL_REA",
                         "finaAmt":700000,
                         "instlAmt":5070,
                         "instlCnt":240,
                         "maturityDate":"2044-09-26",
                         "intrsRx":0.0589,
                         "rpsn":0.0606,
                         "camCode":null,
                         "minAvlblAmt":null,
                         "minInstlAmt":null,
                         "minInstlCnt":null,
                         "maxAvlblAmt":null,
                         "maxInstlAmt":null,
                         "maxInstlCnt":null,
                         "upsellAmt":100000,
                         "maxUpsellAmt":null,
                         "ccyId":"CZK",
                         "totRpmtAmt":1216800,
                         "refiSavedAmt":null,
                         "accNumPrefix":null,
                         "accNum":null,
                         "accBankCode":null,
                         "actSumInstlAmt":null,
                         "consLoanInstlAmt":null,
                         "dayIntrsAmt":null,
                         "intrsDscntFlag":null,
                         "intnOblgtnAmt":null,
                         "extnOblgtnAmt":null,
                         "repreDrawDate":null,
                         "repreMaturityDate":null,
                         "repreInt":null,
                         "repreInt3M":null,
                         "costs3M":null,
                         "delFlag":null,
                         "declaredPurpose":null,
                         "maxExtOblgtnAmt":null,
                         "minIrForFinalization":null,
                         "origScOfrIntrsRx":0.0589,
                         "payCpcyForFinal":null,
                         "maxAvlblTopup":null,
                         "prodIntrsCodeId":null,
                         "loanPurposes":[
                           \s
                         ],
                         "loanCategories":[
                            {
                               "purpCatgId":"REFI",
                               "primFlag":true
                            }
                         ],
                         "variantParameters":[
                           \s
                         ],
                         "signChannels":null,
                         "insurances":[
                           \s
                         ],
                         "fixPeriod":"5",
                         "closeRelCoFin":null
                      }
                   ],
                   "applObligations":null,
                   "obligationSignChannels":null,
                   "applMetadata":null,
                   "docs":null
                }""";
        try {
            GetBuildingLoanApplResponse dto = ApplicationUtils.convertJsonToType(json, GetBuildingLoanApplResponse.class);
        } catch (JsonProcessingException e) {
            System.out.println(e.getMessage());
        }
    }
}