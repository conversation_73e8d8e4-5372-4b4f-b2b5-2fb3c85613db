package cz.equa.camapp.reko.worker;

import cz.equa.camapp.reko.ProcessVariables;
import cz.equa.camapp.rest.service.ApplicationService;
import cz.equa.camapp.rest.service.PartyService;
import cz.equa.camapp.service.ServiceException;
import org.camunda.bpm.client.task.ExternalTask;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AssignCategoryTest {

    @Mock
    private ApplicationService applicationService;

    @Mock
    private PartyService partyService;

    @Mock
    private ExternalTask externalTask;

    @InjectMocks
    private AssignCategory assignCategory;

    private ProcessVariables pv;
    private static final String CLIENT_ID = "12345";
    private static final String CORRELATION_ID = "corr-123";
    private static final String VALID_TYPE = "VALID_TYPE";
    private static final String VALID_CATEGORIZATION = "VALID_CATEGORIZATION";
    private static final String VALID_FROM = "VALID_FROM";

    @BeforeEach
    void setUp() {
        pv = new ProcessVariables();
        pv.setClientId(CLIENT_ID);
        pv.setCorrelationId(CORRELATION_ID);

        ReflectionTestUtils.setField(assignCategory, "externalTask", externalTask);
        ReflectionTestUtils.setField(assignCategory, "pv", pv);
    }

    @Test
    void executeWorker_Success() throws ServiceException {
        Map<String, Object> pvMap = new HashMap<>();
        pvMap.put(ProcessVariables.CLIENT_ID, CLIENT_ID);
        pvMap.put(ProcessVariables.CORRELATION_ID, CORRELATION_ID);

        when(externalTask.getVariable(AssignCategory.IN_TYPE)).thenReturn(VALID_TYPE);
        when(externalTask.getVariable(AssignCategory.IN_CATEGORIZATION)).thenReturn(VALID_CATEGORIZATION);
        when(externalTask.getVariable(AssignCategory.IN_VALID_FROM)).thenReturn(VALID_FROM);
        when(externalTask.getAllVariables()).thenReturn(pvMap);

        assignCategory.executeWorker();

        verify(partyService).assignCategory(eq(CLIENT_ID), argThat(category -> {
            assertEquals(VALID_TYPE, category.getType());
            assertEquals(VALID_CATEGORIZATION, category.getCategorization());
            assertEquals(LocalDate.now(), category.getValidFrom());
            return true;
        }), eq(CORRELATION_ID));
    }

    @Test
    void executeWorker_NullType_ThrowsException() throws ServiceException {
        when(externalTask.getVariable(AssignCategory.IN_TYPE)).thenReturn(null);
        when(externalTask.getVariable(AssignCategory.IN_CATEGORIZATION)).thenReturn(VALID_CATEGORIZATION);

        ServiceException exception = assertThrows(ServiceException.class,
                () -> assignCategory.executeWorker());
        assertEquals("Type and/or categorization is not defined or its value is empty", exception.getMessage());
        verify(partyService, never()).assignCategory(any(), any(), any());
    }

    @Test
    void executeWorker_EmptyType_ThrowsException() throws ServiceException {
        when(externalTask.getVariable(AssignCategory.IN_TYPE)).thenReturn("");
        when(externalTask.getVariable(AssignCategory.IN_CATEGORIZATION)).thenReturn(VALID_CATEGORIZATION);

        ServiceException exception = assertThrows(ServiceException.class,
                () -> assignCategory.executeWorker());
        assertEquals("Type and/or categorization is not defined or its value is empty", exception.getMessage());
        verify(partyService, never()).assignCategory(any(), any(), any());
    }

    @Test
    void executeWorker_NullCategorization_ThrowsException() throws ServiceException {
        when(externalTask.getVariable(AssignCategory.IN_TYPE)).thenReturn(VALID_TYPE);
        when(externalTask.getVariable(AssignCategory.IN_CATEGORIZATION)).thenReturn(null);

        ServiceException exception = assertThrows(ServiceException.class,
                () -> assignCategory.executeWorker());
        assertEquals("Type and/or categorization is not defined or its value is empty", exception.getMessage());
        verify(partyService, never()).assignCategory(any(), any(), any());
    }

    @Test
    void executeWorker_EmptyCategorization_ThrowsException() throws ServiceException {
        when(externalTask.getVariable(AssignCategory.IN_TYPE)).thenReturn(VALID_TYPE);
        when(externalTask.getVariable(AssignCategory.IN_CATEGORIZATION)).thenReturn("  ");

        ServiceException exception = assertThrows(ServiceException.class,
                () -> assignCategory.executeWorker());
        assertEquals("Type and/or categorization is not defined or its value is empty", exception.getMessage());
        verify(partyService, never()).assignCategory(any(), any(), any());
    }

    @Test
    void executeWorker_PartyServiceThrowsException_PropagatesException() throws ServiceException {
        when(externalTask.getVariable(AssignCategory.IN_TYPE)).thenReturn(VALID_TYPE);
        when(externalTask.getVariable(AssignCategory.IN_CATEGORIZATION)).thenReturn(VALID_CATEGORIZATION);
        doThrow(new ServiceException("Service error")).when(partyService)
                .assignCategory(any(), any(), any());

        ServiceException exception = assertThrows(ServiceException.class,
                () -> assignCategory.executeWorker());
        assertEquals("Service error", exception.getMessage());
    }
}