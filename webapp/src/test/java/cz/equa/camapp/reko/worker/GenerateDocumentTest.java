package cz.equa.camapp.reko.worker;

import com.fasterxml.jackson.core.JsonProcessingException;
import cz.equa.camapp.lovs.*;
import cz.equa.camapp.manager.BslManager;
import cz.equa.camapp.manager.LovManager;
import cz.equa.camapp.model.document.GetDocumentListDto;
import cz.equa.camapp.reko.ProcessVariables;
import cz.equa.camapp.rest.model.DocumentType;
import cz.equa.camapp.rest.model.RekoCalculationResponseDTO;
import cz.equa.camapp.rest.service.ApplicationService;
import cz.equa.camapp.rest.service.DmsService;
import cz.equa.camapp.rest.service.ParametrizationService;
import cz.equa.camapp.service.ServiceException;
import cz.equa.camapp.service.applicationservice.model.BuildingApplVariantsDTO;
import cz.equa.camapp.service.applicationservice.model.BuildingLoanApplicationDTO;
import cz.equa.camapp.service.applicationservice.model.BuildingLoanApplicationOwnerDTO;
import cz.equa.camapp.service.applicationservice.model.PtIncomeRstsListDTO;
import cz.equa.camapp.utils.ApplicationUtils;
import cz.equa.camapp.utils.ValidatorXsdUtils;
import cz.rb.cdm.dds.crm.cpa.p0001540._1.********;
import cz.rb.cdm.dds.crm.cpa.p0001541._1.P0001541;
import cz.rb.cdm.dds.crm.cpa.p0001543._1.P0001543;
import cz.rb.cdm.dds.crm.cpa.x0000045._1.********;
import cz.rb.cdm.dds.crm.cpa.x0000046._1.X0000046;
import cz.rb.las.parametrization.model.BSLProductType;
import cz.rb.las.parametrization.model.BSLSubtypeParametrization;
import cz.rb.las.parametrization.model.BSLTypeParametrization;
import jakarta.xml.bind.annotation.XmlElement;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit.jupiter.web.SpringJUnitWebConfig;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@Slf4j
@SpringJUnitWebConfig(classes = {GenerateDocument.class})
class GenerateDocumentTest {

    @MockBean
    DmsService dmsService;
    @MockBean
    LovManager lovManager;
    @MockBean
    BslManager bslManager;
    @MockBean
    ApplicationService applicationService;

    @Test
    void testCreateRequest() throws ServiceException, JsonProcessingException {
        var pv = getProcessVariables();
        GenerateDocument underTest = new GenerateDocument(applicationService, dmsService, lovManager, bslManager);
        Object result = underTest.prepareData(DocumentType.********, pv);

        assertInstanceOf(********.class, result);
        ******** x0000045 = (********) result;
        assertNotNull(x0000045.getDeliveryData());
        assertEquals("Rekopůjčku", x0000045.getDeliveryData().getProductType());
        assertEquals("<EMAIL>", x0000045.getDeliveryData().getReceiver());
    }

    @Test
    void testCreateRequest_X0000046() throws ServiceException, JsonProcessingException {
        var pv = getProcessVariables();
        GenerateDocument underTest = new GenerateDocument(applicationService, dmsService, lovManager, bslManager);
        String reaAddress = "http://mock-rea.com";
        String draAddress = "http://mock-dra.com";

        ReflectionTestUtils.setField(underTest, "rekoReaFinalizationUrl", reaAddress);
        ReflectionTestUtils.setField(underTest, "rekoDraFinalizationUrl", draAddress);
        Object result = underTest.prepareData(DocumentType.X0000046, pv);

        assertInstanceOf(X0000046.class, result);
        X0000046 x0000046 = (X0000046) result;
        String accessKey = x0000046.getAccessKey();
        String encodedAccessKey = URLEncoder.encode(accessKey, StandardCharsets.UTF_8);

        assertTrue(x0000046.getApplUrl().contains(encodedAccessKey), "URL doesn't contains accessKey");
        assertTrue(x0000046.getApplUrl().contains(reaAddress), "URL doesn't contains correct host address");
    }

    @Test
    void testCreateRequest_P0001541() throws ServiceException, JsonProcessingException {
        var pv = getProcessVariables();

        when(lovManager.getLov(any(LovCntry.class), any())).thenReturn(LovCntry.CZ);

        LovCsaVopTarif lovCsaVopTarif = new LovCsaVopTarif();
        lovCsaVopTarif.addDetail("1", "1");
        lovCsaVopTarif.addDetail("TEXT", "abc");
        lovCsaVopTarif.addDetail("MINZUSTPRIDEL", "4");
        lovCsaVopTarif.addDetail("MINHODNOCENI", "3");
        lovCsaVopTarif.addDetail("KUZ", "2");
        when(lovManager.getLovByDetail(eq(LovCsaVopTarif.class), anyString(), anyString(), any(LovLang.class))).thenReturn(lovCsaVopTarif);

        var lovCsaParamCastar = new LovCsaParamCastar();
        lovCsaParamCastar.addDetail("ODCHYLKAURS", "10");
        lovCsaParamCastar.addDetail("ODCHYLKAURU", "5");
        when(lovManager.getLovByDetail(eq(LovCsaParamCastar.class), anyString(), anyString(), any())).thenReturn(lovCsaParamCastar);

        //execute
        GenerateDocument underTest = new GenerateDocument(applicationService, dmsService, lovManager, getBslManagerMock());
        ReflectionTestUtils.setField(underTest, "pv", pv);

        Object result = underTest.prepareData(DocumentType.P0001541, pv);

        assertInstanceOf(P0001541.class, result);
        P0001541 p0001541 = (P0001541) result;

        assertNotNull(p0001541);
        assertEquals("Veronika", p0001541.getParty().getFirstName());
    }

    @Test
    void testCreateRequest_********() throws ServiceException, JsonProcessingException {
        var pv = getProcessVariablesP0001543();
        when(lovManager.getLov(any(LovCntry.class), any())).thenReturn(LovCntry.CZ);
        var idCardTp = mock(LovIdCardTp.class);
        when(idCardTp.getLabel()).thenReturn("OP");
        when(lovManager.getLov(any(LovIdCardTp.class), any())).thenReturn(idCardTp);
        var gender = mock(LovGender.class);
        when(gender.getLabel()).thenReturn("velke");
        when(lovManager.getLov(any(LovGender.class), any())).thenReturn(gender);

        var lovOccCatg = mock(LovOccCatg.class);
        when(lovOccCatg.getLabel()).thenReturn("lovOccCatg");
//        when(lovManager.getLov(any(LovOccCatg.class), any())).thenReturn(lovOccCatg);
        when(lovManager.getTranslatedLovBySystem(any(), any(), any(), any(), any())).thenReturn(lovOccCatg);
        when(dmsService.getDocumentListByApplicationId(any(), any())).thenReturn(getDMSDocumentsList());
        LovCsaVopTarif lovCsaVopTarif = new LovCsaVopTarif();
        lovCsaVopTarif.addDetail("1", "1");
        lovCsaVopTarif.addDetail("TEXT", "abc");
        lovCsaVopTarif.addDetail("MINZUSTPRIDEL", "4");
        lovCsaVopTarif.addDetail("MINHODNOCENI", "3");
        lovCsaVopTarif.addDetail("KUZ", "2");
        when(lovManager.getLovByDetail(eq(LovCsaVopTarif.class), anyString(), anyString(), any(LovLang.class))).thenReturn(lovCsaVopTarif);

        var lovCsaParamCastar = new LovCsaParamCastar();
        lovCsaParamCastar.addDetail("ODCHYLKAURS", "10");
        lovCsaParamCastar.addDetail("ODCHYLKAURU", "5");
        when(lovManager.getLovByDetail(eq(LovCsaParamCastar.class), anyString(), anyString(), any())).thenReturn(lovCsaParamCastar);
        var lovCatgLoanPurp = mock(LovCatgLoanPurp.class);
        when(lovManager.getLov(any(LovCatgLoanPurp.class), any())).thenReturn(lovCatgLoanPurp);
        when(lovCatgLoanPurp.getDescr()).thenReturn("description");
        var lovLoanPurp = mock(LovLoanPurp.class);
        when(lovManager.getLov(any(LovLoanPurp.class), any())).thenReturn(lovLoanPurp);
        when(lovLoanPurp.getDescr()).thenReturn("description");

        //execute
        GenerateDocument underTest = new GenerateDocument(applicationService, dmsService, lovManager, getBslManagerMock());
        ReflectionTestUtils.setField(underTest, "pv", pv);

        Object result = underTest.prepareData(DocumentType.********, pv);

        assertInstanceOf(********.class, result);
        ******** p0001540 = (********) result;

        assertNotNull(p0001540);
        assertEquals("Hugo", p0001540.getParty().getFirstName());
    }

    private BslManager getBslManagerMock() throws ServiceException {
        ParametrizationService parametrizationService = mock(ParametrizationService.class);
        BslManager bslManagerMocked = new BslManager(parametrizationService);
        BSLProductType fakeResponse = new BSLProductType();
        List<BSLSubtypeParametrization> subtypes = new LinkedList<>();
        BSLSubtypeParametrization bslSubtypeParametrization = new BSLSubtypeParametrization();
        bslSubtypeParametrization.setBusProdSubTp("BSL_REA");
        bslSubtypeParametrization.setBusProdSubTpId(1L);
        bslSubtypeParametrization.setMonthlyDepositCoef(2.0);
        bslSubtypeParametrization.setSavingsContractFeeCoef(3.0);
        bslSubtypeParametrization.setSavingsContractFeeAmtMax(4);
        bslSubtypeParametrization.setLoanStatementFeeAmt(5);
        bslSubtypeParametrization.setLoanContractFeeCoef(5.0);
        bslSubtypeParametrization.setSavingsMaintenanceFeeAmt(5);
        bslSubtypeParametrization.setSavingsStatementFeeAmt(6);
        bslSubtypeParametrization.setLoanMaintenanceFeeAmt(7);
        bslSubtypeParametrization.setLoanStatementFeeAmt(8);
        bslSubtypeParametrization.setTariff("AAA");
        bslSubtypeParametrization.setInsNum("81");
        subtypes.add(bslSubtypeParametrization);
        fakeResponse.setSubtypes(subtypes);
        BSLTypeParametrization param = new BSLTypeParametrization();
        param.setCommissionAmt(1);
        param.setCommissionCoef(0.1);
        param.setLoanPenaltyCoef(0.2);
        param.setLoanPenaltyMin(10);
        param.setLoanPenaltyMax(20);
        param.setTariffNum(30);
        param.setAnnouncNum(31);
        param.setInfoNum(32);
        fakeResponse.setProdTypeParameters(param);
        when(parametrizationService.getParamsBSL(any(LocalDate.class), anyString(), anyString()))
                .thenReturn(fakeResponse);
        return bslManagerMocked;
    }

    private ProcessVariables getProcessVariables() throws JsonProcessingException {
        var pv = mock(ProcessVariables.class);
        String jsonAppl = """
                {"applTpId":"NEW_PROD","applStatId":"ZDPDP","applDate":1.7303292E+9,"posId":"18000000","firstTouchPoint":"18000000","applReason":null,"hash":"QjM9JPMQDkUr9HSkewo3n0xrIGFm27PtJCAokSLl9etoU4JP+QgQjKhv8GNS/zH/hc7AO0Act8U7//aATWgMXdzoHWqO05vWI3L4Yw+1H9C2RGPB5WREMKO/DhWOfa5geGm1/+Flo9UbGaiVnGSm9nRExctEvqdzNlMGiStCW3I=","preApprOfrId":null,"opportunityId":"46113371","ccyId":"CZK","distCnlId":"18","fulfillmentCnl":null,"wrkPlaceId":"c1d8c94a-d8ea-4404-a050-d1047a9348f6","busProdSubTp":"BSL_REA","browserInfo":null,"telcoQueryAllowedFlag":false,"authMobSearchFlag":null,"firstTouchPointOwnr":"18000000","promoCode":null,"applDsnKey":null,"applComplPosId":null,"rstsLoanNumber":null,"rstsBuildingSavingsNumber":null,"rstsRegistrationLoanNumber":null,"rstsLoanContractNumber":null,"contrSignDate":null,"contrSignPosId":null,"contrSignAdvisorId":null,"contrSignAdvisorName":null,"lastChangeAdvisorId":"18000000","validFromDate":1.7303292E+9,"validToDate":1.7330076E+9,"moneyTransferDate":null,"rejectRsnId":null,"rejectRsn":null,"rejectRsnTp":null,"personApplModelId":null,"computedSalary":null,"incVerifPSD2Flag":null,"incVerifAccStmFlag":null,"incVerifStmUploadFlag":null,"incVerifCallFlag":null,"incVerifSrcId":null,"incVerifPSD2Discount":null,"contrCond":null,"registryResult":null,"finalRiskClass":null,"rstsDataSharingConsent":null,"rbSegmentDataSharingConsent":null,"rbIncomeDataSharingConsent":null,"consentTimestamp":null,"consents":[]}""";
        String jsonOwner = """
                {"siebelId":"27581514","genderId":"F","eduStatId":"F","housingStatId":"COOPF","citizenship":"CZ","marStatId":"S","rcNum":"006025/4018","salutId":null,"titleBefore":null,"firstName":"Veronika","familyName":"Gromská","birthPlace":"Boskovice","birthName":"Gromská","birthDate":[2000,10,25],"birthCntryId":"CZ","taxDomicile":"CZ","tin":null,"pep":false,"mortExpSum":88,"householdExpSum":22,"othExpSum":1,"crntAddrValidDate":[2022,10,31],"email":"<EMAIL>","phoneNum":"+420739169141","permAddr":{"streetName":"Řípov","streetNum":"38","cityName":"Třebíč","detail":null,"zip":"67401","landRegnNum":null,"cntryId":"CZ","addrInstAddrKey":1548871,"addrSince":null},"postalAddr":{"streetName":"Řípov","streetNum":"38","cityName":"Třebíč","detail":null,"zip":"67401","landRegnNum":null,"cntryId":"CZ","addrInstAddrKey":1548872,"addrSince":null},"idCards":[],"adbInstPtKey":910411,"ptTpId":"FO","cbId":null,"verifications":null,"incomeVerifications":null,"ptCorpDetail":{"ptAcntgTpId":null,"entpInc":null,"taxYear":null,"dtiAmt":null,"dprAmt":null,"taxFlatRx":null,"equityAmt":null,"prftLossAmt":null,"prevRvnAmt":null,"rvnAmt":null,"ast":null},"busName":null,"applPtRoleTpId":"APPLICANT","rstsPartyId":"2664859","rstsPseudoBirthCode":null,"typeOfResidency":null,"residencyFrom":null,"residencyTo":null,"numHouseholdPerson":null,"numSupportedChildren":null,"segments":[]}""";
        String jsonVariants = """
                         {
                    "variants": [{
                        "accBankCode": null,
                        "accNum": null,
                        "accNumPrefix": null,
                        "actSumInstlAmt": null,
                        "applVariantKey": *************,
                        "applVariantSignCnlFlag": null,
                        "applVariantTpId": "REQ",
                        "busProdSubTpId": "BSL_REA",
                        "busProdTpId": "BSL",
                        "camCode": null,
                        "ccyId": "CZK",
                        "closeRelCoFin": null,
                        "consLoanInstlAmt": null,
                        "costs3M": null,
                        "dayIntrsAmt": null,
                        "declaredPurpose": null,
                        "delFlag": null,
                        "extnOblgtnAmt": null,
                        "finaAmt": 600000,
                        "fixPeriod": "5",
                        "instlAmt": 4420,
                        "instlCnt": 240,
                        "insurances": [],
                        "intnOblgtnAmt": null,
                        "intrsDscntFlag": null,
                        "intrsRx": 0.0609,
                        "loanCategories": [{
                            "primFlag": true,
                            "purpCatgId": "MODE"
                        }],
                        "loanPurposes": [],
                        "maturityDate": "2044-11-11",
                        "maxAvlblAmt": null,
                        "maxAvlblTopup": null,
                        "maxExtOblgtnAmt": null,
                        "maxInstlAmt": null,
                        "maxInstlCnt": null,
                        "maxUpsellAmt": null,
                        "minAvlblAmt": null,
                        "minInstlAmt": null,
                        "minInstlCnt": null,
                        "minIrForFinalization": null,
                        "origScOfrIntrsRx": 0.0609,
                        "payCpcyForFinal": null,
                        "prodIntrsCodeId": null,
                        "refiSavedAmt": null,
                        "repreDrawDate": null,
                        "repreInt": null,
                        "repreInt3M": null,
                        "repreMaturityDate": null,
                        "rpsn": 0.0627,
                        "signChannels": null,
                        "totRpmtAmt": 1060800,
                        "upsellAmt": 0,
                        "variantParameters": []
                    },
                    {
                        "accBankCode": null,
                        "accNum": null,
                        "accNumPrefix": null,
                        "actSumInstlAmt": null,
                        "applVariantKey": *************,
                        "applVariantSignCnlFlag": null,
                        "applVariantTpId": "APR",
                        "busProdSubTpId": "BSL_REA",
                        "busProdTpId": "BSL",
                        "camCode": null,
                        "ccyId": "CZK",
                        "closeRelCoFin": null,
                        "consLoanInstlAmt": null,
                        "costs3M": null,
                        "dayIntrsAmt": null,
                        "declaredPurpose": null,
                        "delFlag": null,
                        "extnOblgtnAmt": null,
                        "finaAmt": 600000,
                        "fixPeriod": "5",
                        "instlAmt": 4420,
                        "instlCnt": 240,
                        "insurances": [],
                        "intnOblgtnAmt": null,
                        "intrsDscntFlag": null,
                        "intrsRx": 0.0609,
                        "loanCategories": [{
                            "primFlag": true,
                            "purpCatgId": "MODE"
                        }],
                        "loanPurposes": [],
                        "maturityDate": "2044-11-11",
                        "maxAvlblAmt": null,
                        "maxAvlblTopup": null,
                        "maxExtOblgtnAmt": null,
                        "maxInstlAmt": null,
                        "maxInstlCnt": null,
                        "maxUpsellAmt": null,
                        "minAvlblAmt": null,
                        "minInstlAmt": null,
                        "minInstlCnt": null,
                        "minIrForFinalization": null,
                        "origScOfrIntrsRx": 0.0609,
                        "payCpcyForFinal": null,
                        "prodIntrsCodeId": null,
                        "refiSavedAmt": null,
                        "repreDrawDate": null,
                        "repreInt": null,
                        "repreInt3M": null,
                        "repreMaturityDate": null,
                        "rpsn": 0.0627,
                        "signChannels": null,
                        "totRpmtAmt": 1060800,
                        "upsellAmt": 0,
                        "variantParameters": []
                    },
                    {
                        "accBankCode": null,
                        "accNum": null,
                        "accNumPrefix": null,
                        "actSumInstlAmt": null,
                        "applVariantKey": *************,
                        "applVariantSignCnlFlag": null,
                        "applVariantTpId": "SEL",
                        "busProdSubTpId": "BSL_REA",
                        "busProdTpId": "BSL",
                        "camCode": null,
                        "ccyId": "CZK",
                        "closeRelCoFin": null,
                        "consLoanInstlAmt": null,
                        "costs3M": null,
                        "dayIntrsAmt": null,
                        "declaredPurpose": null,
                        "delFlag": null,
                        "extnOblgtnAmt": null,
                        "finaAmt": 600000,
                        "fixPeriod": "5",
                        "instlAmt": 4420,
                        "instlCnt": 240,
                        "insurances": [],
                        "intnOblgtnAmt": null,
                        "intrsDscntFlag": null,
                        "intrsRx": 0.0609,
                        "loanCategories": [{
                            "primFlag": true,
                            "purpCatgId": "MODE"
                        }],
                        "loanPurposes": [],
                        "maturityDate": "2044-11-11",
                        "maxAvlblAmt": null,
                        "maxAvlblTopup": null,
                        "maxExtOblgtnAmt": null,
                        "maxInstlAmt": null,
                        "maxInstlCnt": null,
                        "maxUpsellAmt": null,
                        "minAvlblAmt": null,
                        "minInstlAmt": null,
                        "minInstlCnt": null,
                        "minIrForFinalization": null,
                        "origScOfrIntrsRx": 0.0609,
                        "payCpcyForFinal": null,
                        "prodIntrsCodeId": null,
                        "refiSavedAmt": null,
                        "repreDrawDate": null,
                        "repreInt": null,
                        "repreInt3M": null,
                        "repreMaturityDate": null,
                        "rpsn": 0.0627,
                        "signChannels": null,
                        "totRpmtAmt": 1060800,
                        "upsellAmt": 0,
                        "variantParameters": []
                    }]
                }""";
        var appl = ApplicationUtils.convertJsonToType(jsonAppl, BuildingLoanApplicationDTO.class);
        var owner = ApplicationUtils.convertJsonToType(jsonOwner, BuildingLoanApplicationOwnerDTO.class);
        var variants = ApplicationUtils.convertJsonToType(jsonVariants, BuildingApplVariantsDTO.class);
        RekoCalculationResponseDTO rpsnCalculation = new RekoCalculationResponseDTO();

        when(pv.getLoanApplicationOwner()).thenReturn(owner);
        when(pv.getBuildingLoanApplication()).thenReturn(appl);
        when(pv.getBuildingLoanApplVariants()).thenReturn(variants);
        when(pv.getRekoRpsnCalculations()).thenReturn(rpsnCalculation);
        when(pv.getLanguage()).thenReturn(LovLang.CZE);
        return pv;
    }

    public boolean validateRequiredFields(Object obj) throws IllegalAccessException {
        Class<?> clazz = obj.getClass();
        boolean result = true;
        for (Field field : clazz.getDeclaredFields()) {
            XmlElement xmlElement = field.getAnnotation(jakarta.xml.bind.annotation.XmlElement.class);
            if (xmlElement != null && xmlElement.required()) {
                field.setAccessible(true);
                Object value = field.get(obj);
                if (value == null) {
                    System.out.println("Required field " + field.getName() + " is null");
                    result = false;
                }
            }
        }
        return result;
    }

    @Test
    void prepareTemplateP0001543() throws JsonProcessingException, IllegalAccessException, ServiceException {
        var pv = getProcessVariablesP0001543();
        GenerateDocument underTest = new GenerateDocument(applicationService, dmsService, lovManager, getBslManagerMock());

        P0001543 result = (P0001543) underTest.prepareData(DocumentType.P0001543, pv);
        assertNotNull(result);
        assertTrue(validateRequiredFields(result));
    }

    @Test
    void prepareTemplate********() throws ServiceException, IOException, IllegalAccessException {
        var pv = getProcessVariablesP0001543();
        when(dmsService.getDocumentListByApplicationId(any(), any())).thenReturn(getDMSDocumentsList());
        when(lovManager.getLov(any(LovCntry.class), any())).thenReturn(LovCntry.CZ);
        var idCardTp = mock(LovIdCardTp.class);
        when(idCardTp.getLabel()).thenReturn("OP");
        when(lovManager.getLov(any(LovIdCardTp.class), any())).thenReturn(idCardTp);
        var gender = mock(LovGender.class);
        when(gender.getLabel()).thenReturn("velke");
        when(lovManager.getLov(any(LovGender.class), any())).thenReturn(gender);
        var lovOccCatg = mock(LovOccCatg.class);
        when(lovOccCatg.getLabel()).thenReturn("lovOccCatg");
        when(lovManager.getTranslatedLovBySystem(any(), any(), any(), any(), any())).thenReturn(lovOccCatg);
        var lovCsaVopTarif = mock(LovCsaVopTarif.class);
        @SuppressWarnings("unchecked")
        Map<String, String> mapValues = mock(Map.class);
        when(mapValues.get(anyString())).thenReturn("10");
        when(lovCsaVopTarif.getLovDetail()).thenReturn(mapValues);
        when(lovManager.getLovByDetail(eq(LovCsaVopTarif.class), anyString(), anyString(), any(LovLang.class))).thenReturn(lovCsaVopTarif);
        var lovCsaParamCastar = mock(LovCsaParamCastar.class);
        when(lovManager.getLovByDetail(eq(LovCsaParamCastar.class), anyString(), anyString(), any())).thenReturn(lovCsaParamCastar);
        when(lovCsaParamCastar.getLovDetail()).thenReturn(mapValues);
        var lovCatgLoanPurp = mock(LovCatgLoanPurp.class);
        when(lovManager.getLov(any(LovCatgLoanPurp.class), any())).thenReturn(lovCatgLoanPurp);
        when(lovCatgLoanPurp.getDescr()).thenReturn("description");
        var lovLoanPurp = mock(LovLoanPurp.class);
        when(lovManager.getLov(any(LovLoanPurp.class), any())).thenReturn(lovLoanPurp);
        when(lovLoanPurp.getDescr()).thenReturn("description");
        var lovCurrency = mock(LovCcy.class);
        when(lovManager.getLov(any(LovCcy.class), any())).thenReturn(lovCurrency);
        when(lovCurrency.getCode()).thenReturn("CZK");
        var lovCntry = mock(LovCntry.class);
        when(lovManager.getLov(any(LovCntry.class), any())).thenReturn(lovCntry);
        when(lovCntry.getLabel()).thenReturn("Ceska republika");
        var lovStayTp = mock(LovStayTp.class);
        when(lovManager.getLov(any(LovStayTp.class), any())).thenReturn(lovStayTp);
        when(lovStayTp.getLabel()).thenReturn("Rezidence");

        GenerateDocument underTest = new GenerateDocument(applicationService, dmsService, lovManager, getBslManagerMock());
        Object result = underTest.prepareData(DocumentType.********, pv);
        try {
            ValidatorXsdUtils validatorXsdUtils = new ValidatorXsdUtils();
            validatorXsdUtils.validateByScripturaXsd(result);
        } catch (Exception e) {
            log.warn("Validation by XSD failed.", e);
        }
        assertNotNull(result);
        assertTrue(validateRequiredFields(result));
    }

    private ProcessVariables getProcessVariablesP0001543() throws JsonProcessingException {
        var pv = mock(ProcessVariables.class);
        String jsonAppl = """
                {"applTpId":"NEW_PROD","applStatId":"ZDSCH","applDate":1739976346,"posId":"18000000","firstTouchPoint":"18000000","applReason":null,"hash":"JS4v6aycWQcsqKlGgolDQHR1Q0MNmbc3JG/KbXLG+dfDXDX4W9K588Syt8C0UZV4Cqukt/Hnk5B+dA7EuQU3qMP0shj03+j/xrN+lSLSzgMev6oMtApsfcNEIgIq16bsKZPlh0vqRRAtH79Dt2pAfdyR/hETaWY/FhFPLn2sKeY=","preApprOfrId":null,"opportunityId":"54791812","ccyId":"CZK","distCnlId":"18","fulfillmentCnl":null,"wrkPlaceId":"89c2d5f3-3dd3-4c60-ba45-adafe263137b","busProdSubTp":"BSL_REA","browserInfo":null,"telcoQueryAllowedFlag":false,"authMobSearchFlag":null,"firstTouchPointOwnr":"18000000","promoCode":null,"applDsnKey":null,"applComplPosId":null,"contrNum":"488325334","contrNumBs":"4880008104","contrNumReg":"21883/2025/01","contrNumCr":"586571","contrSignDate":null,"contrSignPosId":null,"contrSignAdvisorId":null,"contrSignAdvisorName":null,"lastChangeAdvisorId":"18000000","validFromDate":1739976346,"validToDate":1.743894E+9,"moneyTransferDate":null,"rejectRsnId":null,"rejectRsn":null,"rejectRsnTp":null,"personApplModelId":null,"computedSalary":null,"incVerifPSD2Flag":null,"incVerifAccStmFlag":null,"incVerifStmUploadFlag":null,"incVerifCallFlag":null,"incVerifSrcId":null,"incVerifPSD2Discount":null,"contrCond":null,"registryResult":"0","finalRiskClass":"B","consents":[{"consentTpId":"REKO","consentSubTpId":"RSTSDATA","timestamp":1.7399196E+9,"status":"SIGNED"},{"consentTpId":"REKO","consentSubTpId":"RBINCOMEDATA","timestamp":1.7399196E+9,"status":"SIGNED"},{"consentTpId":"REKO","consentSubTpId":"RBSEGMENTDATA","timestamp":1.7399196E+9,"status":"SIGNED"}],"rstsLoanNumber":null,"rstsBuildingSavingsNumber":null,"rstsRegistrationLoanNumber":null,"rstsLoanContractNumber":null,"rstsDataSharingConsent":null,"rbSegmentDataSharingConsent":null,"rbIncomeDataSharingConsent":null,"consentTimestamp":null}""";
        String jsonOwner = """
                {"siebelId":"27738525","genderId":"M","eduStatId":"B","housingStatId":"COOPF","citizenship":"CZ","marStatId":"C","rcNum":"030625/0010","salutId":null,"titleBefore":null,"firstName":"Hugo","familyName":"Růžička","birthPlace":"Praha 2","birthName":"Růžička","birthDate":[2003,6,25],"birthCntryId":"CZ","taxDomicile":"CZ","tin":null,"pep":false,"mortExpSum":8526,"householdExpSum":8526,"othExpSum":null,"crntAddrValidDate":[2020,2,19],"email":"<EMAIL>","phoneNum":"+420606124332","permAddr":{"streetName":"V Tehovičkách","streetNum":"812/10","cityName":"Praha","detail":null,"zip":"10300","landRegnNum":null,"cntryId":"CZ","addrInstAddrKey":5744803,"addrSince":null},"postalAddr":{"streetName":"V Tehovičkách","streetNum":"812/10","cityName":"Praha","detail":null,"zip":"10300","landRegnNum":null,"cntryId":"CZ","addrInstAddrKey":5744804,"addrSince":null},"idCards":[{"idCardTpId":"6","idCardPurpId":"1STID","idCardAppResultId":null,"cardIssuerCntryId":"CZ","issueDate":[2018,6,26],"issuer":"ÚmČ Praha 22","exprDate":[2028,6,26],"cardId":"*********","cardValidityLimitedFlag":null,"investigationId":null,"chngChnlRsn":null,"unapprRsn":null},{"idCardTpId":"7","idCardPurpId":"2NDID","idCardAppResultId":null,"cardIssuerCntryId":"CZ","issueDate":[2021,8,19],"issuer":"Praha 22","exprDate":[2031,8,19],"cardId":"********","cardValidityLimitedFlag":null,"investigationId":null,"chngChnlRsn":null,"unapprRsn":null}],"adbInstPtKey":3844996,"ptTpId":"FO","cbId":null,"verifications":[],"incomeVerifications":[{"instPtIncVerifKey":337844,"verifSource":"STATEMENT3M","personIncomeVerificationResultId":"TOVERIF","providerCode":null,"verifTime":**********,"advisorId":null,"refreshAccounts":null,"accounts":[],"psdUserId":null},{"instPtIncVerifKey":337843,"verifSource":"STATEMENT3M","personIncomeVerificationResultId":null,"providerCode":null,"verifTime":**********,"advisorId":null,"refreshAccounts":null,"accounts":[],"psdUserId":null},{"instPtIncVerifKey":337845,"verifSource":"STATEMENT3M","personIncomeVerificationResultId":"VERIFDONE","providerCode":null,"verifTime":**********.0,"advisorId":null,"refreshAccounts":null,"accounts":[],"psdUserId":null,"incTpId":"EMPLT"}],"ptCorpDetail":{"ptAcntgTpId":null,"entpInc":null,"taxYear":null,"dtiAmt":null,"dprAmt":null,"taxFlatRx":null,"equityAmt":null,"prftLossAmt":null,"prevRvnAmt":null,"rvnAmt":null,"ast":null},"busName":null,"applPtRoleTpId":"APPLICANT","rstsPartyId":"1012666","rstsPseudoBirthCode":null,"typeOfResidency":null,"residencyTpId":"1","residencyFrom":null,"residencyTo":null,"numHouseholdPerson":1,"numSupportedChildren":0,"segments":[],"metadata":[{"name":"aktualniPocetKontraktu","numValue":0,"strValue":null,"dateValue":null},{"name":"bic","numValue":null,"strValue":"1012666","dateValue":null},{"name":"cbKod","numValue":25,"strValue":null,"dateValue":null},{"name":"cbSkore","numValue":25,"strValue":null,"dateValue":null},{"name":"celkovyPocetKontraktu","numValue":0,"strValue":null,"dateValue":null},{"name":"cistyMesicniPrijem","numValue":85263,"strValue":null,"dateValue":null},{"name":"datumVypoctu","numValue":null,"strValue":"2025-02-19T14:48:36+01:00","dateValue":null},{"name":"delkaSporeniAkt","numValue":27,"strValue":null,"dateValue":null},{"name":"delkaSporeniMax","numValue":0,"strValue":null,"dateValue":null},{"name":"dobaVBance","numValue":0,"strValue":null,"dateValue":null},{"name":"dsti","numValue":0.049,"strValue":null,"dateValue":null},{"name":"dti","numValue":0.586,"strValue":null,"dateValue":null},{"name":"hodnota","numValue":null,"strValue":"|||0.000000|708.237036||||0.586000|0.049000||||21.670000|||||||||||||||||||||||||||0.000000||||||0.000000||||||||||||||||||","dateValue":null},{"name":"hranice","numValue":null,"strValue":"0.000000|0.000000||0.000000|-40.000000|||||0.600000||||69.000000||||||||||0.400000||201.000000||0.000000|0.000000||||||||||||0.000000||||1.000000|1.000000|0.000000|2000000.000000|1000000.000000|1000000.000000|10000.000000||365.000000|365.000000|0.500000||0.000000||||0.000000|||3500000.000000|","dateValue":null},{"name":"kraj","numValue":2,"strValue":null,"dateValue":null},{"name":"navyseniSplatekZavazkuZaPar","numValue":0,"strValue":null,"dateValue":null},{"name":"navyseniZavazkuZaPar","numValue":0,"strValue":null,"dateValue":null},{"name":"nejvyssiUpominka","numValue":0,"strValue":null,"dateValue":null},{"name":"oborCinnosti1","numValue":1,"strValue":null,"dateValue":null},{"name":"pocetDuveryhodnost","numValue":0,"strValue":null,"dateValue":null},{"name":"pouziteAplikacniSkore","numValue":668.905,"strValue":null,"dateValue":null},{"name":"pouziteCBSkore","numValue":25,"strValue":null,"dateValue":null},{"name":"provedenaKontrolaAnalytikem","numValue":null,"strValue":"false","dateValue":null},{"name":"refDelkaVzam1","numValue":2,"strValue":null,"dateValue":null},{"name":"refPrijemTyp1","numValue":85263,"strValue":null,"dateValue":null},{"name":"refPrijemTyp2","numValue":0,"strValue":null,"dateValue":null},{"name":"refPrijemTyp3","numValue":0,"strValue":null,"dateValue":null},{"name":"rozhodneDatumProUrceniParametruVypoctu","numValue":null,"strValue":"2025-02-19T14:48:36+01:00","dateValue":null},{"name":"saldoVkladuMax","numValue":0,"strValue":null,"dateValue":null},{"name":"skore","numValue":708.237,"strValue":null,"dateValue":null},{"name":"skoreURGRWA","numValue":668.905,"strValue":null,"dateValue":null},{"name":"skoreURGUW","numValue":708.237,"strValue":null,"dateValue":null},{"name":"solusDluh","numValue":0,"strValue":null,"dateValue":null},{"name":"specialniStatusKlienta","numValue":0,"strValue":null,"dateValue":null},{"name":"splatkaAktualnihoUveruStredniSazba","numValue":4192,"strValue":null,"dateValue":null},{"name":"stupenURG","numValue":10.0,"strValue":null,"dateValue":null},{"name":"stupenURGRWA","numValue":10.0,"strValue":null,"dateValue":null},{"name":"stupenURGoldRWA","numValue":4,"strValue":null,"dateValue":null},{"name":"sumaSaldVkladuAkt","numValue":0,"strValue":null,"dateValue":null},{"name":"typRK","numValue":null,"strValue":"HIST_KLOS|HIST_UPOM|HIST_DUVE|HIST_INSO|APLI_SKOR|BONI_KOMB|BONI_PRVY|BONI_ODSO|BONI_DTI|BONI_DSTI|LTV|MINI_PRIJ|VEK_UVODF|VEK_SPLAT|DOBA_TRVU|MAX_ANGA|MAX_NEAN|RIZI_SUBJ|VERI_PRIJ|VERI_TELE|DOKL_NEPL|DOKL_CISR|DOKL_KCIS|UR_KOMBIN|BRKI_NRKI|CB_SKORE|SOLUS|HIST_SPOR|HIST_UVER|KONT_DOZA|KONT_ZAJI|KONT_UCEL|STRES_PRVY|STRES_DSTI|LTV_AGREG|CIZINEC|CIZINEC|CERP_UCOZ|CERP_CETN|DOKL_MVSR|CRE|OVER_SOUH|STRES_DTI|ANGA_BEMA|IFRS_UVER|IFRS_OSOB|CEE|PODN_NDPH|PODN_PRPO|PODN_PRNA|PODN_KULA|PODN_BEUV|PODN_KDOP|PODN_KDOV|URG_ZHORS|OPAK_ZADO|HIST_VYMU|VYSE_NEUV|FIRM_NACE|PREDS_NAB|HIST_KLUV|VYSE_UVER|FIRM_RIZI|PODN_NADL|","dateValue":null},{"name":"ucel1","numValue":1313,"strValue":null,"dateValue":null},{"name":"uverovaAngazovanostVRsts","numValue":12500000.0,"strValue":null,"dateValue":null},{"name":"vek","numValue":21,"strValue":null,"dateValue":null},{"name":"verzeFceProcesuPosouzeni","numValue":null,"strValue":"2.2 211119","dateValue":null},{"name":"verzeFunkce","numValue":null,"strValue":"2.2 211119","dateValue":null},{"name":"verzeModelu","numValue":null,"strValue":"2.2 211119","dateValue":null},{"name":"vysledek","numValue":null,"strValue":"ERROR|ERROR|ERROR|OK|KOM|ERROR|ERROR|ERROR|ERROR|OK|ERROR|ERROR|ERROR|OK|ERROR|ERROR|ERROR|ERROR|ERROR|ERROR|ERROR|ERROR|ERROR|ERROR|ERROR|ERROR|ERROR|ERROR|ERROR|ERROR|ERROR|ERROR|ERROR|ERROR|ERROR|ERROR|ERROR|ERROR|ERROR|ERROR|OK|ERROR|ERROR|ERROR|ERROR|ERROR|OK|ERROR|ERROR|ERROR|ERROR|ERROR|ERROR|ERROR|ERROR|ERROR|ERROR|ERROR|ERROR|ERROR|ERROR|ERROR|ERROR|ERROR|","dateValue":null},{"name":"vysledneAplikacniSkore","numValue":708.237,"strValue":null,"dateValue":null}]}""";
        String jsonVariants = """
                {"variants":[{"applVariantKey":7777175,"applVariantTpId":"SEL","applVariantSignCnlFlag":null,"busProdTpId":"BSL","busProdSubTpId":"BSL_REA","finaAmt":1.5E+6,"instlAmt":1.069E+4,"instlCnt":240,"maturityDate":[2045,2,19],"intrsRx":0.0574,"rpsn":0.0733,"camCode":null,"minAvlblAmt":null,"minInstlAmt":null,"minInstlCnt":null,"maxAvlblAmt":null,"maxInstlAmt":null,"maxInstlCnt":null,"upsellAmt":0,"maxUpsellAmt":null,"ccyId":"CZK","totRpmtAmt":2.8104E+6,"refiSavedAmt":null,"accNumPrefix":null,"accNum":"**********","accBankCode":"5500","actSumInstlAmt":null,"consLoanInstlAmt":null,"dayIntrsAmt":null,"intrsDscntFlag":null,"intnOblgtnAmt":null,"extnOblgtnAmt":null,"repreDrawDate":null,"repreMaturityDate":null,"repreInt":null,"repreInt3M":null,"costs3M":null,"delFlag":null,"declaredPurpose":null,"maxExtOblgtnAmt":null,"minIrForFinalization":null,"origScOfrIntrsRx":0.0574,"payCpcyForFinal":null,"maxAvlblTopup":null,"prodIntrsCodeId":null,"loanPurposes":[],"loanCategories":[],"variantParameters":[],"signChannels":[{"applVariantSignCnlKey":3050289,"signCnlId":"80","applVariantSignCnlDelFlag":null,"signPosId":null},{"applVariantSignCnlKey":3050288,"signCnlId":"80","applVariantSignCnlDelFlag":null,"signPosId":null}],"insurances":[{"insurApplVrntInsurKey":153384,"insurTpId":"PPI","insurPremAmt":null,"insurPremAmtPct":null,"insurSelFlag":null,"insurDispoFlag":null,"insurDstiFlag":null,"insurRpsnFlag":null}],"surcharges":[],"fixPeriod":"5","ecoInvestTpId":"XNA","ecoInvestShare":null,"closeRelCoFin":null},{"applVariantKey":7777176,"applVariantTpId":"APR","applVariantSignCnlFlag":null,"busProdTpId":"BSL","busProdSubTpId":"BSL_REA","finaAmt":6E+5,"instlAmt":4.28E+3,"instlCnt":240,"maturityDate":null,"intrsRx":0.000569,"rpsn":null,"camCode":null,"minAvlblAmt":5E+4,"minInstlAmt":1515,"minInstlCnt":36,"maxAvlblAmt":6E+5,"maxInstlAmt":4.28E+3,"maxInstlCnt":240,"upsellAmt":0,"maxUpsellAmt":0,"ccyId":"CZK","totRpmtAmt":1.0272E+6,"refiSavedAmt":null,"accNumPrefix":null,"accNum":null,"accBankCode":null,"actSumInstlAmt":null,"consLoanInstlAmt":null,"dayIntrsAmt":null,"intrsDscntFlag":null,"intnOblgtnAmt":null,"extnOblgtnAmt":null,"repreDrawDate":null,"repreMaturityDate":null,"repreInt":null,"repreInt3M":null,"costs3M":null,"delFlag":null,"declaredPurpose":null,"maxExtOblgtnAmt":null,"minIrForFinalization":0.0519,"origScOfrIntrsRx":null,"payCpcyForFinal":null,"maxAvlblTopup":null,"prodIntrsCodeId":null,"loanPurposes":[{"instPurpKey":2763714,"purpId":"1313","instOblgtnKey":null,"purpCatgId":"MODE","purpAmt":6E+5,"photovoltaics":false,"photoDoc":null,"purpDetail":null,"purpStatId":"VERIF","docs":[],"specConds":[{"condId":"2638","condDetail":null,"condStatId":"VERIF","status":null},{"condId":"2636","condDetail":null,"condStatId":"VERIF","status":null},{"condId":"2647","condDetail":null,"condStatId":"VERIF","status":null}]}],"loanCategories":[],"variantParameters":[{"applVariantParKey":********,"prodParDate":[2025,2,19],"applVariantKey":null,"priceVariantId":null,"purpId":null,"priceVariantPriority":1,"regIntrsRxId":null,"regIntrsRxBaseIndex":null,"penaltyIntrsRxBaseIndex":null,"regIntrsRx":null,"penaltyIntrsRx":null,"regIntrsRxDvg":0.0589,"origRegInstrsRxDvg":0.0589,"intrsRxDiff":null,"minRegIntrsRx":null,"penaltyIntrsRxDvg":null,"minTerm":36,"maxTerm":240,"minAmt":0,"maxAmt":699999,"elbosParPtStat":null,"elbosParRiskClassSince":null,"elbosParRiskClassTo":null,"payInsurVariantId":null,"payInsurVariantAmt":null,"loanAccFeeAmt":null,"provideFeeAmt":null,"electronicStmFeeAmt":null,"paperStmFeeAmt":null,"extordInstlFeeAmt":null,"minRefundRx":null,"maxRefundRx":null,"camId":null,"camName":null,"payInsurVariantDescr":null,"promoCode":null,"minTermRefund":null,"maxTermRefund":null,"minAmtRefund":null,"maxAmtRefund":null,"minUpsellAmt":null,"maxUpsellAmt":null,"applVariantParDelFlag":null},{"applVariantParKey":22964898,"prodParDate":[2025,2,19],"applVariantKey":null,"priceVariantId":null,"purpId":null,"priceVariantPriority":2,"regIntrsRxId":null,"regIntrsRxBaseIndex":null,"penaltyIntrsRxBaseIndex":null,"regIntrsRx":null,"penaltyIntrsRx":null,"regIntrsRxDvg":0.0569,"origRegInstrsRxDvg":0.0569,"intrsRxDiff":null,"minRegIntrsRx":null,"penaltyIntrsRxDvg":null,"minTerm":36,"maxTerm":240,"minAmt":0,"maxAmt":699999,"elbosParPtStat":null,"elbosParRiskClassSince":null,"elbosParRiskClassTo":null,"payInsurVariantId":null,"payInsurVariantAmt":null,"loanAccFeeAmt":null,"provideFeeAmt":null,"electronicStmFeeAmt":null,"paperStmFeeAmt":null,"extordInstlFeeAmt":null,"minRefundRx":null,"maxRefundRx":null,"camId":null,"camName":null,"payInsurVariantDescr":null,"promoCode":null,"minTermRefund":null,"maxTermRefund":null,"minAmtRefund":null,"maxAmtRefund":null,"minUpsellAmt":null,"maxUpsellAmt":null,"applVariantParDelFlag":null}],"signChannels":[{"applVariantSignCnlKey":3050289,"signCnlId":"80","applVariantSignCnlDelFlag":null,"signPosId":null},{"applVariantSignCnlKey":3050288,"signCnlId":"80","applVariantSignCnlDelFlag":null,"signPosId":null}],"insurances":[],"surcharges":[],"fixPeriod":"5","ecoInvestTpId":"XNA","ecoInvestShare":null,"closeRelCoFin":false}]}""";
        String jsonRekoRpsnCalculation = """
                {"estimDisbursDate":"2025-03-31","firstPaymentDate":"2025-04-25","finaLoanLength":238,"finaInstCnt":237,"dueDate":"2044-12-25","finaAmt":6E+5,"savingsContractFeeAmt":0,"loanContractFeeAmt":6E+3,"finaInsAmt":2.61E+3,"insFeeAmt":0,"deposit":6E+2,"addRPSN":0.0034,"toBePaidAmt":617007.64,"costToBePaid":1.03,"finaBuildAmt":546570.17,"ipsScheduleRowsCount":null,"ipsScheduleRows":[{"periodID":"03/2025 - 03/2025","ipsScheduleRowType":"_ERP_N_V_RU","finaAmt":-6E+5,"finalIR":0.06,"finaInsAmt":null,"repaidInterest":null,"otherCosts":null,"repaidPrincipal":null,"otherCostsExluded":null,"savingsIncome":null,"remainingPrincipal":-6E+5,"remainingInterest":-6E+3,"savings":0},{"periodID":"03/2025 - 03/2025","ipsScheduleRowType":"PLATBY_NA_STAVEBN_SPO_EN_","finaAmt":null,"finalIR":0.06,"finaInsAmt":null,"repaidInterest":null,"otherCosts":null,"repaidPrincipal":null,"otherCostsExluded":null,"savingsIncome":null,"remainingPrincipal":-6E+5,"remainingInterest":-6E+3,"savings":0},{"periodID":"04/2025 - 04/2025","ipsScheduleRowType":"VKLAD","finaAmt":null,"finalIR":0.06,"finaInsAmt":6E+2,"repaidInterest":0,"otherCosts":0,"repaidPrincipal":0,"otherCostsExluded":0,"savingsIncome":0,"remainingPrincipal":-6E+5,"remainingInterest":-4013.38,"savings":6E+2},{"periodID":"04/2025 - 04/2025","ipsScheduleRowType":"SPL_TKA_PU","finaAmt":null,"finalIR":0.06,"finaInsAmt":2.01E+3,"repaidInterest":0,"otherCosts":-2.01E+3,"repaidPrincipal":0,"otherCostsExluded":0,"savingsIncome":0,"remainingPrincipal":-6E+5,"remainingInterest":-4013.38,"savings":6E+2},{"periodID":"05/2025 - 05/2025","ipsScheduleRowType":"VKLAD","finaAmt":null,"finalIR":0.06,"finaInsAmt":6E+2,"repaidInterest":0,"otherCosts":0,"repaidPrincipal":0,"otherCostsExluded":0,"savingsIncome":0,"remainingPrincipal":-6E+5,"remainingInterest":-2031.44,"savings":1.2E+3},{"periodID":"05/2025 - 05/2025","ipsScheduleRowType":"SPL_TKA_PU","finaAmt":null,"finalIR":0.06,"finaInsAmt":2.01E+3,"repaidInterest":0,"otherCosts":-2.01E+3,"repaidPrincipal":0,"otherCostsExluded":0,"savingsIncome":0,"remainingPrincipal":-6E+5,"remainingInterest":-2031.44,"savings":1.2E+3},{"periodID":"06/2025 - 06/2025","ipsScheduleRowType":"VKLAD","finaAmt":null,"finalIR":0.06,"finaInsAmt":6E+2,"repaidInterest":0,"otherCosts":0,"repaidPrincipal":0,"otherCostsExluded":0,"savingsIncome":0,"remainingPrincipal":-6E+5,"remainingInterest":-50.44,"savings":1.8E+3},{"periodID":"06/2025 - 06/2025","ipsScheduleRowType":"SPL_TKA_PU","finaAmt":null,"finalIR":0.06,"finaInsAmt":2.01E+3,"repaidInterest":-3E+1,"otherCosts":-1.98E+3,"repaidPrincipal":0,"otherCostsExluded":0,"savingsIncome":0,"remainingPrincipal":-6E+5,"remainingInterest":-50.44,"savings":1.8E+3},{"periodID":"07/2025 - 07/2025","ipsScheduleRowType":"VKLAD","finaAmt":null,"finalIR":0.06,"finaInsAmt":6E+2,"repaidInterest":0,"otherCosts":0,"repaidPrincipal":0,"otherCostsExluded":0,"savingsIncome":0,"remainingPrincipal":-598174.5,"remainingInterest":0,"savings":2.4E+3},{"periodID":"07/2025 - 07/2025","ipsScheduleRowType":"SPL_TKA_PU","finaAmt":null,"finalIR":0.06,"finaInsAmt":2.01E+3,"repaidInterest":-78.5,"otherCosts":-106,"repaidPrincipal":-1825.5,"otherCostsExluded":0,"savingsIncome":0,"remainingPrincipal":-598174.5,"remainingInterest":0,"savings":2.4E+3},{"periodID":"08/2025 - 08/2025","ipsScheduleRowType":"VKLAD","finaAmt":null,"finalIR":0.06,"finaInsAmt":6E+2,"repaidInterest":0,"otherCosts":0,"repaidPrincipal":0,"otherCostsExluded":0,"savingsIncome":0,"remainingPrincipal":-596193.41,"remainingInterest":0,"savings":3E+3},{"periodID":"08/2025 - 08/2025","ipsScheduleRowType":"SPL_TKA_PU","finaAmt":null,"finalIR":0.06,"finaInsAmt":2.01E+3,"repaidInterest":-28.91,"otherCosts":0,"repaidPrincipal":-1981.09,"otherCostsExluded":0,"savingsIncome":0,"remainingPrincipal":-596193.41,"remainingInterest":0,"savings":3E+3},{"periodID":"09/2025 - 09/2025","ipsScheduleRowType":"VKLAD","finaAmt":null,"finalIR":0.06,"finaInsAmt":6E+2,"repaidInterest":0,"otherCosts":0,"repaidPrincipal":0,"otherCostsExluded":0,"savingsIncome":0,"remainingPrincipal":-594212.22,"remainingInterest":0,"savings":3.6E+3},{"periodID":"09/2025 - 09/2025","ipsScheduleRowType":"SPL_TKA_PU","finaAmt":null,"finalIR":0.06,"finaInsAmt":2.01E+3,"repaidInterest":-28.81,"otherCosts":0,"repaidPrincipal":-1981.19,"otherCostsExluded":0,"savingsIncome":0,"remainingPrincipal":-594212.22,"remainingInterest":0,"savings":3.6E+3},{"periodID":"10/2025 - 10/2025","ipsScheduleRowType":"VKLAD","finaAmt":null,"finalIR":0.06,"finaInsAmt":6E+2,"repaidInterest":0,"otherCosts":0,"repaidPrincipal":0,"otherCostsExluded":0,"savingsIncome":0,"remainingPrincipal":-592336.01,"remainingInterest":0,"savings":4.2E+3},{"periodID":"10/2025 - 10/2025","ipsScheduleRowType":"SPL_TKA_PU","finaAmt":null,"finalIR":0.06,"finaInsAmt":2.01E+3,"repaidInterest":-27.79,"otherCosts":-106,"repaidPrincipal":-1876.21,"otherCostsExluded":0,"savingsIncome":0,"remainingPrincipal":-592336.01,"remainingInterest":0,"savings":4.2E+3},{"periodID":"11/2025 - 11/2025","ipsScheduleRowType":"VKLAD","finaAmt":null,"finalIR":0.06,"finaInsAmt":6E+2,"repaidInterest":0,"otherCosts":0,"repaidPrincipal":0,"otherCostsExluded":0,"savingsIncome":0,"remainingPrincipal":-590354.64,"remainingInterest":0,"savings":4.8E+3},{"periodID":"11/2025 - 11/2025","ipsScheduleRowType":"SPL_TKA_PU","finaAmt":null,"finalIR":0.06,"finaInsAmt":2.01E+3,"repaidInterest":-28.63,"otherCosts":0,"repaidPrincipal":-1981.37,"otherCostsExluded":0,"savingsIncome":0,"remainingPrincipal":-590354.64,"remainingInterest":0,"savings":4.8E+3},{"periodID":"12/2025 - 12/2025","ipsScheduleRowType":"VKLAD","finaAmt":null,"finalIR":0.06,"finaInsAmt":6E+2,"repaidInterest":0,"otherCosts":0,"repaidPrincipal":0,"otherCostsExluded":0,"savingsIncome":0,"remainingPrincipal":-588372.25,"remainingInterest":0,"savings":5.4E+3},{"periodID":"12/2025 - 12/2025","ipsScheduleRowType":"SPL_TKA_PU","finaAmt":null,"finalIR":0.06,"finaInsAmt":2.01E+3,"repaidInterest":-27.61,"otherCosts":0,"repaidPrincipal":-1982.39,"otherCostsExluded":0,"savingsIncome":0,"remainingPrincipal":-588372.25,"remainingInterest":0,"savings":5.4E+3},{"periodID":"01/2026 - 01/2026","ipsScheduleRowType":"VKLAD","finaAmt":null,"finalIR":0.06,"finaInsAmt":6E+2,"repaidInterest":0,"otherCosts":0,"repaidPrincipal":0,"otherCostsExluded":0,"savingsIncome":0,"remainingPrincipal":-586496.68,"remainingInterest":0,"savings":6113.56},{"periodID":"01/2026 - 01/2026","ipsScheduleRowType":"SPL_TKA_PU","finaAmt":null,"finalIR":0.06,"finaInsAmt":2.01E+3,"repaidInterest":-28.43,"otherCosts":-106,"repaidPrincipal":-1875.57,"otherCostsExluded":0,"savingsIncome":0,"remainingPrincipal":-586496.68,"remainingInterest":0,"savings":6113.56},{"periodID":"02/2026 - 02/2026","ipsScheduleRowType":"VKLAD","finaAmt":null,"finalIR":0.06,"finaInsAmt":6E+2,"repaidInterest":0,"otherCosts":0,"repaidPrincipal":0,"otherCostsExluded":0,"savingsIncome":0,"remainingPrincipal":-584515.02,"remainingInterest":0,"savings":6713.56},{"periodID":"02/2026 - 02/2026","ipsScheduleRowType":"SPL_TKA_PU","finaAmt":null,"finalIR":0.06,"finaInsAmt":2.01E+3,"repaidInterest":-28.34,"otherCosts":0,"repaidPrincipal":-1981.66,"otherCostsExluded":0,"savingsIncome":0,"remainingPrincipal":-584515.02,"remainingInterest":0,"savings":6713.56},{"periodID":"03/2025 - 02/2026","ipsScheduleRowType":"SPL_TKA_PU_A_VKLADY","finaAmt":-6E+5,"finalIR":0.06,"finaInsAmt":2.871E+4,"repaidInterest":-307.02,"otherCosts":-7.95E+3,"repaidPrincipal":-15484.98,"otherCostsExluded":0,"savingsIncome":113.56,"remainingPrincipal":-584515.02,"remainingInterest":0,"savings":6713.56},{"periodID":"03/2026 - 02/2027","ipsScheduleRowType":"SPL_TKA_PU_A_VKLADY","finaAmt":null,"finalIR":0.06,"finaInsAmt":3.132E+4,"repaidInterest":-326.44,"otherCosts":-7526,"repaidPrincipal":-37944.85,"otherCostsExluded":0,"savingsIncome":661.73,"remainingPrincipal":-546570.17,"remainingInterest":null,"savings":null},{"periodID":"03/2027 - 02/2028","ipsScheduleRowType":"SPL_TKA_SU","finaAmt":null,"finalIR":0.06,"finaInsAmt":3.132E+4,"repaidInterest":-305.12,"otherCosts":-424,"repaidPrincipal":-29990.88,"otherCostsExluded":0,"savingsIncome":0,"remainingPrincipal":-515977.07,"remainingInterest":0,"savings":null},{"periodID":"03/2028 - 02/2029","ipsScheduleRowType":"SPL_TKA_SU","finaAmt":null,"finalIR":0.06,"finaInsAmt":3.132E+4,"repaidInterest":-287.09,"otherCosts":-424,"repaidPrincipal":-30608.91,"otherCostsExluded":0,"savingsIncome":0,"remainingPrincipal":-485366.75,"remainingInterest":0,"savings":null},{"periodID":"03/2029 - 02/2030","ipsScheduleRowType":"SPL_TKA_SU","finaAmt":null,"finalIR":0.06,"finaInsAmt":3.132E+4,"repaidInterest":-269.6,"otherCosts":-424,"repaidPrincipal":-30626.4,"otherCostsExluded":0,"savingsIncome":0,"remainingPrincipal":-454738.87,"remainingInterest":0,"savings":null},{"periodID":"03/2030 - 02/2031","ipsScheduleRowType":"SPL_TKA_SU","finaAmt":null,"finalIR":0.06,"finaInsAmt":3.132E+4,"repaidInterest":-252.18,"otherCosts":-424,"repaidPrincipal":-30643.82,"otherCostsExluded":0,"savingsIncome":0,"remainingPrincipal":-424093.57,"remainingInterest":0,"savings":null},{"periodID":"03/2031 - 02/2032","ipsScheduleRowType":"SPL_TKA_SU","finaAmt":null,"finalIR":0.06,"finaInsAmt":3.132E+4,"repaidInterest":-234.69,"otherCosts":-424,"repaidPrincipal":-30661.31,"otherCostsExluded":0,"savingsIncome":0,"remainingPrincipal":-393430.73,"remainingInterest":0,"savings":null},{"periodID":"03/2032 - 02/2033","ipsScheduleRowType":"SPL_TKA_SU","finaAmt":null,"finalIR":0.06,"finaInsAmt":3.132E+4,"repaidInterest":-217.34,"otherCosts":-424,"repaidPrincipal":-30678.66,"otherCostsExluded":0,"savingsIncome":0,"remainingPrincipal":-362750.64,"remainingInterest":0,"savings":null},{"periodID":"03/2033 - 02/2034","ipsScheduleRowType":"SPL_TKA_SU","finaAmt":null,"finalIR":0.06,"finaInsAmt":3.132E+4,"repaidInterest":-199.81,"otherCosts":-424,"repaidPrincipal":-30696.19,"otherCostsExluded":0,"savingsIncome":0,"remainingPrincipal":-332052.96,"remainingInterest":0,"savings":null},{"periodID":"03/2034 - 02/2035","ipsScheduleRowType":"SPL_TKA_SU","finaAmt":null,"finalIR":0.06,"finaInsAmt":3.132E+4,"repaidInterest":-182.34,"otherCosts":-424,"repaidPrincipal":-30713.66,"otherCostsExluded":0,"savingsIncome":0,"remainingPrincipal":-301337.82,"remainingInterest":0,"savings":null},{"periodID":"03/2035 - 02/2036","ipsScheduleRowType":"SPL_TKA_SU","finaAmt":null,"finalIR":0.06,"finaInsAmt":3.132E+4,"repaidInterest":-164.82,"otherCosts":-424,"repaidPrincipal":-30731.18,"otherCostsExluded":0,"savingsIncome":0,"remainingPrincipal":-270605.12,"remainingInterest":0,"savings":null},{"periodID":"03/2036 - 02/2037","ipsScheduleRowType":"SPL_TKA_SU","finaAmt":null,"finalIR":0.06,"finaInsAmt":3.132E+4,"repaidInterest":-147.43,"otherCosts":-424,"repaidPrincipal":-30748.57,"otherCostsExluded":0,"savingsIncome":0,"remainingPrincipal":-239855.1,"remainingInterest":0,"savings":null},{"periodID":"03/2037 - 02/2038","ipsScheduleRowType":"SPL_TKA_SU","finaAmt":null,"finalIR":0.06,"finaInsAmt":3.132E+4,"repaidInterest":-129.89,"otherCosts":-424,"repaidPrincipal":-30766.11,"otherCostsExluded":0,"savingsIncome":0,"remainingPrincipal":-209087.5,"remainingInterest":0,"savings":null},{"periodID":"03/2038 - 02/2039","ipsScheduleRowType":"SPL_TKA_SU","finaAmt":null,"finalIR":0.06,"finaInsAmt":3.132E+4,"repaidInterest":-112.37,"otherCosts":-424,"repaidPrincipal":-30783.63,"otherCostsExluded":0,"savingsIncome":0,"remainingPrincipal":-178302.38,"remainingInterest":0,"savings":null},{"periodID":"03/2039 - 02/2040","ipsScheduleRowType":"SPL_TKA_SU","finaAmt":null,"finalIR":0.06,"finaInsAmt":3.132E+4,"repaidInterest":-94.83,"otherCosts":-424,"repaidPrincipal":-30801.17,"otherCostsExluded":0,"savingsIncome":0,"remainingPrincipal":-147499.7,"remainingInterest":0,"savings":null},{"periodID":"03/2040 - 02/2041","ipsScheduleRowType":"SPL_TKA_SU","finaAmt":null,"finalIR":0.06,"finaInsAmt":3.132E+4,"repaidInterest":-77.34,"otherCosts":-424,"repaidPrincipal":-30818.66,"otherCostsExluded":0,"savingsIncome":0,"remainingPrincipal":-116679.57,"remainingInterest":0,"savings":null},{"periodID":"03/2041 - 02/2042","ipsScheduleRowType":"SPL_TKA_SU","finaAmt":null,"finalIR":0.06,"finaInsAmt":3.132E+4,"repaidInterest":-59.77,"otherCosts":-424,"repaidPrincipal":-30836.23,"otherCostsExluded":0,"savingsIncome":0,"remainingPrincipal":-85841.85,"remainingInterest":0,"savings":null},{"periodID":"03/2042 - 02/2043","ipsScheduleRowType":"SPL_TKA_SU","finaAmt":null,"finalIR":0.06,"finaInsAmt":3.132E+4,"repaidInterest":-42.22,"otherCosts":-424,"repaidPrincipal":-30853.78,"otherCostsExluded":0,"savingsIncome":0,"remainingPrincipal":-54986.58,"remainingInterest":0,"savings":null},{"periodID":"03/2043 - 02/2044","ipsScheduleRowType":"SPL_TKA_SU","finaAmt":null,"finalIR":0.06,"finaInsAmt":3.132E+4,"repaidInterest":-24.65,"otherCosts":-424,"repaidPrincipal":-30871.35,"otherCostsExluded":0,"savingsIncome":0,"remainingPrincipal":-24113.74,"remainingInterest":0,"savings":null},{"periodID":"03/2044 - 02/2045","ipsScheduleRowType":"SPL_TKA_SU","finaAmt":null,"finalIR":0.06,"finaInsAmt":27147.64,"repaidInterest":-7.19,"otherCosts":-418,"repaidPrincipal":-26722.45,"otherCostsExluded":0,"savingsIncome":0,"remainingPrincipal":0,"remainingInterest":0,"savings":null},{"periodID":"03/2025 - 02/2045","ipsScheduleRowType":"CELKEM","finaAmt":0,"finalIR":null,"finaInsAmt":617007.64,"repaidInterest":-3414.93,"otherCosts":-14268,"repaidPrincipal":-587087.07,"otherCostsExluded":0,"savingsIncome":775.29,"remainingPrincipal":null,"remainingInterest":null,"savings":null}],"rpsn":0.0028}""";
        String jsonIncomes = """
                {"incomes":[{"instPtIncKey":3422634,"adbInstPtKey":3844996,"occCatgId":"A","emptTpId":"DPP","businessType":null,"incTpId":"EMPLT","inc":85263,"icoNum":"45274649","dicNum":null,"busName":"ČEZ, a. s.","empSince":[2024,8,19],"empContrEndDate":null,"dismissalFlag":false,"riskBusBranchFlag":null,"ccyId":"CZK","exchangeRate":null,"invVerifSrcId":"STATEMENT3M","futureIncFlag":null,"taxExpTpId":null,"realExpAmt":null,"decrDprAmt":null,"instPtIncVerifKey":337844,"incVerifSrcId":"STATEMENT3M","incVerifRsltId":"VERIFDONE","delvCnlId":"XNA","netIncVerif":null,"netIncFinal":null,"verifTime":**********,"scgSectorCatgId":"11","publSectorActFlag":null}]}""";
        var appl = ApplicationUtils.convertJsonToType(jsonAppl, BuildingLoanApplicationDTO.class);
        var owner = ApplicationUtils.convertJsonToType(jsonOwner, BuildingLoanApplicationOwnerDTO.class);
        var variants = ApplicationUtils.convertJsonToType(jsonVariants, BuildingApplVariantsDTO.class);
        var rpsnCalculation = ApplicationUtils.convertJsonToType(jsonRekoRpsnCalculation, RekoCalculationResponseDTO.class);
        var incomes = ApplicationUtils.convertJsonToType(jsonIncomes, PtIncomeRstsListDTO.class);

        when(pv.getLoanApplicationOwner()).thenReturn(owner);
        when(pv.getBuildingLoanApplication()).thenReturn(appl);
        when(pv.getBuildingLoanApplVariants()).thenReturn(variants);
        when(pv.getRekoRpsnCalculations()).thenReturn(rpsnCalculation);
        when(pv.getLoanApplicationIncomes()).thenReturn(incomes);
        when(pv.getLanguage()).thenReturn(LovLang.CZE);
        return pv;
    }


    private GetDocumentListDto getDMSDocumentsList() {
        GetDocumentListDto generatedDocuments = new GetDocumentListDto();
        List<GetDocumentListDto.Document> documentList = new ArrayList<>();

        GetDocumentListDto.Document doc = new GetDocumentListDto.Document();
        GetDocumentListDto.Document.DocumentDetailFull detail = new GetDocumentListDto.Document.DocumentDetailFull();
        detail.setDocumentClass("P0001543");
        detail.setVersionNumber("1.0");
        detail.setGlobalCreationDate(OffsetDateTime.now().minusDays(2));
        doc.setDocument(detail);

        documentList.add(doc);
        generatedDocuments.setDocumentList(documentList);

        return generatedDocuments;
    }
}