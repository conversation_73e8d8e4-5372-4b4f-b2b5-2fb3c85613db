package cz.equa.camapp.reko.worker;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import cz.equa.camapp.rest.service.ApplicationService;
import cz.equa.camapp.service.ServiceException;
import cz.rb.tif.cus_00050_contact.Cus00050Contact;
import org.camunda.bpm.client.task.ExternalTask;
import org.camunda.bpm.client.task.ExternalTaskService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit.jupiter.web.SpringJUnitWebConfig;

import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@SpringJUnitWebConfig(classes = {UploadContactsFromAppl.class})
public class UploadContactsFromApplTest {

    @MockBean
    private Cus00050Contact cus00050Contact;
    @MockBean
    private ApplicationService applicationService;

    @Mock
    private ExternalTask externalTask;
    @Mock
    private ExternalTaskService externalTaskService;

    private UploadContactsFromAppl underTest;

    private Map<String, Object> getProcessVariables() throws JsonProcessingException {
        String jsonString = """
                {
                    "busApplId": "1",
                    "applKey": 1,
                    "X-Correlation-Id": "1",
                    "contactUpload": {
                        "partId": "1",
                        "addressToInsert": {}
                            }
                }""";
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        return objectMapper.readValue(jsonString, new TypeReference<>() {
        });
    }

    @BeforeEach
    public void setUp() throws ServiceException {
        underTest = new UploadContactsFromAppl(applicationService, cus00050Contact);
    }

    @Test
    void executeWorker() throws JsonProcessingException, ServiceException {
        doNothing().when(cus00050Contact).upload(anyString(), any(), any(), any(), any());
        doNothing().when(externalTaskService).complete(any(), any());
        when(externalTask.getAllVariables()).thenReturn(getProcessVariables());


        UploadContactsFromAppl underTest = new UploadContactsFromAppl(applicationService, cus00050Contact);
        underTest.execute(externalTask, externalTaskService);

        verify(cus00050Contact, times(1)).upload(any(), any(), any(), any(), any());
    }
}
