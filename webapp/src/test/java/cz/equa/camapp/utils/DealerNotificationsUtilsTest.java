package cz.equa.camapp.utils;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class DealerNotificationsUtilsTest {

    @Test
    void determineRoleByChannel() {
        assertNull(DealerNotificationsUtils.determineRoleByChannel(null));
        assertNull(DealerNotificationsUtils.determineRoleByChannel(""));
        assertNull(DealerNotificationsUtils.determineRoleByChannel("0"));
        assertNull(DealerNotificationsUtils.determineRoleByChannel("00"));
        assertNull(DealerNotificationsUtils.determineRoleByChannel("000"));
        assertEquals(DealerNotificationsUtils.Role.BANKER, DealerNotificationsUtils.determineRoleByChannel("10"));
        assertEquals(DealerNotificationsUtils.Role.EFA, DealerNotificationsUtils.determineRoleByChannel("14"));
        assertEquals(DealerNotificationsUtils.Role.EFA, DealerNotificationsUtils.determineRoleByChannel("30"));
        assertEquals(DealerNotificationsUtils.Role.EFA, DealerNotificationsUtils.determineRoleByChannel("32"));
        assertEquals(DealerNotificationsUtils.Role.CLIENT, DealerNotificationsUtils.determineRoleByChannel("15"));
        assertEquals(DealerNotificationsUtils.Role.CLIENT, DealerNotificationsUtils.determineRoleByChannel("18"));
        assertEquals(DealerNotificationsUtils.Role.CLIENT, DealerNotificationsUtils.determineRoleByChannel("35"));
    }

    @Test
    void isForEmailNotification() {
        assertTrue(DealerNotificationsUtils.isForEmailNotification("GNV", "RML_CLASS", DealerNotificationsUtils.Role.EFA));
        assertTrue(DealerNotificationsUtils.isForEmailNotification("NBDV", "RML_CLASS", DealerNotificationsUtils.Role.EFA));
        assertTrue(DealerNotificationsUtils.isForEmailNotification("NBDV", "RML_CLASS", DealerNotificationsUtils.Role.BANKER));
        assertFalse(DealerNotificationsUtils.isForEmailNotification("GNV", "RCL_", DealerNotificationsUtils.Role.EFA));
        assertFalse(DealerNotificationsUtils.isForEmailNotification("NBDV", "RCL_", DealerNotificationsUtils.Role.EFA));

        assertTrue(DealerNotificationsUtils.isForEmailNotification("NBDV", "RCL_STANDARD", DealerNotificationsUtils.Role.EFA));
        assertTrue(DealerNotificationsUtils.isForEmailNotification("NBDV", "RCL_STANDARD", DealerNotificationsUtils.Role.BANKER));
        assertTrue(DealerNotificationsUtils.isForEmailNotification("NBDV", "RCL_REFI", DealerNotificationsUtils.Role.EFA));
        assertTrue(DealerNotificationsUtils.isForEmailNotification("NBDV", "RCL_REFI", DealerNotificationsUtils.Role.BANKER));
        assertFalse(DealerNotificationsUtils.isForEmailNotification("NBDV", "RCL_XXX", DealerNotificationsUtils.Role.EFA));
        assertFalse(DealerNotificationsUtils.isForEmailNotification("NBDV", "RCL_XXX", DealerNotificationsUtils.Role.BANKER));

        assertTrue(DealerNotificationsUtils.isForEmailNotification("ZDZAM", "RCL_STANDARD", DealerNotificationsUtils.Role.EFA));
        assertTrue(DealerNotificationsUtils.isForEmailNotification("ZDZAM", "RCL_STANDARD", DealerNotificationsUtils.Role.BANKER));
        assertTrue(DealerNotificationsUtils.isForEmailNotification("STR", "RCL_STANDARD", DealerNotificationsUtils.Role.BANKER));
        assertTrue(DealerNotificationsUtils.isForEmailNotification("PRDN", "RCL_REFI", DealerNotificationsUtils.Role.EFA));
        assertTrue(DealerNotificationsUtils.isForEmailNotification("FNLEND", "RCL_REFI", DealerNotificationsUtils.Role.EFA));
        assertTrue(DealerNotificationsUtils.isForEmailNotification("PRDN", "RCL_STANDARD", DealerNotificationsUtils.Role.EFA));
        assertFalse(DealerNotificationsUtils.isForEmailNotification("PRDN", "RCC_", DealerNotificationsUtils.Role.EFA));
        assertTrue(DealerNotificationsUtils.isForEmailNotification("PRDN", "RCL_STANDARD", DealerNotificationsUtils.Role.EFA));
        assertFalse(DealerNotificationsUtils.isForEmailNotification("FNLEND", "RCC_", DealerNotificationsUtils.Role.EFA));

        assertTrue(DealerNotificationsUtils.isForEmailNotification("FNLSCR", "RML_CLASS", DealerNotificationsUtils.Role.EFA));
        assertTrue(DealerNotificationsUtils.isForEmailNotification("FNLSCR", "RML_CLASS", DealerNotificationsUtils.Role.BANKER));
    }

    @Test
    void getEmailNotificationRoles() {
        assertEquals(List.of(DealerNotificationsUtils.Role.BANKER), DealerNotificationsUtils.getEmailNotificationRoles("GNV", "RML_CLASS", DealerNotificationsUtils.Role.EFA));
        assertEquals(List.of(DealerNotificationsUtils.Role.EFA, DealerNotificationsUtils.Role.BANKER), DealerNotificationsUtils.getEmailNotificationRoles("NBDV", "RML_CLASS", DealerNotificationsUtils.Role.EFA));
        assertEquals(List.of(DealerNotificationsUtils.Role.EFA, DealerNotificationsUtils.Role.BANKER), DealerNotificationsUtils.getEmailNotificationRoles("NBDV", "RML_CLASS", DealerNotificationsUtils.Role.EFA));
    }

    @ParameterizedTest
    @CsvSource(value = {
            "********,RCL_STANDARD,NBDV,true",
            "********,RCL_STANDARD,NBDV,true",
            "********,RCL_STANDARD,NBDV,true",
            "********,RCL_STANDARD,NBDV,true",
            "********,RCL_STANDARD,NBDV,false",

            "********,RCL_STANDARD,ZDZAM,true",
            "********,RCL_STANDARD,ZDZAM,true",
            "********,RCL_STANDARD,ZDZAM,true",
            "********,RCL_STANDARD,ZDZAM,true",
            "********,RCL_STANDARD,ZDZAM,false",

            "********,RCL_STANDARD,STR,true",
            "********,RCL_STANDARD,STR,false",
            "********,RCL_STANDARD,STR,false",
            "********,RCL_STANDARD,STR,false",
            "********,RCL_STANDARD,STR,false",

            "********,RCL_STANDARD,PRDN,false",
            "********,RCL_STANDARD,PRDN,true",
            "********,RCL_STANDARD,PRDN,true",
            "********,RCL_STANDARD,PRDN,true",
            "********,RCL_STANDARD,PRDN,false",

            "********,RCL_STANDARD,FNLEND,false",
            "********,RCL_STANDARD,FNLEND,false",
            "********,RCL_STANDARD,FNLEND,false",
            "********,RCL_STANDARD,FNLEND,false",
            "********,RCL_STANDARD,FNLEND,false",

            "********,RCL_REFI,FNLEND,false",
            "********,RCL_REFI,FNLEND,true",
            "********,RCL_REFI,FNLEND,true",
            "********,RCL_REFI,FNLEND,true",
            "********,RCL_REFI,FNLEND,false"
    }, delimiter = ',')
    void getEmailNotificationRoles2(String ftp, String productId, String state, boolean result) {
        assertEquals(result, !DealerNotificationsUtils.getEmailNotificationRoles(state, productId, DealerNotificationsUtils.determineRoleByChannel(ftp)).isEmpty());
    }


    @ParameterizedTest
    @CsvSource(value = {
            "********,RML_,NBDV,false,false",
            "********,RML_,NBDV,false,true",
            "********,RML_,NBDV,false,true",
            "********,RML_,NBDV,false,true",
            "********,RML_,NBDV,true,false",

            "********,RML_,NBDU,false,false",
            "********,RML_,NBDU,false,true",
            "********,RML_,NBDU,false,true",
            "********,RML_,NBDU,false,true",
            "********,RML_,NBDU,true,false",

            "********,RML_,ZDPSCH,false,false",
            "********,RML_,ZDPSCH,false,true",
            "********,RML_,ZDPSCH,false,true",
            "********,RML_,ZDPSCH,false,true",
            "********,RML_,ZDPSCH,true,false",

            "********,RML_,ZDZAM,false,false",
            "********,RML_,ZDZAM,false,true",
            "********,RML_,ZDZAM,false,true",
            "********,RML_,ZDZAM,false,true",
            "********,RML_,ZDZAM,true,false",

            "********,RML_,STR,false,false",
            "********,RML_,STR,false,true",
            "********,RML_,STR,false,true",
            "********,RML_,STR,false,true",
            "********,RML_,STR,true,false",

            "********,RML_,PRDN,true,false",
            "********,RML_,PRDN,true,false",
            "********,RML_,PRDN,true,false",
            "********,RML_,PRDN,true,false",
            "********,RML_,PRDN,true,false",

            "********,RML_,OP,true,false",
            "********,RML_,OP,true,false",
            "********,RML_,OP,true,false",
            "********,RML_,OP,true,false",
            "********,RML_,OP,true,false",

            "********,RML_,FNLEND,true,false",
            "********,RML_,FNLEND,true,false",
            "********,RML_,FNLEND,true,false",
            "********,RML_,FNLEND,true,false",
            "********,RML_,FNLEND,true,false",

            "********,RML_,FNLEND,true,false",
            "********,RML_,FNLEND,true,false",
            "********,RML_,FNLEND,true,false",
            "********,RML_,FNLEND,true,false",
            "********,RML_,FNLEND,true,false",

            "********,RML_,FNLSCR,false,false",
            "********,RML_,FNLSCR,false,true",
            "********,RML_,FNLSCR,false,true",
            "********,RML_,FNLSCR,false,true",
            "********,RML_,FNLSCR,true,false",

            "********,RML_,GNSCR,true,false",
            "********,RML_,GNSCR,true,false",
            "********,RML_,GNSCR,true,false",
            "********,RML_,GNSCR,true,false",
            "********,RML_,GNSCR,true,false"
    }, delimiter = ',')
    void isForEmailNotification(String ftp, String productId, String state, boolean isEmpty, boolean hasEfa) {
        List<DealerNotificationsUtils.Role> roles = DealerNotificationsUtils.getEmailNotificationRoles(state, productId, DealerNotificationsUtils.determineRoleByChannel(ftp));
        assertEquals(isEmpty, roles.isEmpty());
        assertEquals(hasEfa, roles.contains(DealerNotificationsUtils.Role.EFA));
    }
}