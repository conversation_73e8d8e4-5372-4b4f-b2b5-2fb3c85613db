package cz.equa.camapp.utils;

import org.junit.jupiter.api.Test;

import java.io.UnsupportedEncodingException;

import static org.junit.jupiter.api.Assertions.assertEquals;

class FileUtilsWrapperTest {

    @Test
    void normalizeFilePath() throws UnsupportedEncodingException {
        assertEquals("asdf_23-escrzyaieu_ESCRZYAIEU.pdf", FileUtilsWrapper.normalizeFilePath("asdf 23-ěščřžýáíéů ĚŠČŘŽÝÁÍÉŮ.pdf"));
    }
}