package cz.equa.camapp.utils;

import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.engine.delegate.ExecutionListener;
import org.camunda.bpm.engine.impl.core.model.PropertyKey;
import org.camunda.bpm.engine.impl.el.FixedValue;
import org.camunda.bpm.engine.impl.persistence.entity.ExecutionEntity;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class RecorderExecutionListener implements ExecutionListener, Serializable {
	private static final long serialVersionUID = 1L;
	private FixedValue parameter;
	private static final List<RecorderExecutionListener.RecordedEvent> recordedEvents = Collections.synchronizedList(new ArrayList<>());

	public static class RecordedEvent {
		private final String activityId;
		private final String eventName;
		private final String activityName;
		private final String parameter;
		private final String activityInstanceId;
		private final String transitionId;
		private final boolean canceled;
		private final String executionId;

		public RecordedEvent(String activityId, String activityName, String eventName, String parameter, String activityInstanceId, String transitionId, boolean canceled, String executionId) {
			this.activityId = activityId;
			this.activityName = activityName;
			this.parameter = parameter;
			this.eventName = eventName;
			this.activityInstanceId = activityInstanceId;
			this.transitionId = transitionId;
			this.canceled = canceled;
			this.executionId = executionId;
		}

		public String getActivityId() {
			return activityId;
		}

		public String getEventName() {
			return eventName;
		}

		public String getActivityName() {
			return activityName;
		}

		public String getParameter() {
			return parameter;
		}

		public String getActivityInstanceId() {
			return activityInstanceId;
		}

		public String getTransitionId() {
			return transitionId;
		}

		public boolean isCanceled(){
			return canceled;
		}

		public String getExecutionId(){
			return executionId;
		}
	}

	@Override
	public void notify(DelegateExecution execution) throws Exception {
		ExecutionEntity executionCasted = ((ExecutionEntity)execution);
		String parameterValue = null;
		if (parameter != null) {
			parameterValue = (String)parameter.getValue(execution);
		}

		String activityName = null;
		if (executionCasted.getActivity() != null) {
			activityName = executionCasted.getActivity().getProperties().get(new PropertyKey<String>("name"));
		}

		recordedEvents.add( new RecordedEvent(
				executionCasted.getActivityId(),
				activityName,
				execution.getEventName(),
				parameterValue,
				execution.getActivityInstanceId(),
				execution.getCurrentTransitionId(),
				execution.isCanceled(),
				execution.getId()));
	}

	public static void clear() {
		recordedEvents.clear();
	}

	public static List<RecordedEvent> getRecordedEvents() {
		return recordedEvents;
	}

	public static String getString() {
		StringBuilder text = new StringBuilder("Recorded events:\n");
		RecorderExecutionListener.getRecordedEvents().forEach(event->text.append(event.getEventName()).append("\n"));
		return text.toString();
	}
}
