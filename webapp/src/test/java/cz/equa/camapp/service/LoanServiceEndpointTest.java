package cz.equa.camapp.service;

import cz.equa.camapp.lovs.LovProcessTp;
import cz.equa.camapp.manager.LovManager;
import cz.equa.camapp.process.*;
import cz.equa.camapp.rest.service.*;
import cz.equa.camapp.service.model.rest.BusinessKeyEntity;
import cz.equa.camapp.service.model.rest.BusinessKeyQuery;
import cz.equa.camapp.service.paymentorderserviceinternal.model.PaymentDTO;
import cz.equa.camapp.task.TaskManager;
import cz.equa.camunda.loan.*;
import cz.equabank.lovs.LovBankCzCode;
import cz.rb.camapp.kafka.KafkaProducer;
import cz.rb.tif.bai_00001_loan_process_consolidation.Bai00001LoanProcessConsolidation;
import cz.rb.tif.bai_00015_current_account.Bai00015CurrentAccount;
import cz.rb.tif.bai_00018_remote_cash_account.Bai00018RemoteCashAccount;
import cz.rb.tif.bai_00029_consumer_loan.Bai00029ConsumerLoan;
import cz.rb.tif.crd_00008_ent_payment_card.Crd00008EntPaymentCard;
import cz.rb.tif.cus_00041_ent_party.Cus00041EntParty;
import cz.rb.tif.cus_00046_questionnaire.Cus00046Questionnaire;
import cz.rb.tif.cus_00050_contact.Cus00050Contact;
import cz.rb.tif.evn_00015_payment_card.Evn00015PaymentCard;
import cz.rb.tif.ifr_00014_card_processing.Ifr00014CardProcessing;
import cz.rb.tif.ifr_00016_start_service.Ifr00016StartService;
import org.camunda.bpm.engine.test.junit5.ProcessEngineExtension;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.context.junit.jupiter.web.SpringJUnitWebConfig;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.*;

@ExtendWith({ProcessEngineExtension.class, SpringExtension.class})
@SpringJUnitWebConfig(classes = {LoanServiceEndpoint.class})
public class LoanServiceEndpointTest {

    @Autowired
    private LoanServiceEndpoint underTest;
    
    @MockBean
    protected ProcessManager processManager;

    @MockBean
	public ApplicationService applicationService;
    
    @MockBean
    private JavaMailSender javaMailSender;
    
    @MockBean
    private LovManager lovManager;
            
    @MockBean
    private OperationService operationService;
    
    @MockBean
    private LoanApprovalService loanService;

    @MockBean
    private Cus00041EntParty cus00041EntParty;
    
    @MockBean
    private Cus00050Contact cus00050Contact;
    
    @MockBean
    private Cus00046Questionnaire cus00046Questionnaire;

    @MockBean
    private HadPartnersService hadPartnersService;
    
    @MockBean
    private Bai00015CurrentAccount bai00015CurrentAccount;
    
    @MockBean
    private ParametrizationService parametrizationService;

    @MockBean
    private TaskManager taskManager;
    
    @MockBean
    private Ifr00016StartService ifr00016StartService;
    
    @MockBean
    private Bai00018RemoteCashAccount bai00018RemoteCashAccount;
    
    @MockBean
    private Bai00001LoanProcessConsolidation bai00001LoanProcessConsolidation;
    
    @MockBean
    private Crd00008EntPaymentCard crd00008EntPaymentCard;
    
    @MockBean
    private HadInterestChangeService hadInterestChangeService;
    
    @MockBean
    private Bai00029ConsumerLoan bai00029ConsumerLoan;
    
    @MockBean
    private CashloanApplicationService cashloanApplicationService;

    @MockBean
    private Ifr00014CardProcessing ifr00014CardProcessing;
    
    @MockBean
    private Evn00015PaymentCard evn00015PaymentCard;
    
    @MockBean
    private CardProcessingService cardProcessingService;
    
    @MockBean
    private MessageService messageService;
    
    @MockBean
    private PartyService partyService;
    
    @MockBean
    private BusinessKeyService businessKeyService;
    
    @MockBean
    private ProcessDefinitionsCustomService processDefinitionsCustomService;
    
    @MockBean
    private KafkaProducer kafkaProducer;
    
    @Mock
    private BusinessKeyQuery businessKeyQuery;
        
    @Mock
    private List<BusinessKeyEntity> businessKeys;
    
    @Mock
    ProcessStarted processStarted;
    
    @Mock
    ProcessStartResult processStartResult;

    @MockBean
    private PartyApplicationService partyApplicationService;

    @BeforeEach
    public void setup() {
        when(businessKeyService.createBusinessKeyQuery()).thenReturn(businessKeyQuery);
        when(businessKeyQuery.withParameters(any())).thenReturn(businessKeyQuery);
        when(businessKeyQuery.list()).thenReturn(businessKeys);
        when(processManager.startProcess(any(), any(), anyMap())).thenReturn(processStarted);
        when(processManager.startProcess(any(), anyMap())).thenReturn(processStarted);
        when(processStarted.getProcessStartResult()).thenReturn(processStartResult);
    }
    
    @Test
    public void testStartProcessAddCreditCard() {
        final StartProcessRequest request = new StartProcessRequest();
        request.setProcessTp(LovProcessTp.ADD_CREDIT_CARD.getCode());
        request.setClientId("clientId");
        final CommonServiceResponse response = underTest.startProcess(request);
        verify(processManager, times(1)).startProcess(eq("LoanApproval"), any(), anyMap());
        assertEquals(ResponseCode.OK, response.getStatus());
    }

    @Test
    public void testStartProcesReko() {
        final StartProcessRequest request = new StartProcessRequest();
        request.setProcessTp(LovProcessTp.ADD_REKO.getCode());
        request.setClientId("clientId");
        request.setClientId("clientId");
        final CommonServiceResponse response = underTest.startProcess(request);
        verify(processManager, times(1)).startProcess(eq("RekoApproval"), any(), anyMap());
        assertEquals(ResponseCode.OK, response.getStatus());
    }

    @Test
    public void testStartProcess() {
        final StartProcessRequest request = new StartProcessRequest();
        request.setProcessTp(LovProcessTp.ADD_UNSECURED_LOAN.getCode());
        request.setClientId("clientId");
        final CommonServiceResponse response = underTest.startProcess(request);
        verify(processManager, times(1)).startProcess(eq("LoanApproval"), any(), anyMap());
        assertEquals(ResponseCode.OK, response.getStatus());
    }
    
    @Test
    public void testStartProcessForTheSecondTime() {
        when(processStarted.getProcessStartResult()).thenReturn(ProcessStartResult.OK).thenReturn(ProcessStartResult.PROCESS_EXISTS);
        
        final StartProcessRequest request = new StartProcessRequest();
        request.setProcessTp(LovProcessTp.ADD_UNSECURED_LOAN.getCode());
        request.setClientId("clientId");
        final CommonServiceResponse response = underTest.startProcess(request);
        verify(processManager, times(1)).startProcess(eq("LoanApproval"), any(), anyMap());
        assertEquals(ResponseCode.OK, response.getStatus());
        final CommonServiceResponse response2 = underTest.startProcess(request);
        verify(processManager, times(2)).startProcess(eq("LoanApproval"), any(), anyMap());
        assertEquals(ResponseCode.WARNING, response2.getStatus());
    }

    @Test
    public void testStartProcessException() {
        final StartProcessRequest request = new StartProcessRequest();
        request.setProcessTp(LovProcessTp.ADD_UNSECURED_LOAN.getCode());
        request.setClientId("clientId");
        doThrow(new RuntimeException("FAILURE")).when(processManager).startProcess(any(), any(), anyMap());
        final CommonServiceResponse response = underTest.startProcess(request);
        verify(processManager, times(1)).startProcess(eq("LoanApproval"), anyString(), anyMap());
        assertEquals(ResponseCode.NOK, response.getStatus());
    }

    @Test
    public void testStartProcessCreditCardIncr() {
        final StartProcessRequest request = new StartProcessRequest();
        request.setProcessTp(LovProcessTp.CH_CREDIT_CARD_INCR.getCode());
        request.setClientId("clientId");
        final CommonServiceResponse response = underTest.startProcess(request);
        verify(processManager, times(1)).startProcess(eq("LoanApproval"), any(), anyMap());
        assertEquals(ResponseCode.OK, response.getStatus());
    }

    @Test
	public void testStornoProcess() throws MessageCorrelationException {
    	final StornoProcessRequest request = new StornoProcessRequest();
    	request.setApplKey(123L);
        final CommonServiceResponse response = underTest.stornoProcess(request);
		final ArgumentCaptor<Map> captor = ArgumentCaptor.forClass(Map.class);
		verify(processManager, times(1)).notify(eq(Message.STORNO_PROCESS), captor.capture(), any());
		final Map correlation = captor.getValue();
		assertEquals(123L, correlation.get(ProcessVariable.APPLICATION_KEY.getKey()));
	}

    @Test
    public void testManualApprovingDone() throws MessageCorrelationException {
        final ManualApprovingDoneRequest request = new ManualApprovingDoneRequest();
        request.setApplKey(123L);
        final CommonServiceResponse response = underTest.manualApprovingDone(request);
        final ArgumentCaptor<Map> captor = ArgumentCaptor.forClass(Map.class);
        verify(processManager, times(1)).notify(eq(Message.MANUAL_SCORING), captor.capture(), any());
        final Map correlation = captor.getValue();
        assertEquals(123L, correlation.get(ProcessVariable.APPLICATION_KEY.getKey()));
    }

    @Test
    public void testStartApproval() throws MessageCorrelationException {
        final StartApprovalRequest request = new StartApprovalRequest();
        request.setApplKey(123L);
        final CommonServiceResponse response = underTest.startApproval(request);
        final ArgumentCaptor<Map> captor = ArgumentCaptor.forClass(Map.class);
        verify(processManager, times(1)).notify(eq(Message.START_APPROVAL), captor.capture(), any());
        final Map correlation = captor.getValue();
        assertEquals(123L, correlation.get(ProcessVariable.APPLICATION_KEY.getKey()));
    }

    @Test
    public void testNotifyDrawing() throws MessageCorrelationException {
        final NotifyDrawingRequest request = new NotifyDrawingRequest();
        request.setApplKey(123L);
        final CommonServiceResponse response = underTest.notifyDrawing(request);
        final ArgumentCaptor<Map> captor = ArgumentCaptor.forClass(Map.class);
        verify(processManager, times(1)).notify(eq(Message.NOTIFY_DRAWING), captor.capture(), any());
        final Map correlation = captor.getValue();
        assertEquals(123L, correlation.get(ProcessVariable.APPLICATION_KEY.getKey()));
    }


    @Test
    public void testPersonVerifiedBranch() throws MessageCorrelationException {
        final PersonVerifiedRequest request = new PersonVerifiedRequest();
        request.setApplKey(123L);
        final cz.equabank.lovs.LovPersonVerifTp personVerificationType = new cz.equabank.lovs.LovPersonVerifTp();
        personVerificationType.setValue("BRANCH");
        request.setAccOwnerVerifSource(personVerificationType);
        final CommonServiceResponse response = underTest.personVerified(request);
        final ArgumentCaptor<Map> correlationMap = ArgumentCaptor.forClass(Map.class);
        final ArgumentCaptor<Map> variableMap = ArgumentCaptor.forClass(Map.class);
        verify(processManager, times(1)).notify(eq(Message.PERSON_VERIFIED), correlationMap.capture(), variableMap.capture());
        final Map correlation = correlationMap.getValue();
        assertEquals(123L, correlation.get(ProcessVariable.APPLICATION_KEY.getKey()));
        final Map variables = variableMap.getValue();
        assertEquals("BRANCH", variables.get(ProcessVariable.PERSON_VERIFICATION_TYPE.getKey()));
    }

    @Test
    public void testPersonVerifiedTrans() throws MessageCorrelationException {
        final PersonVerifiedRequest request = new PersonVerifiedRequest();
        request.setApplKey(123L);
        request.setPennyTransaction(new PennyTransaction());
        request.getPennyTransaction().setAccountNumber("123");
        request.getPennyTransaction().setAccountPrefix("000");
        request.getPennyTransaction().setAccOwnerName("Dluznik");
        final LovBankCzCode bankCode = new LovBankCzCode();
        bankCode.setValue("0300");
        request.getPennyTransaction().setBankCode(bankCode);
        final cz.equabank.lovs.LovPersonVerifTp personVerificationType = new cz.equabank.lovs.LovPersonVerifTp();
        personVerificationType.setValue(cz.equa.camapp.lovs.LovPersonVerifTp.TRANS.getCode());
        request.setAccOwnerVerifSource(personVerificationType);
        final CommonServiceResponse response = underTest.personVerified(request);
        final ArgumentCaptor<Map> correlationMap = ArgumentCaptor.forClass(Map.class);
        final ArgumentCaptor<Map> variableMap = ArgumentCaptor.forClass(Map.class);
        verify(processManager, times(1)).notify(eq(Message.PERSON_VERIFIED), correlationMap.capture(), variableMap.capture());
        final Map correlation = correlationMap.getValue();
        assertEquals(123L, correlation.get(ProcessVariable.APPLICATION_KEY.getKey()));
        final Map variables = variableMap.getValue();
        assertEquals("TRANS", variables.get(ProcessVariable.PERSON_VERIFICATION_TYPE.getKey()));

        final PaymentDTO payment = (PaymentDTO)variables.get(ProcessVariable.PENNY_TRANSACTION.getKey());
        assertEquals("000-123", payment.getDebtorAccNum());
        assertEquals("Dluznik", payment.getDebtorAccName());
        assertEquals("0300", payment.getDebtorBankCode().getCode());
    }
    
    @Test
    public void testGetLoanApprovalProcess() throws Exception {
        
        List<BusinessKeyEntity> businessKeysInSystem = new ArrayList<>();
        BusinessKeyEntity businessKeyEntity = new BusinessKeyEntity();
        businessKeyEntity.setBusinessKey("LoanApproval-storno-null-**********");
        businessKeysInSystem.add(businessKeyEntity);
        businessKeyEntity = new BusinessKeyEntity();
        businessKeyEntity.setBusinessKey("LoanApproval-90241-**********");
        businessKeysInSystem.add(businessKeyEntity);
        when(businessKeyQuery.list()).thenReturn(businessKeysInSystem);
        
        Optional<BusinessKeyEntity> result = underTest.getLoanApprovalProcess(null, "**********");
        
        assertEquals("LoanApproval-90241-**********", result.get().getBusinessKey());
    }
}
