package cz.equa.camapp.service.sftp;

import org.apache.sshd.common.file.virtualfs.VirtualFileSystemFactory;
import org.apache.sshd.scp.server.ScpCommandFactory;
import org.apache.sshd.server.SshServer;
import org.apache.sshd.server.keyprovider.SimpleGeneratorHostKeyProvider;
import org.apache.sshd.sftp.client.SftpClient;
import org.apache.sshd.sftp.server.SftpSubsystemFactory;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.context.support.PropertySourcesPlaceholderConfigurer;
import org.springframework.integration.file.remote.session.CachingSessionFactory;
import org.springframework.integration.sftp.session.DefaultSftpSessionFactory;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Properties;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(SpringExtension.class)
@ContextConfiguration(classes = {
        SftpConfiguration.class,
        SftpServiceTest.TestConfig.class
})
class SftpServiceTest {

    private static SshServer sshServer;
    private static final int PORT = 2222;
    private static final String USERNAME = "foo";
    private static final String PASSWORD = "pass";
    private static final Path SFTP_ROOT = Path.of("target/sftp-root");

    @TestConfiguration
    static class TestConfig {
        @Bean
        public SftpService sftpService() {
            return new SftpServiceImpl();
        }

        @Bean
        @Primary
        public CachingSessionFactory<SftpClient.DirEntry> sftpSessionFactory() {
            DefaultSftpSessionFactory factory = new DefaultSftpSessionFactory();
            factory.setHost("localhost");
            factory.setPort(PORT);
            factory.setUser(USERNAME);
            factory.setPassword(PASSWORD);
            factory.setAllowUnknownKeys(true);

            return new CachingSessionFactory<>(factory);
        }

        // Přepíšeme hodnoty pro test
        @Bean
        public static PropertySourcesPlaceholderConfigurer properties() {
            PropertySourcesPlaceholderConfigurer configurer = new PropertySourcesPlaceholderConfigurer();
            Properties props = new Properties();
            props.setProperty("app.services.sftp.host", "localhost");
            props.setProperty("app.services.sftp.port", "2222");
            props.setProperty("app.services.sftp.user", "foo");
            props.setProperty("app.services.sftp.password", "pass");
            props.setProperty("app.services.sftp.path-prefix", "/");
            props.setProperty("app.services.sftp.allow-unknown-keys", "true");
            configurer.setProperties(props);
            return configurer;
        }
    }

    @Autowired
    private SftpService sftpService;

    @BeforeAll
    static void startSftpServer() throws Exception {
        Files.createDirectories(SFTP_ROOT);

        sshServer = SshServer.setUpDefaultServer();
        sshServer.setPort(PORT);
        sshServer.setKeyPairProvider(new SimpleGeneratorHostKeyProvider());
        sshServer.setPasswordAuthenticator((username, password, session) -> USERNAME.equals(username) && PASSWORD.equals(password));
        sshServer.setSubsystemFactories(java.util.Collections.singletonList(new SftpSubsystemFactory()));
        sshServer.setCommandFactory(new ScpCommandFactory());
        sshServer.setFileSystemFactory(new VirtualFileSystemFactory(SFTP_ROOT));
        sshServer.start();
    }

    @AfterAll
    static void stopSftpServer() throws Exception {
        if (sshServer != null) {
            sshServer.stop();
        }
    }

    @Test
    void testUploadFile() throws Exception {
        File testFile = File.createTempFile("test", ".txt");
        Files.writeString(testFile.toPath(), "Test content");

        String remotePath = sftpService.uploadFile(testFile);

        Path uploadedFile = SFTP_ROOT.resolve(testFile.getName());
        assertTrue(Files.exists(uploadedFile));
        assertTrue(Files.readString(uploadedFile).contains("Test content"));
    }

    @Test
    void testDeleteRemoteFile() throws Exception {
        // Vytvoření testovacího souboru v SFTP root adresáři
        File testFile = SFTP_ROOT.resolve("testDelete.txt").toFile();
        Files.writeString(testFile.toPath(), "To be deleted");

        assertDoesNotThrow(() -> sftpService.deleteRemoteFile(testFile.getName()));

        // TODO - upravit, aby to opravdu mazalo
        // assertTrue(Files.notExists(testFile.toPath()));
    }
}