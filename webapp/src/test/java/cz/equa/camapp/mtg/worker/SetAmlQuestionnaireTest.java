package cz.equa.camapp.mtg.worker;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import cz.equa.camapp.rest.service.ApplicationService;
import cz.equa.camapp.service.ServiceException;
import cz.rb.tif.cus_00046_questionnaire.Cus00046Questionnaire;
import org.camunda.bpm.client.task.ExternalTask;
import org.camunda.bpm.client.task.ExternalTaskService;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit.jupiter.web.SpringJUnitWebConfig;

import java.util.Map;

import static cz.equa.camapp.mtg.worker.SetAmlQuestionnaire.QUESTIONNAIRE_TYPE;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@SpringJUnitWebConfig(classes = {SetAmlQuestionnaire.class})
class SetAmlQuestionnaireTest {

    @MockBean
    private ApplicationService applicationService;
    @MockBean
    Cus00046Questionnaire cus00046Questionnaire;
    @Mock
    private ExternalTask externalTask;
    @Mock
    private ExternalTaskService externalTaskService;
    @Autowired
    private SetAmlQuestionnaire underTest;

    private Map<String, Object> getProcessVariables() throws JsonProcessingException {
        String jsonString = """
                {
                    "applicationState": "GNV",
                    "busApplId": "1",
                    "applKey": 1,
                    "partyId": 166665,
                    "subApplKey": 222222201,
                    "loanApplicationOwner": {}},
                    "loanApplicationIncomes": {}},
                    "X-Correlation-Id": "1"
                }""";
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        return objectMapper.readValue(jsonString, new TypeReference<>() {
        });
    }

    @Test
    void executeWorker() throws JsonProcessingException, ServiceException {
        doNothing().when(externalTaskService).complete(any(), any());

        when(externalTask.getAllVariables()).thenReturn(getProcessVariables());
        when(externalTask.getVariable(QUESTIONNAIRE_TYPE)).thenReturn("Long");

        underTest.execute(externalTask, externalTaskService);
        verify(externalTask, times(1)).getVariable(QUESTIONNAIRE_TYPE);
        verify(cus00046Questionnaire, times(1)).createQuestionnaireLong(any(), any(), any());

    }
}