package cz.equa.camapp.mtg.worker;

import com.fasterxml.jackson.core.JsonProcessingException;
import cz.equa.camapp.model.document.GetDocumentListDto;
import cz.equa.camapp.rest.service.ApplicationService;
import cz.equa.camapp.rest.service.DmsService;
import cz.equa.camapp.rest.service.MutOcrService;
import cz.equa.camapp.service.ServiceException;
import cz.equa.camapp.service.applicationservice.model.*;
import cz.equa.camapp.utils.ApplicationUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith({SpringExtension.class})
class ProcessUploadedDocumentsTest {

    @MockBean
    private ApplicationService applicationService;
    @MockBean
    MutOcrService mutOcrService;
    @MockBean
    DmsService dmsService;


    @Test
    void prepareApplicants() throws JsonProcessingException, ServiceException {
        ProcessUploadedDocuments underTest = new ProcessUploadedDocuments(applicationService, mutOcrService, dmsService);
        GetMortApplResponseDTO applResp = getMortApplDTO();

        GetMortApplDTO subAppl = applResp.getMortgageAppls().stream()
                .filter(appl -> appl.getApplId().getBusApplId().equals("2580035956-02"))
                .findFirst().orElseThrow();
        List<PtIncomeMortDTO> incomes = subAppl.getIncomes();
        List<PersonMortGetDTO> persons = applResp.getPersons();
        List<Long> instPtKeys = incomes.stream().map(PtIncomeMortDTO::getAdbInstPtKey).distinct().toList();

        List<String> docTpIds = incomes.stream()
                .filter(inc -> inc.getAdbInstPtKey().equals(instPtKeys.get(0)))
                .flatMap(inc -> inc.getPtIncVerifDocs().stream())
                .map(PtIncomeVerifDocsDTO::getDocTpId)
                .toList();

        GetDocumentListDto dto = new GetDocumentListDto();

        docTpIds.forEach(doc -> {
            var document = new GetDocumentListDto.Document();
            GetDocumentListDto.Document.DocumentDetailFull detailFull = new GetDocumentListDto.Document.DocumentDetailFull();
            detailFull.setDocumentId("ID");
            detailFull.setDocumentName("name");
            detailFull.setDocumentClass("P0001483");

            document.setDocument(detailFull);
            dto.getDocumentList().add(document);
        });


        when(dmsService.getDocumentList(anyList(), anyString())).thenReturn(dto);

        var res = underTest.prepareApplicants(instPtKeys, persons, incomes, "correlationId");
        assertEquals(1, res.size());
        assertEquals(10, res.get(0).getDocuments().size());
    }

    private GetMortApplResponseDTO getMortApplDTO() throws JsonProcessingException {
        return ApplicationUtils.convertJsonToType("""
                {
                    "applId": {
                        "applKey": 4023040,
                        "busApplId": "2580035956"
                    },
                    "mortgageAppls": [
                        {
                            "advisorId": null,
                            "appctAgeRangeId": null,
                            "appctRole": "HYFE_ADVISOR",
                            "applComplPosId": null,
                            "applDate": "2025-01-13T05:37:55Z",
                            "applDateTo": "2025-04-13T23:00:00Z",
                            "applDecisions": [
                            ],
                            "applDsnKey": null,
                            "applId": {
                                "applKey": 4023040,
                                "busApplId": "2580035956"
                            },
                            "applMetadata": [
                            ],
                            "applObligations": [
                            ],
                            "applReason": null,
                            "applStatId": "OP",
                            "applTpId": "NEW_PROD",
                            "applVariants": [
                            ],
                            "authMobSearchFlag": null,
                            "browserInfo": null,
                            "busProdSubTp": "RML_CLAS",
                            "busProdTp": "RML",
                            "ccyId": "CZK",
                            "coappAgeRangeId": null,
                            "complCnlId": null,
                            "contrNum": null,
                            "contrSignAdvisorId": null,
                            "contrSignAdvisorName": null,
                            "contrSignDate": null,
                            "contrSignPosId": null,
                            "distCnlId": "10",
                            "extnApplId": null,
                            "finalRiskClass": null,
                            "fiOperCode": null,
                            "firstTouchPoint": "10338936",
                            "firstTouchPointBnkr": "10338936",
                            "firstTouchPointOwnr": "10338936",
                            "fulfillmentCnlId": null,
                            "hash": "HcDhZFCPeMJgr9EA3LdeHcMMrtXhqfD6XljvyJZwl2VyM//iThQderQ/xLASoaZsmJfs5eg3p3Pi+VrhAzSwaPxkmQ2WRdhGsVCkxRKiqFJ72imyQcaAeTqa7OiN5qYE0e2augpHUZeWP/CmnyTDcUqHuo3wKQ02kZFvBkSKYHw=",
                            "households": [
                            ],
                            "chieldAppls": [
                                {
                                    "chieldApplApplRelTpId": "SUBAPPL",
                                    "chieldApplBusApplId": "2580035956-02",
                                    "chieldApplKey": 4023042,
                                    "chieldApplPrimFlag": true,
                                    "chieldApplStatId": "OP"
                                },
                                {
                                    "chieldApplApplRelTpId": "SUBAPPL",
                                    "chieldApplBusApplId": "2580035956-01",
                                    "chieldApplKey": 4023041,
                                    "chieldApplPrimFlag": true,
                                    "chieldApplStatId": "GNUPL"
                                }
                            ],
                            "incomes": [
                            ],
                            "lastChangeAdvisorId": "10338936",
                            "mortCentrePosId": null,
                            "opportunityId": "54769193",
                            "persons": [
                                {
                                    "adbInstPtKey": 3833675,
                                    "applPtRoleTpId": "APPLICANT",
                                    "apprProcessPartId": null,
                                    "dsnTpId": null,
                                    "extnRejRsnTpDescr": null,
                                    "regRiskBandId": null,
                                    "regRiskClassId": null,
                                    "rejectRsnTpIds": null,
                                    "relationToApplicant": null,
                                    "scgAppctHighestIncFlag": null,
                                    "scgExpsSum": null,
                                    "scgFinalRiskClassId": null,
                                    "scgInstlSum": null,
                                    "scgMortInstlSum": null
                                }
                            ],
                            "posId": "18000000",
                            "preApprOfrId": null,
                            "prntApplBusApplId": null,
                            "prntApplKey": null,
                            "promoCode": null,
                            "registryResult": null,
                            "rejectRsn": null,
                            "rejectRsnTp": null,
                            "telcoQueryAllowedFlag": false,
                            "wrkPlaceId": "b94c1c69-1505-4e8c-95e2-37605af83066"
                        },
                        {
                            "advisorId": null,
                            "appctAgeRangeId": "AGE1836",
                            "appctRole": "HYFE_ADVISOR",
                            "applComplPosId": null,
                            "applDate": "2025-01-13T05:37:55Z",
                            "applDateTo": "2025-04-13T23:00:00Z",
                            "applDecisions": [
                                {
                                    "appctsAge": 25,
                                    "applDsti": 0.085,
                                    "applDti": 1.337,
                                    "apprProcessPartId": "1ST_SCORING",
                                    "crntFlag": true,
                                    "decrExp": null,
                                    "dsnTpId": "INCVERREQ",
                                    "expstSumFinal": 8360,
                                    "expSumFinal": 8360,
                                    "instlSumFinal": 494,
                                    "instlSumMortFinal": 0,
                                    "maxDsti": 0.6,
                                    "maxDti": 99,
                                    "netIncSumFinal": 125000
                                }
                            ],
                            "applDsnKey": "4826501",
                            "applId": {
                                "applKey": 4023042,
                                "busApplId": "2580035956-02"
                            },
                            "applMetadata": [
                            ],
                            "applObligations": [
                            ],
                            "applReason": null,
                            "applStatId": "OP",
                            "applTpId": "SUBAPPL_SEL",
                            "applVariants": [
                            ],
                            "authMobSearchFlag": null,
                            "browserInfo": null,
                            "busProdSubTp": "RML_CLAS",
                            "busProdTp": "RML",
                            "ccyId": "CZK",
                            "coappAgeRangeId": null,
                            "complCnlId": "10",
                            "contrNum": null,
                            "contrSignAdvisorId": null,
                            "contrSignAdvisorName": null,
                            "contrSignDate": null,
                            "contrSignPosId": null,
                            "distCnlId": "10",
                            "extnApplId": null,
                            "finalRiskClass": null,
                            "fiOperCode": null,
                            "firstTouchPoint": "10338936",
                            "firstTouchPointBnkr": "10338936",
                            "firstTouchPointOwnr": "10338936",
                            "fulfillmentCnlId": null,
                            "hash": null,
                            "households": [
                            ],
                            "chieldAppls": [
                                {
                                    "chieldApplApplRelTpId": "CLONE",
                                    "chieldApplBusApplId": "2580035956-01",
                                    "chieldApplKey": 4023041,
                                    "chieldApplPrimFlag": true,
                                    "chieldApplStatId": "GNUPL"
                                },
                                {
                                    "chieldApplApplRelTpId": "GOFR",
                                    "chieldApplBusApplId": "2580035956-01",
                                    "chieldApplKey": 4023041,
                                    "chieldApplPrimFlag": true,
                                    "chieldApplStatId": "GNUPL"
                                },
                                {
                                    "chieldApplApplRelTpId": "SUBAPPL",
                                    "chieldApplBusApplId": "2580035956-02",
                                    "chieldApplKey": 4023042,
                                    "chieldApplPrimFlag": false,
                                    "chieldApplStatId": "OP"
                                }
                            ],
                            "incomes": [
                                {
                                    "adbInstPtKey": 3833675,
                                    "busName": null,
                                    "ccyId": "CZK",
                                    "decrDprAmt": null,
                                    "delvCnlId": "UPLOAD",
                                    "dicNum": null,
                                    "dismissalFlag": null,
                                    "empContrEndDate": null,
                                    "empSince": null,
                                    "emptTpId": null,
                                    "futureIncFlag": false,
                                    "icoNum": null,
                                    "inc": 25000,
                                    "incTpId": "LEASE",
                                    "incVerifRsltId": "DATADONE",
                                    "incVerifSrcId": "STATEMENT3MLV",
                                    "instPtIncKey": 3411143,
                                    "instPtIncVerifKey": 329577,
                                    "invVerifSrcId": "STATEMENT",
                                    "netIncFinal": null,
                                    "netIncVerif": null,
                                    "occCatgId": null,
                                    "ptIncVerifDocs": [
                                        {
                                            "docId": "09f3c3dd83a42411",
                                            "docTpId": "P0001483"
                                        },
                                        {
                                            "docId": "09f3c3dd83a4255b",
                                            "docTpId": "P0001483"
                                        },
                                        {
                                            "docId": "09f3c3dd83a42412",
                                            "docTpId": "P0001483"
                                        },
                                        {
                                            "docId": "09f3c3dd83a42413",
                                            "docTpId": "P0001515"
                                        }
                                    ],
                                    "publSectorActFlag": false,
                                    "realExpAmt": null,
                                    "riskBusBranchFlag": null,
                                    "scgSectorCatgId": null,
                                    "taxExpTpId": null,
                                    "verifTime": "2025-01-13T05:49:05Z"
                                },
                                {
                                    "adbInstPtKey": 3833675,
                                    "busName": "Deutsche Börse Services s.r.o.",
                                    "ccyId": "CZK",
                                    "decrDprAmt": null,
                                    "delvCnlId": "UPLOAD",
                                    "dicNum": null,
                                    "dismissalFlag": false,
                                    "empContrEndDate": null,
                                    "empSince": "2023-01-13",
                                    "emptTpId": "PRMNT",
                                    "futureIncFlag": null,
                                    "icoNum": "27577015",
                                    "inc": 100000,
                                    "incTpId": "EMPLT",
                                    "incVerifRsltId": "DATADONE",
                                    "incVerifSrcId": "STATEMENT6M",
                                    "instPtIncKey": 3411144,
                                    "instPtIncVerifKey": 329576,
                                    "invVerifSrcId": null,
                                    "netIncFinal": null,
                                    "netIncVerif": null,
                                    "occCatgId": "W",
                                    "ptIncVerifDocs": [
                                        {
                                            "docId": "09f3c3dd83a42420",
                                            "docTpId": "P0001483"
                                        },
                                        {
                                            "docId": "09f3c3dd83a42421",
                                            "docTpId": "P0001483"
                                        },
                                        {
                                            "docId": "09f3c3dd83a4240f",
                                            "docTpId": "P0001483"
                                        },
                                        {
                                            "docId": "09f3c3dd83a42559",
                                            "docTpId": "P0001483"
                                        },
                                        {
                                            "docId": "09f3c3dd83a4255a",
                                            "docTpId": "P0001483"
                                        },
                                        {
                                            "docId": "09f3c3dd83a42410",
                                            "docTpId": "P0001483"
                                        }
                                    ],
                                    "publSectorActFlag": false,
                                    "realExpAmt": null,
                                    "riskBusBranchFlag": null,
                                    "scgSectorCatgId": "4",
                                    "taxExpTpId": null,
                                    "verifTime": "2025-01-13T05:49:05Z"
                                }
                            ],
                            "lastChangeAdvisorId": "10338936",
                            "mortCentrePosId": "RBZLI",
                            "opportunityId": "54769195",
                            "persons": [
                                {
                                    "adbInstPtKey": 3833675,
                                    "applPtRoleTpId": "APPLICANT",
                                    "apprProcessPartId": "1ST_SCORING",
                                    "dsnTpId": "APPROVE",
                                    "extnRejRsnTpDescr": null,
                                    "regRiskBandId": "0",
                                    "regRiskClassId": null,
                                    "rejectRsnTpIds": null,
                                    "relationToApplicant": null,
                                    "scgAppctHighestIncFlag": true,
                                    "scgExpsSum": 6542,
                                    "scgFinalRiskClassId": null,
                                    "scgInstlSum": 494,
                                    "scgMortInstlSum": 0
                                }
                            ],
                            "posId": null,
                            "preApprOfrId": null,
                            "prntApplBusApplId": null,
                            "prntApplKey": null,
                            "promoCode": null,
                            "registryResult": "0",
                            "rejectRsn": null,
                            "rejectRsnTp": null,
                            "telcoQueryAllowedFlag": false,
                            "wrkPlaceId": null
                        },
                        {
                            "advisorId": null,
                            "appctAgeRangeId": "AGE1836",
                            "appctRole": "HYFE_ADVISOR",
                            "applComplPosId": null,
                            "applDate": "2025-01-13T05:37:55Z",
                            "applDateTo": "2025-02-12T23:00:00Z",
                            "applDecisions": [
                                {
                                    "appctsAge": 25,
                                    "applDsti": 0.091,
                                    "applDti": 1.354,
                                    "apprProcessPartId": "RML_GOFR",
                                    "crntFlag": true,
                                    "decrExp": null,
                                    "dsnTpId": "APPROVE",
                                    "expstSumFinal": 8360,
                                    "expSumFinal": 8360,
                                    "instlSumFinal": 1190,
                                    "instlSumMortFinal": 0,
                                    "maxDsti": 0.6,
                                    "maxDti": 99,
                                    "netIncSumFinal": 125000
                                }
                            ],
                            "applDsnKey": "4826500",
                            "applId": {
                                "applKey": 4023041,
                                "busApplId": "2580035956-01"
                            },
                            "applMetadata": [
                            ],
                            "applObligations": [
                            ],
                            "applReason": null,
                            "applStatId": "GNUPL",
                            "applTpId": "SUBAPPL_GOFR",
                            "applVariants": [
                            ],
                            "authMobSearchFlag": null,
                            "browserInfo": null,
                            "busProdSubTp": "RML_CLAS",
                            "busProdTp": "RML",
                            "ccyId": "CZK",
                            "coappAgeRangeId": null,
                            "complCnlId": "10",
                            "contrNum": null,
                            "contrSignAdvisorId": null,
                            "contrSignAdvisorName": null,
                            "contrSignDate": null,
                            "contrSignPosId": null,
                            "distCnlId": "10",
                            "extnApplId": null,
                            "finalRiskClass": null,
                            "fiOperCode": null,
                            "firstTouchPoint": "10338936",
                            "firstTouchPointBnkr": "10338936",
                            "firstTouchPointOwnr": "10338936",
                            "fulfillmentCnlId": null,
                            "hash": null,
                            "households": [
                            ],
                            "chieldAppls": [
                                {
                                    "chieldApplApplRelTpId": "CLONE",
                                    "chieldApplBusApplId": "2580035956-01",
                                    "chieldApplKey": 4023041,
                                    "chieldApplPrimFlag": false,
                                    "chieldApplStatId": "GNUPL"
                                },
                                {
                                    "chieldApplApplRelTpId": "GOFR",
                                    "chieldApplBusApplId": "2580035956-01",
                                    "chieldApplKey": 4023041,
                                    "chieldApplPrimFlag": false,
                                    "chieldApplStatId": "GNUPL"
                                },
                                {
                                    "chieldApplApplRelTpId": "SUBAPPL",
                                    "chieldApplBusApplId": "2580035956-01",
                                    "chieldApplKey": 4023041,
                                    "chieldApplPrimFlag": false,
                                    "chieldApplStatId": "GNUPL"
                                }
                            ],
                            "incomes": [
                                {
                                    "adbInstPtKey": 3833675,
                                    "busName": null,
                                    "ccyId": "CZK",
                                    "decrDprAmt": null,
                                    "delvCnlId": null,
                                    "dicNum": null,
                                    "dismissalFlag": null,
                                    "empContrEndDate": null,
                                    "empSince": null,
                                    "emptTpId": null,
                                    "futureIncFlag": false,
                                    "icoNum": null,
                                    "inc": 100000,
                                    "incTpId": "EMPLT",
                                    "incVerifRsltId": null,
                                    "incVerifSrcId": null,
                                    "instPtIncKey": 3411140,
                                    "instPtIncVerifKey": null,
                                    "invVerifSrcId": null,
                                    "netIncFinal": null,
                                    "netIncVerif": null,
                                    "occCatgId": null,
                                    "ptIncVerifDocs": [
                                    ],
                                    "publSectorActFlag": null,
                                    "realExpAmt": null,
                                    "riskBusBranchFlag": null,
                                    "scgSectorCatgId": null,
                                    "taxExpTpId": null,
                                    "verifTime": null
                                },
                                {
                                    "adbInstPtKey": 3833675,
                                    "busName": null,
                                    "ccyId": "CZK",
                                    "decrDprAmt": null,
                                    "delvCnlId": null,
                                    "dicNum": null,
                                    "dismissalFlag": null,
                                    "empContrEndDate": null,
                                    "empSince": null,
                                    "emptTpId": null,
                                    "futureIncFlag": false,
                                    "icoNum": null,
                                    "inc": 25000,
                                    "incTpId": "LEASE",
                                    "incVerifRsltId": null,
                                    "incVerifSrcId": "STATEMENT",
                                    "instPtIncKey": 3411141,
                                    "instPtIncVerifKey": null,
                                    "invVerifSrcId": "STATEMENT",
                                    "netIncFinal": null,
                                    "netIncVerif": null,
                                    "occCatgId": null,
                                    "ptIncVerifDocs": [
                                    ],
                                    "publSectorActFlag": null,
                                    "realExpAmt": null,
                                    "riskBusBranchFlag": null,
                                    "scgSectorCatgId": null,
                                    "taxExpTpId": null,
                                    "verifTime": null
                                }
                            ],
                            "lastChangeAdvisorId": "10338936",
                            "mortCentrePosId": "RBZLI",
                            "opportunityId": null,
                            "persons": [
                                {
                                    "adbInstPtKey": 3833675,
                                    "applPtRoleTpId": "APPLICANT",
                                    "apprProcessPartId": "RML_GOFR",
                                    "dsnTpId": "APPROVE",
                                    "extnRejRsnTpDescr": null,
                                    "regRiskBandId": null,
                                    "regRiskClassId": null,
                                    "rejectRsnTpIds": null,
                                    "relationToApplicant": null,
                                    "scgAppctHighestIncFlag": true,
                                    "scgExpsSum": 32000,
                                    "scgFinalRiskClassId": null,
                                    "scgInstlSum": 1190,
                                    "scgMortInstlSum": 0
                                }
                            ],
                            "posId": null,
                            "preApprOfrId": null,
                            "prntApplBusApplId": "2580035956",
                            "prntApplKey": 4023040,
                            "promoCode": null,
                            "registryResult": null,
                            "rejectRsn": null,
                            "rejectRsnTp": null,
                            "telcoQueryAllowedFlag": false,
                            "wrkPlaceId": null
                        }
                    ],
                    "persons": [
                        {
                            "adbInstPtKey": 3833675,
                            "appctAge": 25,
                            "applDataSeg": [
                            ],
                            "birthCntryId": "CZ",
                            "birthDate": "1999-04-07",
                            "birthName": "Fridrich",
                            "birthPlace": "Zlín",
                            "busName": null,
                            "cbId": "8962008",
                            "citizenship": "CZ",
                            "crntAddrValidDate": "2020-01-13",
                            "eduStatId": "B",
                            "email": "<EMAIL>",
                            "euFlag": true,
                            "familyName": "Fridrich",
                            "firstName": "Jakub",
                            "genderId": "M",
                            "householdExpSum": null,
                            "housingStatId": "ATPAR",
                            "idCards": [
                                {
                                    "cardId": "209281367",
                                    "cardIssuerCntryId": "CZ",
                                    "cardValidityLimitedFlag": null,
                                    "exprDate": "2028-02-14",
                                    "chngChnlRsn": null,
                                    "idCardAppResultId": null,
                                    "idCardPurpId": "1STID",
                                    "idCardTpId": "6",
                                    "investigationId": null,
                                    "issueDate": "2018-02-14",
                                    "issuer": "Magistrát města Zlín",
                                    "unapprRsn": null
                                }
                            ],
                            "incomeVerifications": [
                            ],
                            "marStatId": "S",
                            "mntdChildCnt": null,
                            "mortExpSum": null,
                            "othExpSum": null,
                            "pep": false,
                            "permAddr": {
                                "addrInstAddrKey": 5737126,
                                "addrSince": null,
                                "cityName": "Zlín",
                                "cntryId": "CZ",
                                "detail": null,
                                "landRegnNum": null,
                                "streetName": "Dřevnická",
                                "streetNum": "377",
                                "zip": "76001"
                            },
                            "phoneNum": "+420605878334",
                            "postalAddr": {
                                "addrInstAddrKey": 5737127,
                                "addrSince": null,
                                "cityName": "Zlín",
                                "cntryId": "CZ",
                                "detail": null,
                                "landRegnNum": null,
                                "streetName": "Dřevnická",
                                "streetNum": "377",
                                "zip": "76001"
                            },
                            "ptCorpDetail": null,
                            "ptStatId": "INCOME_DOCUMENTED",
                            "ptTpId": "FO",
                            "rcNum": "990407/4477",
                            "rsdntFlag": null,
                            "salutId": null,
                            "siebelId": "22145210",
                            "taxDomicile": null,
                            "tin": null,
                            "titleBefore": null,
                            "verifications": [
                            ]
                        }
                    ]
                }""", GetMortApplResponseDTO.class);
    }
}