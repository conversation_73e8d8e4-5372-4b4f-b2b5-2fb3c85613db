package cz.equa.camapp.mtg.worker;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import cz.equa.camapp.rest.service.ApplicationService;
import cz.equa.camapp.rest.service.MutExternalService;
import cz.equa.camapp.service.ServiceException;
import org.camunda.bpm.client.task.ExternalTask;
import org.camunda.bpm.client.task.ExternalTaskService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit.jupiter.web.SpringJUnitWebConfig;

import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@SpringJUnitWebConfig(classes = {UploadHycRealties.class})
class UploadHycRealtiesTest {

    @MockBean
    private ApplicationService applicationService;
    @Mock
    private ExternalTask externalTask;
    @Mock
    private ExternalTaskService externalTaskService;

    private UploadHycRealties underTest;

    private Map<String, Object> getProcessVariables() throws JsonProcessingException {
        String jsonString = """
                {
                    "applicationState": "GNV",
                    "busApplId": "1",
                    "applKey": 1,
                    "clientId": 34445,
                    "subApplKey": 222222201,
                    "X-Correlation-Id": "1",
                    "callApprovalResult": "INCVERREQ"
                }""";
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        return objectMapper.readValue(jsonString, new TypeReference<>() {
        });
    }

    @BeforeEach
    public void setUp() throws ServiceException {
        underTest = new UploadHycRealties(applicationService);
    }

    @Test
    void executeWorker() throws JsonProcessingException, ServiceException {
        // TODO - implement test
        when(externalTask.getAllVariables()).thenReturn(getProcessVariables());

        underTest.execute(externalTask, externalTaskService);

        verify(applicationService, times(1)).uploadHycRealties(any(), any());

    }
}