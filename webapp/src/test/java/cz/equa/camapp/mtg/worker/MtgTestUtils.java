package cz.equa.camapp.mtg.worker;

import cz.equa.camapp.lovs.LovApplPtRoleTp;
import cz.equa.camapp.service.applicationservice.model.*;

import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

public class MtgTestUtils {

    public static GetMortApplResponseDTO getMtgApplication() {
        GetMortApplResponseDTO application = new GetMortApplResponseDTO();
        ApplIdKeyIdDTO applIdKeyIdDTO = new ApplIdKeyIdDTO();
        applIdKeyIdDTO.setApplKey(123L);
        applIdKeyIdDTO.setBusApplId("123");
        application.setApplId(applIdKeyIdDTO);
        List<PersonMortGetDTO> persons = new ArrayList<>();
        PersonMortGetDTO main = new PersonMortGetDTO();
        main.setAdbInstPtKey(123L);
        main.setFirstName("Hlavni");
        main.setFamilyName("Hypotekar");
        persons.add(main);
        PersonMortGetDTO other = new PersonMortGetDTO();
        other.setAdbInstPtKey(20L);
        other.setFirstName("Vedlejsi");
        other.setFamilyName("Hypotekar");
        persons.add(other);
        application.setPersons(persons);
        List<GetMortApplDTO> appls = new ArrayList<>();
        GetMortApplDTO appl = new GetMortApplDTO();
        appl.setApplId(applIdKeyIdDTO);
        List<GetMortApplPersDTO> pers = new ArrayList<>();
        GetMortApplPersDTO p = new GetMortApplPersDTO();
        p.setAdbInstPtKey(123L);
        p.setApplPtRoleTpId(LovApplPtRoleTp.MAIN.getCode());
        pers.add(p);
        appl.setPersons(pers);
        appl.setHash("hash");
        appl.setApplDate(OffsetDateTime.now());
        appl.setApplDateTo(OffsetDateTime.now());
        appls.add(appl);
        application.setMortgageAppls(appls);
        List<MortApplVariantDTO> vars = new LinkedList<>();
        MortApplVariantDTO var = new MortApplVariantDTO();
        vars.add(var);
        appl.setApplVariants(vars);
        return application;
    }
}
