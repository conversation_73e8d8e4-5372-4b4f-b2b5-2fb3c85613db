package cz.equa.camapp.utils;

import cz.equa.camapp.lovs.LovBusProdSubTp;

import java.util.HashMap;
import java.util.Map;

public class LoanUtils {
    public static final String OVD_PRODUCT_PDU = "PDU";
    public static final String OVD_PRODUCT_BR = "BR";

    public static Map<String, String> getProductMapping() {
        final String busProdSubTpRodAutdebit = LovBusProdSubTp.ROD_AUTDEBIT.getCode();
        final String busProdSubTpRodInfre = LovBusProdSubTp.ROD_INFRE.getCode();

        Map<String, String> productMapping = new HashMap<>();
        productMapping.put(busProdSubTpRodAutdebit, OVD_PRODUCT_PDU);
        productMapping.put(busProdSubTpRodInfre, OVD_PRODUCT_BR);

        return productMapping;
    }
}
