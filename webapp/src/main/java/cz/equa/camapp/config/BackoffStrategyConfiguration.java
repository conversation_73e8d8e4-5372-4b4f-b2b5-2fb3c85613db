package cz.equa.camapp.config;

import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.client.backoff.BackoffStrategy;
import org.camunda.bpm.client.task.ExternalTask;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Configuration
@Slf4j
public class BackoffStrategyConfiguration {

    @Value("${backoffstrategy.initTime:500}")
    protected long initTime;
    @Value("${backoffstrategy.factor:2}")
    protected float factor;
    protected int level;
    @Value("${backoffstrategy.maxTime:1000}")
    protected long maxTime;

    @Bean
    public BackoffStrategy backoffStrategy() {
        log.info("BackoffStrategy configuration: initTime={}, factor={}, maxTime={}", initTime, factor, maxTime);
        return new BackoffStrategy() {
            @Override
            public void reconfigure(List<ExternalTask> externalTasks) {
                if (externalTasks.isEmpty()) {
                    level++;
                } else {
                    level = 0;
                }
            }

            @Override
            public long calculateBackoffTime() {
                if (level == 0) {
                    return 0L;
                }
                long backoffTime = (long) (initTime * Math.pow(factor, (level - 1)));
                return Math.min(backoffTime, maxTime);
            }
        };
    }
}