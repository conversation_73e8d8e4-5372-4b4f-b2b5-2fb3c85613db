package cz.equa.camapp.config;

import cz.equa.camapp.process.Message;
import cz.equa.camapp.process.MessageCorrelationException;
import cz.equa.camapp.process.ProcessManager;
import cz.equa.camapp.utils.MDCFields;
import cz.equa.camapp.utils.MDCHelper;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.engine.impl.context.Context;
import org.camunda.bpm.engine.impl.incident.DefaultIncidentHandler;
import org.camunda.bpm.engine.impl.incident.IncidentContext;
import org.camunda.bpm.engine.impl.persistence.entity.ExecutionEntity;
import org.camunda.bpm.engine.impl.persistence.entity.IncidentEntity;
import org.camunda.bpm.engine.runtime.Incident;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class FailedExternalWorkerIncidentHandler extends DefaultIncidentHandler {

    @Autowired
    private IncidentHandlingConfiguration configuration;

    @Autowired
    @Lazy
    private ProcessManager processManager;

    public FailedExternalWorkerIncidentHandler() {
        super(Incident.EXTERNAL_TASK_HANDLER_TYPE);
    }

    @Override
    public Incident handleIncident(IncidentContext context, String message) {
        final ExecutionEntity executionEntity = getExecution(context);
        if (executionEntity != null) {
            MDCHelper.put(MDCFields.PROCESS_ID, executionEntity.getBusinessKey());
            final IncidentHandlingType incidentHandlingType = configuration.getIncidentHandlingType(executionEntity);
            log.info("Incident in instance {} in process {} will be handled as {}", executionEntity.getBusinessKey(), executionEntity.getProcessDefinitionId(), incidentHandlingType);
            switch (incidentHandlingType) {
                case TCHER:
                    processManager.startTcherProcess(executionEntity);
                    break;
                case STORNO:
                    sendStornoMessage(executionEntity);
                    break;
                case NONE:
                    break;
            }
        }
        return createIncident(context, message);
    }

    @Override
    public Incident createIncident(IncidentContext context, String message) {
        IncidentEntity newIncident = IncidentEntity.createAndInsertIncident(getIncidentHandlerType(), context, message);

        if (context.getExecutionId() != null) {
            newIncident.createRecursiveIncidents();
        }

        return newIncident;
    }

    @Override
    public void resolveIncident(IncidentContext context) {
        removeIncident(context, true);
    }

    @Override
    public void deleteIncident(IncidentContext context) {
        removeIncident(context, false);
    }

    @Override
    protected void removeIncident(IncidentContext context, boolean incidentResolved) {
        List<Incident> incidents = Context.getCommandContext().getIncidentManager().findIncidentByConfiguration(context.getConfiguration());

        for (Incident currentIncident : incidents) {
            IncidentEntity incident = (IncidentEntity) currentIncident;
            if (incidentResolved) {
                incident.resolve();
            } else {
                incident.delete();
            }
        }
    }

    private void sendStornoMessage(final ExecutionEntity executionEntity) {
        try {
            processManager.notify(Message.STORNO_PROCESS, executionEntity.getProcessBusinessKey());
        } catch (MessageCorrelationException mce) {
            log.error(mce.getMessage(), mce);
        }
    }

    private ExecutionEntity getExecution(final IncidentContext incidentContext) {
        ExecutionEntity execution = null;
        if (incidentContext.getExecutionId() != null) {
            execution = Context.getCommandContext().getExecutionManager().findExecutionById(incidentContext.getExecutionId());
        }
        return execution;
    }
}
