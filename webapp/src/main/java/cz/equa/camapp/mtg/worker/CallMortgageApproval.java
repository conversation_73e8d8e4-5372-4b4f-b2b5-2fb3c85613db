package cz.equa.camapp.mtg.worker;

import cz.equa.camapp.lovs.LovDsnTp;
import cz.equa.camapp.mtg.MtgAbstractWorker;
import cz.equa.camapp.mtg.ProcessVariables;
import cz.equa.camapp.rest.service.ApplicationService;
import cz.equa.camapp.rest.service.ApprovalService;
import cz.equa.camapp.service.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class CallMortgageApproval extends MtgAbstractWorker {
    public static final String IN_PROCESS_PART = "processPart";
    public static final String DEFAULT_PROCESS_PART = "RML_GOFR";
    public static final String RML_GOFR_PROLONGED = "RML_GOFR_PROLONGED";
    private final ApprovalService approvalService;

    @Autowired
    public CallMortgageApproval(ApplicationService applicationService, ApprovalService approvalService) {
        super(applicationService);
        this.approvalService = approvalService;
    }

    @Override
    public void executeWorker() {
        String processPart = externalTask.getVariable(CallMortgageApproval.IN_PROCESS_PART);
        if (StringUtils.isBlank(processPart)) {
            if (pv.getProlongedOfr() != null && pv.getProlongedOfr().equals(Boolean.TRUE)) {
                processPart = RML_GOFR_PROLONGED;
            }else{
                processPart = DEFAULT_PROCESS_PART;
            }

        }

        loadProcessVariables();
        try {
            LovDsnTp res = approvalService.callMortgageApproval(pv.getApplKey(), pv.getSubApplKey(), processPart, pv.getCorrelationId());
            log.info("callMortgageApproval applKey={} subApplKey={} : result={}", pv.getApplKey(), pv.getSubApplKey(), res.getCode());
            setProcessVariable(ProcessVariables.CALL_APPROVAL_RESULT, res.getCode());
        } catch (ServiceException e) {
            throw new RuntimeException(e);
        }
    }
}
