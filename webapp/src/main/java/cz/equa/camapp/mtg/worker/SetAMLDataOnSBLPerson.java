package cz.equa.camapp.mtg.worker;

import cz.equa.camapp.model.party.UpdatePartyDto;
import cz.equa.camapp.model.update_party.basic_info.BasicInfoDTO;
import cz.equa.camapp.model.update_party.basic_info.PrivatePartyDTO;
import cz.equa.camapp.mtg.MtgAbstractWorker;
import cz.equa.camapp.rest.model.cus.party.GetDetailResultDto;
import cz.equa.camapp.rest.service.ApplicationService;
import cz.equa.camapp.rest.service.PartyService;
import cz.equa.camapp.service.ServiceException;
import cz.equa.camapp.service.applicationservice.model.NewPersonDTO;
import cz.equa.camapp.service.applicationservice.model.PtIncomeListDTO;
import cz.equa.camapp.utils.SetAmlDataUtils;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.client.spring.annotation.ExternalTaskSubscription;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@ExternalTaskSubscription(topicName = "mtgSetAmlDataOnSBLPerson")
public class SetAMLDataOnSBLPerson extends MtgAbstractWorker {

    PartyService partyService;

    @Autowired
    public SetAMLDataOnSBLPerson(ApplicationService applicationService, PartyService partyService) {
        super(applicationService);
        this.partyService = partyService;
    }

    @Override
    public void executeWorker() throws ServiceException {
        loadProcessVariables();
        Boolean clientHasAccount = pv.getClientHasAccount();
        Boolean clientHasAmlQuestionaire = pv.getClientHasAmlQuestionaire();
        GetDetailResultDto getDetailResultDto = pv.getGetDetailResultDto();
        String processTp = pv.getProcessTp();
        PtIncomeListDTO incomes = pv.getLoanApplicationIncomes();
        NewPersonDTO applicationOwner = pv.getLoanApplicationOwner();

        UpdatePartyDto updatePartyDto = new UpdatePartyDto();
        // basic info
        updatePartyDto.setBasicInfo(new BasicInfoDTO());
        updatePartyDto.getBasicInfo().setPartyType(BasicInfoDTO.PARTY_TYPE_PRIVATE);
        updatePartyDto.getBasicInfo().setPrivateParty(new PrivatePartyDTO());
        updatePartyDto.setAdditionalInfo(SetAmlDataUtils.createAdditionalInfo(processTp, incomes, applicationOwner));

        if (((clientHasAccount == null || !clientHasAccount) &&
                (clientHasAmlQuestionaire == null || !clientHasAmlQuestionaire)) ||
                (getDetailResultDto != null && !getDetailResultDto.getPerson().getIsClient() && !getDetailResultDto.getPerson().getIsEligible())) {
            partyService.update(pv.getClientId(), updatePartyDto.getBasicInfo(), updatePartyDto.getAdditionalInfo(), pv.getCorrelationId());
        }
    }
}
