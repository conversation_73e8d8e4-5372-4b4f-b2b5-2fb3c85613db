package cz.equa.camapp.mtg.worker;

import cz.equa.camapp.mtg.MtgAbstractWorker;
import cz.equa.camapp.mtg.ProcessVariables;
import cz.equa.camapp.rest.service.ApplicationService;
import cz.equa.camapp.service.ServiceException;
import cz.equa.camapp.service.applicationservice.model.GetMortApplDTO;
import cz.equa.camapp.service.applicationservice.model.GetMortgageDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.client.spring.annotation.ExternalTaskSubscription;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@ExternalTaskSubscription(topicName = "mtgCheckFTPCode", variableNames = {
        ProcessVariables.APPL_KEY, ProcessVariables.BUS_APPL_ID, ProcessVariables.APPLICATION_STATE,
        ProcessVariables.PROCESS_STATE, ProcessVariables.CORRELATION_ID})
public class CheckFTPCode extends MtgAbstractWorker {
    public static final String FTP_FAKE_CLIENT = "99999990";

    @Autowired
    public CheckFTPCode(ApplicationService applicationService) {
        super(applicationService);
    }

    @Override
    public void executeWorker() throws ServiceException {
        loadProcessVariables();
        GetMortApplDTO getMortApplDTO = getMortApplDTO(pv.getBusApplId(), pv.getCorrelationId());
        setProcessVariable(ProcessVariables.IS_FAKE_CLIENT, StringUtils.equalsIgnoreCase(FTP_FAKE_CLIENT, getFtp(getMortApplDTO)));
        setProcessVariable(ProcessVariables.PRODUCT_ID, getMortApplDTO.getBusProdSubTp());
    }

    private GetMortApplDTO getMortApplDTO(final String applId, String correlationId) throws ServiceException {
        log.info("get mortgage application {}", applId);
        GetMortgageDTO getMortgageDTO = new GetMortgageDTO();
        getMortgageDTO.setApplFlag(true);
        return applicationService.getMortgageAppl(null, applId, getMortgageDTO, correlationId).getMortgageAppls().stream()
                .filter(appl -> appl.getApplId().getBusApplId().equals(applId))
                .findFirst().orElseThrow();
    }

    private String getFtp(final GetMortApplDTO getMortApplDTO) {
        return getMortApplDTO.getFirstTouchPoint();
    }
}
