package cz.equa.camapp.mtg.worker;

import cz.equa.camapp.mtg.MtgAbstractWorker;
import cz.equa.camapp.mtg.ProcessVariables;
import cz.equa.camapp.rest.model.bai.account.GetListResDTO;
import cz.equa.camapp.rest.service.AccountService;
import cz.equa.camapp.rest.service.ApplicationService;
import cz.equa.camapp.service.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.client.spring.annotation.ExternalTaskSubscription;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@ExternalTaskSubscription(topicName = "mtgClientHasAccount", variableNames = {
        ProcessVariables.APPL_KEY, ProcessVariables.BUS_APPL_ID, ProcessVariables.APPLICATION_STATE,
        ProcessVariables.PROCESS_STATE, ProcessVariables.CORRELATION_ID,
        ProcessVariables.CLIENT_ID})
public class ClientHasAccount extends MtgAbstractWorker {

    AccountService accountService;

    @Autowired
    public ClientHasAccount(ApplicationService applicationService, AccountService accountService) {
        super(applicationService);
        this.accountService = accountService;
    }

    @Override
    public void executeWorker() throws ServiceException {
        loadProcessVariables();
        String clientId = pv.getClientId();

        GetListResDTO getListResDTO = accountService.getList(clientId,
                null,
                pv.getCorrelationId(),
                false, true, true);
        log.info("Does Client {} own accounts size: {}", clientId, getListResDTO.getBankAccount().size());

        if (getListResDTO.getBankAccount().isEmpty()) {
            setProcessVariable(ProcessVariables.CLIENT_HAS_ACCOUNT, Boolean.FALSE);
        } else {
            setProcessVariable(ProcessVariables.CLIENT_HAS_ACCOUNT, Boolean.TRUE);
        }
    }
}
