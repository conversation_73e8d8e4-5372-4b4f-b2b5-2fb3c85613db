package cz.equa.camapp.mtg.worker;

import cz.equa.camapp.lovs.LovIncTp;
import cz.equa.camapp.model.document.GetDocumentListDto;
import cz.equa.camapp.mtg.MtgAbstractWorker;
import cz.equa.camapp.rest.service.ApplicationService;
import cz.equa.camapp.rest.service.DmsService;
import cz.equa.camapp.rest.service.MutOcrService;
import cz.equa.camapp.service.ServiceException;
import cz.equa.camapp.service.applicationservice.model.*;
import cz.rb.ocr.mut.model.DMSDocumentModel;
import cz.rb.ocr.mut.model.IncomeModel;
import cz.rb.ocr.mut.model.MultiDmsDocumentRequestModel;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.client.spring.annotation.ExternalTaskSubscription;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
@ExternalTaskSubscription(topicName = "mtgProcessUploadedDocuments", includeExtensionProperties = true)
public class ProcessUploadedDocuments extends MtgAbstractWorker {

    private final MutOcrService mutOcrService;
    private final DmsService dmsService;

    public ProcessUploadedDocuments(ApplicationService applicationService, MutOcrService mutOcrService, DmsService dmsService) {
        super(applicationService);
        this.mutOcrService = mutOcrService;
        this.dmsService = dmsService;
    }

    @Override
    public void executeWorker() throws ServiceException {
        loadProcessVariables();

        String busApplId = pv.getBusApplId();
        String busSubApplId = pv.getBusSubApplId();
        String correlationId = pv.getCorrelationId();

        // prepare data
        GetMortgageDTO getMortgageDTO = new GetMortgageDTO();
        getMortgageDTO.setApplFlag(true);
        getMortgageDTO.setPersonsFlag(true);
        getMortgageDTO.setIncomeFlag(true);
        GetMortApplResponseDTO applResp = applicationService.getMortgageAppl(null, busApplId, getMortgageDTO, correlationId);
        List<PersonMortGetDTO> persons = applResp.getPersons();
        GetMortApplDTO subAppl = applResp.getMortgageAppls().stream()
                .filter(appl -> appl.getApplId().getBusApplId().equals(busSubApplId))
                .findFirst().orElseThrow();
        List<PtIncomeMortDTO> incomes = subAppl.getIncomes();
        List<Long> instPtKeys = incomes.stream().distinct().map(PtIncomeMortDTO::getAdbInstPtKey).toList();

        // call ocr
        mutOcrService.validateStatements(String.valueOf(subAppl.getApplId().getApplKey()), prepareApplicants(instPtKeys, persons, incomes, correlationId), true, pv.getCorrelationId());
    }

    protected List<MultiDmsDocumentRequestModel> prepareApplicants(List<Long> instPtKeys, List<PersonMortGetDTO> persons, List<PtIncomeMortDTO> incomes, String correlationId) {
        List<MultiDmsDocumentRequestModel> result = new ArrayList<>();
        instPtKeys.forEach(instPtKey -> {
            PersonMortGetDTO person = persons.stream().filter(personMortGetDTO -> personMortGetDTO.getAdbInstPtKey().equals(instPtKey)).findFirst().orElseThrow();
            MultiDmsDocumentRequestModel model = new MultiDmsDocumentRequestModel();
            model.setApplicantFirstName(person.getFirstName());
            model.setApplicantLastName(person.getFamilyName());
            model.setApplicantSiebelID(person.getSiebelId());
            model.setApplicantIncomes(prepareIncomes(instPtKey, incomes));

            try {
                model.setDocuments(prepareDocs(instPtKey, incomes, correlationId));
            } catch (ServiceException e) {
                log.error("Error while preparing documents for applicant", e);
            }

            result.add(model);
        });
        return result;
    }

    protected List<IncomeModel> prepareIncomes(Long instPtKey, List<PtIncomeMortDTO> incomes) {
        return incomes.stream().filter(a -> a.getAdbInstPtKey().equals(instPtKey))
                .map(p -> {
                    IncomeModel model = new IncomeModel();
                    model.setIncomeAmt(p.getInc().intValueExact());
                    model.setIncomeName(LovIncTp.fromString(p.getIncTpId()).getDescr());
                    model.setIncomeType(p.getIncTpId());
                    return model;
                })
                .toList();
    }

    protected List<DMSDocumentModel> prepareDocs(Long instPtKey, List<PtIncomeMortDTO> incomes, String correlationId) throws ServiceException {
        List<DMSDocumentModel> result = new ArrayList<>();

        List<String> docIds = incomes.stream()
                .filter(inc -> inc.getAdbInstPtKey().equals(instPtKey))
                .flatMap(inc -> inc.getPtIncVerifDocs().stream())
                .map(PtIncomeVerifDocsDTO::getDocId)
                .toList();

        if (docIds.isEmpty()) {
            return result;
        }

        GetDocumentListDto response = dmsService.getDocumentList(docIds, correlationId);
        response.getDocumentList().forEach(doc -> {
            var documentDetail = doc.getDocument();
            DMSDocumentModel model = new DMSDocumentModel();
            model.setDocId(documentDetail.getDocumentId());
            model.setDocName(documentDetail.getDocumentName());
            model.setDocType(DMSDocumentModel.DocTypeEnum.fromValue(documentDetail.getDocumentClass()));
            result.add(model);
        });

        return result;
    }

    @Override
    protected void saveCurrentApplicationState() {
        // DO NOTHING
    }
}
