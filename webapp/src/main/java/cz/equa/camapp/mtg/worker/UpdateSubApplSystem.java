package cz.equa.camapp.mtg.worker;

import cz.equa.camapp.mtg.MtgAbstractWorker;
import cz.equa.camapp.rest.service.ApplicationService;
import cz.equa.camapp.service.ServiceException;
import cz.equa.camapp.service.applicationservice.model.ApplIdKeyIdDTO;
import cz.equa.camapp.service.applicationservice.model.SetMortApplRequestDTO;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.client.spring.annotation.ExternalTaskSubscription;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@ExternalTaskSubscription(topicName = "mtgUpdateSubApplSystem")
public class UpdateSubApplSystem extends MtgAbstractWorker {

    public static final String APPROVAL_SYSTEM = "approvalSystem";

    public UpdateSubApplSystem(ApplicationService applicationService) {
        super(applicationService);
    }

    @Override
    public void executeWorker() throws ServiceException {
        //TODO: namapovat do atributu appr_sys_id
        //String approvalSystem = externalTask.getVariable(APPROVAL_SYSTEM);
        SetMortApplRequestDTO setMortApplRequestDTO = new SetMortApplRequestDTO();
        ApplIdKeyIdDTO applId = new ApplIdKeyIdDTO();
        applId.setApplKey(pv.getApplKey());
        applId.setBusApplId(pv.getBusApplId());
        setMortApplRequestDTO.setApplId(applId);

        applicationService.setMortgageAppl(setMortApplRequestDTO, pv.getCorrelationId());
    }
}
