package cz.equa.camapp.mtg.worker;

import cz.equa.camapp.mtg.MtgAbstractWorker;
import cz.equa.camapp.mtg.ProcessVariables;
import cz.equa.camapp.process.Message;
import cz.equa.camapp.process.ProcessVariable;
import cz.equa.camapp.rest.service.ApplicationService;
import cz.equa.camapp.service.ServiceException;
import cz.equa.camapp.service.applicationservice.model.GetMortApplDTO;
import cz.equa.camapp.utils.DealerNotificationsUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.client.spring.annotation.ExternalTaskSubscription;
import org.camunda.bpm.engine.RuntimeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;

import static cz.equa.camapp.utils.DealerNotificationsUtils.determineRoleByChannel;

@Component("mtgTriggerEmailNotification")
@Slf4j
@ExternalTaskSubscription(topicName = "mtgTriggerEmailNotification")
public class TriggerEmailNotification extends MtgAbstractWorker {

    public static final String APPL_ID = "applId";
    public static final String PROCESS_STATE = "processState";
    public static final String STATE = "state";

    private final RuntimeService runtimeService;

    @Autowired
    public TriggerEmailNotification(ApplicationService applicationService, RuntimeService runtimeService) {
        super(applicationService);
        this.runtimeService = runtimeService;
    }

    @Override
    public void executeWorker() throws ServiceException {
        String applId = externalTask.getVariable(TriggerEmailNotification.APPL_ID);
        String state = externalTask.getVariable(TriggerEmailNotification.STATE);
        if (state == null) {
            state = externalTask.getVariable(TriggerEmailNotification.PROCESS_STATE);
        }
        handleEmailNotification(state, pv.getMainAppl(), applId);
    }

    private void handleEmailNotification(String state, GetMortApplDTO mortApplDto, String applId) {
        List<DealerNotificationsUtils.Role> roles = DealerNotificationsUtils.getEmailNotificationRoles(state, mortApplDto.getBusProdSubTp(), determineRoleByChannel(mortApplDto.getFirstTouchPoint()));

        if (StringUtils.isNotBlank(mortApplDto.getFirstTouchPointOwnr())) {
            DealerNotificationsUtils.Role ownerRole = DealerNotificationsUtils.determineRoleByChannel(mortApplDto.getFirstTouchPointOwnr());
            if (ownerRole != null && roles.contains(ownerRole)) {
                triggerEmailNotification(state, applId, mortApplDto.getFirstTouchPointOwnr(), mortApplDto.getFirstTouchPointOwnr());
            }
        }

        if (StringUtils.isNotBlank(mortApplDto.getFirstTouchPointBnkr())) {
            DealerNotificationsUtils.Role bankerRole = DealerNotificationsUtils.determineRoleByChannel(mortApplDto.getFirstTouchPointBnkr());
            if (bankerRole != null && !mortApplDto.getFirstTouchPointBnkr().equals(mortApplDto.getFirstTouchPointOwnr()) && roles.contains(bankerRole)) {
                triggerEmailNotification(state, applId, mortApplDto.getFirstTouchPointOwnr(), mortApplDto.getFirstTouchPointBnkr());
            }
        }
    }

    private void triggerEmailNotification(String state, String busApplId, String ftp, String recipientId) {
        HashMap<String, Object> pvMap = new HashMap<>();
        pvMap.put("businessKey", externalTask.getBusinessKey());
        pvMap.put(ProcessVariables.APPLICATION_STATE, state);
        pvMap.put(ProcessVariables.BUS_APPL_ID, busApplId);
        pvMap.put(ProcessVariables.APPL_KEY, pv.getApplKey());
        pvMap.put(ProcessVariables.PRODUCT_ID, StringUtils.isNotBlank(pv.getProductId()) ? pv.getProductId() : "RML_");
        pvMap.put(ProcessVariables.PROCESS_TP, pv.getProcessTp());
        pvMap.put(ProcessVariable.FTP.getKey(), ftp);
        pvMap.put(ProcessVariable.RECIPIENT_ID.getKey(), recipientId);
        runtimeService.correlateMessage(Message.SEND_EMAIL_NOTIFICATION.getValue(), new HashMap<>(), pvMap);
        log.debug("sent message {} for busApplId {}", Message.SEND_EMAIL_NOTIFICATION.getValue(), busApplId);
    }
}
