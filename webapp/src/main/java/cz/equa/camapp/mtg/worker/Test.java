package cz.equa.camapp.mtg.worker;

import cz.equa.camapp.mtg.MtgAbstractWorker;
import cz.equa.camapp.rest.service.ApplicationService;
import cz.equa.camapp.service.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.client.spring.annotation.ExternalTaskSubscription;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@ExternalTaskSubscription(topicName = "test")
public class Test extends MtgAbstractWorker {

    public Test(ApplicationService applicationService) {
        super(applicationService);
    }

    @Override
    public void executeWorker() throws ServiceException, InterruptedException {
        log.info(pv.getClientId());
        log.info("wait 6s");
        Thread.sleep(6000L);
    }
}
