package cz.equa.camapp.mtg.worker;

import cz.equa.camapp.mtg.MtgAbstractWorker;
import cz.equa.camapp.mtg.ProcessVariables;
import cz.equa.camapp.rest.service.ApplicationService;
import cz.equa.camapp.service.applicationservice.model.GetMortApplDTO;
import cz.equa.camapp.service.applicationservice.model.GetMortApplResponseDTO;
import cz.equa.camapp.service.applicationservice.model.GetMortgageDTO;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.client.spring.annotation.ExternalTaskSubscription;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
@Slf4j
@ExternalTaskSubscription(topicName = "mtgPutOriginalStatesToProcessVariable", variableNames = {
        ProcessVariables.APPL_KEY, ProcessVariables.BUS_APPL_ID, ProcessVariables.APPLICATION_STATE,
        ProcessVariables.PROCESS_STATE, ProcessVariables.CORRELATION_ID,
        ProcessVariables.ORIGINAL_STATE, ProcessVariables.ORIGINAL_STATE_SUBAPPL_SEL})
public class PutOriginalStatesToPv extends MtgAbstractWorker {

    @Autowired
    public PutOriginalStatesToPv(ApplicationService applicationService) {
        super(applicationService);
    }

    @Override
    public void executeWorker() throws Exception {
        loadProcessVariables();
        String busApplId = pv.getBusApplId();
        GetMortgageDTO getMortgageDTO = new GetMortgageDTO();
        getMortgageDTO.setApplFlag(true);
        getMortgageDTO.setPersonsFlag(true);
        GetMortApplResponseDTO mortAppl = applicationService.getMortgageAppl(null, busApplId, getMortgageDTO, pv.getCorrelationId());
        Optional<GetMortApplDTO> subApplSel = mortAppl.getMortgageAppls().stream()
                .filter(appl -> appl.getApplTpId().equals("SUBAPPL_SEL"))
                .findFirst();
        if (subApplSel.isEmpty()) {
            setProcessVariable(ProcessVariables.EXISTS_SUBAPPL_SEL, Boolean.FALSE);
        }else {
            setProcessVariable(ProcessVariables.EXISTS_SUBAPPL_SEL, Boolean.TRUE);
            setProcessVariable(ProcessVariables.ORIGINAL_STATE_SUBAPPL_SEL, subApplSel.get().getApplStatId());
        }

        setProcessVariable(ProcessVariables.ORIGINAL_STATE, pv.getApplicationState());
    }

}
