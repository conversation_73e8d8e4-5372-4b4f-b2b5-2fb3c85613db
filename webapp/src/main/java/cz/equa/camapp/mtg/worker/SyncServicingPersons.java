package cz.equa.camapp.mtg.worker;

import cz.equa.camapp.mtg.MtgAbstractWorker;
import cz.equa.camapp.rest.model.cus.party.AddressDto;
import cz.equa.camapp.rest.model.cus.party.EmailDto;
import cz.equa.camapp.rest.model.cus.party.GetDetailResultDto;
import cz.equa.camapp.rest.model.cus.party.PhoneDto;
import cz.equa.camapp.rest.service.ApplicationMapper;
import cz.equa.camapp.rest.service.ApplicationService;
import cz.equa.camapp.rest.service.PartyService;
import cz.equa.camapp.service.ServiceException;
import cz.equa.camapp.service.applicationservice.model.MortgageServicingDTO;
import cz.equa.camapp.service.applicationservice.model.PersonMortgageServicingDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.client.spring.annotation.ExternalTaskSubscription;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

@Component
@Slf4j
@ExternalTaskSubscription(topicName = "mtgSyncServicingPersons")
public class SyncServicingPersons extends MtgAbstractWorker {

    private final PartyService partyService;

    public SyncServicingPersons(ApplicationService applicationService, PartyService partyService) {
        super(applicationService);
        this.partyService = partyService;
    }

    @Override
    public void executeWorker() throws ServiceException {
        loadProcessVariables();
        MortgageServicingDTO mortgageServicingDTO = ApplicationMapper.INSTANCE.esbToDto(applicationService.getMortgageServicing(null, pv.getBusApplId(), true, pv.getCorrelationId(), null));
        if (syncPartyFromSBLToMortgageServicing(mortgageServicingDTO)) {
            applicationService.setMortgageServicing(ApplicationMapper.INSTANCE.dtoToEsb(mortgageServicingDTO), pv.getCorrelationId(), null);
        }
    }

    protected AddressDto getPostalAddress(List<AddressDto> resultDto) {
        return resultDto.stream().filter(addressDto -> Boolean.TRUE.equals(addressDto.getIsPreferred()) && !Boolean.TRUE.equals(addressDto.getUndeliverableFlag())).findFirst().orElse(null);
    }

    protected EmailDto getEmail(List<EmailDto> resultDto) {
        return resultDto.stream().filter(emailDto -> Boolean.TRUE.equals(emailDto.getPreferred()) && !Boolean.TRUE.equals(emailDto.getUndeliverableFlag())).findFirst().orElse(null);
    }

    protected PhoneDto getPhone(List<PhoneDto> resultDto) {
        return resultDto.stream().filter(phoneDto -> Boolean.TRUE.equals(phoneDto.getPreferred()) && !Boolean.TRUE.equals(phoneDto.getUndeliverableFlag())).findFirst().orElse(null);
    }

    /**
     * Sync data from SBL to mortgage servicing DTO.
     */
    protected boolean syncPerson(GetDetailResultDto resultDto, PersonMortgageServicingDTO personMortgageServicingDTO) {
        boolean modified = false;
        if (StringUtils.isBlank(personMortgageServicingDTO.getFirstName()) && StringUtils.isNotBlank(resultDto.getPerson().getFirstName())) {
            personMortgageServicingDTO.setFirstName(resultDto.getPerson().getFirstName());
            modified = true;
        }
        if (StringUtils.isBlank(personMortgageServicingDTO.getFamilyName()) && StringUtils.isNotBlank(resultDto.getPerson().getLastName())) {
            personMortgageServicingDTO.setFamilyName(resultDto.getPerson().getLastName());
            modified = true;
        }
        if (StringUtils.isBlank(personMortgageServicingDTO.getRcNum()) && StringUtils.isNotBlank(resultDto.getPerson().getBirthCode())) {
            personMortgageServicingDTO.setRcNum(resultDto.getPerson().getBirthCode());
            modified = true;
        }
        if (personMortgageServicingDTO.getBirthDate() == null && resultDto.getPerson().getBirthDate() != null) {
            personMortgageServicingDTO.setBirthDate(resultDto.getPerson().getBirthDate());
            modified = true;
        }
        if (StringUtils.isBlank(personMortgageServicingDTO.getGenderId()) && StringUtils.isNotBlank(resultDto.getPerson().getGender())) {
            personMortgageServicingDTO.setGenderId(resultDto.getPerson().getGender());
            modified = true;
        }
        if (StringUtils.isBlank(personMortgageServicingDTO.getGenderId()) && StringUtils.isNotBlank(resultDto.getPerson().getGender())) {
            personMortgageServicingDTO.setGenderId(resultDto.getPerson().getGender());
            modified = true;
        }

        PhoneDto phoneDto = getPhone(resultDto.getContacts().getPhone());
        if (StringUtils.isBlank(personMortgageServicingDTO.getPhoneNum()) && phoneDto != null && StringUtils.isNotBlank(phoneDto.getPhone())) {
            personMortgageServicingDTO.setPhoneNum(phoneDto.getPhone());
            modified = true;
        }

        EmailDto emailDto = getEmail(resultDto.getContacts().getEmail());
        if (StringUtils.isBlank(personMortgageServicingDTO.getEmail()) && emailDto != null && StringUtils.isNotBlank(emailDto.getEmail())) {
            personMortgageServicingDTO.setEmail(emailDto.getEmail());
            modified = true;
        }

        AddressDto postalAddress = getPostalAddress(resultDto.getContacts().getAddress());
        if (postalAddress != null && personMortgageServicingDTO.getPostalAddr() != null) {
            if (StringUtils.isBlank(personMortgageServicingDTO.getPostalAddr().getStreetName()) && StringUtils.isNotBlank(postalAddress.getStreet())) {
                personMortgageServicingDTO.getPostalAddr().setStreetName(postalAddress.getStreet());
                modified = true;
            }
            if (StringUtils.isBlank(personMortgageServicingDTO.getPostalAddr().getStreetNum()) && StringUtils.isNotBlank(postalAddress.getStreetNumber())) {
                personMortgageServicingDTO.getPostalAddr().setStreetNum(postalAddress.getStreetNumber());
                modified = true;
            }
            if (StringUtils.isBlank(personMortgageServicingDTO.getPostalAddr().getCityName()) && StringUtils.isNotBlank(postalAddress.getCity())) {
                personMortgageServicingDTO.getPostalAddr().setCityName(postalAddress.getCity());
                modified = true;
            }
            if (StringUtils.isBlank(personMortgageServicingDTO.getPostalAddr().getZip()) && StringUtils.isNotBlank(postalAddress.getPostalCode())) {
                personMortgageServicingDTO.getPostalAddr().setZip(postalAddress.getPostalCode());
                modified = true;
            }
            if (StringUtils.isBlank(personMortgageServicingDTO.getPostalAddr().getCntryId()) && StringUtils.isNotBlank(postalAddress.getCountry())) {
                personMortgageServicingDTO.getPostalAddr().setCntryId(postalAddress.getCountry());
                modified = true;
            }
        }
        return modified;
    }

    protected boolean syncPartyFromSBLToMortgageServicing(final MortgageServicingDTO mortgageServicingDTO) {
        AtomicBoolean modified = new AtomicBoolean(false);
        if (mortgageServicingDTO == null) {
            return modified.get();
        }
        mortgageServicingDTO.getPersons().forEach(person -> {
            try {
                GetDetailResultDto getDetailResultDto = partyService.getDetail(person.getSiebelId(), pv.getCorrelationId());
                modified.set(modified.get() || syncPerson(getDetailResultDto, person));
            } catch (ServiceException e) {
                throw new RuntimeException(e);
            }
        });
        return modified.get();
    }
}