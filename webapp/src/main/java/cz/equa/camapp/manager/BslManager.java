package cz.equa.camapp.manager;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import cz.equa.camapp.rest.service.ParametrizationService;
import cz.equa.camapp.service.ServiceException;
import cz.rb.las.parametrization.model.BSLProductType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class BslManager {

    private final ParametrizationService parametrizationService;

    private final Cache<String, BSLProductType> bslCache = Caffeine.newBuilder()
            .expireAfterWrite(24, TimeUnit.HOURS)
            .build();

    public BslManager(ParametrizationService parametrizationService) {
        this.parametrizationService = parametrizationService;
    }

    public BSLProductType getCachedParamsBSL(LocalDate applDate, String busProdSubTpId) {
        if (busProdSubTpId == null) {
            throw new IllegalArgumentException("busProdSubTpId cannot be null");
        }
        String cacheKey = applDate.toString() + "-" + busProdSubTpId;
        log.debug("Cache key: {}", cacheKey);
        BSLProductType response =  bslCache.get(cacheKey, key -> {
            String correlationId = UUID.randomUUID().toString();
            try {
                BSLProductType responseService = parametrizationService.getParamsBSL(applDate, busProdSubTpId, correlationId);
                log.debug("Fetch from service: {}", responseService);
                return responseService;
            } catch (ServiceException e) {
                log.error("Failed to fetch BSL data from parametrizationService.getParamsBSL", e);
                throw new RuntimeException("Failed to fetch BSL data from parametrizationService.getParamsBSL", e);
            }
        });
        log.debug("Fetch from cache: {}", response);
        return response;
    }
}
