package cz.equa.camapp.logging;

import jakarta.servlet.ReadListener;
import jakarta.servlet.ServletInputStream;

import java.io.IOException;
import java.util.Arrays;

public class LoggingServletInputStreamWrapper extends ServletInputStream {

	private final ServletInputStream inputStream;

	private byte[] cache;
	private int read = 0;

	private final int maxPeekLength;

	public LoggingServletInputStreamWrapper(ServletInputStream inputStream, int maxPeekLength) {
		this.inputStream = inputStream;
		this.maxPeekLength = maxPeekLength;
	}

	/**
	 * returns array of bytes from the beginning of wrapped inputStream.
	 *
	 * @return precaches bytes and returns them.
	 * @throws IOException
	 */
	public byte[] peek() throws IOException {
		if (this.cache == null) {
			byte[] tmp = new byte[maxPeekLength];
			int readFromPeak = this.inputStream.read(tmp);
			this.cache = Arrays.copyOf(tmp, readFromPeak == -1 ? 0 : readFromPeak);
		}
		return Arrays.copyOf(this.cache, this.cache.length);
	}

	@Override
	public int read() throws IOException {
		if ((this.cache != null) && (this.cache.length > this.read)) {
			return this.cache[this.read++] & 0xFF;
		}
		return this.inputStream.read();
	}

	@Override
	public void close() throws IOException {
		super.close();
		this.cache = null;
		this.inputStream.close();
	}

	@Override
	public int available() throws IOException {
		if ((this.cache != null) && (this.cache.length > this.read)) {
			return (this.cache.length - this.read) + this.inputStream.available();
		}
		return inputStream.available();
	}

	@Override
	public boolean isFinished() {
		return inputStream.isFinished();
	}

	@Override
	public boolean isReady() {
		return (this.cache.length - this.read) > 0 || inputStream.isReady();
	}

	@Override
	public void setReadListener(ReadListener readListener) {
		throw new UnsupportedOperationException("Not supported yet.");

	}

}
