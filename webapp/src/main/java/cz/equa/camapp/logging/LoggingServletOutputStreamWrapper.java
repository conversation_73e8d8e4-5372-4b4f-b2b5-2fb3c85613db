package cz.equa.camapp.logging;

import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.WriteListener;

import java.io.IOException;

public class LoggingServletOutputStreamWrapper extends ServletOutputStream {
	private final ServletOutputStream outputStream;
	private final byte[] cache;
	private int written = 0;

	public LoggingServletOutputStreamWrapper(ServletOutputStream outputStream, int maxLength) {
		this.outputStream = outputStream;
		this.cache = new byte[maxLength];
	}

	@Override
	public void write(int b) throws IOException {
		outputStream.write(b);
		if (written < cache.length) {
			cache[written++] = (byte) b;
		}
	}

	@Override
	public void write(byte[] b, int off, int len) throws IOException {
		for (int i = 0; i < len; i++) {
			write(b[off + i]);
		}
	}

	@Override
	public void write(byte[] b) throws IOException {
		this.write(b, 0, b.length);
	}

	@Override
	public void close() throws IOException {
		super.close();
		outputStream.close();
	}

	@Override
	public void flush() throws IOException {
		super.flush();
		outputStream.flush();
	}

	public byte[] getContent() {
		int size = written;
		byte[] result = new byte[size];
		if (size >= 0) {
			System.arraycopy(cache, 0, result, 0, size);
		}
		return result;
	}

	@Override
	public boolean isReady() {
		return outputStream.isReady();
	}

	@Override
	public void setWriteListener(WriteListener writeListener) {
		outputStream.setWriteListener(writeListener);
	}

}
