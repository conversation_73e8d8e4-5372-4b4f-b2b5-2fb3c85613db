package cz.equa.camapp.service.model.rest;

import org.camunda.bpm.engine.impl.AbstractQuery;
import org.camunda.bpm.engine.impl.interceptor.CommandExecutor;

public abstract class ProcessDefinitionsCustomQuery extends AbstractQuery<ProcessDefinitionsCustomQuery, ProcessDefinitionCustomEntity> {

    private String[] withStates;
    
	public ProcessDefinitionsCustomQuery(CommandExecutor commandExecutor) {
		super(commandExecutor);
	}
    
    public ProcessDefinitionsCustomQuery withParameters(final String[] withStates) {
        this.withStates = withStates;
		return this;
	}
    
    public String[] getWithStates() {
        return withStates;
    }
}
