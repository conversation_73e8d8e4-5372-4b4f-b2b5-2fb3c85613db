package cz.equa.camapp.service;

import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.StringWriter;
import java.util.Map;

@Service
public class FreeMarkerTemplateService {

    private final Configuration freemarkerConfig;

    @Autowired
    public FreeMarkerTemplateService(@Qualifier("freemarkerConfiguration") Configuration freemarkerConfig) {
        // TODO vybrat freemarkerConfiguration nebo freemarkerServletWebConfiguration
        this.freemarkerConfig = freemarkerConfig;
    }

    public String renderTemplate(String templateName, Map<String, Object> model) throws IOException, TemplateException {
        Template template = freemarkerConfig.getTemplate(templateName);
        try (StringWriter stringWriter = new StringWriter()) {
            template.process(model, stringWriter);
            return stringWriter.toString();
        }
    }
}
