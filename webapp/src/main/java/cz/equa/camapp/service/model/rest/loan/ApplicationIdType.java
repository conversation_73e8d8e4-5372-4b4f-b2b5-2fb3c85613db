package cz.equa.camapp.service.model.rest.loan;

import java.util.Arrays;
import java.util.Locale;

public enum ApplicationIdType {
    BUSAPPLID("BUSAPPLID"),
    APPLKEY("APPLKEY");

    private final String value;

    ApplicationIdType(final String value) {
        this.value = value.toUpperCase(Locale.ROOT);
    }

    public String getValue() {
        return value;
    }

    public static ApplicationIdType fromString(final String value) {
        return Arrays.stream(values()).filter(e -> e.getValue().equalsIgnoreCase(value)).findFirst().orElse(null);
    }
}