package cz.equa.camapp.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import cz.equa.camapp.lovs.LovTicketTp;
import cz.equa.camapp.service.model.rest.CompleteTaskByNameRequest;
import cz.equa.camapp.task.TaskManager;
import cz.equa.camunda.task.CompletionType;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static cz.equa.camapp.lovs.LovTicketTp.TKT_CUST_ACC_CLOSING_4EYES;

@Path("/task-service")
@Component
@Slf4j
public class TaskServiceRest {
    
    @Autowired
	private TaskManager taskManager;
    
    @POST
	@Path("/complete-task-by-name")
    @Consumes(MediaType.APPLICATION_JSON)
	@Produces(MediaType.APPLICATION_JSON)
	public String completeTaskByName(Object request) {
		final CompleteTaskByNameRequest completeTaskByNameRequest = new ObjectMapper().convertValue(request, CompleteTaskByNameRequest.class);
        try {
            taskManager.completeTaskByName(completeTaskByNameRequest.getTaskName(), completeTaskByNameRequest.getBusApplId(), completeTaskByNameRequest.getResult(), completeTaskByNameRequest.getImpersonatedUser());
			return "{ result: OK }";
		} catch (final Exception ex) {
			log.error("Completing task failed", ex);
			return "{ result: ERROR }";
		}
	}
    
    @GET
	@Path("/task-resolution-data/{busApplId}/{taskName}")
	@Produces(MediaType.APPLICATION_JSON)
	public Object getTaskResolutionData(@PathParam("busApplId") String busApplId, @PathParam("taskName") String taskName) {
        try {
            LovTicketTp ticketType = taskManager.getTaskType(taskName, busApplId);
            if (TKT_CUST_ACC_CLOSING_4EYES.equals(ticketType)) {
                return "[\"verified\", \"canceled\"]";
            } else {
                return CompletionType.values();
            }
		} catch (final Exception ex) {
			log.error("Completing task failed", ex);
			return "[ \"ERROR\" ]";
		}
	}
}
