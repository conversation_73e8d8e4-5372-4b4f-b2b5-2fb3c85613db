package cz.equa.camapp.service.model.rest;

import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.engine.impl.AbstractQuery;
import org.camunda.bpm.engine.impl.interceptor.CommandExecutor;

public abstract class BusinessKeyIncidentsQuery extends AbstractQuery<BusinessKeyIncidentsQuery, BusinessKeyIncidentsEntity> {
    private final String datasourceUrl;
    private String businessKeyLike;
    private boolean activeOnly;
    private String sortOrder; //date/businessKey
    private String sortBy; //asc/desc

    public BusinessKeyIncidentsQuery(CommandExecutor commandExecutor, String datasourceUrl) {
        super(commandExecutor);
        this.datasourceUrl = datasourceUrl;
    }

    public BusinessKeyIncidentsQuery withParameters(final BusinessKeyIncidentsQueryDto businessKeyIncidentsQueryDto) {
        this.activeOnly = businessKeyIncidentsQueryDto.isActiveOnly();
        this.businessKeyLike = addWildcardsForLike(businessKeyIncidentsQueryDto.getBusinessKeyLike());
        this.sortOrder = StringUtils.isEmpty(businessKeyIncidentsQueryDto.getSortOrder()) ? "ASC" : businessKeyIncidentsQueryDto.getSortOrder();
        this.sortBy = StringUtils.isEmpty(businessKeyIncidentsQueryDto.getSortBy()) ? "businessKey" : businessKeyIncidentsQueryDto.getSortBy();
        return this;
    }

    public String getBusinessKeyLike() {
        return businessKeyLike;
    }

    public boolean isActiveOnly() {
        return activeOnly;
    }

    public String getSortOrder() {
        return sortOrder;
    }

    public String getSortBy() {
        return sortBy;
    }

    //Warning - does not protext against sql injections. For internal use only
    private static String addWildcardsForLike(String searchString) {
        if (StringUtils.isEmpty(searchString)) {
            return "%";
        } else if (searchString.contains("%")) {
            return searchString;
        } else {
            return "%" + searchString + "%";
        }
    }

}
