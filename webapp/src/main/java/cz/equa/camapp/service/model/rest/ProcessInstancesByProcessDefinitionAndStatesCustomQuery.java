package cz.equa.camapp.service.model.rest;

import org.camunda.bpm.engine.impl.AbstractQuery;
import org.camunda.bpm.engine.impl.interceptor.CommandExecutor;


public abstract class ProcessInstancesByProcessDefinitionAndStatesCustomQuery extends AbstractQuery<ProcessInstancesByProcessDefinitionAndStatesCustomQuery,
                                                                                                    String> {
    
    private String[] withStates;
    private String processDefinitionId;
    
	public ProcessInstancesByProcessDefinitionAndStatesCustomQuery(CommandExecutor commandExecutor) {
		super(commandExecutor);
	}
    
    public ProcessInstancesByProcessDefinitionAndStatesCustomQuery withStates(final String[] withStates) {
        this.withStates = withStates;
		return this;
	}
    
    public ProcessInstancesByProcessDefinitionAndStatesCustomQuery withProcessDefinitionId(final String processDefinitionId) {
        this.processDefinitionId = processDefinitionId;
		return this;
	}
    
    public String[] getWithStates() {
        return withStates;
    }

    public String getProcessDefinitionId() {
        return processDefinitionId;
    }
}
