package cz.equa.camapp;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import cz.equa.camapp.mtg.ProcessVariables;
import cz.equa.camapp.utils.MDCFields;
import cz.equa.camapp.utils.MDCHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.camunda.bpm.client.task.ExternalTask;
import org.camunda.bpm.client.task.ExternalTaskHandler;
import org.camunda.bpm.client.task.ExternalTaskService;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Abstract class for parsing process variables and logging.
 */
@Component
@Slf4j
public abstract class AbstractWorker implements ExternalTaskHandler {

    public static final String RETRY_CONFIG = "retry_config";

    private static final ThreadLocal<Map<String, Object>> processVariablesToSave = new ThreadLocal<>();
    protected ExternalTask externalTask;
    protected ExternalTaskService externalTaskService;
    protected WorkerRetryBehaviour retryBehaviour;

    protected Object getAllProcessVariables(Class clazz) {
        return getAllProcessVariables(clazz, externalTask);
    }

    protected Object getAllProcessVariables(Class clazz, ExternalTask externalTask) {
        return new ObjectMapper()
                .registerModule(new JavaTimeModule())
                .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
                .convertValue(externalTask.getAllVariables(), clazz);
    }

    protected WorkerRetryBehaviour getRetryBehaviour() {
        if (retryBehaviour == null) {
            retryBehaviour = new WorkerRetryBehaviour(externalTask.getExtensionProperty(RETRY_CONFIG));
        }
        return retryBehaviour;
    }

    /**
     * Define logic for retry count.
     *
     * @return retry count
     */
    protected Integer getRetries() {
        return getRetryBehaviour().getNextRetries(externalTask.getRetries());
    }

    /**
     * Returns timeout for next retry.
     * Override this method to define next retry based on actual state or exception type.
     *
     * @param retries retries count
     * @return timeout period
     */
    protected Long getNextTimeout(Integer retries) {
        return getRetryBehaviour().getNextTimeout(retries);
    }

    protected void setProcessVariable(String processVariable, Object value) {
        processVariablesToSave.get().put(processVariable, value);
    }

    @Override
    public void execute(ExternalTask externalTask, ExternalTaskService externalTaskService) {
        long start = System.currentTimeMillis();
        MDCHelper.put(MDCFields.PROCESS_ID, externalTask.getBusinessKey());
        MDCHelper.put(MDCFields.TASK, externalTask.getId());
        MDCHelper.put(MDCFields.CORRELATION_ID, externalTask.getVariable(ProcessVariables.CORRELATION_ID));
        log.info("Start worker {}", this.getClass().getName());

        processVariablesToSave.set(new HashMap<>());
        this.externalTask = externalTask;
        this.externalTaskService = externalTaskService;

        try {
            preExecuteWorker();
            executeWorker();
            externalTaskService.complete(externalTask, processVariablesToSave.get());
            log.info("task {} from topic {} completed", externalTask.getId(), externalTask.getTopicName());
        } catch (Exception e) {
            // retry handling
            log.error("failed process task {} with message: {}", externalTask.getId(), e.getStackTrace());
            Integer retries = this.getRetries();
            Long timeout = this.getNextTimeout(retries);
            log.info("remaining retries {} next timeout: {}", retries, timeout);
            externalTaskService.handleFailure(externalTask, e.getMessage(), ExceptionUtils.getStackTrace(e), retries, timeout);
        } finally {
            processVariablesToSave.remove();
        }
        log.info("End worker {} duration {}", this.getClass().getName(), System.currentTimeMillis() - start);
        MDCHelper.clear();
    }

    /**
     * Worker business logic
     */
    public abstract void executeWorker() throws Exception;

    protected abstract void preExecuteWorker();
}
