package cz.equa.camapp.reko.worker;

import cz.equa.camapp.delegate.loan.PrepareDocumentUpdateDelegate;
import cz.equa.camapp.model.GeneratedDocumentsDTO;
import cz.equa.camapp.reko.ProcessVariables;
import cz.equa.camapp.reko.RekoAbstractWorker;
import cz.equa.camapp.rest.service.ApplicationService;
import cz.equa.camapp.service.ServiceException;
import cz.equa.camapp.service.applicationservice.model.BuildingApplVariantDTO;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.client.spring.annotation.ExternalTaskSubscription;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@ExternalTaskSubscription(topicName = "rekoPrepareDocumentUpdate")
@Slf4j
public class PrepareDocumentUpdate extends RekoAbstractWorker {

    @Autowired
    public PrepareDocumentUpdate(ApplicationService applicationService) {
        super(applicationService);
    }

    @Override
    public void executeWorker() throws ServiceException {
        GeneratedDocumentsDTO generatedDocuments = pv.getGeneratedDocuments();
        PrepareDocumentUpdateDelegate.resetIsSuccess(generatedDocuments);
        setProcessVariable(ProcessVariables.GENERATED_DOCUMENTS, generatedDocuments);

        var insuranceSelected = pv.getInsuranceSelected();
        if (Boolean.TRUE.equals(insuranceSelected)) {
            BuildingApplVariantDTO selVariant = pv.getBuildingLoanApplVariants().getVariant("SEL");
            if (selVariant != null && selVariant.getSurcharges() != null) {
                boolean insuranceRemoved =  selVariant.getSurcharges().stream().noneMatch(
                        surcharge -> "DIS_PPI".equals(surcharge.getSurchrgTpId()));
                setProcessVariable(ProcessVariables.INSURANCE_REMOVED, insuranceRemoved);
            }
        }
    }
}
