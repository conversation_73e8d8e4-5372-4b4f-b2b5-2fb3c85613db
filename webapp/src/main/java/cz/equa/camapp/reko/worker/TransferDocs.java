package cz.equa.camapp.reko.worker;

import cz.equa.camapp.lovs.AbstractLov;
import cz.equa.camapp.lovs.LovDocTp;
import cz.equa.camapp.lovs.LovLang;
import cz.equa.camapp.manager.LovManager;
import cz.equa.camapp.model.document.GetDocumentListDto;
import cz.equa.camapp.model.document.GetDocumentUrlDto;
import cz.equa.camapp.reko.ProcessVariables;
import cz.equa.camapp.reko.RekoAbstractWorker;
import cz.equa.camapp.rest.service.ApplicationService;
import cz.equa.camapp.rest.service.DmsService;
import cz.equa.camapp.service.ServiceException;
import cz.equa.camapp.service.applicationservice.model.BuildingApplVariantDTO;
import cz.equa.camapp.service.applicationservice.model.BuildingApplVariantsDTO;
import cz.equa.camapp.service.applicationservice.model.BuildingLoanApplicationDTO;
import cz.equa.camapp.service.applicationservice.model.BuildingLoanApplicationOwnerDTO;
import cz.equa.camapp.service.product_service.LovTranslationsItemDTO;
import cz.equa.camapp.service.sftp.SftpService;
import cz.equa.camapp.utils.FileUtilsWrapper;
import cz.rb.services.entityservice.document.create_4_0.OpCreate40;
import cz.rb.tif.dms_00008_ent_document.Dms00008EntDocumentCreate;
import cz.rb.tif.dms_00008_ent_document.DocumentType;
import cz.rb.tif.ims.handler.ApiException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.camunda.bpm.client.spring.annotation.ExternalTaskSubscription;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.math.BigInteger;
import java.net.URI;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
@ExternalTaskSubscription(topicName = "rekoTransferDocs")
public class TransferDocs extends RekoAbstractWorker {
    private final DmsService dmsService;
    private final SftpService sftpService;
    private final Dms00008EntDocumentCreate dms00008EntDocumentCreate;
    private final LovManager lovManager;

    protected static final String SYSTEM_CSA = "CSA";

    @Value("${loan.document.download.file.path}")
    private String filePath;

    @Value("${loan.document.download.connect.timeout:9600}")
    private int connectTimeout;

    @Value("${loan.document.download.read.timeout:9600}")
    private int readTimeout;

    @Autowired
    public TransferDocs(ApplicationService applicationService, DmsService dmsService, SftpService sftpService, Dms00008EntDocumentCreate dms00008EntDocumentCreate, LovManager lovManager) {
        super(applicationService);
        this.dmsService = dmsService;
        this.sftpService = sftpService;
        this.dms00008EntDocumentCreate = dms00008EntDocumentCreate;
        this.lovManager = lovManager;
        log.info("filePath: {}, connectTimeout: {}, readTimeout: {}", filePath, connectTimeout, readTimeout);
    }

    @Override
    public void executeWorker() throws ServiceException {
        loadProcessVariables();
        getDocuments(pv.getBusApplId(), pv.getCorrelationId()).forEach(document -> {
            try {
                if (isForProcessing(document, pv)) {
                    processDocument(document, pv.getCorrelationId());
                }
            } catch (Exception ex) {
                log.error("Error downloading or uploading document: {}", ex.getMessage());
                throw new RuntimeException(new ServiceException("Error processing document: " + ex.getMessage(), ex));
            }
        });
    }

    protected List<GetDocumentListDto.Document> getDocuments(String busApplId, String correlationId) throws ServiceException {
        GetDocumentListDto response = dmsService.getDocumentListByApplicationId(busApplId, correlationId);
        return response.getDocumentList();
    }

    protected boolean isForProcessing(GetDocumentListDto.Document document, ProcessVariables pv) {
        if (document == null || document.getDocument() == null) {
            return false;
        }
        switch (document.getDocument().getDocumentClass()) {
            case "P0001644" -> {
                var selVariant = pv.getBuildingLoanApplVariants().getVariant("SEL");
                if (selVariant == null || selVariant.getSurcharges() == null) {
                    return false;
                }
                return selVariant.getSurcharges().stream().anyMatch(surcharge -> "DIS_PPI".equals(surcharge.getSurchrgTpId()));
            }
            default -> {
                return true;
            }
        }
    }

    protected void processDocument(GetDocumentListDto.Document document, String correlationId) throws ServiceException, ApiException, IOException {
        String documentId = document.getDocument().getDocumentId();
        String documentName = document.getDocument().getDocumentName();
        String documentClass = document.getDocument().getDocumentClass();
        log.info("Processing document: {} (ID: {}, Class: {})", documentName, documentId, documentClass);

        GetDocumentUrlDto documentUrlDto = dmsService.getDocumentUrl(documentId, correlationId);
        log.info("DocumentUrl: {}, filename: {})", documentUrlDto.getDocumentUrl(), documentUrlDto.getFilename());

        File downloadedFile = null;
        String remoteUri;
        try {
            downloadedFile = downloadDocumentToFile(documentId, documentUrlDto);
            remoteUri = sftpService.uploadFile(downloadedFile);
            log.info("Document {} successfully uploaded to SFTP, location: {}", documentName, remoteUri);
        } finally {
            if (downloadedFile != null && downloadedFile.exists()) {
                boolean deleted = downloadedFile.delete();
                if (!deleted) {
                    log.warn("Failed to delete temporary file: {}", downloadedFile.getAbsolutePath());
                }
            }
        }

        OpCreate40 createRequest = new OpCreate40();
        createRequest.setDocumentClass("arcis");
        createRequest.setDocumentName(documentName);
        createRequest.setDocumentSource("RSTS");
        createRequest.setExternalDocumentId(document.getDocument().getDocumentId());
        createRequest.setObjectOfCreation(createDocumentContentWithUri(remoteUri, document, pv.getBusApplId(), pv.getBuildingLoanApplication(), pv.getBuildingLoanApplVariants(), pv.getLoanApplicationOwner()));
        String createdDocumentId = dms00008EntDocumentCreate.create(createRequest);

        if (downloadedFile != null) {
            deleteFileFromSftp(downloadedFile.getName());
        }

        log.info("Document successfully created in ECM with ID: {}", createdDocumentId);
    }

    private OpCreate40.ObjectOfCreation createDocumentContentWithUri(String remoteUri, GetDocumentListDto.Document document, String busApplId, BuildingLoanApplicationDTO buildingLoanAppl, BuildingApplVariantsDTO variants, BuildingLoanApplicationOwnerDTO owner) {
        try {
            OpCreate40.ObjectOfCreation.Content.RSTSContent rstsContent = new OpCreate40.ObjectOfCreation.Content.RSTSContent();
            rstsContent.setUri(remoteUri);
            rstsContent.setGarant("RBCI");
            rstsContent.setMimeType(determineMimeTypeForDocument(document.getDocument().getDocumentClass()));
            rstsContent.setCorrelationId(BigInteger.ONE);
            rstsContent.setDocStatus("V");

            OpCreate40.ObjectOfCreation objectOfCreation = new OpCreate40.ObjectOfCreation();
            OpCreate40.ObjectOfCreation.Content content = new OpCreate40.ObjectOfCreation.Content();
            content.setFormat(document.getDocument().getContentType());
            content.setRSTSContent(rstsContent);
            objectOfCreation.setContent(content);
            objectOfCreation.setMetadata(createMetadata(busApplId, document.getDocument().getDocumentClass(), buildingLoanAppl, variants, owner));

            return objectOfCreation;
        } catch (Exception e) {
            log.error("Error creating document content with URI: {}", e.getMessage(), e);
            return null;
        }
    }

    private String determineMimeTypeForDocument(String documentClass) {
        try {
            DocumentType documentType = DocumentType.valueOf(documentClass);
            return switch (documentType.getFormat()) {
                case "pdf" -> "application/pdf";
                case "jpg", "jpeg" -> "image/jpeg";
                case "png" -> "image/png";
                default -> "application/pdf";
            };
        } catch (IllegalArgumentException e) {
            return "application/pdf";
        }
    }

    protected OpCreate40.ObjectOfCreation.Metadata createMetadata(String busApplId, String documentClass, BuildingLoanApplicationDTO buildingLoanAppl, BuildingApplVariantsDTO variants, BuildingLoanApplicationOwnerDTO owner) {
        OpCreate40.ObjectOfCreation.Metadata metadata = new OpCreate40.ObjectOfCreation.Metadata();
        OpCreate40.ObjectOfCreation.Metadata.Dynamic dynamic = new OpCreate40.ObjectOfCreation.Metadata.Dynamic();

        try {
            if (buildingLoanAppl != null && buildingLoanAppl.getContrNumBs() != null) {
                dynamic.getItem().add(createDynamicItem(
                        "mainTable", "attrs_rsts1", "CONTRACT_NO", buildingLoanAppl.getContrNumBs()
                ));
            }

            if ("P0001540".equals(documentClass)) {
                dynamic.getItem().add(createDynamicItem(
                        "nextTable", "attrs_rsts1_doplnek", "DRUH_PODPISU", "A"
                ));
            }

            if (owner != null && owner.getRcNum() != null) {
                dynamic.getItem().add(createDynamicItem(
                        "nextTable", "attrs_rsts1_doplnek", "RC", owner.getRcNum().replace("/", "")
                ));
            }

            dynamic.getItem().add(createDynamicItem(
                    "nextTable", "attrs_rsts1_doplnek", "typ_komunikace", "e"
            ));

            LovDocTp docTp = getLovByRefId(LovDocTp.class, documentClass);
            if (docTp != null) {
                dynamic.getItem().add(createDynamicItem(
                        "nextTable", "attrs_rsts1_doplnek", "doc_type_new", docTp.getCode()
                ));

                var catgId = docTp.getLovDetail().get("DOC_CATG_ID");
                if (catgId != null) {
                    dynamic.getItem().add(createDynamicItem(
                            "nextTable", "attrs_rsts1_doplnek", "annotation_new", catgId
                    ));
                }

                var catgSubId = docTp.getLovDetail().get("DOC_SUB_CATG_ID");
                if (catgSubId != null) {
                    dynamic.getItem().add(createDynamicItem(
                            "nextTable", "attrs_rsts1_doplnek", "subannotation_new", catgSubId
                    ));
                }
            }

            dynamic.getItem().add(createDynamicItem(
                    "nextTable", "attrs_rsts1_doplnek", "cuz", busApplId
            ));

            if (variants != null && variants.getVariants() != null) {
                BuildingApplVariantDTO aprVariant = variants.getVariant("APR");
                if (aprVariant != null) {
                    dynamic.getItem().add(createDynamicItem(
                            "nextTable", "attrs_rsts1_doplnek", "vyse_uveru", aprVariant.getFinaAmt().stripTrailingZeros().toPlainString()
                    ));
                }
            }

            if (buildingLoanAppl != null && buildingLoanAppl.getBusProdSubTp() != null) {
                dynamic.getItem().add(createDynamicItem(
                        "nextTable", "attrs_rsts1_doplnek", "produkt", getTranslatedLovId("BUS_PROD_SUB_TP", buildingLoanAppl.getBusProdSubTp())
                ));
            }

            if (buildingLoanAppl != null && buildingLoanAppl.getContrNumReg() != null) {
                dynamic.getItem().add(createDynamicItem(
                        "nextTable", "attrs_rsts1_doplnek", "registracni_CS", buildingLoanAppl.getContrNumReg()
                ));
            }

            if (owner != null && owner.getRcNum() != null) {
                dynamic.getItem().add(createDynamicItem(
                        "nextTable", "attrs_rsts1_doplnek", "nazev", String.format("%s %s", owner.getFirstName(), owner.getFamilyName()).trim()
                ));
            }

            dynamic.getItem().add(createDynamicItem(
                    "nextTable", "attrs_rsts1_doplnek", "typ_procesu", "F"
            ));

            dynamic.getItem().add(createDynamicItem(
                    "nextTable", "attrs_rsts1_doplnek", "sp_uver", "1"
            ));

        } catch (Exception e) {
            log.error("Error creating document metadata: {}", e.getMessage(), e);
        }

        metadata.setDynamic(dynamic);
        return metadata;
    }

    private OpCreate40.ObjectOfCreation.Metadata.Dynamic.Item createDynamicItem(String group, String groupName, String name, String value) {
        OpCreate40.ObjectOfCreation.Metadata.Dynamic.Item item = new OpCreate40.ObjectOfCreation.Metadata.Dynamic.Item();
        item.setGroup(group);
        item.setGroupName(groupName);
        item.setName(name);
        item.setValue(value);
        return item;
    }

    protected File downloadDocumentToFile(String documentId, GetDocumentUrlDto documentUrlDto) throws IOException {
        if (documentUrlDto.getDocumentUrl().equals("test")) {
            return new File(filePath + "test_file.tmp");
        }
        String encodedPath = filePath + FileUtilsWrapper.normalizeFilePath(documentId + "_" + System.currentTimeMillis());
        File file = new File(encodedPath);
        FileUtils.copyURLToFile(
                URI.create(documentUrlDto.getDocumentUrl()).toURL(),
                file,
                connectTimeout,
                readTimeout);
        return file;
    }

    protected void deleteFileFromSftp(String remoteUri) {
        try {
            sftpService.deleteRemoteFile(remoteUri);
        } catch (Exception e) {
            log.warn("Error deleting file from SFTP: {}", e.getMessage(), e);
        }
    }

    protected <T extends AbstractLov> T getLovByRefId(Class<T> lovClass, String srcId) {
        List<T> output = lovManager.getLovs(lovClass, LovLang.CZE2, Map.of("REF_ID", srcId), SYSTEM_CSA);
        return (output != null && !output.isEmpty()) ? output.getFirst() : null;
    }

    protected String getTranslatedLovId(String lovId, String srcId) throws ServiceException {
        List<LovTranslationsItemDTO> response = lovManager.getLovTranslationFromCache(lovId, srcId, LovLang.CZE2, "CMN", SYSTEM_CSA);
        if (response.get(0) != null) {
            return response.get(0).getTrgId();
        }
        return null;
    }
}
