package cz.equa.camapp.reko.worker;

import cz.equa.camapp.model.GeneratedDocumentsDTO;
import cz.equa.camapp.reko.ProcessVariables;
import cz.equa.camapp.reko.RekoAbstractWorker;
import cz.equa.camapp.rest.service.ApplicationService;
import cz.equa.camapp.rest.service.DmsService;
import cz.equa.camapp.service.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.client.spring.annotation.ExternalTaskSubscription;
import org.springframework.stereotype.Component;


@Component
@ExternalTaskSubscription(topicName = "rekoDeleteDocumentByTemplateId")
@Slf4j
public class DeleteDocumentByTemplateId extends RekoAbstractWorker {

    private static final int RETENTION_DAYS = 30;

    private final DmsService dmsService;

    public DeleteDocumentByTemplateId(ApplicationService applicationService, DmsService dmsService) {
        super(applicationService);
        this.dmsService = dmsService;
    }

    @Override
    public void executeWorker() throws ServiceException {
        loadProcessVariables();

        GeneratedDocumentsDTO docs = pv.getGeneratedDocuments();
        if (docs == null) {
            log.info("No documents to delete.");
            return;
        }

        try {
            dmsService.delete(pv.getGeneratedDocuments().getGeneratedObjectIds().get(pv.getTemplate()),
                    DmsService.DELETE_SCOPE_ALL_VERSIONS,
                    null,
                    RETENTION_DAYS,
                    pv.getCorrelationId());
            pv.getGeneratedDocuments().getGeneratedObjectIds().remove(pv.getTemplate());
            setProcessVariable(ProcessVariables.GENERATED_DOCUMENTS, pv.getGeneratedDocuments());
            log.info("Document templateId {} was successfully deleted.", pv.getTemplate());
        } catch (ServiceException exception) {
            if ((exception.getMessage() != null) && exception.getMessage().contains("EMC8234")) {
                log.info("Error EMC8234 - document not found.");
            } else {
                if ("YES".equals(pv.getSuppressErrorAndContinue())) {
                    log.info("Error suppressed by input variable [suppressErrorAndContinue] = YES - skipped error and continued.");
                } else {
                    throw exception;
                }
            }
        }
    }
}
