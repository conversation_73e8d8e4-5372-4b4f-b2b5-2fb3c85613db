package cz.equa.camapp.reko.worker;

import cz.equa.camapp.reko.RekoAbstractWorker;
import cz.equa.camapp.rest.service.ApplicationService;
import cz.equa.camapp.rest.service.OperationService;
import cz.equa.camapp.service.ServiceException;
import cz.equa.camapp.service.product_service.PersonCLInfoDTO;
import org.camunda.bpm.client.spring.annotation.ExternalTaskSubscription;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

import static cz.equa.camapp.rest.service.OperationService.PARTYID;


@Component("rekoGetPersonInfo")
@ExternalTaskSubscription(topicName = "rekoGetPersonInfo")
public class GetPersonInfo extends RekoAbstractWorker {

    OperationService operationService;

    @Autowired
    public GetPersonInfo(ApplicationService applicationService, OperationService operationService) {
        super(applicationService);
        this.operationService = operationService;
    }

    @Override
    public void executeWorker() throws ServiceException {
        loadProcessVariables();
        pv.setLoanApplicationPersonInfo(getPersonInfo(pv.getBusApplId(), pv.getClientId(), pv.getCorrelationId()));
    }

    private PersonCLInfoDTO getPersonInfo(final String busApplId, final String partyId, final String correlationId) throws ServiceException {
        List<PersonCLInfoDTO> personsInfo = operationService.getPersonInfo(PARTYID, partyId, busApplId, false, correlationId, true);
        if (personsInfo == null) {
            return null;
        }
        return personsInfo.stream()
                .filter(person -> person.getApplications() != null)
                .filter(person -> person.getApplications().stream()
                        .anyMatch(appl -> busApplId.equals(appl.getBusApplId())))
                .findFirst().orElse(null);
    }
}
