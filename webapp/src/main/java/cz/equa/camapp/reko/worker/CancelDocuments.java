package cz.equa.camapp.reko.worker;

import cz.equa.camapp.model.GeneratedDocumentInfoDTO;
import cz.equa.camapp.model.GeneratedDocumentsDTO;
import cz.equa.camapp.model.document.GetDocumentListDto;
import cz.equa.camapp.reko.RekoAbstractWorker;
import cz.equa.camapp.rest.model.DocumentType;
import cz.equa.camapp.rest.service.ApplicationService;
import cz.equa.camapp.rest.service.DmsService;
import cz.equa.camapp.service.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.client.spring.annotation.ExternalTaskSubscription;
import org.springframework.stereotype.Component;

import java.util.List;

import static cz.equa.camapp.rest.service.DmsService.*;

@Component("rekoCancelDocuments")
@ExternalTaskSubscription(topicName = "cancelDocuments")
@Slf4j
public class CancelDocuments extends RekoAbstractWorker {

    private final DmsService dmsService;

    public CancelDocuments(ApplicationService applicationService, DmsService dmsService) {
        super(applicationService);
        this.dmsService = dmsService;
    }

    @Override
    public void executeWorker() throws ServiceException {
        loadProcessVariables();

        GeneratedDocumentsDTO docs = pv.getGeneratedDocuments();
        if (docs == null || docs.getGeneratedObjectIds() == null || docs.getGeneratedObjectIds().isEmpty()) {
            log.info("No documents to cancel.");
            return;
        }
        List<String> documentIds = docs.getGeneratedObjectIds().values().stream()
                .map(GeneratedDocumentInfoDTO::getDocumentId)
                .toList();
        GetDocumentListDto response = dmsService.getDocumentList(documentIds, pv.getCorrelationId());

        if (response == null) {
            log.info("No documents found.");
            return;
        }

        List<GetDocumentListDto.Document> documents = response.getDocumentList();
        for (GetDocumentListDto.Document document : documents) {
            GetDocumentListDto.Document.DocumentDetailFull documentDetail = document.getDocument();

            String documentSmState = documentDetail.getSmStateId();
            String documentId = documentDetail.getDocumentId();
            if (StringUtils.isNotBlank(documentSmState) && !REKO_RSTS.equals(documentSmState) && !REKO_EXPIRED.equals(documentSmState) && !REKO_CANCEL.equals(documentSmState)) {
                // for REKO_EXPIRED do nothing - it is terminal
                DocumentType docType = DocumentType.valueOf(documentDetail.getDocumentClass());
                dmsService.documentRenderUpdateSmState(documentId, DmsService.getDocumentIdType(documentId), pv.getCorrelationId(), docType.getCancelSmState() != null ? docType.getCancelSmState() : REKO_CANCEL);
            }
        }
    }
}
