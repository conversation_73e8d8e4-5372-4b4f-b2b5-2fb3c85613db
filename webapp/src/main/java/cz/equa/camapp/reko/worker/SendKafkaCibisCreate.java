package cz.equa.camapp.reko.worker;

import com.fasterxml.jackson.core.JsonProcessingException;
import cz.equa.camapp.constants.LovEnum;
import cz.equa.camapp.lovs.*;
import cz.equa.camapp.manager.BslManager;
import cz.equa.camapp.manager.LovManager;
import cz.equa.camapp.reko.KafkaCibisAbstract;
import cz.equa.camapp.reko.ProcessVariables;
import cz.equa.camapp.rest.model.RekoCalculationResponseDTO;
import cz.equa.camapp.rest.service.ApplicationService;
import cz.equa.camapp.rest.service.OperationService;
import cz.equa.camapp.service.ServiceException;
import cz.equa.camapp.service.applicationservice.model.*;
import cz.equa.camapp.service.product_service.LovTranslationsItemDTO;
import cz.equa.camapp.utils.ApplicationUtils;
import cz.equa.camapp.utils.Chain;
import cz.equa.camapp.utils.DateTimeUtils;
import cz.equa.camapp.utils.StrUtils;
import cz.rb.camapp.kafka.KafkaProducerCibis;
import cz.rb.camapp.kafka.KafkaTopic;
import cz.rb.camapp.kafka.model.ClientIdentification;
import cz.rb.camapp.kafka.model.cibis.*;
import cz.rb.las.parametrization.model.BSLProductType;
import cz.rb.las.parametrization.model.BSLSubtypeParametrization;
import cz.rb.las.parametrization.model.BSLTypeParametrization;
import jakarta.validation.groups.Default;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.client.spring.annotation.ExternalTaskSubscription;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

import static cz.equa.camapp.utils.ApplicationUtils.sanitizePhoneNumber;

@Component
@Slf4j
@ExternalTaskSubscription(topicName = "rekoSendKafkaCibisCreate")
public class SendKafkaCibisCreate extends KafkaCibisAbstract {

    private final KafkaProducerCibis kafkaProducerCibis;
    private final BslManager bslManager;

    private static final String OPERATION_TYPE = "create";
    private static final String APR = "APR";
    private static final String SEL = "SEL";
    private static final String DIS_PPI = "DIS_PPI";
    private static final String SURCHRG_TP = "SURCHRG_TP";
    private static final String LOAN_PURP = "LOAN_PURP";
    private static final String VERIFDONE = "VERIFDONE";
    private static final String OWNBS = "OWNBS";

    public SendKafkaCibisCreate(ApplicationService applicationService,
                                KafkaProducerCibis kafkaProducerCibis,
                                LovManager lovManager,
                                OperationService operationService,
                                BslManager bslManager,
                                DateTimeUtils dateTimeUtils) {
        super(applicationService, lovManager, operationService, dateTimeUtils);
        this.kafkaProducerCibis = kafkaProducerCibis;
        this.bslManager = bslManager;
    }

    @Override
    public void executeWorker() throws ServiceException, JsonProcessingException {
        loadProcessVariables();
        processAndSendKafkaMessage(pv);
    }

    protected void processAndSendKafkaMessage(ProcessVariables pv) throws JsonProcessingException {
        BuildingLoanApplicationDTO buildingLoanAppl = pv.getBuildingLoanApplication();
        BuildingLoanApplicationOwnerDTO buildingLoanApplOwner = pv.getLoanApplicationOwner();
        BuildingApplVariantDTO variantApr = pv.getBuildingLoanApplVariants().getVariant(APR);
        RekoCalculationResponseDTO calculateBuildingLoanIPS = pv.getRekoRpsnCalculations();
        PtIncomeRstsListDTO incomes = pv.getLoanApplicationIncomes();

        LocalDate applDate = buildingLoanAppl.getApplDate().toLocalDate();
        String busProdSubTpId = buildingLoanAppl.getBusProdSubTp();
        BSLProductType getParamsBSL = bslManager.getCachedParamsBSL(applDate, busProdSubTpId);
        BSLSubtypeParametrization subtypeParametrization = prepareSubtypeParametrization(pv);


        RstsBslCreateMessageDto message = new RstsBslCreateMessageDto();
        prepareHeader(message);

        MessageBodyRstsBslCreate body = new MessageBodyRstsBslCreate();
        Long sourceChannel = mapDistributionChannelToSourceChannel(buildingLoanAppl.getDistCnlId());
        if (sourceChannel != null) {
            body.setZdrojovyKanal(sourceChannel);
        }
        body.setObchZastupceSepsani(createBusinessRepresentative(buildingLoanAppl.getFirstTouchPointOwnr()));
        body.setKlient(prepareClient(buildingLoanApplOwner, buildingLoanAppl, getParamsBSL));
        body.setSporeni(prepareSavings(buildingLoanAppl, getParamsBSL, subtypeParametrization, calculateBuildingLoanIPS));
        body.setUver(prepareLoan(pv));
        body.setRiskKontroly(prepareRiskControls(buildingLoanAppl, buildingLoanApplOwner, calculateBuildingLoanIPS, variantApr, incomes));

        message.setBody(body);
        validateObjectRequiredByAnnotations(message, ValidationGroups.Create.class, Default.class);

        kafkaProducerCibis.sendCreateMessage(message);
        log.info("Message sent to Kafka (Cibis Create): {}", message);
    }

    protected BSLSubtypeParametrization prepareSubtypeParametrization(ProcessVariables pv) {
        BuildingLoanApplicationDTO buildingLoanAppl = pv.getBuildingLoanApplication();

        LocalDate applDate = buildingLoanAppl.getApplDate().toLocalDate();
        String busProdSubTpId = buildingLoanAppl.getBusProdSubTp();
        BSLProductType getParamsBSL = bslManager.getCachedParamsBSL(applDate, busProdSubTpId);

        return getParamsBSL.getSubtypes().stream().filter(
                subtype -> subtype.getBusProdSubTp().equals(busProdSubTpId)
        ).findFirst().orElseThrow();
    }

    protected List<SpecifickaPodminkaDto> prepareSpecificConditions(BuildingApplVariantDTO variantDTO) {
        List<SpecifickaPodminkaDto> specificConditionsList = new LinkedList<>();

        if (variantDTO.getLoanPurposes() == null || variantDTO.getLoanPurposes().isEmpty()) {
            return specificConditionsList;
        }

        AtomicInteger k = new AtomicInteger(1);
        variantDTO.getLoanPurposes().stream()
                .filter(purpose -> VERIF.equals(purpose.getPurpStatId()))
                .forEach(purpose -> {
                    purpose.getSpecConds().stream().filter(
                            specCond -> VERIF.equals(specCond.getCondStatId())
                    ).forEach(specCond -> {
                        SpecifickaPodminkaDto specifickaPodminka = new SpecifickaPodminkaDto();
                        Long specCondId = getLovTranslationId("SPEC_COND", specCond.getCondId(), Long::valueOf);
                        specifickaPodminka.setTypPodminky(specCondId);

                        var purpSpecCond = Chain.of(lovManager.getLovByDetail(LovVPurpSpeccond.class, "SPECCOND_ID", specCond.getCondId(), getLanguage(), SYSTEM_CSA)).map(AbstractLov::getLovDetail).get();
                        specifickaPodminka.setTextPodminky(StrUtils.truncate(specCond.getCondDetail(), 2000));
                        specifickaPodminka.setTypPredmetu(Chain.of(purpSpecCond).map(t -> t.get("SUBJ_TP")).get());
                        specifickaPodminka.setIdPredmetu((long) k.get());
                        specifickaPodminka.setFazeSplneni(Chain.of(purpSpecCond).map(t -> t.get("PHASE_ID")).get());
                        specifickaPodminka.setUdalostProStartLhutyProSplneni(Chain.of(purpSpecCond).map(t -> t.get("START_EVENT")).get());
                        specifickaPodminka.setLhutaProSplneni(Chain.of(purpSpecCond).map(t -> t.get("PERIOD")).map(Long::parseLong).get());
                        specificConditionsList.add(specifickaPodminka);
                    });
                    k.getAndIncrement();
                });

        return specificConditionsList;
    }


    protected String getContrSignDate(BuildingLoanApplicationDTO buildingLoanAppl) {
        return Chain.of(buildingLoanAppl).safe(BuildingLoanApplicationDTO::getContrSignDate).map(LocalDate::toString).get();
    }

    protected String getContrSignDateTime(BuildingLoanApplicationDTO buildingLoanAppl) {
        return Chain.of(buildingLoanAppl)
                .safe(BuildingLoanApplicationDTO::getContrSignDate)
                .map(dateTimeUtils::localDateToZonedDateTime)
                .map(t -> t.format(DateTimeFormatter.ISO_OFFSET_DATE_TIME))
                .get();
    }

    protected String calculateDueDate(BuildingLoanApplicationDTO buildingLoanAppl) {
        return Chain.of(buildingLoanAppl)
                .safe(BuildingLoanApplicationDTO::getContrSignDate)
                .map(date -> date.plusMonths(24))
                .map(date -> date.withDayOfMonth(date.lengthOfMonth()))
                .map(LocalDate::toString)
                .get();
    }

    protected BuildingApplVariantPurposeDTO findPurpose(BuildingApplVariantDTO variant, String purpId) {
        var purposes = variant.getLoanPurposes();
        if (purposes == null || purpId == null) {
            return null;
        }

        return purposes.stream().filter(
                purpose -> purpose.getPurpId().equals(purpId) && purpose.getPurpStatId().equals(VERIF)
        ).findFirst().orElse(null);
    }

    protected LinkedList<UverovaPrilohaDto> prepareLoanAttachments(BuildingApplVariantDTO variant) {
        var surcharges = variant.getSurcharges();
        if (surcharges == null || surcharges.isEmpty()) {
            return null;
        }

        var attachments = new LinkedList<UverovaPrilohaDto>();

        surcharges.stream()
                .filter(surcharge -> !DIS_PPI.equals(surcharge.getSurchrgTpId()))
                .forEach(surcharge -> {
                    UverovaPrilohaDto priloha = new UverovaPrilohaDto();
                    priloha.setTypPrilohy(Chain.of(surcharge.getSurchrgTpId()).safe(surchrgTpId -> getLovTranslationFirstSafe(SURCHRG_TP, surchrgTpId)).map(LovTranslationsItemDTO::getTrgId).map(Long::valueOf).get());
                    attachments.add(priloha);
                });

        return attachments.isEmpty() ? null : attachments;
    }

    protected LinkedList<UcelDto> preparePurposesList(BuildingApplVariantDTO variant) {
        var list = new LinkedList<UcelDto>();

        var purposes = variant.getLoanPurposes();
        if (purposes == null) {
            return list;
        }

        AtomicInteger i = new AtomicInteger(1);
        purposes.stream()
                .filter(purpose -> VERIF.equals(purpose.getPurpStatId()))
                .forEach(purpose -> {
                    UcelDto ucel = new UcelDto();
                    ucel.setPoradi((long) i.getAndIncrement());
                    ucel.setTypUcelu(Chain.of(purpose.getPurpId()).safe(purpId -> getLovTranslationFirstSafe(LOAN_PURP, purpId)).map(LovTranslationsItemDTO::getTrgId).map(Long::valueOf).get());
                    ucel.setCastkaUcelu(Chain.of(purpose.getPurpAmt()).safe(BigDecimal::doubleValue).get());
                    ucel.setDefiniceUcelu(Chain.of(purpose.getPurpDetail()).safe(t -> StrUtils.truncate(t, 1000)).get());
                    list.add(ucel);
                });

        return list;
    }

    protected List<OsobniDokladDto> createClientDocuments(BuildingLoanApplicationOwnerDTO owner) {
        final Map<String, Long> ID_PRIORITIES = Map.of(
                "1STID", 1L,
                "2NDID", 2L
        );

        return owner.getIdCards().stream()
                .filter(idCard -> ID_PRIORITIES.containsKey(idCard.getIdCardPurpId()))
                .map(idCard -> {
                    OsobniDokladDto doklad = new OsobniDokladDto();
                    doklad.setCislo(idCard.getCardId());

                    String citszpId = Chain.of(lovManager.getLov(new LovStayTp(owner.getResidencyTpId()), getLanguage(), SRC_SYSTEM))
                            .map(AbstractLov::getLovDetail)
                            .map(t -> t != null ? t.get("CITZSP_ID") : null)
                            .get();

                    LovIdCardTp item = Chain.of(citszpId)
                            .map(id -> id != null ? Map.of(
                                    "REF_ID", idCard.getIdCardTpId(),
                                    "CITZSP", id)
                                    : null)
                            .map(details -> details != null ? lovManager.getLovs(LovIdCardTp.class, LovLang.CZE2, details, SYSTEM_CSA) : null)
                            .map(items -> items != null && !items.isEmpty() ? items.get(0) : null).get();

                    doklad.setDruh(Chain.of(item).map(AbstractLov::getCode).map(Long::parseLong).get());

                    doklad.setVydal(StrUtils.truncate(idCard.getIssuer(), 45));
                    doklad.setPlatnostDo(Chain.of(idCard.getExprDate()).map(LocalDate::toString).get());
                    doklad.setPriorita(ID_PRIORITIES.getOrDefault(idCard.getIdCardPurpId(), null));

                    return doklad;
                })
                .toList();
    }

    protected Long findOzNumberForTouchPoint(String touchPointOwner) {
        final Map<String, Long> prefixOzMapping = Map.of(
                "15", 37700019L,
                "35", 37700019L
        );
        final Long defaultOzNumber = 47173419L;

        if (touchPointOwner == null) {
            return defaultOzNumber;
        }

        for (Map.Entry<String, Long> entry : prefixOzMapping.entrySet()) {
            if (touchPointOwner.startsWith(entry.getKey())) {
                return entry.getValue();
            }
        }

        return defaultOzNumber;
    }

    protected String findExtOzForTouchPoint(String touchPointOwner) {
        final List<String> extOzPrefixes = List.of("103");

        if (touchPointOwner == null) {
            return null;
        }

        for (String prefix : extOzPrefixes) {
            if (touchPointOwner.startsWith(prefix)) {
                return touchPointOwner.substring(3);
            }
        }

        return null;
    }

    protected ObchodniZastupceDto createBusinessRepresentative(String touchPointOwner) {
        ObchodniZastupceDto representative = new ObchodniZastupceDto();
        representative.setCisloOZ(findOzNumberForTouchPoint(touchPointOwner));

        String cisloExtOZ = findExtOzForTouchPoint(touchPointOwner);
        if (cisloExtOZ != null) {
            representative.setCisloExtOZ(cisloExtOZ);
        }

        return representative;
    }

    protected ClientIdentification getClientIdentification(BuildingLoanApplicationOwnerDTO buildingLoanApplOwner) {
        if (buildingLoanApplOwner.getRcNum() != null && !buildingLoanApplOwner.getRcNum().isBlank()) {
            return new ClientIdentification(
                    buildingLoanApplOwner.getRcNum().replace("/", ""),
                    "R"
            );
        } else {
            return new ClientIdentification(
                    buildingLoanApplOwner.getRstsPseudoBirthCode(),
                    "I"
            );
        }
    }

    protected AdresaDto createAddress(CtAddressDTO addressDTO) {
        AdresaDto adresa = new AdresaDto();
        if (addressDTO == null) {
            return adresa;
        }

        var street = addressDTO.getStreetName() == null || addressDTO.getStreetName().isBlank() ? addressDTO.getCityName() : addressDTO.getStreetName();
        adresa.setUlice(StrUtils.truncate(street, 45));

        String streetNum = addressDTO.getStreetNum();
        if (streetNum.contains("/")) {
            adresa.setCisloPopisne(streetNum.substring(0, streetNum.indexOf("/")));
            adresa.setCisloOrientacni(streetNum.substring(streetNum.indexOf("/") + 1));
        } else {
            adresa.setCisloPopisne(streetNum);
        }

        adresa.setObec(StrUtils.truncate(addressDTO.getCityName(), 60));
        if ((StringUtils.isNotBlank(addressDTO.getZip()) && addressDTO.getZip().length() != 5) || (StringUtils.isNotBlank(addressDTO.getCntryId()) && !addressDTO.getCntryId().equals("CZ"))) {
            adresa.setPsc("00000");
        } else {
            adresa.setPsc(addressDTO.getZip());
        }
        adresa.setStat(getLovTranslationId(LovEnum.CNTRY.name(), addressDTO.getCntryId()));

        return adresa;
    }

    protected List<KomunikacniKanalDto> getCommunicationChannels(BuildingLoanApplicationOwnerDTO buildingLoanApplOwner) {
        List<KomunikacniKanalDto> komunikacniKanaly = new LinkedList<>();
        //Phone:
        KomunikacniKanalDto kanalPhone = new KomunikacniKanalDto();
        var phoneNum = StrUtils.truncate(buildingLoanApplOwner.getPhoneNum(), 45);

        kanalPhone.setAdresa(sanitizePhoneNumber(phoneNum, ApplicationUtils.PhoneNumberFormat.ZEROS_FORMAT));
        kanalPhone.setPriorita(1L);
        kanalPhone.setTyp(10L);
        komunikacniKanaly.add(kanalPhone);
        //Email:
        KomunikacniKanalDto kanalEmail = new KomunikacniKanalDto();
        kanalEmail.setAdresa(StrUtils.truncate(buildingLoanApplOwner.getEmail(), 45));
        kanalEmail.setPriorita(1L);
        kanalEmail.setTyp(9L);
        komunikacniKanaly.add(kanalEmail);

        return komunikacniKanaly;
    }

    protected OsobaDto prepareClient(BuildingLoanApplicationOwnerDTO buildingLoanApplOwner, BuildingLoanApplicationDTO buildingLoanAppl, BSLProductType getParamsBSL) {
        List<CtCsaPtMetadDTO> metadata = buildingLoanApplOwner.getMetadata();

        OsobaDto klient = new OsobaDto();
        klient.setBic(Chain.of(buildingLoanApplOwner).map(BuildingLoanApplicationOwnerDTO::getRstsPartyId).map(Long::parseLong).get());
        ClientIdentification identification = getClientIdentification(buildingLoanApplOwner);
        klient.setId(identification.getId());
        klient.setTypId(identification.getTypId());

        klient.setDatumNarozeni(buildingLoanApplOwner.getBirthDate().toString());
        String firstName = StrUtils.truncate(buildingLoanApplOwner.getFirstName(), 24);
        klient.setJmeno(firstName);
        String familyName = StrUtils.truncate(buildingLoanApplOwner.getFamilyName(), 35);
        klient.setPrijmeni(familyName);

        klient.setTrvalaAdresa(createAddress(buildingLoanApplOwner.getPermAddr()));

        klient.setPohlavi(getLovTranslationId(LovEnum.GENDER.name(), buildingLoanApplOwner.getGenderId(), Long::valueOf));
        klient.setStatniPrislusnost(getLovTranslationId(LovEnum.CNTRY.name(), buildingLoanApplOwner.getCitizenship()));
        klient.setRodinnyStav(getLovTranslationId(LovEnum.MAR_STAT.name(), buildingLoanApplOwner.getMarStatId(), Long::valueOf));
        klient.setSpecialniStatusKlienta(getSPSSMetadataNumberValue("specialniStatusKlienta", metadata).longValue());
        String birthName = StrUtils.truncate(buildingLoanApplOwner.getBirthName(), 35);
        klient.setRodnePrijmeni(birthName);
        String birthPlace = StrUtils.truncate(buildingLoanApplOwner.getBirthPlace(), 45);
        klient.setMistoNarozeni(birthPlace);
        klient.setZemeNarozeni(getLovTranslationId(LovEnum.CNTRY.name(), buildingLoanApplOwner.getBirthCntryId()));

        klient.setPolitickyExponovanaOsoba(buildingLoanApplOwner.getPep());
        klient.setNejvyssiDosazeneVzdelani(getLovTranslationId(LovEnum.EDU_STAT.name(), buildingLoanApplOwner.getEduStatId(), Long::valueOf));
        klient.setZpusobBydleni(getLovTranslationId(LovEnum.HOUS_STAT.name(), buildingLoanApplOwner.getHousingStatId(), Long::valueOf));
        klient.setInterniDruhPobytu(getLovTranslationId(LovEnum.STAY_TP.name(), buildingLoanApplOwner.getResidencyTpId(), Long::valueOf));

        klient.setNovePodepsanaSPJ(true);

        var contrSignDate = Chain.of(buildingLoanAppl).safe(BuildingLoanApplicationDTO::getContrSignDate).map(LocalDate::toString).get();

        klient.setDatumPodpisuSPJ(contrSignDate);
        klient.setVerzeSPJ(Chain.of(getParamsBSL).map(BSLProductType::getProdTypeParameters).map(BSLTypeParametrization::getLegalContrVer).map(Long::parseLong).get());
        klient.setDanovyRezident("CZ".equals(buildingLoanApplOwner.getTaxDomicile()));
        klient.setOsobniDoklady(createClientDocuments(buildingLoanApplOwner));
        klient.setKomunikacniKanaly(getCommunicationChannels(buildingLoanApplOwner));

        PobytProMFDto pobytProMF = new PobytProMFDto();
        if (buildingLoanApplOwner.getResidencyTpId() != null) {
            pobytProMF.setDruhPobytu(Chain.of(lovManager.getLov(new LovStayTp(buildingLoanApplOwner.getResidencyTpId()), getLanguage(), SRC_SYSTEM)).map(AbstractLov::getLovDetail).map(t -> t.get("MF_ID")).get());
        }

        var applDateString = Chain.of(buildingLoanAppl).map(BuildingLoanApplicationDTO::getApplDate).map(OffsetDateTime::toLocalDate).map(LocalDate::toString).get();

        pobytProMF.setDatumOd(buildingLoanApplOwner.getResidencyFrom());
        pobytProMF.setDatumDo(buildingLoanApplOwner.getResidencyTo());
        pobytProMF.setDatumOvereni(applDateString);
        RoleOsobyDto udajeRole = new RoleOsobyDto();
        udajeRole.setPouzivatKorespAdr(true);
        udajeRole.setAdresa(createAddress(buildingLoanApplOwner.getPostalAddr()));
        udajeRole.setSouhlasDotazNRKI(1);
        udajeRole.setSouhlasDotazSOLUS(1);
        udajeRole.setSouhlasDavkaSOLUS(1);
        udajeRole.setDatumSouhlasu(applDateString);
        klient.setUdajeRole(udajeRole);
        klient.setPobytProMF(pobytProMF);

        return klient;
    }

    protected SporeniDto prepareSavings(BuildingLoanApplicationDTO buildingLoanAppl, BSLProductType getParamsBSL, BSLSubtypeParametrization subtypeParametrization, RekoCalculationResponseDTO calculateBuildingLoanIPS) {
        var contrSignDate = Chain.of(buildingLoanAppl).safe(BuildingLoanApplicationDTO::getContrSignDate).map(LocalDate::toString).get();
        var applDateString = Chain.of(buildingLoanAppl).map(BuildingLoanApplicationDTO::getApplDate).map(OffsetDateTime::toLocalDate).map(LocalDate::toString).get();

        SporeniDto sporeni = new SporeniDto();
        sporeni.setCisloSmlouvy(Chain.of(buildingLoanAppl).map(BuildingLoanApplicationDTO::getContrNumBs).map(Long::parseLong).get());
        sporeni.setDatumUzavreni(contrSignDate);
        sporeni.setDatumNavrhu(applDateString);
        sporeni.setZakon(Chain.of(getParamsBSL).map(BSLProductType::getProdTypeParameters).map(BSLTypeParametrization::getActNum).map(Integer::longValue).get());
        sporeni.setVop(Chain.of(getParamsBSL).map(BSLProductType::getProdTypeParameters).map(BSLTypeParametrization::getTermCond).map(Long::parseLong).get());
        sporeni.setTarif(Chain.of(subtypeParametrization).map(BSLSubtypeParametrization::getTariff).map(Long::parseLong).get());
        sporeni.setVklad(calculateBuildingLoanIPS.getDeposit());
        sporeni.setKoeficientVstupniUhrady(new BigDecimal(100));

        sporeni.setKoeficientVstupniUhradyCis(Chain.of(subtypeParametrization.getSavingsContractFeeCoef())
                .map(BigDecimal::valueOf)
                .map(bd -> bd.multiply(new BigDecimal(100)))
                .get());

        sporeni.setVyseVstupniUhrady(calculateBuildingLoanIPS.getSavingsContractFeeAmt());

        SmluvniDefiniceUroceniDto sazbaSporeni = new SmluvniDefiniceUroceniDto();
        SmluvniDefiniceUroceniDto sazbaUveru = new SmluvniDefiniceUroceniDto();
        var tariffSubtype = subtypeParametrization.getTariff();

        LovCsaParamCastar csaParamCastar = lovManager.getLovByDetail(LovCsaParamCastar.class, "TARIF", tariffSubtype, getLanguage(), SRC_SYSTEM);
        if (csaParamCastar != null && csaParamCastar.getLovDetail() != null) {
            var lovDetail = csaParamCastar.getLovDetail();

            sazbaSporeni.setPerioda(lovDetail.get("PERIODAS"));
            sazbaSporeni.setIndexBazoveSazby(Chain.of(lovDetail).map(t -> t.get("INDEXBAZES")).map(Long::parseLong).get());
            sazbaSporeni.setOdchylka(Chain.of(lovDetail).map(t -> t.get("ODCHYLKAURS")).map(BigDecimal::new).get());

            sazbaUveru.setPerioda(lovDetail.get("PERIODAU"));
            sazbaUveru.setIndexBazoveSazby(Chain.of(lovDetail).map(t -> t.get("INDEXBAZEU")).map(Long::parseLong).get());
            sazbaUveru.setOdchylka(Chain.of(lovDetail).map(t -> t.get("ODCHYLKAURU")).map(BigDecimal::new).get());
        }

        sporeni.setSazbaSporeni(sazbaSporeni);
        sporeni.setSazbaUveru(sazbaUveru);

        return sporeni;
    }

    protected UverDto prepareLoan(ProcessVariables pv) {
        BuildingLoanApplicationDTO buildingLoanAppl = pv.getBuildingLoanApplication();
        BuildingLoanApplicationOwnerDTO buildingLoanApplOwner = pv.getLoanApplicationOwner();
        BuildingApplVariantDTO variantApr = pv.getBuildingLoanApplVariants().getVariant(APR);
        BuildingApplVariantDTO variantSel = pv.getBuildingLoanApplVariants().getVariant(SEL);
        RekoCalculationResponseDTO calculateBuildingLoanIPS = pv.getRekoRpsnCalculations();
        PtIncomeRstsListDTO incomes = pv.getLoanApplicationIncomes();

        BSLSubtypeParametrization subtypeParametrization = prepareSubtypeParametrization(pv);


        var metadata = buildingLoanApplOwner.getMetadata();
        var contrSignDate = getContrSignDate(buildingLoanAppl);
        var contrSignDateTime = getContrSignDateTime(buildingLoanAppl);
        var applDateStr = Chain.of(buildingLoanAppl).safe(BuildingLoanApplicationDTO::getApplDate).map(dateTimeUtils::offsetDateTimeToLocalDate).map(LocalDate::toString).get();

        UverDto uver = new UverDto();
        uver.setUverovaSmlouva(Chain.of(buildingLoanAppl).map(BuildingLoanApplicationDTO::getContrNum).map(Long::parseLong).get());
        uver.setVariantaPu(getLovTranslationId(LovEnum.BUS_PROD_SUB_TP.name(), buildingLoanAppl.getBusProdSubTp()));
        uver.setVysePreklenovacihoUveru(calculateBuildingLoanIPS.getFinaAmt());
        uver.setVyseStavebnihoUveru(calculateBuildingLoanIPS.getFinaBuildAmt());
        uver.setDatumSplatnostiPu(calculateDueDate(buildingLoanAppl));
        uver.setDatumSplatnostiSu(calculateBuildingLoanIPS.getDueDate());

        var intrsRx = Chain.of(variantApr.getIntrsRx()).map(bd -> bd.multiply(new BigDecimal(100))).get();

        uver.setPreklenovaciUrokSazbaBezna(intrsRx);
        uver.setStavebniUrokSazbaBezna(intrsRx);

        List<KorekceSazbyDto> seznamKorekciSazeb = new LinkedList<>();
        KorekceSazbyDto korekceSazby = new KorekceSazbyDto();
        korekceSazby.setFazeUveru("PU");
        korekceSazby.setTypPravidlaText("Základní sazba");
        korekceSazby.setTypKorekce("Z");
        korekceSazby.setHodnotaKorekce(intrsRx);
        seznamKorekciSazeb.add(korekceSazby);
        uver.setSeznamKorekciSazeb(seznamKorekciSazeb);

        uver.setSplatkaPu(calculateBuildingLoanIPS.getFinaInsAmt().subtract(
                calculateBuildingLoanIPS.getInsFeeAmt().add(calculateBuildingLoanIPS.getDeposit())
        ));
        uver.setSplatkaPuVcetnePoj(calculateBuildingLoanIPS.getFinaInsAmt().subtract(calculateBuildingLoanIPS.getDeposit()));
        uver.setSplatkaSu(calculateBuildingLoanIPS.getFinaInsAmt().subtract(calculateBuildingLoanIPS.getInsFeeAmt()));
        uver.setSplatkaSuVcetnePoj(calculateBuildingLoanIPS.getFinaInsAmt());
        uver.setVariantaFixace(getLovTranslationId(LovEnum.RX_FIX_PER_TP.name(), variantApr.getFixPeriod(), Long::valueOf));
        uver.setDatumPrijetiZadosti(applDateStr);
        uver.setDatumSchvaleni(contrSignDate);
        uver.setDatumUzavreni(contrSignDate);
        uver.setDatumUzavreniSys(contrSignDate);
        uver.setOdhadDatumPrvnihoCerpani(calculateBuildingLoanIPS.getEstimDisbursDate());
        uver.setReferentI("we");
        uver.setReferentII("rb");

        uver.setDatumUdelSouhlas(applDateStr);
        uver.setSpotrebitelskyUver(true);
        uver.setPodUcinSpotrZakona(true);
        uver.setPodUcinHypotecniSmernice(true);
        BuildingApplVariantSurDTO existEcoSale = variantSel.getSurcharges().stream().filter(
                surcharge -> surcharge.getSurchrgTpId().equals("DIS_ECO")
        ).findFirst().orElse(null);

        uver.setJeEkoUver(existEcoSale != null);

        uver.setTypEkoInvestice(getLovTranslationId("ECO_INVS_TP", variantSel.getEcoInvestTpId(), Long::valueOf));
        uver.setMiraVyuzitiEkoInvestice(Chain.of(variantSel.getEcoInvestShare()).map(bd -> bd.multiply(new BigDecimal(100))).get());

        String verzeFceProcesuPosouzeni = getSPSSMetadataStringValue("verzeFceProcesuPosouzeni", metadata);
        uver.setVerzeFceProcesuPosouzeni(StrUtils.truncate(verzeFceProcesuPosouzeni, 10));

        uver.setDatumACasPodpisuUsdKlientem(contrSignDateTime);
        uver.setDatumACaspodpsuUsdCentralou(contrSignDateTime);

        var foundPurpose1501 = findPurpose(variantApr, "1501");
        var foundPurpose1502 = findPurpose(variantApr, "1502");

        uver.setRefinancovaniInterni(foundPurpose1501 != null);
        uver.setRefinancovaniExterni(foundPurpose1502 != null);

        uver.setVyplatitProviziOzSepsani(true);
        uver.setManualniOvereniUveru(true);
        uver.setObchZastupceOvereni(createBusinessRepresentative(buildingLoanAppl.getFirstTouchPointOwnr()));
        uver.setVyseUhradyZaUzavreniUveru(calculateBuildingLoanIPS.getLoanContractFeeAmt());
        uver.setSeznamUverovychPriloh(prepareLoanAttachments(variantSel));
        uver.setSeznamUcelu(preparePurposesList(variantApr));

        boolean existDisPpi = variantSel.getSurcharges().stream().anyMatch(surcharge -> surcharge.getSurchrgTpId().equals(DIS_PPI));
        if (existDisPpi) {
            uver.setSeznamZajisteni(new LinkedList<>());
            ZajisteniDto zajisteni = new ZajisteniDto();
            zajisteni.setPoradi(1L);
            zajisteni.setTypZajisteni(10L);
            PojisteniDto pojisteniUniqa = new PojisteniDto();
            if (subtypeParametrization.getInsVarTp() != null) {
                pojisteniUniqa.setVariantaPojisteni(Long.valueOf(subtypeParametrization.getInsVarTp()));
            }
            pojisteniUniqa.setRamcovaSmlouvaPoj(subtypeParametrization.getInsNum());
            pojisteniUniqa.setSkupinaProPoj("A");
            pojisteniUniqa.setSplatkaPojisteniPu(calculateBuildingLoanIPS.getInsFeeAmt());
            pojisteniUniqa.setSplatkaPojisteniSu(calculateBuildingLoanIPS.getInsFeeAmt());
            pojisteniUniqa.setZdravotniKomplikace(false);
            zajisteni.setPojisteniUniqa(pojisteniUniqa);
            uver.getSeznamZajisteni().add(zajisteni);
        }
        uver.setDatumSepsaniPokynuCerp(contrSignDate);

        uver.setSeznamPlatebCerpani(new LinkedList<>());
        PlatbaCerpaniDto platbaCerpani = new PlatbaCerpaniDto();

        BigDecimal purposeVerif1501Amt = Chain.of(foundPurpose1501).map(BuildingApplVariantPurposeDTO::getPurpAmt).or(BigDecimal.ZERO);
        BigDecimal purposeVerif1502Amt = Chain.of(foundPurpose1502).map(BuildingApplVariantPurposeDTO::getPurpAmt).or(BigDecimal.ZERO);

        platbaCerpani.setPozadovanaCastka(calculateBuildingLoanIPS.getFinaAmt().subtract((purposeVerif1501Amt.add(purposeVerif1502Amt))));
        String accountString = Chain.of(variantSel).map(BuildingApplVariantDTO::getAccNumPrefix).or("") + Chain.of(variantSel).map(BuildingApplVariantDTO::getAccNum).or("");

        platbaCerpani.setKontoPlatby(Chain.of(accountString).safe(BigDecimal::new).get());
        platbaCerpani.setBanka(Chain.of(variantSel).map(BuildingApplVariantDTO::getAccBankCode).safe(BigDecimal::new).get());
        platbaCerpani.setRefinancovan(
                variantApr
                        .getLoanPurposes()
                        .stream()
                        .anyMatch(purpose -> (("1501".equals(purpose.getPurpId()) || "1502".equals(purpose.getPurpId())) && VERIF.equals(purpose.getPurpStatId())))
        );
        uver.getSeznamPlatebCerpani().add(platbaCerpani);

        uver.setFinancniSituaceOsob(new LinkedList<>());
        var incomesList = incomes != null ? incomes.getIncomes().stream().filter(income -> VERIFDONE.equals(income.getIncVerifRsltId())).toList() : null;
        uver.getFinancniSituaceOsob().add(getPersonFinancialSituation(incomesList, buildingLoanApplOwner, metadata));

        uver.setSpecifickePodminky(prepareSpecificConditions(variantApr));
        uver.setSeznamAmlOsob(new LinkedList<>());
        AmlOsobyDto amlOsoba = buildAmlOsobyDto(buildingLoanApplOwner,pv.getGetDetailResultDto());
        uver.getSeznamAmlOsob().add(amlOsoba);

        return uver;
    }


    protected RiskKontrolyDto prepareRiskControls(BuildingLoanApplicationDTO buildingLoanAppl, BuildingLoanApplicationOwnerDTO buildingLoanApplOwner, RekoCalculationResponseDTO calculateBuildingLoanIPS, BuildingApplVariantDTO variantApr, PtIncomeRstsListDTO incomes) {
        var metadata = buildingLoanApplOwner.getMetadata();
        var foundPurpose1501 = variantApr.getLoanPurposes().stream().filter(
                purpose -> purpose.getPurpId().equals("1501") &&
                        purpose.getPurpStatId().equals(VERIF)
        ).findFirst().orElse(null);
        var foundPurpose1502 = variantApr.getLoanPurposes().stream().filter(
                purpose -> purpose.getPurpId().equals("1502") &&
                        purpose.getPurpStatId().equals(VERIF)
        ).findFirst().orElse(null);

        //Risk Kontroly
        RiskKontrolyDto riskKontroly = new RiskKontrolyDto();
        buildInicialniRating(riskKontroly, metadata);

        riskKontroly.setRiskKontrolyProOsoby(new LinkedList<>());

        RiskKontrolyProOsobuDto riskKontrolyProOsoby = new RiskKontrolyProOsobuDto();
        buildRiskKontrolyProOsobu(riskKontrolyProOsoby, buildingLoanApplOwner);


        AplikacniScoringDto aplikacniScoring = new AplikacniScoringDto();
        aplikacniScoring.setVek(Chain.of(getSPSSMetadataNumberValue("vek", metadata)).map(BigDecimal::longValue).get());
        aplikacniScoring.setRodinnyStav(getLovTranslationId(LovEnum.MAR_STAT.name(), buildingLoanApplOwner.getMarStatId(), Long::valueOf));
        aplikacniScoring.setNejvyssiDosazeneVzdelani(getLovTranslationId(LovEnum.EDU_STAT.name(), buildingLoanApplOwner.getEduStatId(), Long::valueOf));


        List<PtIncomeRstsDTO> incomesForJobCollect = incomes.getIncomes().stream()
                .filter(income -> VERIFDONE.equals(income.getIncVerifRsltId()) &&
                        (income.getIncTpId().equals(EMPLT) || income.getIncTpId().equals(EMPLT2) || income.getIncTpId().equals(OWNBS) || income.getIncTpId().equals("BS"))
                ).toList();

        buildAplikacniScoringPart2(incomesForJobCollect, buildingLoanApplOwner, buildingLoanAppl, foundPurpose1501, foundPurpose1502, aplikacniScoring);
        aplikacniScoring.setVyseUveru(calculateBuildingLoanIPS.getFinaAmt());
        List<PtIncomeRstsDTO> verifDoneIncomes = incomes.getIncomes().stream()
                .filter(income -> VERIFDONE.equals(income.getIncVerifRsltId()))
                .toList();
        aplikacniScoring.setRefPrijemTyp1(Chain.of(verifDoneIncomes).safe(t ->
                selectHighestIncome(t, getLanguage()).getIncTpId()).map(t -> getLovTranslationFirstSafe(LovEnum.INC_TP.name(), t)).map(LovTranslationsItemDTO::getTrgId).map(Long::valueOf).get());


        riskKontrolyProOsoby.setAplikacniScoring(aplikacniScoring);

        UrgDto urg = new UrgDto();
        buildUrg(urg, metadata);
        riskKontrolyProOsoby.setUrg(urg);

        riskKontroly.getRiskKontrolyProOsoby().add(riskKontrolyProOsoby);

        return riskKontroly;
    }

    private static void prepareHeader(RstsBslCreateMessageDto message) {
        MessageHeaderRstsBsl header = new MessageHeaderRstsBsl();
        header.setId(KafkaTopic.PUSH_BAI_RSTS_BSL_CRT_JSON.name());
        header.setVersion(VERSION);
        header.setPublisher(CMN);
        header.setPublishedAt(OffsetDateTime.now().withNano(0).toString());
        header.setOperationType(OPERATION_TYPE);
        message.setHeader(header);
    }

}
