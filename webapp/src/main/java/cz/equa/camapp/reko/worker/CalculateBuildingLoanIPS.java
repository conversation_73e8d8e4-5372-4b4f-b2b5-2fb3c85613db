package cz.equa.camapp.reko.worker;

import cz.equa.camapp.lovs.LovCsaParamCastar;
import cz.equa.camapp.manager.BslManager;
import cz.equa.camapp.manager.LovManager;
import cz.equa.camapp.reko.ProcessVariables;
import cz.equa.camapp.reko.RekoAbstractWorker;
import cz.equa.camapp.rest.model.RekoCalculationResponseDTO;
import cz.equa.camapp.rest.service.ApplicationService;
import cz.equa.camapp.rest.service.OperationService;
import cz.equa.camapp.service.ServiceException;
import cz.equa.camapp.service.applicationservice.model.BuildingApplVariantDTO;
import cz.equa.camapp.service.applicationservice.model.BuildingApplVariantInsDTO;
import cz.equa.camapp.service.applicationservice.model.BuildingLoanApplicationDTO;
import cz.rb.las.operation.model.CalculateBuildingLoanIPSRequest;
import cz.rb.las.parametrization.model.BSLProductType;
import cz.rb.las.parametrization.model.BSLSubtypeParametrization;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.client.spring.annotation.ExternalTaskSubscription;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.Date;

import static cz.equa.camapp.lovs.LovApplVariantTp.APR;

@Component
@Slf4j
@ExternalTaskSubscription(topicName = "rekoCalculateBuildingLoanIPS", includeExtensionProperties = true)
public class CalculateBuildingLoanIPS extends RekoAbstractWorker {

    private final OperationService operationService;
    private final LovManager lovManager;
    private final BslManager bslManager;

    @Autowired
    public CalculateBuildingLoanIPS(ApplicationService applicationService,
                                    OperationService operationService,
                                    LovManager lovManager,
                                    BslManager bslManager) {
        super(applicationService);
        this.operationService = operationService;
        this.lovManager = lovManager;
        this.bslManager = bslManager;
    }

    @Override
    public void executeWorker() throws ServiceException {
        loadProcessVariables();
        BuildingLoanApplicationDTO buildingLoanAppl = pv.getBuildingLoanApplication();
        LocalDate applDate = buildingLoanAppl.getApplDate().toLocalDate();
        String busProdSubTpId = buildingLoanAppl.getBusProdSubTp();
        BSLProductType getParamsBSL = bslManager.getCachedParamsBSL(applDate, busProdSubTpId);
        BuildingApplVariantDTO aprVariant = pv.getBuildingLoanApplVariants().getVariant("APR");
        BuildingApplVariantDTO selVariant = pv.getBuildingLoanApplVariants().getVariant("SEL");
        BSLSubtypeParametrization subtypeParametrization = getParamsBSL.getSubtypes().stream().filter(i -> {
            assert i.getBusProdSubTpId() != null;
            return aprVariant.getBusProdSubTpId().equals(i.getBusProdSubTp());
        }).findFirst().orElseThrow();

        var csaParamCastar = lovManager.getLovByDetail(LovCsaParamCastar.class, "TARIF", subtypeParametrization.getTariff(), getLanguage());
        var depositRateString = csaParamCastar.getLovDetail().get("ODCHYLKAURS");
        log.debug("Deposit rate: {}", depositRateString);

        CalculateBuildingLoanIPSRequest request = new CalculateBuildingLoanIPSRequest();
        request.setReportDate(new SimpleDateFormat("yyyy-MM-dd").format(new Date()));
        request.setFinaAmt(aprVariant.getFinaAmt());
        request.setFixPeriod(new BigDecimal(aprVariant.getFixPeriod()));
        request.setInstlCnt(BigDecimal.valueOf(aprVariant.getInstlCnt() / 12));
        request.setInsurTpIds(selVariant.getInsurances().stream().map(BuildingApplVariantInsDTO::getInsurTpId).toList());
        request.setFinalIR(aprVariant.getIntrsRx());
        request.setDepositIRate(new BigDecimal(depositRateString).divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP));
        request.setInsFeeCoef(BigDecimal.valueOf(subtypeParametrization.getInsFeeCoef()));
        request.setLoanContractFeeAmtMax(BigDecimal.valueOf(subtypeParametrization.getLoanContractFeeAmtMax()));
        request.setLoanContractFeeAmtMin(BigDecimal.valueOf(subtypeParametrization.getLoanContractFeeAmtMin()));
        request.setLoanContractFeeCoef(BigDecimal.valueOf(subtypeParametrization.getLoanContractFeeCoef()));
        request.setLoanMaintenanceFeeAmt(BigDecimal.valueOf(subtypeParametrization.getLoanMaintenanceFeeAmt()));
        request.setLoanStatementFeeAmt(BigDecimal.valueOf(subtypeParametrization.getLoanStatementFeeAmt()));
        request.setMonthlyDepositCoef(BigDecimal.valueOf(subtypeParametrization.getMonthlyDepositCoef()));
        request.setSavingsContractFeeAmtMax(BigDecimal.valueOf(subtypeParametrization.getSavingsContractFeeAmtMax()));
        request.setSavingsContractFeeCoef(BigDecimal.valueOf(subtypeParametrization.getSavingsContractFeeCoef()).multiply(new BigDecimal(100)));
        request.setSavingsMaintenanceFeeAmt(BigDecimal.valueOf(subtypeParametrization.getSavingsMaintenanceFeeAmt()));
        request.setSavingsStatementFeeAmt(BigDecimal.valueOf(subtypeParametrization.getSavingsStatementFeeAmt()));

        RekoCalculationResponseDTO calcValues = operationService.getRekoCalculation(request, pv.getCorrelationId(), pv.getClContextId());
        setProcessVariable(ProcessVariables.REKO_RPSN_CALCULATIONS, calcValues);
    }
}
