package cz.equa.camapp.reko.worker;

import com.fasterxml.jackson.core.JsonProcessingException;
import cz.equa.camapp.constants.LovEnum;
import cz.equa.camapp.manager.LovManager;
import cz.equa.camapp.reko.KafkaCibisAbstract;
import cz.equa.camapp.rest.service.ApplicationMapper;
import cz.equa.camapp.rest.service.ApplicationService;
import cz.equa.camapp.rest.service.OperationService;
import cz.equa.camapp.service.ServiceException;
import cz.equa.camapp.service.applicationservice.model.BuildingLoanApplicationDTO;
import cz.equa.camapp.service.applicationservice.model.BuildingLoanApplicationOwnerDTO;
import cz.equa.camapp.utils.Chain;
import cz.equa.camapp.utils.DateTimeUtils;
import cz.rb.camapp.kafka.KafkaProducerCibis;
import cz.rb.camapp.kafka.KafkaTopic;
import cz.rb.camapp.kafka.model.cibis.*;
import cz.rb.las.application.model.CtPersonRstsGet;
import cz.rb.las.application.model.GetBuildingLoanApplResponse;
import jakarta.validation.groups.Default;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.client.spring.annotation.ExternalTaskSubscription;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.UUID;

import static cz.equa.camapp.reko.worker.RekoGetBuildingLoanAppl.APPLICANT;

@Component
@Slf4j
@ExternalTaskSubscription(topicName = "rekoSendKafkaCibisStorno")
public class SendKafkaCibisStorno extends KafkaCibisAbstract {

    private final KafkaProducerCibis kafkaProducerCibis;

    private static final String OPERATION_TYPE = "cancel";

    public SendKafkaCibisStorno(ApplicationService applicationService,
                                KafkaProducerCibis kafkaProducerCibis,
                                LovManager lovManager,
                                OperationService operationService,
                                DateTimeUtils dateTimeUtils
    ) {
        super(applicationService, lovManager, operationService, dateTimeUtils);
        this.kafkaProducerCibis = kafkaProducerCibis;
    }

    @Override
    public void executeWorker() throws ServiceException, JsonProcessingException {
        loadProcessVariables();
        var busApplId = pv.getBusApplId();
        var correlationId = pv.getCorrelationId();
        if (StringUtils.isEmpty(correlationId)) {
            correlationId = UUID.randomUUID().toString();
        }
        var result = getBuildingLoanAppl(busApplId, correlationId, pv.getClContextId());

        assert result.getBuildingLoanAppl() != null;
        BuildingLoanApplicationDTO applicationDto = ApplicationMapper.INSTANCE.esbToDto(result.getBuildingLoanAppl());

        assert result.getPersons() != null;
        CtPersonRstsGet personRstsGet = result.getPersons().stream().filter(person -> {
            assert person.getApplPtRoleTpId() != null;
            return person.getApplPtRoleTpId().equals(APPLICANT);
        }).findFirst().orElse(null);
        if (personRstsGet == null) {
            throw new ServiceException("LOAN_APPLICATION_OWNER not found!");
        }
        BuildingLoanApplicationOwnerDTO applicationOwnerDto = ApplicationMapper.INSTANCE.esbToDto(personRstsGet);

        processAndSendKafkaMessage(applicationDto, applicationOwnerDto);
    }

    protected GetBuildingLoanApplResponse getBuildingLoanAppl(String busApplId, String correlationId, String contextId) throws ServiceException {
        return applicationService.getBuildingLoanAppl(
                null,
                busApplId,
                true,
                false,
                null,
                false,
                correlationId,
                contextId
        );
    }

    protected void processAndSendKafkaMessage(BuildingLoanApplicationDTO buildingLoanAppl, BuildingLoanApplicationOwnerDTO buildingLoanApplOwner) throws JsonProcessingException {

        RstsBslCancelMessageDto message = new RstsBslCancelMessageDto();
        MessageHeaderRstsBsl header = buildMessageHeaderRstsBsl();
        message.setHeader(header);

        // Body
        MessageBodyRstsBslCancel body = new MessageBodyRstsBslCancel();
        Long sourceChannel = mapDistributionChannelToSourceChannel(buildingLoanAppl.getDistCnlId());
        if (sourceChannel != null) {
            body.setZdrojovyKanal(sourceChannel);
        }

        // ObchodniZastupceDto
        ObchodniZastupceDto obchodniZastupce = buildObchodniZastupceDto(buildingLoanAppl);
        body.setObchZastupceSepsani(obchodniZastupce);

        // Klient
        Osoba2Dto klient = new Osoba2Dto();
        klient.setBic(Chain.of(buildingLoanApplOwner).map(BuildingLoanApplicationOwnerDTO::getRstsPartyId).map(Long::parseLong).get());
        body.setKlient(klient);

        // Sporeni
        Sporeni2Dto sporeni = new Sporeni2Dto();
        sporeni.setCisloSmlouvy(Chain.of(buildingLoanAppl).map(BuildingLoanApplicationDTO::getContrNumBs).map(Long::parseLong).get());
        body.setSporeni(sporeni);

        // Uver
        Uver2Dto uver = new Uver2Dto();
        uver.setUverovaSmlouva(Chain.of(buildingLoanAppl).map(BuildingLoanApplicationDTO::getContrNum).map(Long::parseLong).get());
        uver.setVariantaPu(getLovTranslationId(LovEnum.BUS_PROD_SUB_TP.name(), buildingLoanAppl.getBusProdSubTp()));
        body.setUver(uver);

        //Risk Kontroly
        RiskKontrolyDto riskKontroly = new RiskKontrolyDto();
        riskKontroly.setProvedenaKontrolaAnalytikem(false);

        RiskKontrolyProOsobuDto riskKontrolyProOsobu = new RiskKontrolyProOsobuDto();
        riskKontrolyProOsobu.setBic(Chain.of(buildingLoanApplOwner.getRstsPartyId()).map(Long::parseLong).get());

        riskKontroly.getRiskKontrolyProOsoby().add(riskKontrolyProOsobu);
        body.setRiskKontroly(riskKontroly);

        body.setDuvodStoZam(74L);
        body.setDatumStoZam(LocalDate.now().toString());
        body.setProvedenDotazDoUverovehoRegistru(false);
        message.setBody(body);

        validateObjectRequiredByAnnotations(message, ValidationGroups.Storno.class, Default.class);
        kafkaProducerCibis.sendCancelMessage(message);
        log.info("Message sent to Kafka (Cibis Cancel): {}", message);
    }

    @NotNull
    private static ObchodniZastupceDto buildObjObchodniZastupceDto(BuildingLoanApplicationDTO buildingLoanAppl) {
        ObchodniZastupceDto obchZastupceOvereni = new ObchodniZastupceDto();
        if (buildingLoanAppl.getFirstTouchPointOwnr().startsWith("15") ||
                buildingLoanAppl.getFirstTouchPointOwnr().startsWith("35")) {
            obchZastupceOvereni.setCisloOZ(37700019L);
        } else {
            obchZastupceOvereni.setCisloOZ(47173419L);
        }
        return obchZastupceOvereni;
    }

    @NotNull
    private static ObchodniZastupceDto buildObchodniZastupceDto(BuildingLoanApplicationDTO buildingLoanAppl) {
        ObchodniZastupceDto obchodniZastupce = buildObjObchodniZastupceDto(buildingLoanAppl);
        if (buildingLoanAppl.getFirstTouchPointOwnr().startsWith("103")) {
            obchodniZastupce.setCisloExtOZ(buildingLoanAppl.getFirstTouchPointOwnr().substring(3));
        }
        return obchodniZastupce;
    }

    @NotNull
    protected static MessageHeaderRstsBsl buildMessageHeaderRstsBsl() {
        MessageHeaderRstsBsl header = new MessageHeaderRstsBsl();
        header.setId(KafkaTopic.PUSH_BAI_RSTS_BSL_CRT_JSON.name());
        header.setVersion(VERSION);
        header.setPublisher(CMN);
        header.setPublishedAt(OffsetDateTime.now().withNano(0).toString());
        header.setOperationType(OPERATION_TYPE);

        return header;
    }
}
