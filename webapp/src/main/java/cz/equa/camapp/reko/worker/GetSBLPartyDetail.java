package cz.equa.camapp.reko.worker;

import cz.equa.camapp.reko.ProcessVariables;
import cz.equa.camapp.reko.RekoAbstractWorker;
import cz.equa.camapp.rest.model.cus.party.DocumentsDto;
import cz.equa.camapp.rest.model.cus.party.DocumentsDtoList;
import cz.equa.camapp.rest.model.cus.party.GetDetailResultDto;
import cz.equa.camapp.rest.service.ApplicationService;
import cz.equa.camapp.rest.service.PartyService;
import cz.equa.camapp.service.ServiceException;
import cz.equa.camapp.service.applicationservice.model.BuildingApplVariantDTO;
import cz.rb.cus.party.handler.ApiException;
import org.camunda.bpm.client.spring.annotation.ExternalTaskSubscription;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


@Component("rekoGetSBLPartyDetail")
@ExternalTaskSubscription(topicName = "rekoGetSBLPartyDetail")
public class GetSBLPartyDetail extends RekoAbstractWorker {

    PartyService partyService;

    @Autowired
    public GetSBLPartyDetail(ApplicationService applicationService, PartyService partyService) {
        super(applicationService);
        this.partyService = partyService;
    }

    @Override
    public void executeWorker() throws ServiceException, ApiException {
        loadProcessVariables();
        String clientId = pv.getClientId();

        GetDetailResultDto partyDetail = partyService.getDetail(clientId, pv.getCorrelationId());
        List<DocumentsDto> partyDocuments = partyService.getDocuments(clientId, pv.getCorrelationId());

        setProcessVariable(ProcessVariables.GET_DETAIL_RESULT, partyDetail);
        DocumentsDtoList documentsDtoList = new DocumentsDtoList();
        documentsDtoList.setPartyDocuments(partyDocuments);
        setProcessVariable(ProcessVariables.GET_PARTY_DOCUMENTS_RESULT, documentsDtoList);
        setProcessVariable(ProcessVariables.PARTY_ID, clientId);

        BuildingApplVariantDTO selVariant = pv.getBuildingLoanApplVariants().getVariant("SEL");
        if (selVariant != null && selVariant.getSurcharges() != null) {
            boolean insurenceSelected =  selVariant.getSurcharges().stream().anyMatch(
                    surcharge -> "DIS_PPI".equals(surcharge.getSurchrgTpId()));
            setProcessVariable(ProcessVariables.INSURANCE_SELECTED, insurenceSelected);
        }
    }
}
