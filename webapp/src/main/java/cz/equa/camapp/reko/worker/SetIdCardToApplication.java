package cz.equa.camapp.reko.worker;

import cz.equa.camapp.reko.RekoAbstractWorker;
import cz.equa.camapp.rest.model.cus.party.DocumentsDto;
import cz.equa.camapp.rest.service.ApplicationService;
import cz.equa.camapp.rest.service.PartyService;
import cz.equa.camapp.service.ServiceException;
import cz.equa.camapp.service.applicationservice.model.BuildingLoanApplicationOwnerDTO;
import cz.equa.camapp.service.applicationservice.model.CtIdCardDTO;
import cz.rb.cus.party.handler.ApiException;
import cz.rb.tif.mapping.IdCardTransformer;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.client.spring.annotation.ExternalTaskSubscription;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.LinkedList;
import java.util.List;
import java.util.Optional;

import static cz.equa.camapp.lovs.LovIdCardPurp._1STID;


@Component("rekoSetIdCardToApplication")
@ExternalTaskSubscription(topicName = "rekoSetIdCardToApplication")
@Slf4j
public class SetIdCardToApplication extends RekoAbstractWorker {

    PartyService partyService;

    @Autowired
    public SetIdCardToApplication(ApplicationService applicationService, PartyService partyService) {
        super(applicationService);
        this.partyService = partyService;
    }

    @Override
    public void executeWorker() throws ServiceException, ApiException {
        loadProcessVariables();
        BuildingLoanApplicationOwnerDTO applicationOwner = pv.getLoanApplicationOwner();
        List<DocumentsDto> documents = partyService.getDocuments(pv.getClientId(), pv.getCorrelationId());
        if (documents == null) {
            log.info("There are no documents in this ClientId");
            return;
        }
        CtIdCardDTO idCard = getIdCard(documents);
        if (idCard != null) {
            List<CtIdCardDTO> idCards = new LinkedList<>();
            idCards.add(idCard);
            applicationOwner.setIdCards(idCards);

            applicationService.setBuildingApplication(pv.getApplKey(), null, applicationOwner, null, null, true, pv.getCorrelationId(), pv.getClContextId());
        } else {
            log.info("There is no ID document in party");
        }
    }

    protected static CtIdCardDTO getIdCard(List<DocumentsDto> documents) throws ServiceException {
        Optional<DocumentsDto> idCardDocumentOptional = documents
                .stream()
                .filter(documentDTO -> Boolean.TRUE.equals(documentDTO.getPrimaryDocument())
                        && !Boolean.TRUE.equals(documentDTO.getUnacceptable()))
                .findFirst();
        if (idCardDocumentOptional.isPresent()) {
            DocumentsDto idCardDocument = idCardDocumentOptional.get();
            CtIdCardDTO idCard = new CtIdCardDTO();
            IdCardTransformer transformedDocument = IdCardTransformer.mapRb2Eq(idCardDocument.getDocumentType());
            if (transformedDocument == null) {
                throw new ServiceException("Unsupported document type " + idCardDocument.getDocumentType());
            }
            idCard.setIdCardTpId(transformedDocument.getEqId()); // document type (OP, PAS, ...)
            idCard.setCardId(idCardDocument.getDocumentId()); // document reg. number
            idCard.setIdCardPurpId(_1STID.getCode());
            idCard.setCardIssuerCntryId(idCardDocument.getIssuerCountry());
            idCard.setIssuer(idCardDocument.getIssuer());
            idCard.setIssueDate(idCardDocument.getValidFrom());
            idCard.setExprDate(idCardDocument.getValidTo());

            return idCard;
        } else {
            return null;
        }
    }
}
