package cz.equa.camapp.reko;

import cz.equa.camapp.constants.LovEnum;
import cz.equa.camapp.lovs.*;
import cz.equa.camapp.manager.LovManager;
import cz.equa.camapp.rest.model.cus.party.AdditionalInfoPrivatePartyDto;
import cz.equa.camapp.rest.model.cus.party.GetDetailResultDto;
import cz.equa.camapp.rest.service.ApplicationService;
import cz.equa.camapp.rest.service.OperationService;
import cz.equa.camapp.service.ServiceException;
import cz.equa.camapp.service.applicationservice.model.*;
import cz.equa.camapp.service.product_service.LovTranslationsItemDTO;
import cz.equa.camapp.utils.Chain;
import cz.equa.camapp.utils.DateTimeUtils;
import cz.equa.camapp.utils.StrUtils;
import cz.rb.camapp.kafka.model.cibis.*;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
public abstract class KafkaCibisAbstract extends RekoAbstractWorker {

    protected static final String CMN = "CMN";
    protected static final String VERSION = "1.1.0";
    protected static final String SRC_SYSTEM = "ADB";
    protected static final String SYSTEM_CSA = "CSA";
    protected static final String EMPLT = "EMPLT";
    protected static final String EMPLT2 = "EMPLT2";
    protected static final String OWNBS = "OWNBS";
    protected static final String BS = "BS";
    protected static final String VERIF = "VERIF";
    protected static final String VERIFDONE = "VERIFDONE";

    protected final LovManager lovManager;
    protected final OperationService operationService;
    protected final DateTimeUtils dateTimeUtils;

    private static final Function<CtCsaPtMetadDTO, String> STRING_EXTRACTOR = CtCsaPtMetadDTO::getStrValue;
    private static final Function<CtCsaPtMetadDTO, BigDecimal> NUMBER_EXTRACTOR = CtCsaPtMetadDTO::getNumValue;
    private static final Function<CtCsaPtMetadDTO, LocalDate> DATE_EXTRACTOR = CtCsaPtMetadDTO::getDateValue;

    private static final Validator VALIDATOR;

    static {
        try (ValidatorFactory factory = Validation.buildDefaultValidatorFactory()) {
            VALIDATOR = factory.getValidator();
        }
    }

    protected KafkaCibisAbstract(ApplicationService applicationService,
                                 LovManager lovManager,
                                 OperationService operationService,
                                 DateTimeUtils dateTimeUtils
    ) {
        super(applicationService);
        this.operationService = operationService;
        this.lovManager = lovManager;
        this.dateTimeUtils = dateTimeUtils;
    }


    public <T> T getMetadataValue(Collection<CtCsaPtMetadDTO> metadata, String key,
                                  Function<CtCsaPtMetadDTO, T> valueExtractor) {
        if (metadata == null || key == null) {
            return null;
        }

        return metadata.stream()
                .filter(Objects::nonNull)
                .filter(m -> key.equals(m.getName()))
                .findFirst()
                .map(valueExtractor)
                .orElse(null);
    }

    public <T> T getMetadataValue(Collection<CtCsaPtMetadDTO> metadata, String key,
                                  Function<CtCsaPtMetadDTO, T> valueExtractor, T defaultValue) {
        T result = getMetadataValue(metadata, key, valueExtractor);
        return result != null ? result : defaultValue;
    }

    public String getSPSSMetadataStringValue(String key, Collection<CtCsaPtMetadDTO> metadata) {
        return getMetadataValue(metadata, key, STRING_EXTRACTOR);
    }

    public String getSPSSMetadataStringValue(String key, String defaultValue, Collection<CtCsaPtMetadDTO> metadata) {
        return getMetadataValue(metadata, key, STRING_EXTRACTOR, defaultValue);
    }

    public BigDecimal getSPSSMetadataNumberValue(String key, Collection<CtCsaPtMetadDTO> metadata) {
        return getMetadataValue(metadata, key, NUMBER_EXTRACTOR);
    }

    public BigDecimal getSPSSMetadataNumberValue(String key, BigDecimal defaultValue, Collection<CtCsaPtMetadDTO> metadata) {
        return getMetadataValue(metadata, key, NUMBER_EXTRACTOR, defaultValue);
    }

    public LocalDate getSPSSMetadataDateValue(String key, Collection<CtCsaPtMetadDTO> metadata) {
        return getMetadataValue(metadata, key, DATE_EXTRACTOR);
    }

    public LocalDate getSPSSMetadataDateValue(String key, LocalDate defaultValue, Collection<CtCsaPtMetadDTO> metadata) {
        return getMetadataValue(metadata, key, DATE_EXTRACTOR, defaultValue);
    }


    protected String getArrayElement(String[] array, int index) {
        return index < array.length ? array[index] : null;
    }

    protected LovTranslationsItemDTO getLovTranslationFirst(String lovId, String srcId) throws ServiceException {
        List<LovTranslationsItemDTO> response = lovManager.getLovTranslationFromCache(lovId, srcId, LovLang.CZE2, SRC_SYSTEM, SYSTEM_CSA);
        return response == null || response.isEmpty() ? null : response.get(0);
    }

    public LovTranslationsItemDTO getLovTranslationFirstSafe(String type, String id) {
        try {
            return getLovTranslationFirst(type, id);
        } catch (ServiceException e) {
            log.error("Translation error for type: {}", type, e);
            return null;
        }
    }

    protected String getLovTranslationId(String lovId, String srcId) {
        return getLovTranslationId(lovId, srcId, null);
    }

    protected <R> R getLovTranslationId(String lovId, String srcId, Function<String, R> lambda) {
        if (srcId == null || lovId == null) {
            return null;
        }

        return Chain.of(getLovTranslationFirstSafe(lovId, srcId)).map(LovTranslationsItemDTO::getTrgId).safe(t -> lambda != null ? lambda.apply(t) : (R) t).get();
    }

    protected static <T> void validateObjectRequiredByAnnotations(T object, Class<?>... groups) {
        //This method validate Object by annotations @NotNull, @NotEmpty, @NotBlank
        Set<ConstraintViolation<T>> violations = VALIDATOR.validate(object, groups);

        if (!violations.isEmpty()) {
            String messages = violations.stream()
                    .map(v -> v.getPropertyPath() + ": " + v.getMessage())
                    .collect(Collectors.joining("\n"));
            log.error("Validation failed for Object {}:\n {}", object.getClass(), messages);
            throw new IllegalArgumentException("Validation failed:\n" + messages);
        }
    }

    protected void buildInicialniRating(RiskKontrolyCommon riskKontroly, List<CtCsaPtMetadDTO> metadata) {
        riskKontroly.setProvedenaKontrolaAnalytikem("1".equals(getSPSSMetadataStringValue("provedenaKontrolaAnalytikem", metadata)));
        InicialniRatingDto inicialniRating = new InicialniRatingDto();
        inicialniRating.setStupenURG(Chain.of(getSPSSMetadataNumberValue("stupenURG", metadata)).map(BigDecimal::longValue).get());
        inicialniRating.setSkore(getSPSSMetadataNumberValue("skore", metadata));
        inicialniRating.setVerzeModelu(getSPSSMetadataStringValue("verzeModelu", metadata));
        if (inicialniRating.getSkore() != null || inicialniRating.getStupenURG() != null || inicialniRating.getVerzeModelu() != null) {
            riskKontroly.setInicialniRating(inicialniRating);
        }

    }

    @NotNull
    protected AmlOsobyDto buildAmlOsobyDto(BuildingLoanApplicationOwnerDTO buildingLoanApplOwner, GetDetailResultDto getDetailResultDto) {
        AmlOsobyDto amlOsoba = new AmlOsobyDto();
        amlOsoba.setBic(Chain.of(buildingLoanApplOwner.getRstsPartyId()).map(Long::parseLong).get());

        AdditionalInfoPrivatePartyDto additionalInfo = Chain.of(getDetailResultDto).safe(t -> t.getPerson().getAdditionalInfo()).get();
        amlOsoba.setAmlRiskRating(Chain.of(additionalInfo).map(AdditionalInfoPrivatePartyDto::getRiskRate).map(this::mapRiskRate).get());
        amlOsoba.setIblFlag(Chain.of(additionalInfo).map(AdditionalInfoPrivatePartyDto::getIblFlag).get());
        amlOsoba.setFisaFlag(Chain.of(additionalInfo).map(AdditionalInfoPrivatePartyDto::getFisaFlag).get());
        amlOsoba.setPepFlag(Chain.of(additionalInfo).map(AdditionalInfoPrivatePartyDto::getPepFlag).get());
        amlOsoba.setDatumACasProvedeniKontroly(Chain.of(getDetailResultDto).safe(t -> t.getPerson().getAdditionalInfo().getCblCheckDate()).safe(t -> dateTimeUtils.offsetDateTimeToZonedDateTime(t).format(DateTimeFormatter.ISO_OFFSET_DATE_TIME)).get());
        return amlOsoba;
    }

    public PtIncomeRstsDTO selectHighestIncome(List<PtIncomeRstsDTO> incomes, LovLang lang) {
        if (incomes.isEmpty()) {
            return null;
        }

        List<PtIncomeRstsDTO> nonNullIncomes = incomes.stream()
                .filter(income -> income.getNetIncFinal() != null)
                .toList();

        if (nonNullIncomes.isEmpty()) {
            return null;
        }

        BigDecimal maxNetInc = nonNullIncomes.stream()
                .map(PtIncomeRstsDTO::getNetIncFinal)
                .max(Comparator.naturalOrder())
                .orElse(null);

        List<PtIncomeRstsDTO> maxIncomes = nonNullIncomes.stream()
                .filter(income -> maxNetInc.equals(income.getNetIncFinal()))
                .toList();

        if (maxIncomes.size() == 1) {
            return maxIncomes.get(0);
        }

        return maxIncomes.stream()
                .min(Comparator.comparing(income -> {
                    try {
                        LovIncTp lovIncTp = lovManager.getTranslatedLovBySystem(
                                LovIncTp.class,
                                income.getIncTpId(),
                                SRC_SYSTEM,
                                SYSTEM_CSA,
                                lang);

                        return Integer.parseInt(lovIncTp.getLovDetail().get("ORD_NUM"));
                    } catch (Exception e) {
                        return Integer.MAX_VALUE;
                    }
                }))
                .orElse(maxIncomes.get(0));
    }

    protected void buildRiskKontrolyProOsobu(RiskKontrolyProOsobuCommon riskKontrolyProOsoby, BuildingLoanApplicationOwnerDTO applicationOwner) {
        List<CtCsaPtMetadDTO> metadata = applicationOwner.getMetadata();
        riskKontrolyProOsoby.setBic(Chain.of(applicationOwner.getRstsPartyId()).map(Long::parseLong).get());
        riskKontrolyProOsoby.setVysledkyRKProOsobu(new LinkedList<>());

//        kolekce
        String[] arrayTypRk = Chain.of(getSPSSMetadataStringValue("typRK", metadata)).safe(t -> t.split("\\|")).or(new String[0]);
        String[] arrayyVysledek = Chain.of(getSPSSMetadataStringValue("vysledek", metadata)).safe(t -> t.split("\\|")).or(new String[0]);
        String[] arrayHodnota = Chain.of(getSPSSMetadataStringValue("hodnota", metadata)).safe(t -> t.split("\\|")).or(new String[0]);
        String[] arrayHranice = Chain.of(getSPSSMetadataStringValue("hranice", metadata)).safe(t -> t.split("\\|")).or(new String[0]);

        for (int l = 0; l < arrayTypRk.length; l++) {
            VysledekRKDto vysledekRKOsobu = VysledekRKDto.builder()
                    .typRK(getArrayElement(arrayTypRk, l))
                    .vysledek(getArrayElement(arrayyVysledek, l))
                    .hodnota(getArrayElement(arrayHodnota, l))
                    .hranice(getArrayElement(arrayHranice, l))
                    .build();
            riskKontrolyProOsoby.getVysledkyRKProOsobu().add(vysledekRKOsobu);
        }
    }

    protected void buildAplikacniScoringPart2(List<PtIncomeRstsDTO> incomesForJobCollect,
                                              BuildingLoanApplicationOwnerDTO buildingLoanApplOwner,
                                              BuildingLoanApplicationDTO buildingLoanAppl,
                                              BuildingApplVariantPurposeDTO foundPurpose1501,
                                              BuildingApplVariantPurposeDTO foundPurpose1502,
                                              AplikacniScoringCommon aplikacniScoring) {
        List<CtCsaPtMetadDTO> metadata = buildingLoanApplOwner.getMetadata();

        BigDecimal maxNetIncFinal = incomesForJobCollect.stream()
                .map(PtIncomeRstsDTO::getNetIncFinal)
                .filter(Objects::nonNull)
                .max(BigDecimal::compareTo)
                .orElse(null);

        if (maxNetIncFinal != null && !maxNetIncFinal.equals(BigDecimal.ZERO)) {
            List<PtIncomeRstsDTO> allMaxIncomes = incomesForJobCollect.stream().filter(
                    income -> income.getNetIncFinal() != null && income.getNetIncFinal().compareTo(BigDecimal.ZERO) > 0
            ).toList();
            PtIncomeRstsDTO maxHighIncomeChosen = allMaxIncomes.get(0);


            if (Objects.equals(maxHighIncomeChosen.getIncTpId(), "OWNS") || Objects.equals(maxHighIncomeChosen.getIncTpId(), "BS")) {
                aplikacniScoring.setPracovniZarazeni1(23L);
            } else if (Objects.equals(maxHighIncomeChosen.getIncTpId(), EMPLT) || Objects.equals(maxHighIncomeChosen.getIncTpId(), EMPLT2)) {
                aplikacniScoring.setPracovniZarazeni1(getLovTranslationId(LovEnum.OCC_CATG.name(), maxHighIncomeChosen.getOccCatgId(), Long::valueOf));
            }
        }

        aplikacniScoring.setOborCinnosti1(Chain.of(getSPSSMetadataNumberValue("oborCinnosti1", metadata)).map(BigDecimal::longValue).get());
        aplikacniScoring.setCistyMesicniPrijem(getSPSSMetadataNumberValue("cistyMesicniPrijem", metadata));
        aplikacniScoring.setPocetOsobVDomacnosti(Chain.of(buildingLoanApplOwner.getNumHouseholdPerson()).map(BigDecimal::longValue).get());
        aplikacniScoring.setKraj(Chain.of(getSPSSMetadataNumberValue("kraj", metadata)).map(BigDecimal::longValue).get());
        aplikacniScoring.setCizinec(!"CZ".equals(buildingLoanApplOwner.getCitizenship()));
        aplikacniScoring.setSumaSaldVkladuAkt(getSPSSMetadataNumberValue("sumaSaldVkladuAkt", metadata));
        aplikacniScoring.setSaldoVkladuMax(getSPSSMetadataNumberValue("saldoVkladuMax", metadata));
        aplikacniScoring.setDelkaSporeniAkt(Chain.of(getSPSSMetadataNumberValue("delkaSporeniAkt", metadata)).map(BigDecimal::longValue).get());
        aplikacniScoring.setDelkaSporeniMax(Chain.of(getSPSSMetadataNumberValue("delkaSporeniMax", metadata)).map(BigDecimal::longValue).get());
        aplikacniScoring.setUverovaAngazovanostVRsts(getSPSSMetadataNumberValue("uverovaAngazovanostVRsts", metadata));
        aplikacniScoring.setDobaVBance(Chain.of(getSPSSMetadataNumberValue("dobaVBance", metadata)).map(BigDecimal::longValue).get());
        aplikacniScoring.setCelkovyPocetKontraktu(Chain.of(getSPSSMetadataNumberValue("celkovyPocetKontraktu", metadata)).map(BigDecimal::longValue).get());
        aplikacniScoring.setAktualniPocetKontraktu(Chain.of(getSPSSMetadataNumberValue("aktualniPocetKontraktu", metadata)).map(BigDecimal::longValue).get());
        aplikacniScoring.setVariantaPu(getLovTranslationId(LovEnum.BUS_PROD_SUB_TP.name(), buildingLoanAppl.getBusProdSubTp()));

        aplikacniScoring.setInterneRefinancovani(foundPurpose1501 != null);
        aplikacniScoring.setRefinancovaniMimoRsts(foundPurpose1502 != null);
        aplikacniScoring.setUcel1(Chain.of(getSPSSMetadataStringValue("ucel1", metadata)).map(Long::valueOf).get());
        aplikacniScoring.setUverBezUvedeniManzelky("M".equals(buildingLoanApplOwner.getMarStatId()) || "L".equals(buildingLoanApplOwner.getMarStatId()));
        aplikacniScoring.setDsti(getSPSSMetadataNumberValue("dsti", metadata));
        aplikacniScoring.setDti(getSPSSMetadataNumberValue("dti", metadata));
        aplikacniScoring.setSplatkaAktualnihoUveruStredniSazba(getSPSSMetadataNumberValue("splatkaAktualnihoUveruStredniSazba", metadata));
        aplikacniScoring.setAkontaceSporeni(BigDecimal.ZERO);
        aplikacniScoring.setCbSkore(getSPSSMetadataNumberValue("cbSkore", metadata));
        aplikacniScoring.setCbKod(Chain.of(getSPSSMetadataNumberValue("cbKod", metadata)).map(BigDecimal::longValue).get());
        aplikacniScoring.setSolusDluh(getSPSSMetadataNumberValue("solusDluh", metadata));
        aplikacniScoring.setPocetDuveryhodnost(Chain.of(getSPSSMetadataNumberValue("pocetDuveryhodnost", metadata)).map(BigDecimal::longValue).get());
        aplikacniScoring.setNejvyssiUpominka(Chain.of(getSPSSMetadataNumberValue("nejvyssiUpominka", metadata)).map(BigDecimal::longValue).get());
        aplikacniScoring.setPohlavi(getLovTranslationId(LovEnum.GENDER.name(), buildingLoanApplOwner.getGenderId(), Long::valueOf));
        aplikacniScoring.setRefDelkaVzam1(Chain.of(getSPSSMetadataNumberValue("refDelkaVzam1", metadata)).map(BigDecimal::longValue).get());
        aplikacniScoring.setDruhBydleni(getLovTranslationId(LovEnum.HOUS_STAT.name(), buildingLoanApplOwner.getHousingStatId(), Long::valueOf));
        aplikacniScoring.setRozhodneDatumProUrceniParametruVypoctu(getSPSSMetadataStringValue("rozhodneDatumProUrceniParametruVypoctu", metadata));
        aplikacniScoring.setDatumVypoctu(getSPSSMetadataStringValue("datumVypoctu", metadata));
        aplikacniScoring.setVysledneAplikacniSkore(getSPSSMetadataNumberValue("vysledneAplikacniSkore", metadata));
        aplikacniScoring.setVerzeFunkce(getSPSSMetadataStringValue("verzeFunkce", metadata));
    }

    protected void buildUrg(UrgCommon urg, List<CtCsaPtMetadDTO> metadata) {
        urg.setStupenURGRWA(Chain.of(getSPSSMetadataNumberValue("stupenURGRWA", metadata)).map(BigDecimal::longValue).get());
        urg.setStupenURGoldRWA(getSPSSMetadataNumberValue("stupenURGoldRWA", metadata));
        urg.setSkoreURGRWA(getSPSSMetadataNumberValue("skoreURGRWA", metadata));
        urg.setStupenURGUW(Chain.of(getSPSSMetadataNumberValue("stupenURGUW", metadata)).map(BigDecimal::longValue).get());
        urg.setSkoreURGUW(getSPSSMetadataNumberValue("skoreURGUW", metadata));
        urg.setPouziteAplikacniSkore(getSPSSMetadataNumberValue("pouziteAplikacniSkore", metadata));
        urg.setPouziteCBSkore(getSPSSMetadataNumberValue("pouziteCBSkore", metadata));
        urg.setRozhodneDatumProUrceniParametruVypoctu(getSPSSMetadataStringValue("rozhodneDatumProUrceniParametruVypoctu", metadata));
    }

    protected List<PrijemDto> prepareIncomeList(List<PtIncomeRstsDTO> incomes) {
        List<PrijemDto> incomeList = new LinkedList<>();
        final AtomicInteger counter = new AtomicInteger(1);

        incomes.stream()
                .filter(income ->
                                income.getNetIncFinal() != null &&
                                income.getNetIncFinal().compareTo(BigDecimal.ZERO) > 0
                )
                .forEach(income -> {
                    PrijemDto incomeItem = new PrijemDto();
                    incomeItem.setPoradi((long) counter.getAndIncrement());
                    incomeItem.setTypPrijmu(getLovTranslationId(LovEnum.INC_TP.name(), income.getIncTpId(), Long::valueOf));
                    incomeItem.setUznatelnaCastkaPrijmu(income.getNetIncFinal());
                    incomeItem.setMenaPrijmu(getLovTranslationId(LovEnum.CCY.name(), income.getCcyId()));

                    processEmploymentData(income, incomeItem, incomeList);
                });

        return incomeList.isEmpty() ? null : incomeList;
    }

    protected void processEmploymentData(PtIncomeRstsDTO income, PrijemDto incomeItem, List<PrijemDto> incomeList) {
        PrijemZamADapDto employmentData = new PrijemZamADapDto();

        Map<Long, Long> employmentLengthMap = Map.of(
                60L, 7L,  // 5+ years -> code 7
                24L, 6L,  // 2+ years -> code 6
                12L, 5L,  // 1+ year -> code 5
                6L, 4L    // 6+ months -> code 4
        );

        if (income.getIncTpId().equals(BS) || income.getIncTpId().equals(OWNBS)) {
            employmentData.setPovolani(23L);
            employmentData.setDelkaVZamestnani(8L);

        } else if (income.getIncTpId().equals(EMPLT) || income.getIncTpId().equals(EMPLT2)) {
            employmentData.setPovolani(getLovTranslationId(LovEnum.OCC_CATG.name(), income.getOccCatgId(), Long::valueOf));
            long monthsCount = Chain.of(income.getEmpSince())
                    .map(date -> ChronoUnit.MONTHS.between(date, LocalDate.now()))
                    .or(0L);
            employmentLengthMap.entrySet().stream()
                    .filter(entry -> monthsCount >= entry.getKey())
                    .findFirst()
                    .ifPresent(entry -> employmentData.setDelkaVZamestnani(entry.getValue()));
        }


        if (income.getIncTpId().equals(BS) ||
                income.getIncTpId().equals(OWNBS) ||
                income.getIncTpId().equals(EMPLT) ||
                income.getIncTpId().equals(EMPLT2)) {

            if (income.getNaceCodeId() == null) {
                employmentData.setDruhCinnosti(16L);
            } else {
                Long naceValue = Chain.of(lovManager.getTranslatedLovBySystem(LovVNaceFull.class, income.getNaceCodeId(), SRC_SYSTEM, SYSTEM_CSA, LovLang.CZE2))
                        .map(AbstractLov::getLovDetail)
                        .map(t -> t.get("NACE_SECTOR_ID"))
                        .map(Long::valueOf)
                        .or(16L);
                employmentData.setDruhCinnosti(naceValue);
            }
        } else {
            employmentData.setDruhCinnosti(null);
        }

        if (employmentData.getPovolani() != null || employmentData.getDruhCinnosti() != null || employmentData.getDelkaVZamestnani() != null) {
            incomeItem.setUdajePrijmuZamADap(employmentData);
        }

        FirmaPrijmuDto companyData = processCompanyData(income);
        if (companyData.getNazevFirmy() != null) {
            incomeItem.setUdajefirmyPrijmu(companyData);
        }


        incomeList.add(incomeItem);
    }

    protected FirmaPrijmuDto processCompanyData(PtIncomeRstsDTO income) {
        FirmaPrijmuDto companyData = new FirmaPrijmuDto();
        companyData.setNazevFirmy(StrUtils.truncate(income.getBusName(), 200));
        if (income.getIcoNum() != null) {
            companyData.setIcFirmy(normalizeCompanyId(income.getIcoNum()));
        }

        if (income.getNaceCodeId() != null) {
            Chain.of(lovManager.getLov(new LovNace(income.getNaceCodeId()), getLanguage(), SRC_SYSTEM))
                    .map(AbstractLov::getLovDetail)
                    .map(t -> t.get("V_NACE_FULL"))
                    .safe(Long::parseLong)
                    .exec(companyData::setNaceKod);
        }

        return companyData;
    }


    protected String normalizeCompanyId(String icoNum) {
        Map<String, String> specialIcoMap = Map.of(
                "00000000", "99999999"
        );
        return specialIcoMap.getOrDefault(icoNum, icoNum);
    }

    protected FinanciSituaceDto getPersonFinancialSituation(List<PtIncomeRstsDTO> incomesList, BuildingLoanApplicationOwnerDTO buildingLoanApplOwner, List<CtCsaPtMetadDTO> metadata) {
        FinanciSituaceDto financniSituaceOsoby = new FinanciSituaceDto();
        financniSituaceOsoby.setNavyseniZavazkuZaPar(Chain.of(getSPSSMetadataNumberValue("navyseniZavazkuZaPar", metadata)).map(BigDecimal::doubleValue).get());
        financniSituaceOsoby.setNavyseniSplatekZavazkuZaPar(Chain.of(getSPSSMetadataNumberValue("navyseniSplatekZavazkuZaPar", metadata)).map(BigDecimal::doubleValue).get());
        if (incomesList != null) {
            financniSituaceOsoby.setSeznamPrijmu(prepareIncomeList(incomesList));
        }
        MesicniNakladyParuDto nakladyDomacnosti = new MesicniNakladyParuDto();

        nakladyDomacnosti.setCelkNakladyDomacnosti(Chain.of(getSPSSMetadataNumberValue("celkNakladyDomacnosti", metadata)).get());
        nakladyDomacnosti.setPocetOsobVDomacnosti(Chain.of(buildingLoanApplOwner.getNumHouseholdPerson()).map(BigDecimal::intValue).get());
        financniSituaceOsoby.setNakladyDomacnosti(nakladyDomacnosti);
        return financniSituaceOsoby;
    }

    protected Long mapDistributionChannelToSourceChannel(String distCnlId) {
        Map<String, Long> distributionChannelMapping = Map.of(
                "10", 5L,
                "15", 1L,
                "18", 7L,
                "35", 2L
        );

        return distributionChannelMapping.getOrDefault(distCnlId, null);
    }

    protected String mapRiskRate(String inputRiskRate) {
        Map<String, String> riskRateMapping = Map.of(
                "LOW", "LOW",
                "MED", "MEDIUM",
                "HIGH", "HIGH"
        );

        return riskRateMapping.getOrDefault(inputRiskRate, null);
    }
}
