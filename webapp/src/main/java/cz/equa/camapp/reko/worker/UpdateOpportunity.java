package cz.equa.camapp.reko.worker;

import cz.equa.camapp.reko.RekoAbstractWorker;
import cz.equa.camapp.rest.service.ApplicationService;
import cz.equa.camapp.service.ServiceException;
import cz.rb.tif.mar_00022_ent_opportunity.Mar00022EntOpportunity;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.client.spring.annotation.ExternalTaskSubscription;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static cz.equa.camapp.lovs.LovApplStat.APPL_REJECTED;

@Component("rekoUpdateOpportunity")
@Slf4j
@ExternalTaskSubscription(topicName = "rekoUpdateOpportunity")
public class UpdateOpportunity extends RekoAbstractWorker {

    public static final String STATUS = "status";

    private final Mar00022EntOpportunity mar00022EntOpportunity;

    @Autowired
    public UpdateOpportunity(ApplicationService applicationService, Mar00022EntOpportunity mar00022EntOpportunity) {
        super(applicationService);
        this.mar00022EntOpportunity = mar00022EntOpportunity;
    }

    @Override
    public void executeWorker() throws ServiceException {
        String statusTxt = externalTask.getVariable(UpdateOpportunity.STATUS);
        loadProcessVariables();
        String closeOutReason = "Other";
        if (APPL_REJECTED.getCode().equals(pv.getRejectionState())) {
            closeOutReason = "BankDenialNonfinancingClient";
        }
        mar00022EntOpportunity.update(pv.getBuildingLoanApplication().getOpportunityId(), closeOutReason, statusTxt);
    }
}
