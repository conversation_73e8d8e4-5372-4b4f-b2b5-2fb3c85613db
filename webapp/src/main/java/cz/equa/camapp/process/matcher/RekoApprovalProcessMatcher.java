package cz.equa.camapp.process.matcher;

import cz.equa.camapp.lovs.LovProcessTp;
import cz.equa.camapp.process.ProcessManager;
import cz.equa.camapp.process.ProcessStarted;
import cz.equa.camapp.reko.ProcessVariables;
import cz.equa.camunda.loan.StartProcessRequest;

import java.util.Map;

public class RekoApprovalProcessMatcher extends StartProcessMatcher<StartProcessRequest> {

    public static final String PROCESS_NAME = "RekoApproval";


    @Override
    public ProcessStarted apply(StartProcessRequest request, ProcessManager target, String correlationId, String clContextId) {
        if (LovProcessTp.ADD_REKO.getCode().equals(request.getProcessTp())) {
            final Map<String, Object> processVariables = getProcessVariablesWithoutDefaultFromRequest(request);
            processVariables.put(ProcessVariables.CORRELATION_ID, correlationId);
            processVariables.put(ProcessVariables.CL_CONTEXT_ID, clContextId);

            return target.startProcess(PROCESS_NAME, calculateBusinessKey(PROCESS_NAME, processVariables), processVariables);
        }
        return ProcessStarted.NOK();
    }
}
