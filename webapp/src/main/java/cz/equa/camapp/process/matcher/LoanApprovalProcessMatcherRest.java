package cz.equa.camapp.process.matcher;

import cz.equa.camapp.lovs.LovProcessTp;
import cz.equa.camapp.process.ProcessManager;
import cz.equa.camapp.process.ProcessStarted;
import cz.equa.camapp.process.ProcessVariable;
import cz.equa.camapp.service.model.rest.loan.StartProcessRequest;

import java.util.List;
import java.util.Map;

public class LoanApprovalProcessMatcherRest extends StartProcessMatcher<StartProcessRequest> {
    public static final String PROCESS_NAME = "LoanApproval";

    List<String> enabledProcessTypes = List.of(
            LovProcessTp.ADD_UNSECURED_LOAN.getCode(),
            LovProcessTp.ADD_CREDIT_CARD.getCode(),
            LovProcessTp.ADD_ROD.getCode(),
            LovProcessTp.CH_CREDIT_CARD_INCR.getCode()
    );


    @Override
    public ProcessStarted apply(StartProcessRequest request, ProcessManager target, String correlationId, String clContextId) {
        if (enabledProcessTypes.contains(request.getProcessTp())) {
            final Map<String, Object> processVariables = getProcessVariablesFromRequest(request);
            processVariables.put(ProcessVariable.CORRELATION_ID.getKey(), correlationId);
            return target.startProcess(PROCESS_NAME, calculateBusinessKey(PROCESS_NAME, processVariables), processVariables);
        }
        return ProcessStarted.NOK();
    }
}
