package cz.equa.camapp.process.listener;

import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.engine.delegate.ExecutionListener;
import org.camunda.bpm.engine.impl.context.Context;
import org.camunda.bpm.engine.impl.db.entitymanager.DbEntityManager;
import org.camunda.bpm.engine.impl.history.event.HistoricProcessInstanceEventEntity;
import org.camunda.bpm.engine.impl.persistence.entity.ExecutionEntity;

public class BusinessKeyExecutionListener implements ExecutionListener {

    public void notify(DelegateExecution execution) throws Exception {
        String businessKey = getBusinessKeyFromVariable(execution);
        if (StringUtils.isNotBlank(businessKey)) {
            ((ExecutionEntity) execution).setBusinessKey(businessKey);

            DbEntityManager entityManager = Context.getCommandContext().getDbEntityManager();
            HistoricProcessInstanceEventEntity historicProcessInstance =
                    entityManager.getCachedEntity(HistoricProcessInstanceEventEntity.class, execution.getProcessInstanceId());

            if (historicProcessInstance != null) {
                historicProcessInstance.setBusinessKey(businessKey);
            }
        }
    }

    private String getBusinessKeyFromVariable(DelegateExecution execution) {
        return (String) execution.getVariableTyped("businessKey").getValue();
    }
}