package cz.equa.camapp.process.matcher;

import cz.equa.camapp.lovs.LovProcessTp;
import cz.equa.camapp.process.ProcessManager;
import cz.equa.camapp.process.ProcessStarted;
import cz.equa.camapp.process.ProcessVariable;
import cz.equa.camapp.service.model.rest.loan.StartProcessRequest;

import java.util.Map;

public class DeferredPaymentMatcherRest extends StartProcessMatcher<StartProcessRequest> {
	public static final String PROCESS_NAME = "DeferredPayment";

	@Override
	public ProcessStarted apply(StartProcessRequest request, ProcessManager target, String correlationId, String clContextId) {
		if (LovProcessTp.ADD_DEF_PAY.getCode().equals(request.getProcessTp())) {
			final Map<String, Object> processVariables = getProcessVariablesFromRequest(request);
            processVariables.put(ProcessVariable.CORRELATION_ID.getKey(), correlationId);
			return target.startProcess(PROCESS_NAME, calculateBusinessKey(PROCESS_NAME, processVariables), processVariables);
		}
		return ProcessStarted.NOK();
	}
}
