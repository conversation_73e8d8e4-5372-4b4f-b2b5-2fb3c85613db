package cz.equa.camapp.process.matcher;

import cz.equa.camapp.lovs.LovProcessTp;
import cz.equa.camapp.mtg.ProcessVariables;
import cz.equa.camapp.process.ProcessManager;
import cz.equa.camapp.process.ProcessStarted;
import cz.equa.camunda.loan.StartProcessRequest;

import java.util.Map;

public class MtgApprovalProcessMatcher extends StartProcessMatcher<StartProcessRequest> {

    public static final String PROCESS_NAME = "MortgageApproval";


    @Override
    public ProcessStarted apply(StartProcessRequest request, ProcessManager target, String correlationId, String clContextId) {
        if (LovProcessTp.ADD_MORTGAGE.getCode().equals(request.getProcessTp())) {
            final Map<String, Object> processVariables = getProcessVariablesWithoutDefaultFromRequest(request);
            processVariables.put(ProcessVariables.CORRELATION_ID, correlationId);
            processVariables.put(ProcessVariables.GUAR_OFFER_EMAIL_SENT, false);

            return target.startProcess(PROCESS_NAME, calculateBusinessKey(PROCESS_NAME, processVariables), processVariables);
        }
        return ProcessStarted.NOK();
    }
}
