package cz.equa.camapp.delegate.loan;

import cz.equa.camapp.config.MandatoryInputProcessVariables;
import cz.equa.camapp.delegate.ApplicationBasedProcessJavaDelegate;
import cz.equa.camapp.process.ProcessVariable;
import cz.equa.camapp.service.ServiceException;
import cz.rb.tif.cus_00046_questionnaire.Cus00046Questionnaire;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class GetAmlDetailDelegate extends ApplicationBasedProcessJavaDelegate {
    
	@Autowired
    Cus00046Questionnaire cus00046Questionnaire;

	@Override
	public void executeProcessDelegate(DelegateExecution delegateExecution) throws ServiceException {
        final String clientId = getClientID();

        boolean questionnaireExists = cus00046Questionnaire.questionnaireExists(clientId);
        log.info("Has ClientId/PartyId {} questionnaire {}", clientId, questionnaireExists);
        setProcessVariable(ProcessVariable.LOAN_APPLICATION_QUESTIONNAIRE_EXISTS, questionnaireExists);
    }
}
