package cz.equa.camapp.delegate.loan;

import cz.equa.camapp.config.MandatoryInputProcessVariables;
import cz.equa.camapp.process.ProcessVariable;
import cz.equa.camapp.rest.service.OperationService;
import cz.equa.camapp.service.ServiceException;
import cz.equa.camapp.service.product_service.PersonCLInfoDTO;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.List;

import static cz.equa.camapp.process.ProcessVariable.*;
import static cz.equa.camapp.rest.service.OperationService.PARTYID;

@Component
@MandatoryInputProcessVariables({
        BUS_APPL_ID,
        PARTY_ID,
        LOAN_APPLICATION_OWNER})
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class GetPersonInfoDelegate extends GenerateDelegate {

    @Autowired
    private OperationService operationService;

    @Override
    public void executeProcessDelegate(DelegateExecution delegateExecution) throws ServiceException {
        final String partyId = getPartyID();
        final String busApplId = getStringProcessVariable(ProcessVariable.BUS_APPL_ID);

        PersonCLInfoDTO personInfo = getPersonInfo(busApplId, partyId);
        setProcessVariable(LOAN_APPLICATION_PERSON_INFO, personInfo);
    }


    private PersonCLInfoDTO getPersonInfo(final String busApplId, final String partyId) throws ServiceException {
        List<PersonCLInfoDTO> personsInfo = operationService.getPersonInfo(PARTYID, partyId, getBusApplId(), false, getCorrelationId(), true);
        if (personsInfo == null) {
            return null;
        }
        return personsInfo.stream()
                .filter(person -> person.getApplications() != null)
                .filter(person -> person.getApplications().stream()
                        .anyMatch(appl -> busApplId.equals(appl.getBusApplId())))
                .findFirst().orElse(null);
    }
}
