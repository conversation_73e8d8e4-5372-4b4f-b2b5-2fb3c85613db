package cz.equa.camapp.delegate.loan;

import cz.equa.camapp.delegate.ApplicationBasedProcessJavaDelegate;
import cz.equa.camapp.manager.RccManager;
import cz.equa.camapp.process.ProcessVariable;
import cz.equa.camapp.rest.service.MessageService;
import cz.equa.camapp.service.ServiceException;
import cz.equa.camapp.service.applicationservice.model.LoanApplicationDTO;
import cz.equa.camapp.service.applicationservice.model.NewPersonDTO;
import cz.rb.las.parametrization.model.RCCSubtypeParametrization;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Component
public class SendConfirmationSmsCLIPDelegate extends ApplicationBasedProcessJavaDelegate {

    @Autowired
    MessageService messageService;

    @Autowired
    private RccManager rccManager;

    @Override
    public void executeProcessDelegate(DelegateExecution delegateExecution) throws ServiceException {
        NewPersonDTO newPersonDTO = getTypedProcessVariable(ProcessVariable.LOAN_APPLICATION_OWNER, NewPersonDTO.class);
        LoanApplicationDTO loanApplication = getTypedProcessVariable(ProcessVariable.LOAN_APPLICATION, LoanApplicationDTO.class);

        RCCSubtypeParametrization rccParam = rccManager.getFromRccCache(loanApplication.getBusProdSubTp());

        messageService.sendSms(newPersonDTO.getPhoneNum(),
                "Vazeny kliente, Vase zadost o navyseni limitu kreditni karty byla schvalena a limit byl navysen." +
                        " Akceptaci dohody naleznete v mobilnim a internetovem bankovnictvi. Splacite-li kartu inkasem, " +
                        "zkontrolujte prosim limit inkasa na Vasem beznem uctu. Vase Raiffeisenbank",
                getCorrelationId());
    }

    private String getDesignName(RCCSubtypeParametrization rccParam) {
        if (rccParam != null) {
            return rccParam.getDesign().getName();
        } else {
            return null;
        }
    }
}
