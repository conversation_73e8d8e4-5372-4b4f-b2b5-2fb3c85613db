package cz.equa.camapp.delegate.loan;

import cz.equa.camapp.delegate.ApplicationBasedProcessJavaDelegate;
import cz.equa.camapp.lovs.LovBusProdSubTp;
import cz.equa.camapp.process.ProcessVariable;
import cz.equa.camapp.process.ProcessVariableException;
import cz.equa.camapp.rest.service.ApplicationMapper;
import cz.equa.camapp.service.ServiceException;
import cz.equa.camapp.service.applicationservice.model.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.engine.delegate.Expression;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

@Component
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Slf4j
public class GetOverdraftApplWithVariantsDelegate extends ApplicationBasedProcessJavaDelegate {

    @Value("${testing.mock.eshop.ftp}")
    private String mockEshopFtp;

    @SuppressWarnings("unused")
    private Expression requestedVariants;

    @Override
    public void executeProcessDelegate(DelegateExecution delegateExecution) throws ServiceException {
        GetOverdraftRequest request = new GetOverdraftRequest.GetOverdraftRequestBuilder()
                .setPrimaryOwnerFlag(true)
                .setApplVariantsFlag(true)
                .setApplVariantParamsFlag(true)
                .setApplVariantTpId(getRequestedVariants())
                .build();
        GetOverdraftApplResponseDTO overdraft = applicationService.getOverdraft(getApplKey(), getBusApplId(), request, getCorrelationId());
        final LoanApplicationDTO application = ApplicationMapper.INSTANCE.overdraftApplToLoanAppl(overdraft.getOverdraftAppl());
        final NewPersonDTO applicationOwner = overdraft.getPrimaryOwner();
        setProcessVariable(ProcessVariable.OVERDRAFT_APPLICATION, overdraft.getOverdraftAppl());
        setProcessVariable(ProcessVariable.APPLICATION_VARIANTS, new ApplVariantsDTO(overdraft.getApplVariants()));
        setProcessVariable(ProcessVariable.APPLICATION_VARIANT_PARAMETERS, new ApplVarianParametersDTO(overdraft.getApplVariantParams()));

        if ((application == null) || (applicationOwner == null)) {
            log.error("Application or application owner not found");
            throw new ProcessVariableException("Application or application owner not found");
        }
        if (isTestingRequest(application)) {
            setProcessVariable(ProcessVariable.MOCK_APPLICATION, true);
        }
        setProcessVariable(ProcessVariable.LOAN_APPLICATION, application);
        setProcessVariable(ProcessVariable.LOAN_APPLICATION_OWNER, applicationOwner);
        if (application.getBusProdSubTp() == null) {
            setProcessVariable(ProcessVariable.PRODUCT_ID, LovBusProdSubTp.RCL_STANDARD.getCode());
        } else {
            setProcessVariable(ProcessVariable.PRODUCT_ID, application.getBusProdSubTp());
        }
    }

    private boolean isTestingRequest(final LoanApplicationDTO applicationDTO) {
        return applicationDTO.getFirstTouchPoint() != null && applicationDTO.getFirstTouchPoint().equals(mockEshopFtp);
    }

    private List<String> getRequestedVariants() throws ServiceException {
        String requestedVariantsTxt = requestedVariants.getExpressionText();
        if (StringUtils.isBlank(requestedVariantsTxt)) {
            throw new ServiceException("There is no requested variant in the expression");
        }
        if (requestedVariantsTxt.contains(",")) {
            return Arrays.asList(requestedVariantsTxt.split(","));
        } else {
            return List.of(requestedVariantsTxt);
        }
    }
}
