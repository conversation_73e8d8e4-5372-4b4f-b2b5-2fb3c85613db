package cz.equa.camapp.delegate;

import cz.equa.camapp.config.MandatoryInputProcessVariables;
import cz.equa.camapp.process.ProcessVariable;
import cz.equa.camapp.rest.service.ApplicationMapper;
import cz.equa.camapp.service.ServiceException;
import cz.equa.camapp.service.applicationservice.model.ApplIdKeyIdDTO;
import cz.equa.camapp.service.applicationservice.model.NewPersonDTO;
import cz.equa.camapp.service.applicationservice.model.PersonMortSetDTO;
import cz.equa.camapp.service.applicationservice.model.SetMortApplRequestDTO;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.springframework.stereotype.Component;

import java.util.LinkedList;
import java.util.List;

/**
 * To ensure compatibility of Solve applicant process with starting Solve Applicant from MortgageApproval process it is necessary to return mainApplicant when partyId is not present.
 */
@Component
@Slf4j
@MandatoryInputProcessVariables({ProcessVariable.LOAN_APPLICATION_OWNER})
public class SetMtgApplPersonDelegate extends ApplicationBasedProcessJavaDelegate {

    @Override
    public void executeProcessDelegate(DelegateExecution delegateExecution) throws ServiceException {
        NewPersonDTO applicant = getTypedProcessVariable(ProcessVariable.LOAN_APPLICATION_OWNER, NewPersonDTO.class);

        SetMortApplRequestDTO setMortApplRequestDTO = new SetMortApplRequestDTO();
        ApplIdKeyIdDTO applId = new ApplIdKeyIdDTO();
        applId.setApplKey(getSubApplKey());
        applId.setBusApplId(getBusSubApplId());
        setMortApplRequestDTO.setApplId(applId);
        List<PersonMortSetDTO> persons = new LinkedList<>();
        persons.add(ApplicationMapper.INSTANCE.map(applicant));
        setMortApplRequestDTO.setPersons(persons);
        applicationService.setMortgageAppl(setMortApplRequestDTO, getCorrelationId());
    }
}
