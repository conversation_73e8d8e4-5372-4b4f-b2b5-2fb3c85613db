package cz.equa.camapp.delegate.loan;

import cz.equa.camapp.config.MandatoryInputProcessVariables;
import cz.equa.camapp.delegate.ApplicationBasedProcessJavaDelegate;
import cz.equa.camapp.lovs.LovProcessTp;
import cz.equa.camapp.process.ProcessVariable;
import cz.equa.camapp.rest.model.cus.party.GetDetailResultDto;
import cz.equa.camapp.rest.service.ApplicationMapper;
import cz.equa.camapp.service.ServiceException;
import cz.equa.camapp.service.applicationservice.model.*;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.springframework.stereotype.Component;

import java.util.LinkedList;
import java.util.List;

@Component
@MandatoryInputProcessVariables({ProcessVariable.PROCESS_TP, ProcessVariable.GET_DETAIL_RESULT, ProcessVariable.LOAN_APPLICATION_OWNER})
public class SetApplFromPartyDelegate extends ApplicationBasedProcessJavaDelegate {

    @Override
    public void executeProcessDelegate(DelegateExecution delegateExecution) throws ServiceException {

        LoanApplicationDTO loanApplication = getTypedProcessVariable(ProcessVariable.LOAN_APPLICATION, LoanApplicationDTO.class);

        NewPersonDTO loanApplicationOwner = getTypedProcessVariable(ProcessVariable.LOAN_APPLICATION_OWNER, NewPersonDTO.class);
        if (loanApplicationOwner == null) {
            return;
        }
        GetDetailResultDto getDetailResultDto = getTypedProcessVariable(ProcessVariable.GET_DETAIL_RESULT, GetDetailResultDto.class);

        if (getDetailResultDto.getPerson() != null && getDetailResultDto.getPerson().getAdditionalInfo() != null
        && getDetailResultDto.getPerson().getAdditionalInfo().getPepFlag() != null){
            loanApplicationOwner.setPep(getDetailResultDto.getPerson().getAdditionalInfo().getPepFlag());
        }
            else if (getDetailResultDto.getCompany() != null && getDetailResultDto.getCompany().getAdditionalInfo() != null
                    && getDetailResultDto.getCompany().getAdditionalInfo().getPepFlag() != null){
            loanApplicationOwner.setPep(getDetailResultDto.getCompany().getAdditionalInfo().getPepFlag());
        }

        String productTp = getTypedProcessVariable(ProcessVariable.PROCESS_TP, String.class);
        if (LovProcessTp.ADD_MORTGAGE.getCode().equalsIgnoreCase(productTp)) {
            SetMortApplRequestDTO setMortApplRequestDTO = new SetMortApplRequestDTO();
            ApplIdKeyIdDTO applId = new ApplIdKeyIdDTO();
            applId.setApplKey(getLongProcessVariable(ProcessVariable.SUB_APPL_KEY) != null ? getLongProcessVariable(ProcessVariable.SUB_APPL_KEY) : getLongProcessVariable(ProcessVariable.APPLICATION_KEY));
            applId.setBusApplId(StringUtils.isNotBlank(getStringProcessVariable(ProcessVariable.BUS_SUB_APPL_ID)) ? getStringProcessVariable(ProcessVariable.BUS_SUB_APPL_ID) : getStringProcessVariable(ProcessVariable.BUS_APPL_ID));
            setMortApplRequestDTO.setApplId(applId);
            List<PersonMortSetDTO> persons = new LinkedList<>();
            persons.add(ApplicationMapper.INSTANCE.map(loanApplicationOwner));
            setMortApplRequestDTO.setPersons(persons);
            applicationService.setMortgageAppl(setMortApplRequestDTO, getCorrelationId());
        } else if (LovProcessTp.ADD_ROD.getCode().equalsIgnoreCase(productTp)) {
            applicationService.setOverdraft(getApplKey(), loanApplication, loanApplicationOwner, null, true, getCorrelationId());
        } else {
            applicationService.setApplication(getApplKey(), getLoanApplication(), loanApplicationOwner, null, true, getCorrelationId());
        }

        setProcessVariable(ProcessVariable.LOAN_APPLICATION_OWNER, loanApplicationOwner);
    }
}
