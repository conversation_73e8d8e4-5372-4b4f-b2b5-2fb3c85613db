package cz.equa.camapp.delegate.storno;

import cz.equa.camapp.delegate.ApplicationBasedProcessJavaDelegate;
import cz.equa.camapp.process.ProcessVariable;
import cz.equa.camapp.service.ServiceException;
import cz.equa.camapp.service.applicationservice.model.ApplicationToCancelDTO;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.springframework.stereotype.Component;

@Component
public class ExtractApplIdentifiersDelegate extends ApplicationBasedProcessJavaDelegate {

	@Override
	public void executeProcessDelegate(DelegateExecution delegateExecution) throws ServiceException {
        final ApplicationToCancelDTO applicationToCancel = getTypedProcessVariable(ProcessVariable.STORNO_APPLICATION_LIST_ELEMENT, ApplicationToCancelDTO.class);
        setProcessVariable(ProcessVariable.APPLICATION_KEY, (applicationToCancel.getApplKey() != null)? applicationToCancel.getApplKey().longValue() : null);
        setProcessVariable(ProcessVariable.BUS_APPL_ID, applicationToCancel.getBusApplId());
        setProcessVariable(ProcessVariable.CL_CONTEXT_ID, applicationToCancel.getClContextId());
    }    
}
