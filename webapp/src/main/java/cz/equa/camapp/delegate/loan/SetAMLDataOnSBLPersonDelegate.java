package cz.equa.camapp.delegate.loan;

import cz.equa.camapp.delegate.ApplicationBasedProcessJavaDelegate;
import cz.equa.camapp.model.party.PartyDTO;
import cz.equa.camapp.model.party.UpdatePartyDto;
import cz.equa.camapp.model.update_party.UpdateItemDTO;
import cz.equa.camapp.model.update_party.UpdateLocalDateItemDTO;
import cz.equa.camapp.model.update_party.UpdatedBooleanItemDTO;
import cz.equa.camapp.model.update_party.additional_info.*;
import cz.equa.camapp.model.update_party.basic_info.BasicInfoDTO;
import cz.equa.camapp.model.update_party.basic_info.PrivatePartyDTO;
import cz.equa.camapp.process.ProcessVariable;
import cz.equa.camapp.rest.model.cus.party.GetDetailResultDto;
import cz.equa.camapp.rest.service.ApplicationMapper;
import cz.equa.camapp.rest.service.PartyService;
import cz.equa.camapp.service.ServiceException;
import cz.rb.tif.cus_00041_ent_party.Cus00041EntParty;
import cz.equa.camapp.service.applicationservice.model.NewAddressDTO;
import cz.equa.camapp.service.applicationservice.model.NewPersonDTO;
import cz.equa.camapp.service.applicationservice.model.PtIncomeListDTO;
import cz.equa.camapp.utils.SetAmlDataUtils;
import cz.rb.tif.cus_00041_ent_party.mapping.Esa;
import cz.rb.tif.cus_00041_ent_party.mapping.EsaTransformer;
import cz.rb.tif.cus_00041_ent_party.mapping.SourceFunds;
import cz.rb.tif.cus_00041_ent_party.mapping.SourceFundsTransformer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;

import static cz.equa.camapp.process.ProcessVariable.*;
import static cz.rb.tif.cus_00041_ent_party.mapping.EsaTransformer.ESI_ID_1430000;
import static cz.rb.tif.cus_00041_ent_party.mapping.EsaTransformer.ESI_ID_2004300;

@Component
@Slf4j
public class SetAMLDataOnSBLPersonDelegate extends ApplicationBasedProcessJavaDelegate {

    @Autowired
    private PartyService partyService;
    @Autowired
    private Cus00041EntParty entParty;

    @Override
    public void executeProcessDelegate(DelegateExecution delegateExecution) throws ServiceException {
        GetDetailResultDto getDetailResultDto = getTypedProcessVariable(GET_DETAIL_RESULT, GetDetailResultDto.class);
        if (getDetailResultDto == null) {
            //FIXME - deprecated condition, should remove Methods with "executeProcessDelegateWithParty"
            log.warn("This should not happen !! , GET_DETAIL_RESULT is null, should be Called the Rest API to get the party detail");
            log.info("GET_DETAIL_RESULT not found, falling back to deprecated PARTY processing");
            executeProcessDelegateWithParty(delegateExecution);
            return;
        }

        Boolean loanApplicationPartyOwnsYet = getTypedProcessVariable(LOAN_APPLICATION_PARTY_OWNS_ACCOUNT_ALREADY, Boolean.class);
        Boolean loanApplicationQuestionnaireExists = getTypedProcessVariable(LOAN_APPLICATION_QUESTIONNAIRE_EXISTS, Boolean.class);
        String processTp = getTypedProcessVariable(ProcessVariable.PROCESS_TP, String.class);
        PtIncomeListDTO incomes = getTypedProcessVariable(ProcessVariable.LOAN_APPLICATION_INCOMES, PtIncomeListDTO.class);
        NewPersonDTO loanApplicationOwner = getTypedProcessVariable(LOAN_APPLICATION_OWNER, NewPersonDTO.class);
        final String partyId = getPartyID();
        UpdatePartyDto updatePartyDto = new UpdatePartyDto();
        // basic info
        updatePartyDto.setBasicInfo(new BasicInfoDTO());
        updatePartyDto.getBasicInfo().setPartyType(BasicInfoDTO.PARTY_TYPE_PRIVATE);
        updatePartyDto.getBasicInfo().setPrivateParty(new PrivatePartyDTO());
        updatePartyDto.setAdditionalInfo(SetAmlDataUtils.createAdditionalInfo(processTp, incomes, loanApplicationOwner));

        Boolean isClient = null;
        Boolean isEligible = null;
        if (getDetailResultDto.getPerson() != null) {
            isClient = getDetailResultDto.getPerson().getIsClient();
            isEligible = getDetailResultDto.getPerson().getIsEligible();
        } else if (getDetailResultDto.getCompany() != null) {
            isClient = getDetailResultDto.getCompany().getIsClient();
            isEligible = getDetailResultDto.getCompany().getIsEligible();
        }

        if (((loanApplicationPartyOwnsYet == null || !loanApplicationPartyOwnsYet) && (loanApplicationQuestionnaireExists == null || !loanApplicationQuestionnaireExists))
                || (getDetailResultDto != null && Boolean.FALSE.equals(isClient) && Boolean.FALSE.equals(isEligible))) {
            assert getDetailResultDto != null;
            partyService.update(partyId, updatePartyDto.getBasicInfo(), updatePartyDto.getAdditionalInfo(), getCorrelationId());
        }
    }


    @Deprecated(forRemoval = true)
    private void executeProcessDelegateWithParty(DelegateExecution delegateExecution) throws ServiceException {
        Boolean loanApplicationPartyOwnsYet = getTypedProcessVariable(LOAN_APPLICATION_PARTY_OWNS_ACCOUNT_ALREADY, Boolean.class);
        Boolean loanApplicationQuestionnaireExists = getTypedProcessVariable(LOAN_APPLICATION_QUESTIONNAIRE_EXISTS, Boolean.class);
        PartyDTO party = getTypedProcessVariable(PARTY, PartyDTO.class);
        UpdatePartyDto updatePartyDto = new UpdatePartyDto();
        // basic info
        updatePartyDto.setBasicInfo(new BasicInfoDTO());
        updatePartyDto.getBasicInfo().setPartyType(BasicInfoDTO.PARTY_TYPE_PRIVATE);
        updatePartyDto.getBasicInfo().setPrivateParty(new PrivatePartyDTO());
        updatePartyDto.setAdditionalInfo(createAdditionalInfo(party));


        if (((loanApplicationPartyOwnsYet == null || !loanApplicationPartyOwnsYet) && (loanApplicationQuestionnaireExists == null || !loanApplicationQuestionnaireExists))
                || (party != null && !party.getBasicInfo().getIsClient() && !party.getBasicInfo().getIsEligible())) {
            entParty.update(party.getId(), updatePartyDto.getBasicInfo(), updatePartyDto.getAdditionalInfo(), ApplicationMapper.INSTANCE.dtoToDto(party.getContacts()));
        }
    }

    @Deprecated(forRemoval = true)
    private AdditionalInfoDTO createAdditionalInfo(PartyDTO party) {
        String processTp = getTypedProcessVariable(ProcessVariable.PROCESS_TP, String.class);
        AdditionalInfoDTO additionalInfo = createAdditionalInfoWithTaxResidency();
        additionalInfo.setAml(new AmlDTO());
        additionalInfo.getAml().setPlannedTransactionsType("EU15");

        if ("ADD_DEF_PAY".equals(processTp)) {
            return createDeferredPaymentAdditionalInfo(additionalInfo);
        }

        return createDefaultAdditionalInfo(additionalInfo, party);
    }

    @Deprecated(forRemoval = true)
    private AdditionalInfoDTO createDefaultAdditionalInfo(AdditionalInfoDTO additionalInfo, PartyDTO party) {
        PtIncomeListDTO incomes = getTypedProcessVariable(ProcessVariable.LOAN_APPLICATION_INCOMES, PtIncomeListDTO.class);

        if ((incomes == null) || CollectionUtils.isEmpty(incomes.getIncomes())) {
            log.debug("There ar no incomes in process");
            return additionalInfo;
        }
        String primaryOwnerIncome = incomes.getIncomes().get(0).getIncTpId();
        if (primaryOwnerIncome == null) {
            log.debug("Primary income is null");
            return additionalInfo;
        }
        SourceFunds sourceFunds = SourceFundsTransformer.getTransformedValue(primaryOwnerIncome);
        additionalInfo.setSourceOfFunds((sourceFunds != null) ? sourceFunds.getId() : null);
        if ((party.getAdditionalInfo() != null) &&
                (party.getAdditionalInfo().getTaxation() != null) &&
                (party.getAdditionalInfo().getTaxation().getCrs() != null) &&
                (party.getAdditionalInfo().getTaxation().getCrs().getTaxResidencyDetail() != null) &&
                "CZ".equals(party.getAdditionalInfo().getTaxation().getCrs().getTaxResidencyDetail().getCountry())) {

            log.debug("Looking for residential ESA value");
            Esa esa = EsaTransformer.getResidentEsaTransformMap(primaryOwnerIncome);
            additionalInfo.setEsa95(esa != null ? esa.getId() : null);
        } else {
            log.debug("Looking for non-residential ESA value");
            Esa esa = EsaTransformer.getEsaTransformMap(primaryOwnerIncome);
            additionalInfo.setEsa95(esa != null ? esa.getId() : null);
        }
        return additionalInfo;
    }

    @Deprecated(forRemoval = true)
    private AdditionalInfoDTO createAdditionalInfoWithTaxResidency() {
        NewPersonDTO loanApplicationOwner = getTypedProcessVariable(LOAN_APPLICATION_OWNER, NewPersonDTO.class);
        AdditionalInfoDTO additionalInfo = new AdditionalInfoDTO();
        additionalInfo.setProductCustomersWantsUse("CA");

        if (StringUtils.isBlank(loanApplicationOwner.getTaxDomicile())) {
            log.debug("Loan application owner has no tax domicile filled");
            return additionalInfo;
        }
        additionalInfo.setTaxation(createTaxation(loanApplicationOwner));
        return additionalInfo;
    }

    @Deprecated(forRemoval = true)
    private TaxationDTO createTaxation(NewPersonDTO loanApplicationOwner) {
        TaxationDTO taxation = new TaxationDTO();
        taxation.setTaxResidency(new UpdateItemDTO(loanApplicationOwner.getTaxDomicile(), false));
        taxation.setCrs(createCrs(loanApplicationOwner));
        return taxation;
    }

    @Deprecated(forRemoval = true)
    private CrsDTO createCrs(NewPersonDTO loanApplicationOwner) {
        CrsDTO crs = new CrsDTO();
        crs.setSignatureDate(new UpdateLocalDateItemDTO(LocalDate.now(), false));
        crs.setFutureProductOwner(new UpdatedBooleanItemDTO(true, false));
        crs.setTaxResidencyDetail(createTaxResidencyDetail(loanApplicationOwner));
        return crs;
    }

    @Deprecated(forRemoval = true)
    private TaxResidencyDetailDTO createTaxResidencyDetail(NewPersonDTO loanApplicationOwner) {
        TaxResidencyDetailDTO taxResidencyDetail = new TaxResidencyDetailDTO();
        taxResidencyDetail.setCountry(new UpdateItemDTO(loanApplicationOwner.getTaxDomicile(), false));
        taxResidencyDetail.setSourceCode(new UpdateItemDTO("SC", false));
        return taxResidencyDetail;
    }

    @Deprecated(forRemoval = true)
    private AdditionalInfoDTO createDeferredPaymentAdditionalInfo(AdditionalInfoDTO additionalInfo) {
        NewPersonDTO applicationOwner = getTypedProcessVariable(ProcessVariable.LOAN_APPLICATION_OWNER, NewPersonDTO.class);
        NewAddressDTO permAddr = applicationOwner.getPermAddr();
        String countryId = permAddr != null ? permAddr.getCntryId() : null;

        String esa95Value = "CZ".equals(countryId) ? ESI_ID_1430000 : ESI_ID_2004300;

        additionalInfo.setEsa95(esa95Value);
        return additionalInfo;
    }
}
