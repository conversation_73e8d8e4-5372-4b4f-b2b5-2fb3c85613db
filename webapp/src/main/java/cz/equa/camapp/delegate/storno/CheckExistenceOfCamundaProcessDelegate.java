package cz.equa.camapp.delegate.storno;

import cz.equa.camapp.delegate.ApplicationBasedProcessJavaDelegate;
import cz.equa.camapp.process.ProcessUtils;
import cz.equa.camapp.process.ProcessVariable;
import cz.equa.camapp.service.ServiceException;
import cz.equa.camapp.service.applicationservice.model.ApplicationToCancelDTO;
import org.camunda.bpm.engine.ProcessEngine;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class CheckExistenceOfCamundaProcessDelegate extends ApplicationBasedProcessJavaDelegate {

    @Autowired
	private ProcessEngine processEngine;

	@Override
	public void executeProcessDelegate(DelegateExecution delegateExecution) throws ServiceException {
		final ApplicationToCancelDTO application = getTypedProcessVariable(ProcessVariable.STORNO_APPLICATION_LIST_ELEMENT,
                                                                           ApplicationToCancelDTO.class);

        Long applKey = null;
        if (application.getApplKey() != null) {
            applKey = application.getApplKey().longValue();
        }
        
        setProcessVariable(ProcessVariable.STORNO_APPLICATION_CAM_PROCESS_EXISTS,
                           ProcessUtils.checkNotCompletedProcessExistence(processEngine, applKey, getBusApplId()));
	}
}
