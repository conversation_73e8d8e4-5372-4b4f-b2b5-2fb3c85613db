package cz.equa.camapp.delegate;

import cz.equa.camapp.config.MandatoryOutputProcessVariables;
import cz.equa.camapp.process.ProcessVariable;
import cz.equa.camapp.rest.service.OperationService;
import cz.equa.camapp.service.ServiceException;
import cz.equa.camapp.service.applicationservice.model.LoanApplicationDTO;
import cz.equa.camapp.utils.Constants;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.springframework.stereotype.Component;


@Component
@MandatoryOutputProcessVariables(ProcessVariable.CASHLOAN_CHECK_APPL_USER_RIGHTS_RESULT)
@Slf4j
public class CheckApplUserRightsDelegate extends ApplicationBasedProcessJavaDelegate {

    private final OperationService operationService;

    public CheckApplUserRightsDelegate(OperationService operationService) {
        this.operationService = operationService;
    }

    @Override
    public void executeProcessDelegate(DelegateExecution delegateExecution) throws ServiceException {
        final Long applKey = getApplKey();
        final String clientId = getStringProcessVariable(ProcessVariable.CLIENT_ID);
        final LoanApplicationDTO application = getTypedProcessVariable(ProcessVariable.LOAN_APPLICATION, LoanApplicationDTO.class);
        final String busApplId = getBusApplId();

        boolean result;
        try {
            result = operationService.checkApplUserRights(clientId, Constants.SBL_SYSTEM_ID, applKey, application, getCorrelationId());
        } catch (cz.equa.camapp.service.ServiceException e) {
            throw new ServiceException("NO INCIDENT : " + e.getMessage(), e);
        }

        if (!result) {
            delegateExecution.setVariable(ProcessVariable.CASHLOAN_CHECK_APPL_USER_RIGHTS_RESULT.getKey(), false);
            log.info("Request {}: product with sub prod id [{}] and appl key [{}] for client [{}] not allowed - return from action", busApplId,
                    application.getBusProdSubTp(), applKey, clientId);
            return;
        }

        delegateExecution.setVariable(ProcessVariable.CASHLOAN_CHECK_APPL_USER_RIGHTS_RESULT.getKey(), true);
    }
}
