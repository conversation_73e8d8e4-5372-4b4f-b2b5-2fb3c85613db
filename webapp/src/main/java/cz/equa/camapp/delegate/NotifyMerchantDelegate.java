package cz.equa.camapp.delegate;

import cz.equa.camapp.process.ProcessVariable;
import cz.equa.camapp.process.StringListArrayProcessVariableDTO;
import cz.equa.camapp.rest.service.ApprovalService;
import cz.equa.camapp.service.ServiceException;
import cz.equa.camapp.service.applicationservice.model.LoanApplicationDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.engine.delegate.Expression;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Slf4j
public class NotifyMerchantDelegate extends ApplicationBasedProcessJavaDelegate {

    @Autowired
    ApprovalService applicationApprovalService;

    Expression event;

    @Override
    public void executeProcessDelegate(DelegateExecution delegateExecution) throws ServiceException {
        if (isMockApplication()) {
            log.info("Skipping execution of mock application");
            return;
        }
        final String eventName = (String) event.getValue(delegateExecution);
        if (StringUtils.isEmpty(eventName)) {
            log.error("Event name cannot be empty");
            throw new RuntimeException("Event name cannot be empty");
        }
        StringListArrayProcessVariableDTO merchantNotifications = getMerchantNotifications();
        final List<String> eventsNotified = merchantNotifications.getStringList();
        if (eventsNotified.contains(eventName)) {
            log.warn("Merchant has already been notified of event {}", eventName);
            return;
        }

        LoanApplicationDTO loanApplicationDTO = getTypedProcessVariable(ProcessVariable.LOAN_APPLICATION, LoanApplicationDTO.class);
        final String orderNr = loanApplicationDTO.getOrderNr();
        if (StringUtils.isEmpty(orderNr)) {
            log.warn("OrderNr is empty. Merchant won't be notified of {}", eventName);
            return;
        }

        final String merchantNotificationUrl = loanApplicationDTO.getUrl();
        if (StringUtils.isEmpty(merchantNotificationUrl)) {
            log.warn("Notification URL is empty. Merchant won't be notified of {}", eventName);
            return;
        }

        applicationApprovalService.notifyMerchant(merchantNotificationUrl, orderNr);
        merchantNotifications.add(eventName);
        setProcessVariable(ProcessVariable.MERCHANT_NOTIFICATIONS, merchantNotifications);
    }
}

