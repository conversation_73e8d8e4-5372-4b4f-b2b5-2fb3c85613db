package cz.equa.camapp.delegate.loan;

import cz.equa.camapp.config.MandatoryInputProcessVariables;
import cz.equa.camapp.delegate.ApplicationBasedProcessJavaDelegate;
import cz.equa.camapp.process.ProcessVariable;
import cz.equa.camapp.service.ServiceException;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.springframework.stereotype.Component;

@Component
@MandatoryInputProcessVariables({ProcessVariable.LOAN_APPLICATION_SIGNING_CHANNEL})
public class UpdateSignChannelInLoanAppDelegate extends ApplicationBasedProcessJavaDelegate {

	@Override
	public void executeProcessDelegate(final DelegateExecution delegateExecution) throws ServiceException {
		String signingChannelValue = getStringProcessVariable(ProcessVariable.LOAN_APPLICATION_SIGNING_CHANNEL);
        String signPosId = getStringProcessVariable(ProcessVariable.BRANCH_CODE);

        String productId = getTypedProcessVariable(ProcessVariable.PRODUCT_ID, String.class);
        applicationService.setSignChannelOnApplication(productId, getApplKey(), signingChannelValue, signPosId, getCorrelationId());
	}
}
