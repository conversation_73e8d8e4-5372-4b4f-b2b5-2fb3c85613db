package cz.equa.camapp.delegate.loan;

import com.neovisionaries.i18n.CountryCode;
import cz.equa.camapp.delegate.ApplicationBasedProcessJavaDelegate;
import cz.equa.camapp.delegate.loan.card.CardCalculationsUtil;
import cz.equa.camapp.manager.LovManager;
import cz.equa.camapp.manager.RccManager;
import cz.equa.camapp.model.card_processing.DocumentDTO;
import cz.equa.camapp.model.card_processing.OperationTypeEnum;
import cz.equa.camapp.model.card_processing.UpdateAddressDTO;
import cz.equa.camapp.model.new_card.AccountDto;
import cz.equa.camapp.process.ProcessVariable;
import cz.equa.camapp.rest.service.CardProcessingService;
import cz.equa.camapp.rest.util.Utils;
import cz.equa.camapp.service.ServiceException;
import cz.equa.camapp.service.applicationservice.model.CreditCardApplDTO;
import cz.equa.camapp.service.applicationservice.model.LoanApplicationDTO;
import cz.equa.camapp.service.applicationservice.model.NewAddressDTO;
import cz.equa.camapp.service.applicationservice.model.NewPersonDTO;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static cz.equa.camapp.delegate.loan.card.CardCalculationsUtil.CARD_DISTRIBUTION_TYPE_BRANCH_EXTERNAL;
import static cz.equa.camapp.delegate.loan.card.CardCalculationsUtil.CARD_DISTRIBUTION_TYPE_BRANCH_INTERNAL;

@Component
public class AddressesDataUpdatingDelegate extends ApplicationBasedProcessJavaDelegate {

    @Autowired
    CardProcessingService cardProcessingService;

    @Autowired
    RccManager rccManager;

    @Autowired
    LovManager lovManager;

    @Override
    public void executeProcessDelegate(DelegateExecution delegateExecution) throws ServiceException {
        LoanApplicationDTO loanApplicationDTO = getTypedProcessVariable(ProcessVariable.LOAN_APPLICATION, LoanApplicationDTO.class);
        NewPersonDTO newPersonDTO = getTypedProcessVariable(ProcessVariable.LOAN_APPLICATION_OWNER, NewPersonDTO.class);
        CreditCardApplDTO cardAppl = getTypedProcessVariable(ProcessVariable.CARD_APPL, CreditCardApplDTO.class);
        cz.equa.camapp.model.CardDto newCard = getTypedProcessVariable(ProcessVariable.NEW_CARD, cz.equa.camapp.model.CardDto.class);

        UpdateAddressDTO request = new UpdateAddressDTO();
        AccountDto account = new AccountDto();
        account.setIssuer(5);
        account.setBranch(CardCalculationsUtil.getBranchCode(cardAppl, getLanguage(), lovManager));

        account.setProduct(rccManager.getFromRccCache(loanApplicationDTO.getBusProdSubTp()).getRpCProduct().toString());
        account.setAccountNumber(newCard.getAccountNumber());
        request.setAccount(account);

        DocumentDTO document = new DocumentDTO();
        document.setDocumentType(49);
        document.setDocument(newPersonDTO.getSiebelId());
        request.setDocument(document);

        NewAddressDTO address = newPersonDTO.getPostalAddr();
        if (address == null) {
            address = newPersonDTO.getPermAddr();
        }
        
        request.setOperationType(OperationTypeEnum.A);
        request.setAddressType(1);
        request.setValidTo(LocalDate.of(2999,12,31));
        request.setValidFrom(LocalDate.now());
        request.setStreet(address.getStreetName());
        request.setDoorNumber(getDoorNumber(address));
        request.setNeighborhood(address.getDetail());
        request.setCity(address.getCityName());
        request.setComplement(createComplement(address, cardAppl));
        request.setCountry(CountryCode.getByCode(address.getCntryId()).getNumeric());
        request.setZip(address.getZip());
        request.setMobile(newPersonDTO.getPhoneNum());
        request.setEmail(newPersonDTO.getEmail());
        cardProcessingService.updateAddress(Utils.getNowWithoutNanoseconds(), request, getCorrelationId());

        request.setAddressType(5);
        cardProcessingService.updateAddress(Utils.getNowWithoutNanoseconds(), request, getCorrelationId());
    }
    
    private String getDoorNumber(NewAddressDTO address) {
        if ((address == null) || (address.getStreetNum() == null)) {
            return null;
        }

        Pattern pattern = Pattern.compile("\\d[a-zA-Z0-9]{0,4}");
        Matcher matcher = pattern.matcher(address.getStreetNum());

        if (matcher.find()) {
            return matcher.group();
        }
        return null;
    }

    private String createComplement(NewAddressDTO address, CreditCardApplDTO cardAppl) {
        StringBuilder complement = new StringBuilder();
        String streetNum = address.getStreetNum();
        if (((streetNum != null) && streetNum.contains("/")) || (address.getLandRegnNum() != null)) {
            complement.append("/");
        } else {
            complement.append(" ");
        }
        if ((streetNum != null) && streetNum.contains("/")) {
            complement.append(String.format("%-4s", streetNum.substring(streetNum.indexOf("/") + 1)));
        } else if (address.getLandRegnNum() != null) {
            complement.append(String.format("%-4s", address.getLandRegnNum().substring(0,3)));
        } else {
            complement.append("    ");
        }
        boolean cardDistributionTypeInternalAndBranch003 = cardAppl.getCardDistributionType().equals(CARD_DISTRIBUTION_TYPE_BRANCH_INTERNAL) &&
                                                           "003".equals(cardAppl.getCardDistributionInternalBranchId());
        if(cardDistributionTypeInternalAndBranch003) {
            complement.append("C");
        } else if (cardAppl.getCardDistributionType().equals(CARD_DISTRIBUTION_TYPE_BRANCH_EXTERNAL) || cardAppl.getCardDistributionType().equals(CARD_DISTRIBUTION_TYPE_BRANCH_INTERNAL)) {
            complement.append("I");
        } else {
            complement.append("S");
        }
        complement.append("   ");
        if(cardDistributionTypeInternalAndBranch003) {
            complement.append("C");
        } else {
            complement.append("   ");
        }
        return complement.toString();
    }
}
