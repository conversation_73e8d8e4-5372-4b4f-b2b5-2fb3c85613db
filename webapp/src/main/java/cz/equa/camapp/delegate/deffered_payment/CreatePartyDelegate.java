package cz.equa.camapp.delegate.deffered_payment;

import cz.equa.camapp.delegate.ApplicationBasedProcessJavaDelegate;
import cz.equa.camapp.model.party.BirthInfoDTO;
import cz.equa.camapp.model.party.basic_info.BasicInfoDTO;
import cz.equa.camapp.model.party.basic_info.PrivatePartyDTO;
import cz.equa.camapp.model.party.contacts.AddressDTO;
import cz.equa.camapp.model.party.contacts.ContactsDTO;
import cz.equa.camapp.model.party.contacts.EmailDTO;
import cz.equa.camapp.model.party.contacts.PhoneDTO;
import cz.equa.camapp.process.ProcessVariable;
import cz.equa.camapp.rest.service.PartyService;
import cz.equa.camapp.service.ServiceException;
import cz.equa.camapp.service.applicationservice.model.NewPersonDTO;
import org.camunda.bpm.engine.delegate.BpmnError;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static cz.equa.camapp.utils.ApplicationUtils.isAddressEquals;

@Component
public class CreatePartyDelegate extends ApplicationBasedProcessJavaDelegate {

    @Autowired
    private PartyService partyService;

    @Override
    public void executeProcessDelegate(DelegateExecution delegateExecution) throws ServiceException {
        final NewPersonDTO applicationOwner = getTypedProcessVariable(ProcessVariable.LOAN_APPLICATION_OWNER, NewPersonDTO.class);

        BasicInfoDTO basicInfo = new BasicInfoDTO();
        basicInfo.setPartyType(cz.equa.camapp.model.update_party.basic_info.BasicInfoDTO.PARTY_TYPE_PRIVATE);
        PrivatePartyDTO privateParty = new PrivatePartyDTO();
        privateParty.setFirstName(applicationOwner.getFirstName());
        privateParty.setLastName(applicationOwner.getFamilyName());
        if (applicationOwner.getGenderId() != null) {
            privateParty.setGender(applicationOwner.getGenderId());
        }
        BirthInfoDTO birthInfo = new BirthInfoDTO();
        birthInfo.setBirthDate(applicationOwner.getBirthDate());
        birthInfo.setCityOfBirth(applicationOwner.getBirthPlace());
        if (applicationOwner.getBirthCntryId() != null) {
            birthInfo.setCountryOfBirth(applicationOwner.getBirthCntryId());
        }
        birthInfo.setBirthCode(applicationOwner.getRcNum());
        privateParty.setBirthInfo(birthInfo);
        if (applicationOwner.getCitizenship() != null) {
            privateParty.setCitizenship(applicationOwner.getCitizenship());
        }
        privateParty.setBirthLastName(applicationOwner.getBirthName());
        if (applicationOwner.getEduStatId() != null) {
            privateParty.setTitle(applicationOwner.getEduStatId());
        }
        basicInfo.setPrivateParty(privateParty);

        ContactsDTO contacts = new ContactsDTO();

        if (applicationOwner.getEmail() != null) {
            EmailDTO email = new EmailDTO();
            email.setAddress(applicationOwner.getEmail());
            email.setPreferred(true);
            contacts.addEmail(email);
        }

        if (applicationOwner.getPhoneNum() != null) {
            PhoneDTO phone = new PhoneDTO();
            phone.setNumber(applicationOwner.getPhoneNum());
            phone.setPreferred(true);
            contacts.addPhone(phone);
        }

        if (applicationOwner.getPostalAddr() != null && !isAddressEquals(applicationOwner)) {
            AddressDTO postalAddr = new AddressDTO();
            postalAddr.setAddressee(getAddressee(applicationOwner));
            postalAddr.setStreet(applicationOwner.getPostalAddr().getStreetName());
            postalAddr.setNumber(applicationOwner.getPostalAddr().getStreetNum());
            postalAddr.setCity(applicationOwner.getPostalAddr().getCityName());
            postalAddr.setPostalCode(applicationOwner.getPostalAddr().getZip());
            postalAddr.setCountry(applicationOwner.getPostalAddr().getCntryId());
            postalAddr.setAddressType(AddressDTO.ADDRESS_TYPE_MAIL1);
            postalAddr.setPreferred(true);
            contacts.addAddress(postalAddr);
        }

        if (applicationOwner.getPermAddr() != null) {
            AddressDTO addressPerm = new AddressDTO();
            addressPerm.setAddressee(getAddressee(applicationOwner));
            addressPerm.setStreet(applicationOwner.getPermAddr().getStreetName());
            addressPerm.setNumber(applicationOwner.getPermAddr().getStreetNum());
            addressPerm.setCity(applicationOwner.getPermAddr().getCityName());
            addressPerm.setPostalCode(applicationOwner.getPermAddr().getZip());
            addressPerm.setCountry(applicationOwner.getPermAddr().getCntryId());
            addressPerm.setAddressType(AddressDTO.ADDRESS_TYPE_RESIDENCY);
            addressPerm.setPreferred(true);
            contacts.addAddress(addressPerm);
        }

        try {
            String partyId = partyService.create(basicInfo, contacts, null, getCorrelationId());
            setProcessVariable(ProcessVariable.PARTY_ID, partyId);
        } catch (ServiceException e) {
            if (e.getMessage().contains("APL8006") || e.getMessage().contains("SBL8000")) {
                throw new BpmnError("APL8006", e.getMessage(), e);
            } else {
                throw e;
            }
        }
    }

    private String getAddressee(NewPersonDTO applicationOwner) {
        return applicationOwner.getFirstName() + " " + applicationOwner.getFamilyName();
    }
}
