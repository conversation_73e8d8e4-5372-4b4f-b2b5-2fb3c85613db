package cz.equa.camapp.delegate.loan;

import cz.equa.camapp.delegate.ApplicationBasedProcessJavaDelegate;
import cz.equa.camapp.manager.RccManager;
import cz.equa.camapp.process.ProcessVariable;
import cz.equa.camapp.rest.service.MessageService;
import cz.equa.camapp.service.ServiceException;
import cz.equa.camapp.service.applicationservice.model.LoanApplicationDTO;
import cz.equa.camapp.service.applicationservice.model.NewPersonDTO;
import cz.rb.las.parametrization.model.RCCSubtypeParametrization;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Component
public class SendConfirmationSmsDelegate extends ApplicationBasedProcessJavaDelegate {

    @Autowired
    MessageService messageService;

    @Autowired
    private RccManager rccManager;

    @Override
    public void executeProcessDelegate(DelegateExecution delegateExecution) throws ServiceException {
        NewPersonDTO newPersonDTO = getTypedProcessVariable(ProcessVariable.LOAN_APPLICATION_OWNER, NewPersonDTO.class);
        LoanApplicationDTO loanApplication = getTypedProcessVariable(ProcessVariable.LOAN_APPLICATION, LoanApplicationDTO.class);

        RCCSubtypeParametrization rccParam = rccManager.getFromRccCache(loanApplication.getBusProdSubTp());

        messageService.sendSms(newPersonDTO.getPhoneNum(),
                "Vazeny kliente, Vas Navrh na uzavreni smlouvy o vydani kreditni karty " + getDesignName(rccParam) +
                        " c. " + getBusApplId() + " jsme prave akceptovali. Karta je pripravena pro platby a vybery " +
                        "mobilnim telefonem. Staci si ji pridat do mobilni penezenky a nemusite cekat, az dorazi " +
                        "fyzicka karta. Vice na www.rb.cz/karty-v-mobilu. Vase Raiffeisenbank",
                getCorrelationId());
    }

    private String getDesignName(RCCSubtypeParametrization rccParam) {
        if (rccParam != null) {
            return rccParam.getDesign().getName();
        } else {
            return null;
        }
    }
}
