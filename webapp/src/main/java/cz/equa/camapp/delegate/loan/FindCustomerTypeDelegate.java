package cz.equa.camapp.delegate.loan;

import cz.equa.camapp.config.MandatoryOutputProcessVariables;
import cz.equa.camapp.delegate.ApplicationBasedProcessJavaDelegate;
import cz.equa.camapp.lovs.LovApplStat;
import cz.equa.camapp.process.ProcessVariable;
import cz.equa.camapp.process.ProcessVariableConstants;
import cz.equa.camapp.utils.ApplicationUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.springframework.stereotype.Component;

@Component
@MandatoryOutputProcessVariables({ProcessVariable.LOAN_APPLICATION_FIND_CUSTOMER_TYPE_RESULT})
@Slf4j
public class FindCustomerTypeDelegate extends ApplicationBasedProcessJavaDelegate {

    @Override
	public void executeProcessDelegate(DelegateExecution delegateExecution) {
        Boolean cancelAccountRequestExists = getTypedProcessVariable(ProcessVariable.LOAN_APPLICATION_CANCEL_ACCOUNT_REQUEST_EXISTS, Boolean.class);
        String clientAccountNumber = getTypedProcessVariable(ProcessVariable.CLIENT_ACCOUNT_NUMBER, String.class);
        LovApplStat aoStatus = getTypedProcessVariable(ProcessVariable.ACCOUNT_OPENING_STATUS, LovApplStat.class);
        Integer customerType;

        if (ApplicationUtils.isAccountOpeningInProgress(aoStatus)) {
            //AO in progress
            customerType = 4;
        } else if ((cancelAccountRequestExists != null) && cancelAccountRequestExists) {
            //cancel
            customerType = 1;
        } else if (StringUtils.isNotEmpty(clientAccountNumber) && !ProcessVariableConstants.CLIENT_ACCOUNT_NUMBER__NO_ACCOUNT.equals(clientAccountNumber)) {
            //OK
            customerType = 2;
        } else {
            //AO - walk-in, AO - ex-client, add product
            customerType = 3;
        }
        setProcessVariable(ProcessVariable.LOAN_APPLICATION_FIND_CUSTOMER_TYPE_RESULT, customerType);
        log.info("process variable {} set to {}", ProcessVariable.LOAN_APPLICATION_FIND_CUSTOMER_TYPE_RESULT.getKey(), customerType);
    }
}
