package cz.equa.camapp.delegate.loan;

import cz.equa.camapp.delegate.ApplicationBasedProcessJavaDelegate;
import cz.equa.camapp.model.document.ClientApprovalDoc;
import cz.equa.camapp.rest.service.DmsService;
import cz.equa.camapp.service.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static cz.equa.camapp.process.ProcessVariable.*;

@Component
@Slf4j
public class AddClientApprovalDelegate extends ApplicationBasedProcessJavaDelegate {

    @Autowired
	private DmsService dmsService;

	@Override
	public void executeProcessDelegate(DelegateExecution delegateExecution) throws ServiceException {

		ClientApprovalDoc document = getTypedProcessVariable(APPROVAL_DOCUMENT_PARSED, ClientApprovalDoc.class);
		log.info("Processing client approval document: {}", document.toString());

		String clientId = getTypedProcessVariable(CLIENT_ID_FOR_CHALLENGE, String.class);
		boolean success = dmsService.addClientApproval(document, clientId, null, getCorrelationId());

		if (!success){
			throw new ServiceException("Exception occurred during approval of document with ID : " + document.getDocumentId());
		}
	}

}

