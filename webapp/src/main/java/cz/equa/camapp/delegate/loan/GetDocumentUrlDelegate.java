package cz.equa.camapp.delegate.loan;

import cz.equa.camapp.config.MandatoryInputProcessVariables;
import cz.equa.camapp.config.MandatoryOutputProcessVariables;
import cz.equa.camapp.delegate.ApplicationBasedProcessJavaDelegate;
import cz.equa.camapp.model.GeneratedDocumentsDTO;
import cz.equa.camapp.model.document.GetDocumentUrlDto;
import cz.equa.camapp.process.ProcessVariable;
import cz.equa.camapp.rest.model.DocumentType;
import cz.equa.camapp.rest.service.DmsService;
import cz.equa.camapp.service.ServiceException;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.UUID;

@Component
@MandatoryInputProcessVariables(ProcessVariable.GENERATED_DOCUMENTS)
@MandatoryOutputProcessVariables(ProcessVariable.DOCUMENT_URL)
public class GetDocumentUrlDelegate extends ApplicationBasedProcessJavaDelegate {

    private DmsService dmsService;

    @Autowired
    public GetDocumentUrlDelegate(DmsService dmsService) {
        this.dmsService = dmsService;
    }

    @Override
    public void executeProcessDelegate(DelegateExecution delegateExecution) throws ServiceException {
        GeneratedDocumentsDTO generatedDocuments = getTypedProcessVariable(ProcessVariable.GENERATED_DOCUMENTS, GeneratedDocumentsDTO.class);

        GetDocumentUrlDto documentUrl = dmsService.getDocumentUrl(generatedDocuments.getObjectInfo(DocumentType.P0001240.name()).getDocumentId(), getCorrelationId());
        documentUrl.setFilename(UUID.randomUUID() + documentUrl.getFilename());

        setProcessVariable(ProcessVariable.DOCUMENT_URL, documentUrl);
    }
}
