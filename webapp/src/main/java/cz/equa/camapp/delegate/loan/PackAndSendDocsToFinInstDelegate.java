package cz.equa.camapp.delegate.loan;

import cz.equa.camapp.lovs.LovFinInstn;
import cz.equa.camapp.rest.model.LovQuery;
import cz.equa.camapp.rest.service.ApplicationService;
import cz.equa.camapp.rest.service.OperationService;
import cz.equa.camapp.service.EmailService;
import cz.equa.camapp.service.ServiceException;
import cz.equa.camapp.utils.Constants;
import jakarta.annotation.PostConstruct;
import jakarta.mail.MessagingException;
import lombok.extern.slf4j.Slf4j;
import net.lingala.zip4j.ZipFile;
import net.lingala.zip4j.model.ZipParameters;
import net.lingala.zip4j.model.enums.AesKeyStrength;
import net.lingala.zip4j.model.enums.CompressionLevel;
import net.lingala.zip4j.model.enums.EncryptionMethod;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.engine.delegate.JavaDelegate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Stream;


@Component
@Slf4j
public class PackAndSendDocsToFinInstDelegate implements JavaDelegate {
    @Value("#{${refi.zip.pswd}}")
    private Map<String, String> refiZipPswd;
    @Value("${refi.file.location}")
    private String baseDir;
    @Value("${environment.id:PROD}")
    private String environmentId;
    @Value("${bo_smtp_to:<EMAIL>}")
    private String testingEmail;

    protected static final String FROM_EMAIL = "<EMAIL>";
    private static final String DEFAULT_PASSWORD = "DEFAULT";

    @Autowired
    protected ApplicationService applicationService;
    @Autowired
    OperationService operationService;
    @Autowired
    private EmailService emailService;

    public void setBaseDir(String baseDir) {
        this.baseDir = baseDir;
    }

    public void setOperationService(OperationService operationService) {
        this.operationService = operationService;
    }

    public void setEmailService(EmailService emailService) {
        this.emailService = emailService;
    }

    public void setRefiZipPswd(Map<String, String> refiZipPswd) {
        this.refiZipPswd = refiZipPswd;
    }

    public void setEnvironmentId(String environmentId) {
        this.environmentId = environmentId;
    }

    public void setTestingEmail(String testingEmail) {
        this.testingEmail = testingEmail;
    }

    @PostConstruct
    void init() {
        Map<String, String> tmp = new HashMap<>();
        for (Map.Entry<String, String> entry : refiZipPswd.entrySet()) {
            tmp.put(entry.getKey().toUpperCase(), entry.getValue());
        }
        this.refiZipPswd = tmp;
        log.debug("passwords for zip files loaded for: {}", StringUtils.join(tmp.keySet(), ","));
    }

    protected String getFinInstPswd(final String finInstId) throws RuntimeException {
        String finInstPswd = refiZipPswd.get(finInstId.trim().toUpperCase());
        if (finInstPswd == null) {
            finInstPswd = refiZipPswd.get(DEFAULT_PASSWORD);
            if (finInstPswd == null) {
                throw new RuntimeException("Cannot find DEFAULT password for Zip file for FinInst.");
            }
        }
        return finInstPswd;
    }

    @Override
    public void execute(DelegateExecution delegateExecution) throws ServiceException, IOException, MessagingException {
        log.debug("Path location: {}", baseDir);
        var baseDirPath = Paths.get(baseDir);
        var outboxPath = Paths.get(baseDir, "outbox");
        var archivePath = Paths.get(baseDir, "archive");

        ensureFoldersExists(baseDirPath, outboxPath, archivePath);

        Files.walkFileTree(baseDirPath, new SimpleFileVisitor<>() {
            @Override
            public FileVisitResult preVisitDirectory(Path dir, BasicFileAttributes attrs) throws IOException {
                if (dir.equals(baseDirPath)
                        || dir.equals(outboxPath)
                        || dir.equals(archivePath)) {
                    return FileVisitResult.CONTINUE;
                }

                LocalDate today = LocalDate.now();
                Path dateDir = dir.getParent();
                LocalDate folderDate = parseFolderName(dateDir.getFileName().toString());

                if (folderDate != null && folderDate.isBefore(today)) {
                    if (isDirectoryNonEmpty(dir)) {
                        String zipFileName = Paths.get(outboxPath.toString(), dir.getFileName() + "_" + dir.getParent().getFileName() + ".zip").toString();
                        makeZipFile(dir, zipFileName, dateDir);
                    }
                    return FileVisitResult.SKIP_SUBTREE;
                }
                return FileVisitResult.CONTINUE;
            }
        });

        log.debug("will send folder: {}", outboxPath);

        prepareAndSendEmail(outboxPath, archivePath);
        deleteOldFiles(archivePath, 14);
    }

    protected void ensureFoldersExists(final Path baseDirPath, final Path outboxPath, final Path archivePath) throws IOException {
        Files.createDirectories(baseDirPath);
        Files.createDirectories(outboxPath);
        Files.createDirectories(archivePath);
    }

    private void makeZipFile(Path dir, String zipFileName, Path dateDir) throws IOException {
        try (ZipFile zipFile = new ZipFile(zipFileName)) {
            ZipParameters parameters = new ZipParameters();
            parameters.setEncryptFiles(true);
            parameters.setEncryptionMethod(EncryptionMethod.AES);
            parameters.setAesKeyStrength(AesKeyStrength.KEY_STRENGTH_256);
            parameters.setCompressionLevel(CompressionLevel.NORMAL);
            String finInstName = dir.getFileName().toString();
            String password = getFinInstPswd(finInstName);
            zipFile.setPassword(password.toCharArray());
            zipFile.addFolder(dir.toFile(), parameters);
            log.debug("Zipped folder: {}", dir);
        } finally {
            deleteDirectory(dir);
            log.debug("Deleted Folder: {}", dir);

            try (DirectoryStream<Path> directoryStream = Files.newDirectoryStream(dateDir)) {
                if (!directoryStream.iterator().hasNext()) {
                    Files.delete(dateDir);
                    log.debug("Folder Deleted: {}", dateDir);
                }
            }
        }
    }

    private void prepareAndSendEmail(Path outboxPath, Path archivePath) throws ServiceException, IOException, MessagingException {
        if (!Files.exists(outboxPath)) {
            log.warn("Directory {} does not exist", outboxPath);
            return;
        }
        try (DirectoryStream<Path> stream = Files.newDirectoryStream(outboxPath)) {
            Files.createDirectories(archivePath);

            for (Path filePath : stream) {
                if (Files.isRegularFile(filePath)) {
                    log.debug("prepare send file {}", filePath);
                    String fileName = filePath.getFileName().toString();
                    String baseName = fileName.substring(0, fileName.lastIndexOf('_'));
                    String dateString = fileName.substring(fileName.lastIndexOf('_') + 1, fileName.lastIndexOf('.'));
                    String toEmail = getFinInstnEmail(baseName);
                    String subject = "RaiffeisenBank Konsolidace - Žádosti o předčasné splacení " + dateString;
                    String body = "Přílohy rozbalíte s použitím hesla. S pozdravem Raiffeisenbank";
                    emailService.sendEmailWithAttachment(FROM_EMAIL, toEmail, subject, body, filePath.toString());
                    log.debug("file {} sent to {}", filePath, toEmail);
                    Path moveTo = archivePath.resolve(filePath.getFileName());
                    Files.move(filePath, moveTo, StandardCopyOption.REPLACE_EXISTING);
                    log.debug("file {} moved to {}", filePath, moveTo);
                }
            }
        }
    }

    public static void deleteOldFiles(Path dirPath, int daysOld) throws IOException {
        if (!Files.exists(dirPath)) {
            log.warn("Directory {} does not exist", dirPath);
            return;
        }
        LocalDate cutoffDate = LocalDate.now().minusDays(daysOld);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(Constants.REFI_FOLDER_DATE_FORMAT);

        try (DirectoryStream<Path> stream = Files.newDirectoryStream(dirPath)) {
            for (Path filePath : stream) {
                String fileName = filePath.getFileName().toString();

                if (Pattern.matches(".*_\\d{8}\\.zip", fileName)) {
                    String dateSuffix = fileName.substring(fileName.length() - 12, fileName.length() - 4);

                    LocalDate fileDate = LocalDate.parse(dateSuffix, formatter);
                    if (fileDate.isBefore(cutoffDate)) {
                        Files.delete(filePath);
                        log.debug("Deleting: {}", filePath);
                    }
                }
            }
        }
    }

    protected static LocalDate parseFolderName(String folderName) {
        try {
            return LocalDate.parse(folderName, DateTimeFormatter.ofPattern(Constants.REFI_FOLDER_DATE_FORMAT));
        } catch (Exception e) {
            return null;
        }
    }

    private static boolean isDirectoryNonEmpty(Path dir) throws IOException {
        try (DirectoryStream<Path> directoryStream = Files.newDirectoryStream(dir)) {
            return directoryStream.iterator().hasNext();
        }
    }

    private static void deleteDirectory(Path path) throws IOException {
        try (Stream<Path> walk = Files.walk(path).sorted(Comparator.reverseOrder())) {
            walk.forEach(p -> {
                try {
                    Files.delete(p);
                    log.debug("Deleted: {}", p);
                } catch (IOException e) {
                    log.debug("Cannot delete: {} - ", p, e);
                }
            });
        }
    }

    private String getFinInstnEmail(String finInstnId) throws ServiceException {
        if (isTesting()) {
            return testingEmail;
        }
        LovQuery<LovFinInstn> query = new LovQuery(LovFinInstn.class);
        String newCorrelationId = UUID.randomUUID().toString();
        List<LovFinInstn> lovs = operationService.getLov(query, newCorrelationId);
        LovFinInstn bankLov = lovs.stream().filter(lov -> lov.getCode().equals(finInstnId)).findFirst().orElse(null);

        if (bankLov == null || bankLov.getLovDetail() == null || bankLov.getLovDetail().isEmpty()) {
            return null;
        }
        return bankLov.getLovDetail().get("REFI_EMAIL");
    }

    protected boolean isTesting() {
        return StringUtils.isNotBlank(environmentId) && (
                environmentId.equalsIgnoreCase("presit")
                        || environmentId.equalsIgnoreCase("eaas")
                        || environmentId.equalsIgnoreCase("tfx1")
                        || environmentId.equalsIgnoreCase("tfix1")
                        || environmentId.equalsIgnoreCase("prep")
                        || environmentId.equalsIgnoreCase("preprod")
        );
    }
}
