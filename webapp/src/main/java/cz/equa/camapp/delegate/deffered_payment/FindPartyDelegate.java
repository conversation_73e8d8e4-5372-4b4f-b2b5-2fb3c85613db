package cz.equa.camapp.delegate.deffered_payment;

import cz.equa.camapp.config.MandatoryInputProcessVariables;
import cz.equa.camapp.delegate.ApplicationBasedProcessJavaDelegate;
import cz.equa.camapp.process.ProcessVariable;
import cz.equa.camapp.rest.service.PartyService;
import cz.equa.camapp.service.ServiceException;
import cz.equa.camapp.service.applicationservice.model.NewPersonDTO;
import cz.equa.camapp.service.partyservice.model.find_party.FindPartyResponseDTO;
import org.apache.commons.lang3.StringUtils;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@MandatoryInputProcessVariables({ProcessVariable.LOAN_APPLICATION})
public class FindPartyDelegate extends ApplicationBasedProcessJavaDelegate {

    @Autowired
    private PartyService partyService;

    @Override
    public void executeProcessDelegate(DelegateExecution delegateExecution) throws ServiceException {
        final NewPersonDTO applicationOwner = getTypedProcessVariable(ProcessVariable.LOAN_APPLICATION_OWNER, NewPersonDTO.class);
        final FindPartyResponseDTO response = partyService.findPerson(applicationOwner, getCorrelationId());
        if (response != null) {
            setProcessVariable(ProcessVariable.ELIGIBLE, response.getEligibleFlag());
            setProcessVariable(ProcessVariable.FIND_PARTY_RESULT, response);

            boolean partyFound = StringUtils.isNotBlank(response.getPtSrcId());
            setProcessVariable(ProcessVariable.PARTY_FOUND, partyFound);
            if (partyFound) {
                setProcessVariable(ProcessVariable.PARTY_ID, response.getPtSrcId());
                setProcessVariable(ProcessVariable.IS_PARTY_CLIENT, response.getClientFlag());
                setProcessVariable(ProcessVariable.IS_PARTY_ELIGIBLE, response.getEligibleFlag());
                setProcessVariable(ProcessVariable.IS_PARTY_DISPONENT, response.getDisponentFlag());
            }
        }
    }
}
