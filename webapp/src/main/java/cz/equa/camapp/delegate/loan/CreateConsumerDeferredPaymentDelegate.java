package cz.equa.camapp.delegate.loan;

import cz.equa.camapp.config.MandatoryInputProcessVariables;
import cz.equa.camapp.delegate.ApplicationBasedProcessJavaDelegate;
import cz.equa.camapp.lovs.LovApplVariantTp;
import cz.equa.camapp.model.GeneratedDocumentInfoDTO;
import cz.equa.camapp.model.GeneratedDocumentsDTO;
import cz.equa.camapp.model.ShopInfoDTO;
import cz.equa.camapp.model.consumer_loan.CreateConsumerDatesDTO;
import cz.equa.camapp.model.consumer_loan.CreateConsumerDeferredPaymentDTO;
import cz.equa.camapp.process.ProcessVariable;
import cz.equa.camapp.rest.model.DocumentType;
import cz.equa.camapp.service.ServiceException;
import cz.equa.camapp.service.applicationservice.model.ApplVariantsDTO;
import cz.equa.camapp.service.applicationservice.model.LoanApplicationDTO;
import cz.equa.camapp.service.applicationservice.model.NewApplVariantDTO;
import cz.rb.tif.bai_00029_consumer_loan.Bai00029ConsumerLoan;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;

import static cz.equa.camapp.utils.Constants.DEFAULT_CURRENCY;

@Component
@MandatoryInputProcessVariables({
        ProcessVariable.PARTY_ID,
        ProcessVariable.LOAN_APPLICATION,
        ProcessVariable.SHOP_INFO})
public class CreateConsumerDeferredPaymentDelegate extends ApplicationBasedProcessJavaDelegate {

    @Autowired
    private Bai00029ConsumerLoan bai00029ConsumerLoan;

    @Override
    public void executeProcessDelegate(DelegateExecution delegateExecution) throws ServiceException {
        
        GeneratedDocumentsDTO generatedDocuments = getGeneratedDocuments();
        if (generatedDocuments == null) {
            throw new ServiceException("Generated documents process variable is null");
        }
        GeneratedDocumentInfoDTO contractDocumentInfo = generatedDocuments.getObjectInfo(DocumentType.P0001231.name());
        if (contractDocumentInfo == null) {
            throw new ServiceException("Document " + DocumentType.P0001231.name()  + " is not generated");
        }
        
        final String partyId = getPartyID();
        LoanApplicationDTO loanApplicationDTO = getLoanApplication();
        ShopInfoDTO shopInfo = getShopInfo();
        CreateConsumerDeferredPaymentDTO createConsumerDeferredPaymentDTO = new CreateConsumerDeferredPaymentDTO();
        createConsumerDeferredPaymentDTO.setCurrencyCode(DEFAULT_CURRENCY);
        createConsumerDeferredPaymentDTO.setDates(createConsumerDates(loanApplicationDTO));
        createConsumerDeferredPaymentDTO.setProductCode("PLPA");
        createConsumerDeferredPaymentDTO.setOrderId(loanApplicationDTO.getOrderNr());
        createConsumerDeferredPaymentDTO.setMerchantId(loanApplicationDTO.getFirstTouchPoint());
        createConsumerDeferredPaymentDTO.setAgreementNumber(getBusApplId());
        createConsumerDeferredPaymentDTO.setDocumentDmsId(contractDocumentInfo.getDocumentId());
        createConsumerDeferredPaymentDTO.setDocumentDmsType("CONTRACT");
        createConsumerDeferredPaymentDTO.setLoanName("PlatímPak, žádost " + getBusApplId());
        createConsumerDeferredPaymentDTO.setRepaymentDays(shopInfo.getNumberOfDaysUntilMaturity());
        NewApplVariantDTO newApplVariantDTO = getTypedProcessVariable(ProcessVariable.APPLICATION_VARIANTS, ApplVariantsDTO.class)
                .getAllVariants()
                .stream()
                .filter(e -> LovApplVariantTp.APR.getCode().equals(e.getApplVariantTpId()))
                .findAny()
                .orElseThrow(() -> new ServiceException("Application variant APR not found"));
        BigDecimal limit = newApplVariantDTO.getFinaAmt();
        createConsumerDeferredPaymentDTO.setRequestedLimit(limit);
        createConsumerDeferredPaymentDTO.setApprovedLimit(limit);
        setProcessVariable(ProcessVariable.TRS_LOAN_ID, bai00029ConsumerLoan.create(partyId, getBusApplId(), createConsumerDeferredPaymentDTO));
    }

    private CreateConsumerDatesDTO createConsumerDates(LoanApplicationDTO loanApplicationDTO) {
        CreateConsumerDatesDTO createConsumerDatesDTO = new CreateConsumerDatesDTO();
        createConsumerDatesDTO.setApplication(loanApplicationDTO.getApplDate().toLocalDate());
        LocalDate localDate = LocalDate.now();
        createConsumerDatesDTO.setApproval(localDate);
        createConsumerDatesDTO.setProvided(localDate);
        createConsumerDatesDTO.setSignature(localDate);
        return createConsumerDatesDTO;
    }
}
