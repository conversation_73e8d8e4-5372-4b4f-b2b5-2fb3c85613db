package cz.equa.camapp.delegate;

import cz.equa.camapp.service.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.camunda.bpm.engine.MismatchingMessageCorrelationException;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.engine.delegate.JavaDelegate;
import org.camunda.bpm.engine.runtime.MessageCorrelationBuilder;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Component
@Slf4j
public class CorrelateMessageDelegate implements JavaDelegate {

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void execute(DelegateExecution delegateExecution) throws ServiceException {
        String message = (String) delegateExecution.getVariable("message");
        String processInstanceId = (String) delegateExecution.getVariable("processInstanceId");
        String suppressExceptionTxt = (String) delegateExecution.getVariable("suppressException");
        boolean suppressException = suppressExceptionTxt != null && (suppressExceptionTxt.equals("1") || suppressExceptionTxt.equalsIgnoreCase("true"));
        try {
            MessageCorrelationBuilder correlation = delegateExecution.getProcessEngine().getRuntimeService()
                    .createMessageCorrelation(message);
            if (StringUtils.isNotBlank(processInstanceId)) {
                correlation.processInstanceId(delegateExecution.getProcessInstanceId());
            }
            correlation.correlate();
            log.debug("Message {} correlated successfully", message);
        } catch (Exception ex) {
            if (suppressException) {
                log.warn(ex.getMessage());
            } else {
                throw ex;
            }
        }
    }
}
