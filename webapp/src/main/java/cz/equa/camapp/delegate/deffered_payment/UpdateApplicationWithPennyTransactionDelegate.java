package cz.equa.camapp.delegate.deffered_payment;

import cz.equa.camapp.config.MandatoryInputProcessVariables;
import cz.equa.camapp.delegate.ApplicationBasedProcessJavaDelegate;
import cz.equa.camapp.lovs.LovPersonVerifTp;
import cz.equa.camapp.process.ProcessVariable;
import cz.equa.camapp.service.ServiceException;
import cz.equa.camapp.service.applicationservice.model.NewPersonVerificationDTO;
import cz.equa.camapp.service.applicationservice.model.VerificationAccountDTO;
import cz.equa.camapp.service.paymentorderserviceinternal.model.PaymentDTO;
import cz.equa.camapp.utils.AccountUtils;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.springframework.stereotype.Component;

import java.util.Arrays;

@Component
@MandatoryInputProcessVariables({ProcessVariable.PENNY_TRANSACTION})
public class UpdateApplicationWithPennyTransactionDelegate extends ApplicationBasedProcessJavaDelegate {

	@Override
	public void executeProcessDelegate(DelegateExecution delegateExecution) throws ServiceException {
		final PaymentDTO pennyTransaction = getTypedProcessVariable(ProcessVariable.PENNY_TRANSACTION, PaymentDTO.class);
        
        final VerificationAccountDTO account = new VerificationAccountDTO();
        account.setAccPrefix(AccountUtils.getPrefixFromFCFormat(pennyTransaction.getDebtorAccNum()));
		account.setAccNumber(AccountUtils.getAccountNumberFromFCFormat(pennyTransaction.getDebtorAccNum()));
		account.setBankCode(pennyTransaction.getDebtorBankCode().getCode());
		account.setAccName(pennyTransaction.getDebtorAccName());
		final NewPersonVerificationDTO verificationDTO = new NewPersonVerificationDTO();
		verificationDTO.setVerifKey(null);
		verificationDTO.setVerifSource(LovPersonVerifTp.TRANS);
        verificationDTO.setAccounts(Arrays.asList(account));
		verificationDTO.setVerifTime(pennyTransaction.getTrnDate());

        applicationService.setPersonVerification(getApplKey(), getBusApplId(), verificationDTO, getCorrelationId());
	}
}
