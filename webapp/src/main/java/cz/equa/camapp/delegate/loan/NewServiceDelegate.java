package cz.equa.camapp.delegate.loan;

import cz.equa.camapp.delegate.ApplicationBasedProcessJavaDelegate;
import cz.equa.camapp.manager.RccManager;
import cz.equa.camapp.model.CardDto;
import cz.equa.camapp.model.push_new_card.ServiceDto;
import cz.equa.camapp.process.ProcessVariable;
import cz.equa.camapp.service.ServiceException;
import cz.equa.camapp.service.applicationservice.model.*;
import cz.equa.camapp.service.product_service.PersonCLInfoDTO;
import cz.equa.camapp.utils.ApplicationUtils;
import cz.equa.camapp.utils.CreditCardUtils;
import cz.rb.tif.ifr_00014_card_processing.Ifr00014CardProcessing;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class NewServiceDelegate extends ApplicationBasedProcessJavaDelegate {

    @Autowired
    Ifr00014CardProcessing cardProcessing;

    @Autowired
    RccManager rccManager;

    @Override
    public void executeProcessDelegate(DelegateExecution delegateExecution) throws ServiceException {
        LoanApplicationDTO loanApplicationDTO = getTypedProcessVariable(ProcessVariable.LOAN_APPLICATION, LoanApplicationDTO.class);
        CreditCardApplDTO cardAppl = getTypedProcessVariable(ProcessVariable.CARD_APPL, CreditCardApplDTO.class);
        CardDto newCard = getTypedProcessVariable(ProcessVariable.NEW_CARD, CardDto.class);

        NewApplVariantDTO aprVariant = ApplicationUtils.getAprVariant(getTypedProcessVariable(ProcessVariable.APPLICATION_VARIANTS, ApplVariantsDTO.class));
        VariantParameterDTO aprVariantParam = ApplicationUtils.getAprVariantParameter(aprVariant,
                getTypedProcessVariable(ProcessVariable.APPLICATION_VARIANT_PARAMETERS,
                        ApplVarianParametersDTO.class));
        PersonCLInfoDTO personInfo = getTypedProcessVariable(ProcessVariable.LOAN_APPLICATION_PERSON_INFO, PersonCLInfoDTO.class);
        List<ServiceDto> serviceList = CreditCardUtils.createServiceList(cardAppl, loanApplicationDTO, rccManager, personInfo, aprVariantParam);

        for (ServiceDto service: serviceList) {
            try {
                cardProcessing.addNewService(service.getServiceCode().intValue(), 1,newCard.getPan());
            } catch (Exception ex) {
                throw new ServiceException(ex.getMessage(), ex);
            }
        }
    }
}
