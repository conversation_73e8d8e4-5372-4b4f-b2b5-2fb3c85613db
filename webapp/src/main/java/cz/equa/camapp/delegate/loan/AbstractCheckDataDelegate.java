package cz.equa.camapp.delegate.loan;

import cz.equa.camapp.delegate.ApplicationBasedProcessJavaDelegate;
import cz.equa.camapp.lovs.LovCntry;
import cz.equa.camapp.lovs.LovIdCardTp;
import cz.equa.camapp.rest.model.cus.party.DocumentsDto;
import cz.equa.camapp.service.ServiceException;
import cz.equa.camapp.service.applicationservice.model.NewIdCardDTO;
import cz.rb.tif.mapping.IdCardTransformer;

import java.util.List;
import java.util.Optional;

import static cz.equa.camapp.lovs.LovIdCardPurp._1STID;

public abstract class AbstractCheckDataDelegate extends ApplicationBasedProcessJavaDelegate {

    protected NewIdCardDTO getIdCard(List<DocumentsDto> documents) throws ServiceException {
        Optional<DocumentsDto> idCardDocumentOptional = documents
                    .stream()
                    .filter(documentDTO -> Boolean.TRUE.equals(documentDTO.getPrimaryDocument())
                            && !Boolean.TRUE.equals(documentDTO.getUnacceptable()))
                    .findFirst();
        if (idCardDocumentOptional.isPresent()) {
            DocumentsDto idCardDocument = idCardDocumentOptional.get();
            NewIdCardDTO idCard = new NewIdCardDTO();
            IdCardTransformer transformedDocument = IdCardTransformer.mapRb2Eq(idCardDocument.getDocumentType());
            if (transformedDocument == null) {
                throw new ServiceException("Unsupported document type " + idCardDocument.getDocumentType());
            }
            idCard.setIdCardTpId(new LovIdCardTp(transformedDocument.getEqId())); // document type (OP, PAS, ...)
            idCard.setCardId(idCardDocument.getDocumentId()); // document reg. number
            idCard.setIdCardPurpId(_1STID);
            idCard.setCardIssuerCntryId(new LovCntry(idCardDocument.getIssuerCountry()));
            idCard.setIssuer(idCardDocument.getIssuer());
            idCard.setIssueDate(idCardDocument.getValidFrom());
            idCard.setExprDate(idCardDocument.getValidTo());

            return idCard;
        } else {
            return null;
        }
    }
}
