package cz.equa.camapp.delegate.loan;

import cz.equa.camapp.delegate.ApplicationBasedProcessJavaDelegate;
import cz.equa.camapp.process.ProcessVariable;
import cz.equa.camapp.rest.service.OperationService;
import cz.equa.camapp.service.ServiceException;
import cz.equa.camapp.service.applicationservice.model.ObligationDTO;
import cz.equa.camapp.service.applicationservice.model.ObligationsDTO;
import cz.equa.camapp.service.product_service.LoanApplicationSrcSystem;
import cz.equa.camapp.service.product_service.ProductsDTO;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class GetProductDetailDelegate extends ApplicationBasedProcessJavaDelegate {

    @Autowired
    private OperationService operationService;

    @Override
    public void executeProcessDelegate(DelegateExecution delegateExecution) throws ServiceException {
        List<ObligationDTO> obligations = getTypedProcessVariable(ProcessVariable.LOAN_APPLICATION_OBLIGATIONS, ObligationsDTO.class).getObligations();
        setProcessVariable(ProcessVariable.PRODUCTS, new ProductsDTO(operationService.getProductDetail(getSourcesSystems(obligations), getCorrelationId())));
    }

    @NotNull
    protected List<LoanApplicationSrcSystem> getSourcesSystems(List<ObligationDTO> obligations) {
        return obligations.stream().filter(obligation -> (
                        (obligation.getOblgtnSelected() != null)
                                && obligation.getOblgtnSelected()
                                && (obligation.getScIntSourceSystem() == null || !obligation.getScIntSourceSystem().startsWith("OPTI"))
                                && "RB".equals(obligation.getScFinInstnCode())
                ))
                .map(obligation -> new LoanApplicationSrcSystem(obligation.getScIntSourceSystem(), obligation.getScIntSourceSystemId()))
                .collect(Collectors.toList());
    }
}
