package cz.equa.camapp.delegate.storno;

import cz.equa.camapp.delegate.ApplicationBasedProcessJavaDelegate;
import cz.equa.camapp.process.Message;
import cz.equa.camapp.process.ProcessManager;
import cz.equa.camapp.process.ProcessVariable;
import cz.equa.camapp.service.ServiceException;
import cz.equa.camapp.service.applicationservice.model.ApplicationToCancelDTO;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.engine.delegate.Expression;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.HashMap;

@Component
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Slf4j
public class NotifyProcessDelegate extends ApplicationBasedProcessJavaDelegate {
    
    @SuppressWarnings("unused")
	protected Expression notifyMessage;
    
	@Autowired
	private ProcessManager processManager;
    
	@Override
	public void executeProcessDelegate(DelegateExecution delegateExecution) throws ServiceException {
        
        String notifyMessageTxt = notifyMessage.getValue(delegateExecution).toString();
        
		final ApplicationToCancelDTO application = getTypedProcessVariable(ProcessVariable.STORNO_APPLICATION_LIST_ELEMENT, ApplicationToCancelDTO.class);

        Long applKey = application.getApplKey().longValue();
        String busApplId = getBusApplId();
        
        HashMap<String, Object> correlation = new HashMap<>();
		correlation.put(ProcessVariable.APPLICATION_KEY.getKey(), applKey);
        correlation.put(ProcessVariable.BUS_APPL_ID.getKey(), busApplId);

        try {
            processManager.notify(Message.byValue(notifyMessageTxt), correlation, new HashMap<>());
        } catch (Exception e) {
            log.warn("Unable to storno process with key {}, busApplId {} with result {}", applKey, busApplId, e.getMessage());
        }
	}
}
