package cz.equa.camapp.delegate.loan;

import cz.equa.camapp.delegate.ApplicationBasedProcessJavaDelegate;
import cz.equa.camapp.process.ProcessVariable;
import cz.equa.camapp.rest.service.AccountService;
import cz.equa.camapp.service.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class GetAccountListDelegate extends ApplicationBasedProcessJavaDelegate {

    @Autowired
    AccountService accountService;

    @Override
    public void executeProcessDelegate(DelegateExecution delegateExecution) throws ServiceException {
        final String clientId = getClientID();

        boolean partyOwnsAccount = accountService.partyOwnsAccount(clientId, getCorrelationId());
        log.info("Does Client {} own accounts size: {}", clientId, partyOwnsAccount);
        setProcessVariable(ProcessVariable.LOAN_APPLICATION_PARTY_OWNS_ACCOUNT_ALREADY, partyOwnsAccount);
    }
}
