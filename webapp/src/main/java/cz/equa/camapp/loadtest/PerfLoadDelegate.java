package cz.equa.camapp.loadtest;

import cz.equa.camapp.delegate.ApplicationBasedProcessJavaDelegate;
import cz.equa.camapp.service.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class PerfLoadDelegate extends ApplicationBasedProcessJavaDelegate {

	@Override
	public void executeProcessDelegate(DelegateExecution delegateExecution) throws ServiceException {
		final int iterations = Integer.parseInt(String.valueOf(getDelegateExecution().getVariable("ITERATIONS")));
		getDelegateExecution().setVariable("ITERATIONS", iterations - 1);
		final int loadPercent = Integer.parseInt(String.valueOf(getDelegateExecution().getVariable("LOAD_PERCENT")));
		BusyThread busyThread = new BusyThread("Loadtest", ((double) loadPercent) / 100, 1000);
		busyThread.start();
        try {
            busyThread.join();
        } catch (InterruptedException ex) {
             Thread.currentThread().interrupt();
        }
	}

	private static class BusyThread extends Thread {
		private final double load;
		private final long duration;

		public BusyThread(String name, double load, long duration) {
			super(name);
			this.load = load;
			this.duration = duration;
		}

		@Override
		public void run() {
			long startTime = System.currentTimeMillis();
			try {
				// Loop for the given duration
				while (System.currentTimeMillis() - startTime < duration) {
					// Every 100ms, sleep for the percentage of unladen time
					if (System.currentTimeMillis() % 100 == 0) {
						Thread.sleep((long) Math.floor((1 - load) * 100));
					}
				}
			} catch (InterruptedException e) {
				log.debug("Busy thread interrupted.", e);
				Thread.currentThread().interrupt();
			}
		}
	}

}
