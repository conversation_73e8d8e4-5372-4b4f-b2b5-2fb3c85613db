<process-application xmlns="http://www.camunda.org/schema/1.0/ProcessApplication">
        
    <process-archive name="Loans">
        <process-engine>default</process-engine>
        <resource>processes/loan/CL.bpmn</resource>
        <resource>processes/loan/deferred_payment.bpmn</resource>
        <resource>processes/loan/rejection-cancellation.bpmn</resource>
        <resource>processes/loan/solve_applicant.bpmn</resource>
        <resource>processes/loan/cl-send-P0001240.bpmn</resource>
        <resource>processes/email-notification.bpmn</resource>
        <resource>processes/test.bpmn</resource>
        <properties>
            <property name="isDeleteUponUndeploy">false</property>
            <property name="isScanForProcessDefinitions">false</property>
        </properties>
    </process-archive>

    <process-archive name="TCHER">
        <process-engine>default</process-engine>
        <resource>processes/tcher.bpmn</resource>
        <properties>
            <property name="isDeleteUponUndeploy">false</property>
            <property name="isScanForProcessDefinitions">false</property>
        </properties>
    </process-archive>

    <process-archive name="PennyPayment">
        <process-engine>default</process-engine>
        <resource>processes/loan/penny_payment.bpmn</resource>
        <properties>
            <property name="isDeleteUponUndeploy">false</property>
            <property name="isScanForProcessDefinitions">false</property>
        </properties>
    </process-archive>

    <process-archive name="UpdateInterestRate">
        <process-engine>default</process-engine>
        <resource>processes/loan/update_interest_rate.bpmn</resource>
        <properties>
            <property name="isDeleteUponUndeploy">false</property>
            <property name="isScanForProcessDefinitions">false</property>
        </properties>
    </process-archive>

    <process-archive name="Mortgage">
        <process-engine>default</process-engine>
        <resource>processes/hypo/mortgage.bpmn</resource>
        <resource>processes/hypo/mortgage-guar-offer.bpmn</resource>
        <resource>processes/hypo/mortgage-application-check.bpmn</resource>
        <resource>processes/hypo/mortgage-income-verification.bpmn</resource>
        <resource>processes/hypo/mortgage-storno.bpmn</resource>
        <resource>processes/hypo/mortgage-finalization.bpmn</resource>
        <resource>processes/hypo/mortgage-hypoclient-change.bpmn</resource>
        <resource>processes/hypo/mortgage-rollover.bpmn</resource>
        <resource>processes/hypo/mortgage-appraisal.bpmn</resource>
        <properties>
            <property name="isDeleteUponUndeploy">false</property>
            <property name="isScanForProcessDefinitions">false</property>
        </properties>
    </process-archive>

    <process-archive name="Reko">
        <process-engine>default</process-engine>
        <resource>processes/reko/reko.bpmn</resource>
        <resource>processes/reko/reko-solve-applicant.bpmn</resource>
        <resource>processes/reko/reko-rejection-cancellation.bpmn</resource>
        <properties>
            <property name="isDeleteUponUndeploy">false</property>
            <property name="isScanForProcessDefinitions">false</property>
        </properties>
    </process-archive>

    <process-archive name="Utils">
        <process-engine>default</process-engine>
        <resource>processes/healthCheck/healthCheck.bpmn</resource>
        <resource>processes/test/no_op_process.bpmn</resource>
        <properties>
            <property name="isDeleteUponUndeploy">false</property>
            <property name="isScanForProcessDefinitions">false</property>
        </properties>
    </process-archive>

    <process-archive name="Storno">
        <process-engine>default</process-engine>
        <resource>processes/storno/storno.bpmn</resource>
        <resource>processes/storno/storno-application.bpmn</resource>
        <properties>
            <property name="isDeleteUponUndeploy">false</property>
            <property name="isScanForProcessDefinitions">false</property>
        </properties>
    </process-archive>
</process-application>
