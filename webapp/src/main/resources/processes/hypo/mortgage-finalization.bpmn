<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:color="http://www.omg.org/spec/BPMN/non-normative/color/1.0" xmlns:bioc="http://bpmn.io/schema/bpmn/biocolor/1.0" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_0l0631f" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.35.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.18.0">
  <bpmn:process id="MortgageFinalization" name="Mortgage Finalization" isExecutable="true" camunda:historyTimeToLive="7" camunda:isStartableInTasklist="false">
    <bpmn:startEvent id="Event_Start">
      <bpmn:outgoing>Flow_1qij6ks</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:exclusiveGateway id="Gateway_first">
      <bpmn:incoming>Flow_1qij6ks</bpmn:incoming>
      <bpmn:incoming>Flow_14el3ew</bpmn:incoming>
      <bpmn:incoming>Flow_1uzqb20</bpmn:incoming>
      <bpmn:outgoing>Flow_162ubpq</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1qij6ks" sourceRef="Event_Start" targetRef="Gateway_first" />
    <bpmn:exclusiveGateway id="Gateway_approvalResult1" default="Flow_APPROVE">
      <bpmn:incoming>Flow_0ku9hf0</bpmn:incoming>
      <bpmn:outgoing>Flow_APPROVE</bpmn:outgoing>
      <bpmn:outgoing>Flow_CONAPPR</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_162ubpq" sourceRef="Gateway_first" targetRef="Activity_GetMortgageApplSubAppl" />
    <bpmn:sequenceFlow id="Flow_APPROVE" name="APPROVE" sourceRef="Gateway_approvalResult1" targetRef="Activity_reduction" />
    <bpmn:sequenceFlow id="Flow_1rqqnx2" sourceRef="Gateway_waitForEvent" targetRef="Event_rmlApplicationFinalized" />
    <bpmn:sequenceFlow id="Flow_1o611lw" sourceRef="Event_rmlApplicationFinalized" targetRef="Activity_GetMortgageApplSubAppl_2" />
    <bpmn:sequenceFlow id="Flow_1jap1p4" sourceRef="Activity_UpdateApplInHYC" targetRef="Gateway_0fwy36m" />
    <bpmn:sequenceFlow id="Flow_CONAPPR" name="CONAPPR" sourceRef="Gateway_approvalResult1" targetRef="Gateway_setZDPSCH">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${callApprovalResult=="CONAPPR"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1a0479z" sourceRef="Gateway_waitForEvent2" targetRef="Event_rmlApplicationToModify" />
    <bpmn:sequenceFlow id="Flow_05hk21h" sourceRef="Gateway_waitForEvent" targetRef="Event_rmlFinalOfferToModify" />
    <bpmn:sequenceFlow id="Flow_0ufwsnf" sourceRef="Event_rmlApplicationToModify" targetRef="Gateway_setGNUPL" />
    <bpmn:sequenceFlow id="Flow_1s1f5cv" sourceRef="Event_rmlFinalOfferToModify" targetRef="Gateway_setNBDU_1" />
    <bpmn:intermediateCatchEvent id="Event_rmlApplicationFinalized" name="rmlApplicationFinalized">
      <bpmn:incoming>Flow_1rqqnx2</bpmn:incoming>
      <bpmn:outgoing>Flow_1o611lw</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_0edt1ne" messageRef="Message_1sinqo4" />
    </bpmn:intermediateCatchEvent>
    <bpmn:intermediateCatchEvent id="Event_rmlApplicationToModify" name="rmlApplicationToModify">
      <bpmn:incoming>Flow_1a0479z</bpmn:incoming>
      <bpmn:outgoing>Flow_0ufwsnf</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_06ye3rx" messageRef="Message_0smkif7" />
    </bpmn:intermediateCatchEvent>
    <bpmn:intermediateCatchEvent id="Event_rmlFinalOfferToModify" name="rmlFinalOfferToModify">
      <bpmn:incoming>Flow_05hk21h</bpmn:incoming>
      <bpmn:outgoing>Flow_1s1f5cv</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_1du36tl" messageRef="Message_0lnuko3" />
    </bpmn:intermediateCatchEvent>
    <bpmn:intermediateCatchEvent id="Event_rmlFinalOfferModified" name="rmlFinalOfferModified">
      <bpmn:incoming>Flow_02xu7j4</bpmn:incoming>
      <bpmn:outgoing>Flow_0gp2xk5</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_1o8oree" messageRef="Message_0drt89d" />
    </bpmn:intermediateCatchEvent>
    <bpmn:sequenceFlow id="Flow_0gp2xk5" sourceRef="Event_rmlFinalOfferModified" targetRef="Gateway_1qq7uew" />
    <bpmn:exclusiveGateway id="Gateway_approvalResult" default="Flow_1ko4qe3">
      <bpmn:incoming>Flow_06o2uf1</bpmn:incoming>
      <bpmn:outgoing>Flow_1ko4qe3</bpmn:outgoing>
      <bpmn:outgoing>Flow_DECLINE</bpmn:outgoing>
      <bpmn:outgoing>Flow_APPROVE_CONAPPR</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1ko4qe3" sourceRef="Gateway_approvalResult" targetRef="Activity_IncomeVerification" />
    <bpmn:sequenceFlow id="Flow_DECLINE" name="DECLINE" sourceRef="Gateway_approvalResult" targetRef="Gateway_0uuh7js">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${callApprovalResult=="DECLINE"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_APPROVE_CONAPPR" name="APPROVE || CONAPPR" sourceRef="Gateway_approvalResult" targetRef="Gateway_0jblx1f">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${callApprovalResult=="APPROVE" || callApprovalResult=="CONAPPR"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="Activity_mtgApplState_NBDV" name="updateStatus Appl NBDV" camunda:type="external" camunda:topic="mtgUpdateApplicationStatus">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="nextApplicationState">NBDV</camunda:inputParameter>
          <camunda:inputParameter name="applId">${busApplId}</camunda:inputParameter>
          <camunda:inputParameter name="disableEmailNotification">1</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1ykvtyy</bpmn:incoming>
      <bpmn:outgoing>Flow_1z108mq</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1ebdfn6" sourceRef="Gateway_setNBDV" targetRef="Activity_mtgSubApplState_NBDV" />
    <bpmn:sequenceFlow id="Flow_0x8abq9" sourceRef="Gateway_afterSetNBDV" targetRef="Gateway_waitForEvent" />
    <bpmn:sequenceFlow id="Flow_1ykvtyy" sourceRef="Gateway_setNBDV" targetRef="Activity_mtgApplState_NBDV" />
    <bpmn:sequenceFlow id="Flow_1z108mq" sourceRef="Activity_mtgApplState_NBDV" targetRef="Gateway_afterSetNBDV" />
    <bpmn:serviceTask id="Activity_mtgSubApplState_ZDPSCH" name="updateStatus SubAppl ZDPSCH" camunda:type="external" camunda:topic="mtgUpdateApplicationStatus">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="nextApplicationState">ZDPSCH</camunda:inputParameter>
          <camunda:inputParameter name="applId">${busSubApplId}</camunda:inputParameter>
          <camunda:inputParameter name="forceEmailNotification">1</camunda:inputParameter>
          <camunda:inputParameter name="disableEmailNotification">${execution.getVariable("sent") == null || execution.getVariable("sent") != "1" ? "0" : "1"}</camunda:inputParameter>
        </camunda:inputOutput>
        <camunda:executionListener event="end">
          <camunda:script scriptFormat="groovy">execution.setVariable("sent", "1");</camunda:script>
        </camunda:executionListener>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1g39fwq</bpmn:incoming>
      <bpmn:outgoing>Flow_0imzcyv</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1xmmntl" sourceRef="Gateway_setZDPSCH" targetRef="Activity_mtgApplState_ZDPSCH" />
    <bpmn:sequenceFlow id="Flow_1eaxnkj" sourceRef="Gateway_afterSetZDPSCH" targetRef="Gateway_join2" />
    <bpmn:sequenceFlow id="Flow_1g39fwq" sourceRef="Gateway_setZDPSCH" targetRef="Activity_mtgSubApplState_ZDPSCH" />
    <bpmn:sequenceFlow id="Flow_0imzcyv" sourceRef="Activity_mtgSubApplState_ZDPSCH" targetRef="Gateway_afterSetZDPSCH" />
    <bpmn:sequenceFlow id="Flow_0d5gvan" sourceRef="Gateway_afterSetNBDU" targetRef="Gateway_join2" />
    <bpmn:serviceTask id="Activity_callMortgageApproval" name="callMortgageApproval" camunda:type="external" camunda:topic="mtgCallApproval">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="processPart">1ST_SCORING</camunda:inputParameter>
        </camunda:inputOutput>
        <camunda:properties>
          <camunda:property name="retry_config" value="NO" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_136pmxe</bpmn:incoming>
      <bpmn:outgoing>Flow_06o2uf1</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_06o2uf1" sourceRef="Activity_callMortgageApproval" targetRef="Gateway_approvalResult" />
    <bpmn:sequenceFlow id="Flow_1fhdfwe" sourceRef="Gateway_afterSetFNLSCR" targetRef="Gateway_1mi63ve" />
    <bpmn:endEvent id="Event_callSTR" name="STR">
      <bpmn:incoming>Flow_toSTR</bpmn:incoming>
      <bpmn:escalationEventDefinition id="EscalationEventDefinition_1uode6f" escalationRef="Escalation_105vibk" />
    </bpmn:endEvent>
    <bpmn:endEvent id="Event_callZDZAM" name="ZDZAM">
      <bpmn:incoming>Flow_11euxau</bpmn:incoming>
      <bpmn:escalationEventDefinition id="EscalationEventDefinition_1kaggsi" escalationRef="Escalation_0aoph10" />
    </bpmn:endEvent>
    <bpmn:serviceTask id="Activity_mtgApplState_GNUPL" name="updateStatus Appl GNUPL" camunda:type="external" camunda:topic="mtgUpdateApplicationStatus">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="nextApplicationState">GNUPL</camunda:inputParameter>
          <camunda:inputParameter name="applId">${busApplId}</camunda:inputParameter>
          <camunda:inputParameter name="disableEmailNotification">1</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0klz1ar</bpmn:incoming>
      <bpmn:outgoing>Flow_1kqx9tc</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_mtgApplState_NBDU" name="updateStatus Appl NBDU" camunda:type="external" camunda:topic="mtgUpdateApplicationStatus">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="nextApplicationState">NBDU</camunda:inputParameter>
          <camunda:inputParameter name="applId">${busApplId}</camunda:inputParameter>
          <camunda:inputParameter name="disableEmailNotification">1</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_03qtxqa</bpmn:incoming>
      <bpmn:outgoing>Flow_1ktobpw</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0eybdng" sourceRef="Gateway_setGNUPL" targetRef="Activity_mtgSubApplState_GNUPL" />
    <bpmn:sequenceFlow id="Flow_0klz1ar" sourceRef="Gateway_setGNUPL" targetRef="Activity_mtgApplState_GNUPL" />
    <bpmn:sequenceFlow id="Flow_03qtxqa" sourceRef="Gateway_setNBDU" targetRef="Activity_mtgApplState_NBDU" />
    <bpmn:sequenceFlow id="Flow_1iylcfx" sourceRef="Gateway_setNBDU" targetRef="Activity_mtgSubApplState_NBDU" />
    <bpmn:sequenceFlow id="Flow_1ktobpw" sourceRef="Activity_mtgApplState_NBDU" targetRef="Gateway_afterSetNBDU" />
    <bpmn:sequenceFlow id="Flow_1kqx9tc" sourceRef="Activity_mtgApplState_GNUPL" targetRef="Gateway_afterSetGNUPL" />
    <bpmn:serviceTask id="Activity_mtgApplState_ZPRC" name="updateStatus Appl ZPRC" camunda:type="external" camunda:topic="mtgUpdateApplicationStatus">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="nextApplicationState">ZPRC</camunda:inputParameter>
          <camunda:inputParameter name="applId">${busApplId}</camunda:inputParameter>
          <camunda:inputParameter name="disableEmailNotification">1</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0lzjlns</bpmn:incoming>
      <bpmn:outgoing>Flow_0d3gprc</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1hsvecr" sourceRef="Gateway_setFNLSCR" targetRef="Activity_mtgSubApplState_ZPRC" />
    <bpmn:sequenceFlow id="Flow_0lzjlns" sourceRef="Gateway_setFNLSCR" targetRef="Activity_mtgApplState_ZPRC" />
    <bpmn:sequenceFlow id="Flow_0d3gprc" sourceRef="Activity_mtgApplState_ZPRC" targetRef="Gateway_afterSetFNLSCR" />
    <bpmn:eventBasedGateway id="Gateway_waitForEvent">
      <bpmn:incoming>Flow_0x8abq9</bpmn:incoming>
      <bpmn:outgoing>Flow_1rqqnx2</bpmn:outgoing>
      <bpmn:outgoing>Flow_05hk21h</bpmn:outgoing>
      <bpmn:outgoing>Flow_1awngul</bpmn:outgoing>
    </bpmn:eventBasedGateway>
    <bpmn:serviceTask id="Activity_UpdateApplInHYC" name="Create Application In Hypoclient" camunda:type="external" camunda:topic="mtgCreateApplInHYC">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="retry_config" value="NO" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0gdgcv7</bpmn:incoming>
      <bpmn:outgoing>Flow_1jap1p4</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:parallelGateway id="Gateway_setNBDV">
      <bpmn:incoming>Flow_acceptable</bpmn:incoming>
      <bpmn:outgoing>Flow_1ebdfn6</bpmn:outgoing>
      <bpmn:outgoing>Flow_1ykvtyy</bpmn:outgoing>
      <bpmn:outgoing>Flow_0y6x4fg</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:parallelGateway id="Gateway_setZDPSCH">
      <bpmn:incoming>Flow_CONAPPR</bpmn:incoming>
      <bpmn:outgoing>Flow_1xmmntl</bpmn:outgoing>
      <bpmn:outgoing>Flow_1g39fwq</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:parallelGateway id="Gateway_afterSetZDPSCH">
      <bpmn:incoming>Flow_0imzcyv</bpmn:incoming>
      <bpmn:incoming>Flow_1jnv3ht</bpmn:incoming>
      <bpmn:outgoing>Flow_1eaxnkj</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:parallelGateway id="Gateway_afterSetNBDV">
      <bpmn:incoming>Flow_1z108mq</bpmn:incoming>
      <bpmn:incoming>Flow_0nib3ad</bpmn:incoming>
      <bpmn:incoming>Flow_0lyadsu</bpmn:incoming>
      <bpmn:outgoing>Flow_0x8abq9</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:parallelGateway id="Gateway_setFNLSCR">
      <bpmn:incoming>Flow_03tgsm6</bpmn:incoming>
      <bpmn:outgoing>Flow_1hsvecr</bpmn:outgoing>
      <bpmn:outgoing>Flow_0lzjlns</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:parallelGateway id="Gateway_afterSetFNLSCR">
      <bpmn:incoming>Flow_0d3gprc</bpmn:incoming>
      <bpmn:incoming>Flow_1vzn8y6</bpmn:incoming>
      <bpmn:outgoing>Flow_1fhdfwe</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:parallelGateway id="Gateway_afterSetGNUPL">
      <bpmn:incoming>Flow_1kqx9tc</bpmn:incoming>
      <bpmn:incoming>Flow_0n1src3</bpmn:incoming>
      <bpmn:outgoing>Flow_1iqs32m</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:parallelGateway id="Gateway_setGNUPL">
      <bpmn:incoming>Flow_0ufwsnf</bpmn:incoming>
      <bpmn:outgoing>Flow_0eybdng</bpmn:outgoing>
      <bpmn:outgoing>Flow_0klz1ar</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:parallelGateway id="Gateway_setNBDU">
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_0e41nyb</bpmn:incoming>
      <bpmn:outgoing>Flow_03qtxqa</bpmn:outgoing>
      <bpmn:outgoing>Flow_1iylcfx</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:parallelGateway id="Gateway_afterSetNBDU">
      <bpmn:incoming>Flow_1ktobpw</bpmn:incoming>
      <bpmn:incoming>Flow_13xy3ib</bpmn:incoming>
      <bpmn:outgoing>Flow_0d5gvan</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:serviceTask id="Activity_mtgSubApplState_NBDV" name="updateStatus SubAppl NBDV" camunda:type="external" camunda:topic="mtgUpdateApplicationStatus">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="nextApplicationState">NBDV</camunda:inputParameter>
          <camunda:inputParameter name="applId">${busSubApplId}</camunda:inputParameter>
          <camunda:inputParameter name="forceEmailNotification">1</camunda:inputParameter>
          <camunda:inputParameter name="disableEmailNotification">${execution.getVariable("sent") == null || execution.getVariable("sent") != "1" ? "0" : "1"}</camunda:inputParameter>
        </camunda:inputOutput>
        <camunda:executionListener event="end">
          <camunda:script scriptFormat="groovy">execution.setVariable("sent", "1");</camunda:script>
        </camunda:executionListener>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1ebdfn6</bpmn:incoming>
      <bpmn:outgoing>Flow_0nib3ad</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0nib3ad" sourceRef="Activity_mtgSubApplState_NBDV" targetRef="Gateway_afterSetNBDV" />
    <bpmn:serviceTask id="Activity_mtgApplState_ZDPSCH" name="updateStatus Appl ZDPSCH" camunda:type="external" camunda:topic="mtgUpdateApplicationStatus">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="nextApplicationState">ZDPSCH</camunda:inputParameter>
          <camunda:inputParameter name="applId">${busApplId}</camunda:inputParameter>
          <camunda:inputParameter name="disableEmailNotification">1</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1xmmntl</bpmn:incoming>
      <bpmn:outgoing>Flow_1jnv3ht</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1jnv3ht" sourceRef="Activity_mtgApplState_ZDPSCH" targetRef="Gateway_afterSetZDPSCH" />
    <bpmn:serviceTask id="Activity_mtgSubApplState_ZPRC" name="updateStatus SubAppl ZPRC" camunda:type="external" camunda:topic="mtgUpdateApplicationStatus">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="nextApplicationState">ZPRC</camunda:inputParameter>
          <camunda:inputParameter name="applId">${busSubApplId}</camunda:inputParameter>
          <camunda:inputParameter name="forceEmailNotification">1</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1hsvecr</bpmn:incoming>
      <bpmn:outgoing>Flow_1vzn8y6</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1vzn8y6" sourceRef="Activity_mtgSubApplState_ZPRC" targetRef="Gateway_afterSetFNLSCR" />
    <bpmn:serviceTask id="Activity_mtgSubApplState_GNUPL" name="updateStatus SubAppl GNUPL" camunda:type="external" camunda:topic="mtgUpdateApplicationStatus">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="nextApplicationState">GNUPL</camunda:inputParameter>
          <camunda:inputParameter name="applId">${busSubApplId}</camunda:inputParameter>
          <camunda:inputParameter name="forceEmailNotification">1</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0eybdng</bpmn:incoming>
      <bpmn:outgoing>Flow_0n1src3</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0n1src3" sourceRef="Activity_mtgSubApplState_GNUPL" targetRef="Gateway_afterSetGNUPL" />
    <bpmn:serviceTask id="Activity_mtgSubApplState_NBDU" name="updateStatus SubAppl NBDU" camunda:type="external" camunda:topic="mtgUpdateApplicationStatus">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="nextApplicationState">NBDU</camunda:inputParameter>
          <camunda:inputParameter name="applId">${busSubApplId}</camunda:inputParameter>
          <camunda:inputParameter name="forceEmailNotification">1</camunda:inputParameter>
          <camunda:inputParameter name="disableEmailNotification">${execution.getVariable("sent") == null || execution.getVariable("sent") != "1" ? "0" : "1"}</camunda:inputParameter>
        </camunda:inputOutput>
        <camunda:executionListener event="end">
          <camunda:script scriptFormat="groovy">execution.setVariable("sent", "1");</camunda:script>
        </camunda:executionListener>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1iylcfx</bpmn:incoming>
      <bpmn:outgoing>Flow_13xy3ib</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_13xy3ib" sourceRef="Activity_mtgSubApplState_NBDU" targetRef="Gateway_afterSetNBDU" />
    <bpmn:serviceTask id="Activity_generateEmail_X0000042" name="generateEmail&#10;(X0000042)" camunda:type="external" camunda:topic="mtgGenerateEmailByTemplate">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="template">X0000042</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0y6x4fg</bpmn:incoming>
      <bpmn:outgoing>Flow_0lyadsu</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0y6x4fg" sourceRef="Gateway_setNBDV" targetRef="Activity_generateEmail_X0000042" />
    <bpmn:sequenceFlow id="Flow_0lyadsu" sourceRef="Activity_generateEmail_X0000042" targetRef="Gateway_afterSetNBDV" />
    <bpmn:serviceTask id="Activity_GetMortgageApplSubAppl" name="get Mortgage Appl SubAppl" camunda:type="external" camunda:topic="mtgGetApplication">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="busApplId">${busApplId}</camunda:inputParameter>
          <camunda:inputParameter name="busSubApplId">${busSubApplId}</camunda:inputParameter>
          <camunda:inputParameter name="getVariants">OFR,REQ</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_162ubpq</bpmn:incoming>
      <bpmn:outgoing>Flow_0ku9hf0</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_reduction" name="Check Offer Reduction" camunda:type="external" camunda:topic="mtgCheckOfferReduction">
      <bpmn:documentation>Call getParamsRML</bpmn:documentation>
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="applId">${busApplId}</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_APPROVE</bpmn:incoming>
      <bpmn:outgoing>Flow_08l7sbh</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_08l7sbh" sourceRef="Activity_reduction" targetRef="Gateway_checkOfferReduction" />
    <bpmn:exclusiveGateway id="Gateway_checkOfferReduction" name="Check Offer Reduction ?" default="Flow_acceptable">
      <bpmn:incoming>Flow_08l7sbh</bpmn:incoming>
      <bpmn:outgoing>Flow_acceptable</bpmn:outgoing>
      <bpmn:outgoing>Flow_not_acceptable</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_acceptable" name="is acceptable" sourceRef="Gateway_checkOfferReduction" targetRef="Gateway_setNBDV" />
    <bpmn:sequenceFlow id="Flow_not_acceptable" name="Not acceptable" sourceRef="Gateway_checkOfferReduction" targetRef="Gateway_setNBDU_1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${!isOfferReductionAcceptable}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:intermediateCatchEvent id="Event_rmlGuarOfferApplied" name="rmlGuarOfferApplied">
      <bpmn:incoming>Flow_14advrz</bpmn:incoming>
      <bpmn:outgoing>Flow_0mojkof</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_1wwqlph" messageRef="Message_34c5c3m" />
    </bpmn:intermediateCatchEvent>
    <bpmn:exclusiveGateway id="Gateway_join2">
      <bpmn:incoming>Flow_0d5gvan</bpmn:incoming>
      <bpmn:incoming>Flow_1eaxnkj</bpmn:incoming>
      <bpmn:outgoing>Flow_0a5557b</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0a5557b" sourceRef="Gateway_join2" targetRef="Gateway_waitForEvent2" />
    <bpmn:eventBasedGateway id="Gateway_waitForEvent2">
      <bpmn:incoming>Flow_0a5557b</bpmn:incoming>
      <bpmn:outgoing>Flow_1a0479z</bpmn:outgoing>
      <bpmn:outgoing>Flow_14advrz</bpmn:outgoing>
      <bpmn:outgoing>Flow_02xu7j4</bpmn:outgoing>
    </bpmn:eventBasedGateway>
    <bpmn:sequenceFlow id="Flow_14advrz" sourceRef="Gateway_waitForEvent2" targetRef="Event_rmlGuarOfferApplied" />
    <bpmn:intermediateCatchEvent id="Event_rmlGuarOfferApplied2" name="rmlGuarOfferApplied">
      <bpmn:incoming>Flow_1awngul</bpmn:incoming>
      <bpmn:outgoing>Flow_0mx1zl0</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_0a28lpk" messageRef="Message_34c5c3m" />
    </bpmn:intermediateCatchEvent>
    <bpmn:sequenceFlow id="Flow_1awngul" sourceRef="Gateway_waitForEvent" targetRef="Event_rmlGuarOfferApplied2" />
    <bpmn:exclusiveGateway id="Gateway_1qq7uew">
      <bpmn:incoming>Flow_0gp2xk5</bpmn:incoming>
      <bpmn:incoming>Flow_0mojkof</bpmn:incoming>
      <bpmn:incoming>Flow_0mx1zl0</bpmn:incoming>
      <bpmn:outgoing>Flow_1ka5lix</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1ka5lix" sourceRef="Gateway_1qq7uew" targetRef="Activity_mtgApplState_ZDPDP" />
    <bpmn:sequenceFlow id="Flow_0mojkof" sourceRef="Event_rmlGuarOfferApplied" targetRef="Gateway_1qq7uew" />
    <bpmn:sequenceFlow id="Flow_0mx1zl0" sourceRef="Event_rmlGuarOfferApplied2" targetRef="Gateway_1qq7uew" />
    <bpmn:sequenceFlow id="Flow_02xu7j4" sourceRef="Gateway_waitForEvent2" targetRef="Event_rmlFinalOfferModified" />
    <bpmn:serviceTask id="Activity_mtgApplState_ZDPDP" name="updateStatus Appl ZDPDP" camunda:type="external" camunda:topic="mtgUpdateApplicationStatus">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="nextApplicationState">ZDPDP</camunda:inputParameter>
          <camunda:inputParameter name="applId">${busApplId}</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1ka5lix</bpmn:incoming>
      <bpmn:outgoing>Flow_136pmxe</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_136pmxe" sourceRef="Activity_mtgApplState_ZDPDP" targetRef="Activity_callMortgageApproval" />
    <bpmn:exclusiveGateway id="Gateway_setNBDU_1">
      <bpmn:incoming>Flow_not_acceptable</bpmn:incoming>
      <bpmn:incoming>Flow_1s1f5cv</bpmn:incoming>
      <bpmn:incoming>Flow_1mjhaf7</bpmn:incoming>
      <bpmn:outgoing>Flow_0e41nyb</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0e41nyb" sourceRef="Gateway_setNBDU_1" targetRef="Gateway_setNBDU" />
    <bpmn:callActivity id="Activity_IncomeVerification" name="Income Verification" calledElement="MortgageIncomeVerification">
      <bpmn:extensionElements>
        <camunda:in variables="all" />
        <camunda:out source="incomeVerificationResult" target="incomeVerificationResult" />
        <camunda:in businessKey="#{execution.processBusinessKey}" />
        <camunda:out source="callApprovalResult" target="callApprovalResult" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1ko4qe3</bpmn:incoming>
      <bpmn:outgoing>Flow_0dlszbc</bpmn:outgoing>
    </bpmn:callActivity>
    <bpmn:sequenceFlow id="Flow_0dlszbc" sourceRef="Activity_IncomeVerification" targetRef="Gateway_0m22u43" />
    <bpmn:exclusiveGateway id="Gateway_0m22u43">
      <bpmn:incoming>Flow_0dlszbc</bpmn:incoming>
      <bpmn:outgoing>Flow_toSTR</bpmn:outgoing>
      <bpmn:outgoing>Flow_toZDZAM</bpmn:outgoing>
      <bpmn:outgoing>Flow_toEZSO</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_toSTR" name="STR" sourceRef="Gateway_0m22u43" targetRef="Event_callSTR">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${incomeVerificationResult=='STR'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0uuh7js">
      <bpmn:incoming>Flow_DECLINE</bpmn:incoming>
      <bpmn:incoming>Flow_toZDZAM</bpmn:incoming>
      <bpmn:outgoing>Flow_11euxau</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_11euxau" sourceRef="Gateway_0uuh7js" targetRef="Event_callZDZAM" />
    <bpmn:sequenceFlow id="Flow_toZDZAM" name="ZDZAM" sourceRef="Gateway_0m22u43" targetRef="Gateway_0uuh7js">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${incomeVerificationResult=='ZDZAM'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_toEZSO" name="EZSO" sourceRef="Gateway_0m22u43" targetRef="Gateway_0jblx1f">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${incomeVerificationResult=='EZSO'}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0jblx1f">
      <bpmn:incoming>Flow_APPROVE_CONAPPR</bpmn:incoming>
      <bpmn:incoming>Flow_toEZSO</bpmn:incoming>
      <bpmn:outgoing>Flow_14el3ew</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_14el3ew" sourceRef="Gateway_0jblx1f" targetRef="Gateway_first" />
    <bpmn:sequenceFlow id="Flow_1iqs32m" sourceRef="Gateway_afterSetGNUPL" targetRef="Event_call_GNUPL" />
    <bpmn:endEvent id="Event_call_GNUPL" name="GNUPL">
      <bpmn:incoming>Flow_1iqs32m</bpmn:incoming>
      <bpmn:escalationEventDefinition id="EscalationEventDefinition_1pyx14k" escalationRef="Escalation_1qfi72d" />
    </bpmn:endEvent>
    <bpmn:serviceTask id="Activity_GetMortgageApplSubAppl_2" name="get Mortgage Appl SubAppl" camunda:type="external" camunda:topic="mtgGetApplication">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="busApplId">${busApplId}</camunda:inputParameter>
          <camunda:inputParameter name="busSubApplId">${busSubApplId}</camunda:inputParameter>
          <camunda:inputParameter name="getVariants">OFR,REQ</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1o611lw</bpmn:incoming>
      <bpmn:outgoing>Flow_03tgsm6</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_03tgsm6" sourceRef="Activity_GetMortgageApplSubAppl_2" targetRef="Gateway_setFNLSCR" />
    <bpmn:sequenceFlow id="Flow_0ku9hf0" sourceRef="Activity_GetMortgageApplSubAppl" targetRef="Gateway_approvalResult1" />
    <bpmn:exclusiveGateway id="Gateway_0ozilpy" default="Flow_09ojzpn">
      <bpmn:incoming>Flow_1yh41j8</bpmn:incoming>
      <bpmn:outgoing>Flow_0gdgcv7</bpmn:outgoing>
      <bpmn:outgoing>Flow_09ojzpn</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0gdgcv7" sourceRef="Gateway_0ozilpy" targetRef="Activity_UpdateApplInHYC">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${execution.getVariable("isInHYC") != null &amp;&amp; isInHYC}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_0fwy36m">
      <bpmn:incoming>Flow_1jap1p4</bpmn:incoming>
      <bpmn:incoming>Flow_1iwegm1</bpmn:incoming>
      <bpmn:outgoing>Flow_04vpddd</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_09ojzpn" sourceRef="Gateway_0ozilpy" targetRef="Activity_CreateApplInHYC" />
    <bpmn:serviceTask id="Activity_CreateApplInHYC" name="Create Application In Hypoclient" camunda:type="external" camunda:topic="mtgCreateApplInHYC">
      <bpmn:extensionElements>
        <camunda:properties>
          <camunda:property name="retry_config" value="NO" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_09ojzpn</bpmn:incoming>
      <bpmn:outgoing>Flow_1iwegm1</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1iwegm1" sourceRef="Activity_CreateApplInHYC" targetRef="Gateway_0fwy36m" />
    <bpmn:sequenceFlow id="Flow_04vpddd" sourceRef="Gateway_0fwy36m" targetRef="Activity_realtyAppraisalAndSupervision" />
    <bpmn:exclusiveGateway id="Gateway_0on8vcm" default="Flow_1uzqb20">
      <bpmn:incoming>Flow_0pltd6u</bpmn:incoming>
      <bpmn:outgoing>Flow_Realties_APPROVE</bpmn:outgoing>
      <bpmn:outgoing>Flow_Realties_DECLINE</bpmn:outgoing>
      <bpmn:outgoing>Flow_1uzqb20</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0pltd6u" sourceRef="Activity_realtyAppraisalAndSupervision" targetRef="Gateway_0on8vcm" />
    <bpmn:sequenceFlow id="Flow_Realties_APPROVE" name="APPROVE" sourceRef="Gateway_0on8vcm" targetRef="Gateway_1add0cc">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${callApprovalResult=="APPROVE"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_02bqy16" sourceRef="Gateway_1add0cc" targetRef="Activity_mtgSubApplState_NBDV2" />
    <bpmn:sequenceFlow id="Flow_0403qns" sourceRef="Activity_mtgSubApplState_NBDV2" targetRef="Gateway_0vokfwm" />
    <bpmn:sequenceFlow id="Flow_0m2erll" sourceRef="Gateway_0vokfwm" targetRef="Gateway_1jt7e2i" />
    <bpmn:sequenceFlow id="Flow_0kzyiom" sourceRef="Gateway_1jt7e2i" targetRef="Event_rmlApplicationFinalized_2" />
    <bpmn:sequenceFlow id="Flow_1rfpz7p" sourceRef="Event_rmlApplicationFinalized_2" targetRef="Gateway_0fhkb9j" />
    <bpmn:sequenceFlow id="Flow_1xq9wjj" sourceRef="Gateway_0fhkb9j" targetRef="Activity_mtgSubApplState_UPZ" />
    <bpmn:sequenceFlow id="Flow_1g9l5g0" sourceRef="Activity_mtgSubApplState_UPZ" targetRef="Gateway_1adobfn" />
    <bpmn:sequenceFlow id="Flow_0306qhi" sourceRef="Gateway_1adobfn" targetRef="Gateway_0h3u83m" />
    <bpmn:sequenceFlow id="Flow_1h3o07m" sourceRef="Gateway_0h3u83m" targetRef="Event_rmlTermsAndConditionsDone" />
    <bpmn:endEvent id="Event_FNLSCR" name="FNLSCR">
      <bpmn:incoming>Flow_1k9ykav</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1k9ykav" sourceRef="Event_rmlTermsAndConditionsDone" targetRef="Event_FNLSCR" />
    <bpmn:sequenceFlow id="Flow_0qnd7n2" sourceRef="Gateway_0fhkb9j" targetRef="Activity_mtgApplState_UPZ" />
    <bpmn:sequenceFlow id="Flow_1mr7bj1" sourceRef="Activity_mtgApplState_UPZ" targetRef="Gateway_1adobfn" />
    <bpmn:sequenceFlow id="Flow_1k5bqdo" sourceRef="Gateway_1add0cc" targetRef="Activity_mtgApplState_NBDV2" />
    <bpmn:sequenceFlow id="Flow_0b8n29m" sourceRef="Activity_mtgApplState_NBDV2" targetRef="Gateway_0vokfwm" />
    <bpmn:sequenceFlow id="Flow_0bflj5o" sourceRef="Gateway_1jt7e2i" targetRef="Event_rmlRealtiesToModify" />
    <bpmn:callActivity id="Activity_realtyAppraisalAndSupervision" name="Realty Appraisal and Supervision" calledElement="MortgageAppraisal">
      <bpmn:extensionElements>
        <camunda:in businessKey="#{execution.processBusinessKey}" />
        <camunda:out source="callApprovalResult" target="callApprovalResult" />
        <camunda:in source="busApplId" target="busApplId" />
        <camunda:in source="applKey" target="applKey" />
        <camunda:in source="X-Correlation-Id" target="X-Correlation-Id" />
        <camunda:in source="X-Cl-Context-Id" target="X-Cl-Context-Id" />
        <camunda:in source="busSubApplId" target="busSubApplId" />
        <camunda:in source="productId" target="productId" />
        <camunda:in source="processTp" target="processTp" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_04vpddd</bpmn:incoming>
      <bpmn:outgoing>Flow_0pltd6u</bpmn:outgoing>
    </bpmn:callActivity>
    <bpmn:sequenceFlow id="Flow_Realties_DECLINE" name="DECLINE" sourceRef="Gateway_0on8vcm" targetRef="Event_ZDZAM">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${callApprovalResult=="DECLINE"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:endEvent id="Event_ZDZAM" name="ZDZAM">
      <bpmn:incoming>Flow_Realties_DECLINE</bpmn:incoming>
      <bpmn:escalationEventDefinition id="EscalationEventDefinition_02fqr1n" escalationRef="Escalation_0aoph10" />
    </bpmn:endEvent>
    <bpmn:exclusiveGateway id="Gateway_1mi63ve">
      <bpmn:incoming>Flow_1fhdfwe</bpmn:incoming>
      <bpmn:incoming>Flow_1v3wvic</bpmn:incoming>
      <bpmn:outgoing>Flow_1yh41j8</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1yh41j8" sourceRef="Gateway_1mi63ve" targetRef="Gateway_0ozilpy" />
    <bpmn:sequenceFlow id="Flow_0yp5i0p" sourceRef="Event_rmlRealtiesToModify" targetRef="Gateway_0xd0j34" />
    <bpmn:intermediateCatchEvent id="Event_rmlRealtiesToModify" name="rmlRealtiesToModify">
      <bpmn:incoming>Flow_0bflj5o</bpmn:incoming>
      <bpmn:outgoing>Flow_0yp5i0p</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_1s4yjqk" messageRef="Message_2gnl5eh" />
    </bpmn:intermediateCatchEvent>
    <bpmn:intermediateCatchEvent id="Event_rmlApplicationFinalized_2" name="rmlApplicationFinalized">
      <bpmn:incoming>Flow_0kzyiom</bpmn:incoming>
      <bpmn:outgoing>Flow_1rfpz7p</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_05w4lmm" messageRef="Message_1sinqo4" />
    </bpmn:intermediateCatchEvent>
    <bpmn:intermediateCatchEvent id="Event_rmlTermsAndConditionsDone" name="rmlTermsAndConditionsDone">
      <bpmn:incoming>Flow_1h3o07m</bpmn:incoming>
      <bpmn:outgoing>Flow_1k9ykav</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_1092u36" messageRef="Message_2v5g4gq" />
    </bpmn:intermediateCatchEvent>
    <bpmn:serviceTask id="Activity_mtgApplState_NBDV2" name="updateStatus Appl NBDV2" camunda:type="external" camunda:topic="mtgUpdateApplicationStatus">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="nextApplicationState">NBDV2</camunda:inputParameter>
          <camunda:inputParameter name="applId">${busApplId}</camunda:inputParameter>
          <camunda:inputParameter name="disableEmailNotification">1</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1k5bqdo</bpmn:incoming>
      <bpmn:outgoing>Flow_0b8n29m</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_mtgSubApplState_NBDV2" name="updateStatus SubAppl NBDV2" camunda:type="external" camunda:topic="mtgUpdateApplicationStatus">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="nextApplicationState">NBDV2</camunda:inputParameter>
          <camunda:inputParameter name="applId">${busSubApplId}</camunda:inputParameter>
          <camunda:inputParameter name="forceEmailNotification">1</camunda:inputParameter>
          <camunda:inputParameter name="disableEmailNotification">${execution.getVariable("sent") == null || execution.getVariable("sent") != "1" ? "0" : "1"}</camunda:inputParameter>
        </camunda:inputOutput>
        <camunda:executionListener event="end">
          <camunda:script scriptFormat="groovy">execution.setVariable("sent", "1");</camunda:script>
        </camunda:executionListener>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_02bqy16</bpmn:incoming>
      <bpmn:outgoing>Flow_0403qns</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_mtgApplState_UPZ" name="updateStatus Appl UPZ" camunda:type="external" camunda:topic="mtgUpdateApplicationStatus">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="nextApplicationState">NBDU</camunda:inputParameter>
          <camunda:inputParameter name="applId">${busApplId}</camunda:inputParameter>
          <camunda:inputParameter name="disableEmailNotification">1</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0qnd7n2</bpmn:incoming>
      <bpmn:outgoing>Flow_1mr7bj1</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_mtgSubApplState_UPZ" name="updateStatus SubAppl UPZ" camunda:type="external" camunda:topic="mtgUpdateApplicationStatus">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="nextApplicationState">UPZ</camunda:inputParameter>
          <camunda:inputParameter name="applId">${busSubApplId}</camunda:inputParameter>
          <camunda:inputParameter name="forceEmailNotification">0</camunda:inputParameter>
          <camunda:inputParameter name="disableEmailNotification">1</camunda:inputParameter>
        </camunda:inputOutput>
        <camunda:executionListener event="end">
          <camunda:script scriptFormat="groovy">execution.setVariable("sent", "1");</camunda:script>
        </camunda:executionListener>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1xq9wjj</bpmn:incoming>
      <bpmn:outgoing>Flow_1g9l5g0</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:eventBasedGateway id="Gateway_1jt7e2i">
      <bpmn:incoming>Flow_0m2erll</bpmn:incoming>
      <bpmn:outgoing>Flow_0kzyiom</bpmn:outgoing>
      <bpmn:outgoing>Flow_0bflj5o</bpmn:outgoing>
      <bpmn:outgoing>Flow_1gdsdb5</bpmn:outgoing>
    </bpmn:eventBasedGateway>
    <bpmn:eventBasedGateway id="Gateway_0h3u83m">
      <bpmn:incoming>Flow_0306qhi</bpmn:incoming>
      <bpmn:outgoing>Flow_1h3o07m</bpmn:outgoing>
      <bpmn:outgoing>Flow_1v30jda</bpmn:outgoing>
    </bpmn:eventBasedGateway>
    <bpmn:intermediateCatchEvent id="Event_rmlRealtiesToModify_2" name="rmlRealtiesToModify">
      <bpmn:incoming>Flow_1v30jda</bpmn:incoming>
      <bpmn:outgoing>Flow_13lomjp</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_0hjiati" messageRef="Message_2gnl5eh" />
    </bpmn:intermediateCatchEvent>
    <bpmn:sequenceFlow id="Flow_1v30jda" sourceRef="Gateway_0h3u83m" targetRef="Event_rmlRealtiesToModify_2" />
    <bpmn:exclusiveGateway id="Gateway_0xd0j34">
      <bpmn:incoming>Flow_0yp5i0p</bpmn:incoming>
      <bpmn:incoming>Flow_13lomjp</bpmn:incoming>
      <bpmn:outgoing>Flow_1v3wvic</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_1v3wvic" sourceRef="Gateway_0xd0j34" targetRef="Gateway_1mi63ve" />
    <bpmn:sequenceFlow id="Flow_13lomjp" sourceRef="Event_rmlRealtiesToModify_2" targetRef="Gateway_0xd0j34" />
    <bpmn:parallelGateway id="Gateway_0vokfwm">
      <bpmn:incoming>Flow_0403qns</bpmn:incoming>
      <bpmn:incoming>Flow_0b8n29m</bpmn:incoming>
      <bpmn:outgoing>Flow_0m2erll</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:parallelGateway id="Gateway_1add0cc">
      <bpmn:incoming>Flow_Realties_APPROVE</bpmn:incoming>
      <bpmn:outgoing>Flow_02bqy16</bpmn:outgoing>
      <bpmn:outgoing>Flow_1k5bqdo</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:parallelGateway id="Gateway_1adobfn">
      <bpmn:incoming>Flow_1g9l5g0</bpmn:incoming>
      <bpmn:incoming>Flow_1mr7bj1</bpmn:incoming>
      <bpmn:outgoing>Flow_0306qhi</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:parallelGateway id="Gateway_0fhkb9j">
      <bpmn:incoming>Flow_1rfpz7p</bpmn:incoming>
      <bpmn:outgoing>Flow_1xq9wjj</bpmn:outgoing>
      <bpmn:outgoing>Flow_0qnd7n2</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:sequenceFlow id="Flow_1uzqb20" sourceRef="Gateway_0on8vcm" targetRef="Gateway_first" />
    <bpmn:intermediateCatchEvent id="Event_0v3v18j">
      <bpmn:incoming>Flow_1gdsdb5</bpmn:incoming>
      <bpmn:outgoing>Flow_1mjhaf7</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_0geeuip" messageRef="Message_0lnuko3" />
    </bpmn:intermediateCatchEvent>
    <bpmn:sequenceFlow id="Flow_1gdsdb5" sourceRef="Gateway_1jt7e2i" targetRef="Event_0v3v18j" />
    <bpmn:sequenceFlow id="Flow_1mjhaf7" sourceRef="Event_0v3v18j" targetRef="Gateway_setNBDU_1" />
    <bpmn:boundaryEvent id="Event_TerminateProcess" name="TerminateProcess" attachedToRef="Activity_realtyAppraisalAndSupervision">
      <bpmn:outgoing>Flow_14yujl3</bpmn:outgoing>
      <bpmn:escalationEventDefinition id="EscalationEventDefinition_1c04u3q" escalationRef="Escalation_37b2qgo" />
    </bpmn:boundaryEvent>
    <bpmn:sequenceFlow id="Flow_14yujl3" sourceRef="Event_TerminateProcess" targetRef="Event_0k5q8fw" />
    <bpmn:endEvent id="Event_0k5q8fw">
      <bpmn:incoming>Flow_14yujl3</bpmn:incoming>
      <bpmn:terminateEventDefinition id="TerminateEventDefinition_0010ug9" />
    </bpmn:endEvent>
  </bpmn:process>
  <bpmn:escalation id="Escalation_0zpnpiy" name="EZSO" escalationCode="EZSO" />
  <bpmn:escalation id="Escalation_0aoph10" name="ZDZAM" escalationCode="ZDZAM" />
  <bpmn:message id="Message_1mxaoc9" name="rmlPersonalDataCollected" />
  <bpmn:escalation id="Escalation_02rl8lr" name="Cancellation" escalationCode="STR" />
  <bpmn:message id="Message_35d9v98" name="rmlGuarOfferUsed" />
  <bpmn:message id="Message_0o529ir" name="rmlGuarOfferAccepted" />
  <bpmn:message id="Message_12dvkfe" name="rmlGuarOfferCanceled" />
  <bpmn:message id="Message_306vj21" name="rmlGuarOfferCreated" />
  <bpmn:escalation id="Escalation_3o830db" name="PRDN" escalationCode="PRDN" />
  <bpmn:signal id="Signal_00mp1fg" name="PRDN" />
  <bpmn:escalation id="Escalation_105vibk" name="STR" escalationCode="STR" />
  <bpmn:signal id="Signal_04k1mg4" name="EZSO" />
  <bpmn:signal id="Signal_293e25u" name="GuarOfferCreated" />
  <bpmn:signal id="Signal_0n76mus" name="GuarOfferUsed" />
  <bpmn:message id="Message_0116q0v" name="stornoReceivedMessage" />
  <bpmn:signal id="Signal_01mfi74" name="STR" />
  <bpmn:signal id="Signal_14ak2nf" name="ZDZAM" />
  <bpmn:escalation id="Escalation_3qtid1k" name="GuarOfferCreated" escalationCode="GuarOfferCreated" />
  <bpmn:escalation id="Escalation_0pe32qp" name="GuarOfferUsed" escalationCode="GuarOfferUsed" />
  <bpmn:escalation id="Escalation_3iq1hb3" name="SendGuarOfferEmail" escalationCode="SendGuarOfferEmail" />
  <bpmn:message id="Message_3omofgu" name="rmlApplicationPreparationFinished" />
  <bpmn:escalation id="Escalation_20fbbei" name="ZDOP" escalationCode="ZDOP" />
  <bpmn:message id="Message_1sinqo4" name="rmlApplicationFinalized" />
  <bpmn:message id="Message_0smkif7" name="rmlApplicationToModify" />
  <bpmn:message id="Message_0lnuko3" name="rmlFinalOfferToModify" />
  <bpmn:message id="Message_0drt89d" name="rmlFinalOfferModified" />
  <bpmn:message id="Message_34c5c3m" name="rmlGuarOfferApplied" />
  <bpmn:escalation id="Escalation_1qfi72d" name="GNUPL" escalationCode="GNUPL" />
  <bpmn:message id="Message_2gnl5eh" name="rmlRealtiesToModify" />
  <bpmn:message id="Message_2v5g4gq" name="rmlTermsAndConditionsDone" />
  <bpmn:escalation id="Escalation_37b2qgo" name="TerminateProcess" escalationCode="TerminateProcess" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="MortgageFinalization">
      <bpmndi:BPMNShape id="Event_0yk1phk_di" bpmnElement="Event_Start" color:background-color="#fff" color:border-color="#000">
        <dc:Bounds x="152" y="282" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0rmzzc5_di" bpmnElement="Gateway_first" isMarkerVisible="true">
        <dc:Bounds x="235" y="275" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0cd75fw_di" bpmnElement="Gateway_approvalResult1" isMarkerVisible="true">
        <dc:Bounds x="495" y="275" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0lu498i_di" bpmnElement="Event_rmlApplicationFinalized">
        <dc:Bounds x="1482" y="172" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1458" y="215" width="85" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0hdjthv_di" bpmnElement="Event_rmlApplicationToModify">
        <dc:Bounds x="1482" y="612" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1461" y="655" width="81" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1rs8oop_di" bpmnElement="Event_rmlFinalOfferToModify">
        <dc:Bounds x="1372" y="502" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1348" y="545" width="85" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_073tx77_di" bpmnElement="Event_rmlFinalOfferModified">
        <dc:Bounds x="1482" y="832" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1457" y="875" width="88" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1v50sco_di" bpmnElement="Gateway_approvalResult" isMarkerVisible="true">
        <dc:Bounds x="1865" y="1025" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0uyhcrh" bpmnElement="Activity_mtgApplState_NBDV" bioc:stroke="#0d4372" bioc:fill="#bbdefb" color:background-color="#bbdefb" color:border-color="#0d4372">
        <dc:Bounds x="990" y="260" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_12nlxzl" bpmnElement="Activity_mtgSubApplState_ZDPSCH" bioc:stroke="#0d4372" bioc:fill="#bbdefb" color:background-color="#bbdefb" color:border-color="#0d4372">
        <dc:Bounds x="990" y="920" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1mtmx6j_di" bpmnElement="Activity_callMortgageApproval" color:background-color="#fff" color:border-color="#000">
        <dc:Bounds x="1840" y="700" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_16tt4ky_di" bpmnElement="Event_callSTR">
        <dc:Bounds x="2212" y="1122" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2219" y="1165" width="24" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0hcdaxu_di" bpmnElement="Event_callZDZAM">
        <dc:Bounds x="2212" y="1032" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2213" y="1075" width="37" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0bv6oc5" bpmnElement="Activity_mtgApplState_GNUPL" color:background-color="#fff" color:border-color="#000">
        <dc:Bounds x="1840" y="480" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_09vi949" bpmnElement="Activity_mtgApplState_NBDU" bioc:stroke="#0d4372" bioc:fill="#bbdefb" color:background-color="#bbdefb" color:border-color="#0d4372">
        <dc:Bounds x="990" y="590" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0xx71bw" bpmnElement="Activity_mtgApplState_ZPRC" bioc:stroke="#0d4372" bioc:fill="#bbdefb" color:background-color="#bbdefb" color:border-color="#0d4372">
        <dc:Bounds x="1840" y="150" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0omtk33_di" bpmnElement="Gateway_waitForEvent">
        <dc:Bounds x="1365" y="275" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0w4917u_di" bpmnElement="Activity_UpdateApplInHYC">
        <dc:Bounds x="2310" y="260" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0suyjs3_di" bpmnElement="Gateway_setNBDV">
        <dc:Bounds x="875" y="275" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1fzs1zh_di" bpmnElement="Gateway_setZDPSCH">
        <dc:Bounds x="875" y="825" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_01emm1m_di" bpmnElement="Gateway_afterSetZDPSCH">
        <dc:Bounds x="1155" y="825" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0s1wdt7_di" bpmnElement="Gateway_afterSetNBDV">
        <dc:Bounds x="1155" y="275" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0pbbmfc_di" bpmnElement="Gateway_setFNLSCR">
        <dc:Bounds x="1725" y="165" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1l2zgj2_di" bpmnElement="Gateway_afterSetFNLSCR">
        <dc:Bounds x="2005" y="165" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0w5gaut_di" bpmnElement="Gateway_afterSetGNUPL">
        <dc:Bounds x="2015" y="605" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_14qcf0t_di" bpmnElement="Gateway_setGNUPL">
        <dc:Bounds x="1725" y="605" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_16tsby1_di" bpmnElement="Gateway_setNBDU">
        <dc:Bounds x="875" y="605" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0og9k2w_di" bpmnElement="Gateway_afterSetNBDU">
        <dc:Bounds x="1155" y="605" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0udky6n" bpmnElement="Activity_mtgSubApplState_NBDV" bioc:stroke="#0d4372" bioc:fill="#bbdefb" color:background-color="#bbdefb" color:border-color="#0d4372">
        <dc:Bounds x="990" y="370" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_10m0gvw" bpmnElement="Activity_mtgApplState_ZDPSCH" bioc:stroke="#0d4372" bioc:fill="#bbdefb" color:background-color="#bbdefb" color:border-color="#0d4372">
        <dc:Bounds x="990" y="810" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0291kcv" bpmnElement="Activity_mtgSubApplState_ZPRC" bioc:stroke="#0d4372" bioc:fill="#bbdefb" color:background-color="#bbdefb" color:border-color="#0d4372">
        <dc:Bounds x="1840" y="260" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0w1qm5w" bpmnElement="Activity_mtgSubApplState_GNUPL" color:background-color="#fff" color:border-color="#000">
        <dc:Bounds x="1840" y="590" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0xbwwbk" bpmnElement="Activity_mtgSubApplState_NBDU" bioc:stroke="#0d4372" bioc:fill="#bbdefb" color:background-color="#bbdefb" color:border-color="#0d4372">
        <dc:Bounds x="990" y="700" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1kdhu06" bpmnElement="Activity_generateEmail_X0000042" color:background-color="#fff" color:border-color="#000">
        <dc:Bounds x="990" y="150" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_06fihkn" bpmnElement="Activity_GetMortgageApplSubAppl" color:background-color="#fff" color:border-color="#000">
        <dc:Bounds x="340" y="260" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0qd7jem" bpmnElement="Activity_reduction" color:background-color="#fff" color:border-color="#000">
        <dc:Bounds x="620" y="260" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1arccjl" bpmnElement="Gateway_checkOfferReduction" isMarkerVisible="true">
        <dc:Bounds x="778" y="275" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="773" y="238" width="60" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0vtjend_di" bpmnElement="Event_rmlGuarOfferApplied">
        <dc:Bounds x="1482" y="722" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1457" y="765" width="87" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_10spi6a_di" bpmnElement="Gateway_join2" isMarkerVisible="true">
        <dc:Bounds x="1255" y="715" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_05xe6gs_di" bpmnElement="Gateway_waitForEvent2">
        <dc:Bounds x="1365" y="715" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0rnj8y2" bpmnElement="Event_rmlGuarOfferApplied2">
        <dc:Bounds x="1482" y="282" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1457" y="325" width="87" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1qq7uew_di" bpmnElement="Gateway_1qq7uew" isMarkerVisible="true">
        <dc:Bounds x="1595" y="715" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_07cbm5a" bpmnElement="Activity_mtgApplState_ZDPDP" color:background-color="#fff" color:border-color="#000">
        <dc:Bounds x="1700" y="700" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0ns1h3s_di" bpmnElement="Gateway_setNBDU_1" isMarkerVisible="true">
        <dc:Bounds x="875" y="495" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0oc4btl_di" bpmnElement="Activity_IncomeVerification">
        <dc:Bounds x="1840" y="1100" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0m22u43_di" bpmnElement="Gateway_0m22u43" isMarkerVisible="true">
        <dc:Bounds x="2015" y="1115" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0uuh7js_di" bpmnElement="Gateway_0uuh7js" isMarkerVisible="true">
        <dc:Bounds x="2015" y="1025" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0jblx1f_di" bpmnElement="Gateway_0jblx1f" isMarkerVisible="true">
        <dc:Bounds x="235" y="1025" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_112j97l_di" bpmnElement="Event_call_GNUPL">
        <dc:Bounds x="2212" y="612" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2212" y="655" width="39" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1bukybk" bpmnElement="Activity_GetMortgageApplSubAppl_2" color:background-color="#fff" color:border-color="#000">
        <dc:Bounds x="1570" y="150" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0ozilpy_di" bpmnElement="Gateway_0ozilpy" isMarkerVisible="true">
        <dc:Bounds x="2205" y="165" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0fwy36m_di" bpmnElement="Gateway_0fwy36m" isMarkerVisible="true">
        <dc:Bounds x="2465" y="165" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1pr8r7k" bpmnElement="Activity_CreateApplInHYC">
        <dc:Bounds x="2310" y="150" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0on8vcm_di" bpmnElement="Gateway_0on8vcm" isMarkerVisible="true">
        <dc:Bounds x="2725" y="165" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1ogxln0_di" bpmnElement="Event_FNLSCR">
        <dc:Bounds x="3952" y="172" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3948" y="215" width="46" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0xigvkq_di" bpmnElement="Activity_realtyAppraisalAndSupervision">
        <dc:Bounds x="2570" y="150" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0wuo516_di" bpmnElement="Event_ZDZAM">
        <dc:Bounds x="2732" y="282" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2732" y="325" width="37" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1mi63ve_di" bpmnElement="Gateway_1mi63ve" isMarkerVisible="true">
        <dc:Bounds x="2105" y="165" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0r7v7ko_di" bpmnElement="Event_rmlRealtiesToModify">
        <dc:Bounds x="3292" y="282" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3345" y="286" width="89" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1yopner_di" bpmnElement="Event_rmlApplicationFinalized_2">
        <dc:Bounds x="3292" y="172" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3268" y="215" width="85" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_14v6ihh_di" bpmnElement="Event_rmlTermsAndConditionsDone">
        <dc:Bounds x="3852" y="172" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3829" y="215" width="85" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_028k1lw" bpmnElement="Activity_mtgApplState_NBDV2">
        <dc:Bounds x="2930" y="150" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_08xlw7o" bpmnElement="Activity_mtgSubApplState_NBDV2">
        <dc:Bounds x="2930" y="260" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_15agdil" bpmnElement="Activity_mtgApplState_UPZ">
        <dc:Bounds x="3490" y="150" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_100s1fn" bpmnElement="Activity_mtgSubApplState_UPZ">
        <dc:Bounds x="3490" y="260" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_02mriv2_di" bpmnElement="Gateway_1jt7e2i">
        <dc:Bounds x="3185" y="165" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0tbb8o7_di" bpmnElement="Gateway_0h3u83m">
        <dc:Bounds x="3745" y="165" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0j2yhfw_di" bpmnElement="Event_rmlRealtiesToModify_2">
        <dc:Bounds x="3752" y="282" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="3795" y="286" width="89" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0xd0j34_di" bpmnElement="Gateway_0xd0j34" isMarkerVisible="true">
        <dc:Bounds x="3285" y="375" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_17djl3d_di" bpmnElement="Gateway_0vokfwm">
        <dc:Bounds x="3085" y="165" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1nruigd_di" bpmnElement="Gateway_1add0cc">
        <dc:Bounds x="2825" y="165" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0m88046_di" bpmnElement="Gateway_1adobfn">
        <dc:Bounds x="3645" y="165" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0tn33o3_di" bpmnElement="Gateway_0fhkb9j">
        <dc:Bounds x="3375" y="165" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_19jioje_di" bpmnElement="Event_0v3v18j">
        <dc:Bounds x="3192" y="282" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1kbgcc3_di" bpmnElement="Event_0k5q8fw">
        <dc:Bounds x="2602" y="282" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_19im9a2_di" bpmnElement="Event_TerminateProcess">
        <dc:Bounds x="2602" y="212" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2576" y="255" width="88" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_1qij6ks_di" bpmnElement="Flow_1qij6ks">
        <di:waypoint x="188" y="300" />
        <di:waypoint x="235" y="300" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_162ubpq_di" bpmnElement="Flow_162ubpq">
        <di:waypoint x="285" y="300" />
        <di:waypoint x="340" y="300" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1phzqkd_di" bpmnElement="Flow_APPROVE">
        <di:waypoint x="545" y="300" />
        <di:waypoint x="620" y="300" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="553" y="273" width="53" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1rqqnx2_di" bpmnElement="Flow_1rqqnx2">
        <di:waypoint x="1390" y="275" />
        <di:waypoint x="1390" y="190" />
        <di:waypoint x="1482" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1o611lw_di" bpmnElement="Flow_1o611lw">
        <di:waypoint x="1518" y="190" />
        <di:waypoint x="1570" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1jap1p4_di" bpmnElement="Flow_1jap1p4">
        <di:waypoint x="2410" y="300" />
        <di:waypoint x="2490" y="300" />
        <di:waypoint x="2490" y="215" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1g824ta_di" bpmnElement="Flow_CONAPPR">
        <di:waypoint x="520" y="325" />
        <di:waypoint x="520" y="850" />
        <di:waypoint x="875" y="850" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="532" y="443" width="56" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1a0479z_di" bpmnElement="Flow_1a0479z">
        <di:waypoint x="1390" y="715" />
        <di:waypoint x="1390" y="630" />
        <di:waypoint x="1482" y="630" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_05hk21h_di" bpmnElement="Flow_05hk21h">
        <di:waypoint x="1390" y="325" />
        <di:waypoint x="1390" y="502" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ufwsnf_di" bpmnElement="Flow_0ufwsnf">
        <di:waypoint x="1518" y="630" />
        <di:waypoint x="1725" y="630" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1s1f5cv_di" bpmnElement="Flow_1s1f5cv">
        <di:waypoint x="1372" y="520" />
        <di:waypoint x="925" y="520" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0gp2xk5_di" bpmnElement="Flow_0gp2xk5">
        <di:waypoint x="1518" y="850" />
        <di:waypoint x="1620" y="850" />
        <di:waypoint x="1620" y="765" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ko4qe3_di" bpmnElement="Flow_1ko4qe3">
        <di:waypoint x="1890" y="1075" />
        <di:waypoint x="1890" y="1100" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1toyj8v_di" bpmnElement="Flow_DECLINE">
        <di:waypoint x="1915" y="1050" />
        <di:waypoint x="2015" y="1050" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1934" y="1023" width="49" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_021p9gx_di" bpmnElement="Flow_APPROVE_CONAPPR">
        <di:waypoint x="1865" y="1050" />
        <di:waypoint x="285" y="1050" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1767" y="1016" width="62" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ebdfn6_di" bpmnElement="Flow_1ebdfn6">
        <di:waypoint x="900" y="325" />
        <di:waypoint x="900" y="410" />
        <di:waypoint x="990" y="410" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0x8abq9_di" bpmnElement="Flow_0x8abq9">
        <di:waypoint x="1205" y="300" />
        <di:waypoint x="1365" y="300" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ykvtyy_di" bpmnElement="Flow_1ykvtyy">
        <di:waypoint x="925" y="300" />
        <di:waypoint x="990" y="300" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1z108mq_di" bpmnElement="Flow_1z108mq">
        <di:waypoint x="1090" y="300" />
        <di:waypoint x="1155" y="300" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1xmmntl_di" bpmnElement="Flow_1xmmntl">
        <di:waypoint x="925" y="850" />
        <di:waypoint x="990" y="850" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1eaxnkj_di" bpmnElement="Flow_1eaxnkj">
        <di:waypoint x="1205" y="850" />
        <di:waypoint x="1280" y="850" />
        <di:waypoint x="1280" y="765" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1g39fwq_di" bpmnElement="Flow_1g39fwq">
        <di:waypoint x="900" y="875" />
        <di:waypoint x="900" y="960" />
        <di:waypoint x="990" y="960" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0imzcyv_di" bpmnElement="Flow_0imzcyv">
        <di:waypoint x="1090" y="960" />
        <di:waypoint x="1180" y="960" />
        <di:waypoint x="1180" y="875" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0d5gvan_di" bpmnElement="Flow_0d5gvan">
        <di:waypoint x="1205" y="630" />
        <di:waypoint x="1280" y="630" />
        <di:waypoint x="1280" y="715" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_06o2uf1_di" bpmnElement="Flow_06o2uf1">
        <di:waypoint x="1890" y="780" />
        <di:waypoint x="1890" y="1025" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1fhdfwe_di" bpmnElement="Flow_1fhdfwe">
        <di:waypoint x="2055" y="190" />
        <di:waypoint x="2105" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0eybdng_di" bpmnElement="Flow_0eybdng">
        <di:waypoint x="1775" y="630" />
        <di:waypoint x="1840" y="630" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0klz1ar_di" bpmnElement="Flow_0klz1ar">
        <di:waypoint x="1750" y="605" />
        <di:waypoint x="1750" y="520" />
        <di:waypoint x="1840" y="520" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_03qtxqa_di" bpmnElement="Flow_03qtxqa">
        <di:waypoint x="925" y="630" />
        <di:waypoint x="990" y="630" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1iylcfx_di" bpmnElement="Flow_1iylcfx">
        <di:waypoint x="900" y="655" />
        <di:waypoint x="900" y="740" />
        <di:waypoint x="990" y="740" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ktobpw_di" bpmnElement="Flow_1ktobpw">
        <di:waypoint x="1090" y="630" />
        <di:waypoint x="1155" y="630" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1kqx9tc_di" bpmnElement="Flow_1kqx9tc">
        <di:waypoint x="1940" y="520" />
        <di:waypoint x="2040" y="520" />
        <di:waypoint x="2040" y="605" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1hsvecr_di" bpmnElement="Flow_1hsvecr">
        <di:waypoint x="1750" y="215" />
        <di:waypoint x="1750" y="300" />
        <di:waypoint x="1840" y="300" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0lzjlns_di" bpmnElement="Flow_0lzjlns">
        <di:waypoint x="1775" y="190" />
        <di:waypoint x="1840" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0d3gprc_di" bpmnElement="Flow_0d3gprc">
        <di:waypoint x="1940" y="190" />
        <di:waypoint x="2005" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0nib3ad_di" bpmnElement="Flow_0nib3ad">
        <di:waypoint x="1090" y="410" />
        <di:waypoint x="1180" y="410" />
        <di:waypoint x="1180" y="325" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1jnv3ht_di" bpmnElement="Flow_1jnv3ht">
        <di:waypoint x="1090" y="850" />
        <di:waypoint x="1155" y="850" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1vzn8y6_di" bpmnElement="Flow_1vzn8y6">
        <di:waypoint x="1940" y="300" />
        <di:waypoint x="2030" y="300" />
        <di:waypoint x="2030" y="215" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0n1src3_di" bpmnElement="Flow_0n1src3">
        <di:waypoint x="1940" y="630" />
        <di:waypoint x="2015" y="630" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_13xy3ib_di" bpmnElement="Flow_13xy3ib">
        <di:waypoint x="1090" y="740" />
        <di:waypoint x="1180" y="740" />
        <di:waypoint x="1180" y="655" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0y6x4fg_di" bpmnElement="Flow_0y6x4fg">
        <di:waypoint x="900" y="275" />
        <di:waypoint x="900" y="190" />
        <di:waypoint x="990" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0lyadsu_di" bpmnElement="Flow_0lyadsu">
        <di:waypoint x="1090" y="190" />
        <di:waypoint x="1180" y="190" />
        <di:waypoint x="1180" y="275" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_08l7sbh_di" bpmnElement="Flow_08l7sbh">
        <di:waypoint x="720" y="300" />
        <di:waypoint x="778" y="300" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0740ovd_di" bpmnElement="Flow_acceptable">
        <di:waypoint x="828" y="300" />
        <di:waypoint x="875" y="300" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="819" y="273" width="65" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0nh3cfx_di" bpmnElement="Flow_not_acceptable">
        <di:waypoint x="803" y="325" />
        <di:waypoint x="803" y="520" />
        <di:waypoint x="875" y="520" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="713" y="393" width="74" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0a5557b_di" bpmnElement="Flow_0a5557b">
        <di:waypoint x="1305" y="740" />
        <di:waypoint x="1365" y="740" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_14advrz_di" bpmnElement="Flow_14advrz">
        <di:waypoint x="1415" y="740" />
        <di:waypoint x="1482" y="740" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1awngul_di" bpmnElement="Flow_1awngul">
        <di:waypoint x="1415" y="300" />
        <di:waypoint x="1482" y="300" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ka5lix_di" bpmnElement="Flow_1ka5lix">
        <di:waypoint x="1645" y="740" />
        <di:waypoint x="1700" y="740" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0mojkof_di" bpmnElement="Flow_0mojkof">
        <di:waypoint x="1518" y="740" />
        <di:waypoint x="1595" y="740" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0mx1zl0_di" bpmnElement="Flow_0mx1zl0">
        <di:waypoint x="1518" y="300" />
        <di:waypoint x="1620" y="300" />
        <di:waypoint x="1620" y="715" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_02xu7j4_di" bpmnElement="Flow_02xu7j4">
        <di:waypoint x="1390" y="765" />
        <di:waypoint x="1390" y="850" />
        <di:waypoint x="1482" y="850" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_136pmxe_di" bpmnElement="Flow_136pmxe">
        <di:waypoint x="1800" y="740" />
        <di:waypoint x="1840" y="740" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0e41nyb_di" bpmnElement="Flow_0e41nyb">
        <di:waypoint x="900" y="545" />
        <di:waypoint x="900" y="605" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0dlszbc_di" bpmnElement="Flow_0dlszbc">
        <di:waypoint x="1940" y="1140" />
        <di:waypoint x="2015" y="1140" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_03uw641_di" bpmnElement="Flow_toSTR">
        <di:waypoint x="2065" y="1140" />
        <di:waypoint x="2212" y="1140" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2128" y="1122" width="24" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_11euxau_di" bpmnElement="Flow_11euxau">
        <di:waypoint x="2065" y="1050" />
        <di:waypoint x="2212" y="1050" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0lb1gji_di" bpmnElement="Flow_toZDZAM">
        <di:waypoint x="2040" y="1115" />
        <di:waypoint x="2040" y="1075" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1991" y="1092" width="37" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1hn4oko_di" bpmnElement="Flow_toEZSO">
        <di:waypoint x="2040" y="1165" />
        <di:waypoint x="2040" y="1240" />
        <di:waypoint x="260" y="1240" />
        <di:waypoint x="260" y="1075" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1994" y="1213" width="31" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_14el3ew_di" bpmnElement="Flow_14el3ew">
        <di:waypoint x="260" y="1025" />
        <di:waypoint x="260" y="325" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1iqs32m_di" bpmnElement="Flow_1iqs32m">
        <di:waypoint x="2065" y="630" />
        <di:waypoint x="2212" y="630" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_03tgsm6_di" bpmnElement="Flow_03tgsm6">
        <di:waypoint x="1670" y="190" />
        <di:waypoint x="1725" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ku9hf0_di" bpmnElement="Flow_0ku9hf0">
        <di:waypoint x="440" y="300" />
        <di:waypoint x="495" y="300" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0gdgcv7_di" bpmnElement="Flow_0gdgcv7">
        <di:waypoint x="2230" y="215" />
        <di:waypoint x="2230" y="300" />
        <di:waypoint x="2310" y="300" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_09ojzpn_di" bpmnElement="Flow_09ojzpn">
        <di:waypoint x="2255" y="190" />
        <di:waypoint x="2310" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1iwegm1_di" bpmnElement="Flow_1iwegm1">
        <di:waypoint x="2410" y="190" />
        <di:waypoint x="2465" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_04vpddd_di" bpmnElement="Flow_04vpddd">
        <di:waypoint x="2515" y="190" />
        <di:waypoint x="2570" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0pltd6u_di" bpmnElement="Flow_0pltd6u">
        <di:waypoint x="2670" y="190" />
        <di:waypoint x="2725" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0g08ag7_di" bpmnElement="Flow_Realties_APPROVE">
        <di:waypoint x="2775" y="190" />
        <di:waypoint x="2825" y="190" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2774" y="172" width="53" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_02bqy16_di" bpmnElement="Flow_02bqy16">
        <di:waypoint x="2850" y="215" />
        <di:waypoint x="2850" y="300" />
        <di:waypoint x="2930" y="300" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0403qns_di" bpmnElement="Flow_0403qns">
        <di:waypoint x="3030" y="300" />
        <di:waypoint x="3110" y="300" />
        <di:waypoint x="3110" y="215" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0m2erll_di" bpmnElement="Flow_0m2erll">
        <di:waypoint x="3135" y="190" />
        <di:waypoint x="3185" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0kzyiom_di" bpmnElement="Flow_0kzyiom">
        <di:waypoint x="3235" y="190" />
        <di:waypoint x="3292" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1rfpz7p_di" bpmnElement="Flow_1rfpz7p">
        <di:waypoint x="3328" y="190" />
        <di:waypoint x="3375" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1xq9wjj_di" bpmnElement="Flow_1xq9wjj">
        <di:waypoint x="3400" y="215" />
        <di:waypoint x="3400" y="300" />
        <di:waypoint x="3490" y="300" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1g9l5g0_di" bpmnElement="Flow_1g9l5g0">
        <di:waypoint x="3590" y="300" />
        <di:waypoint x="3670" y="300" />
        <di:waypoint x="3670" y="215" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0306qhi_di" bpmnElement="Flow_0306qhi">
        <di:waypoint x="3695" y="190" />
        <di:waypoint x="3745" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1h3o07m_di" bpmnElement="Flow_1h3o07m">
        <di:waypoint x="3795" y="190" />
        <di:waypoint x="3852" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1k9ykav_di" bpmnElement="Flow_1k9ykav">
        <di:waypoint x="3888" y="190" />
        <di:waypoint x="3952" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0qnd7n2_di" bpmnElement="Flow_0qnd7n2">
        <di:waypoint x="3425" y="190" />
        <di:waypoint x="3490" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1mr7bj1_di" bpmnElement="Flow_1mr7bj1">
        <di:waypoint x="3590" y="190" />
        <di:waypoint x="3645" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1k5bqdo_di" bpmnElement="Flow_1k5bqdo">
        <di:waypoint x="2875" y="190" />
        <di:waypoint x="2930" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0b8n29m_di" bpmnElement="Flow_0b8n29m">
        <di:waypoint x="3030" y="190" />
        <di:waypoint x="3085" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0bflj5o_di" bpmnElement="Flow_0bflj5o">
        <di:waypoint x="3222" y="203" />
        <di:waypoint x="3298" y="287" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ii8ztl_di" bpmnElement="Flow_Realties_DECLINE">
        <di:waypoint x="2750" y="215" />
        <di:waypoint x="2750" y="282" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="2765" y="241" width="49" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1yh41j8_di" bpmnElement="Flow_1yh41j8">
        <di:waypoint x="2155" y="190" />
        <di:waypoint x="2205" y="190" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0yp5i0p_di" bpmnElement="Flow_0yp5i0p">
        <di:waypoint x="3310" y="318" />
        <di:waypoint x="3310" y="375" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1v30jda_di" bpmnElement="Flow_1v30jda">
        <di:waypoint x="3770" y="215" />
        <di:waypoint x="3770" y="282" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1v3wvic_di" bpmnElement="Flow_1v3wvic">
        <di:waypoint x="3285" y="400" />
        <di:waypoint x="2130" y="400" />
        <di:waypoint x="2130" y="215" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_13lomjp_di" bpmnElement="Flow_13lomjp">
        <di:waypoint x="3770" y="318" />
        <di:waypoint x="3770" y="400" />
        <di:waypoint x="3335" y="400" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1uzqb20_di" bpmnElement="Flow_1uzqb20">
        <di:waypoint x="2750" y="165" />
        <di:waypoint x="2750" y="80" />
        <di:waypoint x="260" y="80" />
        <di:waypoint x="260" y="275" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1gdsdb5_di" bpmnElement="Flow_1gdsdb5">
        <di:waypoint x="3210" y="215" />
        <di:waypoint x="3210" y="282" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1mjhaf7_di" bpmnElement="Flow_1mjhaf7">
        <di:waypoint x="3210" y="318" />
        <di:waypoint x="3210" y="440" />
        <di:waypoint x="1180" y="440" />
        <di:waypoint x="919" y="514" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_14yujl3_di" bpmnElement="Flow_14yujl3">
        <di:waypoint x="2620" y="248" />
        <di:waypoint x="2620" y="282" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
