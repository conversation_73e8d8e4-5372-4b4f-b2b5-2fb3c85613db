<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:color="http://www.omg.org/spec/BPMN/non-normative/color/1.0" xmlns:bioc="http://bpmn.io/schema/bpmn/biocolor/1.0" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_07rtbgt" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.35.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.22.0">
  <bpmn:process id="MortgageAppraisal" name="Mortgage Appraisal" isExecutable="true" camunda:historyTimeToLive="7">
    <bpmn:startEvent id="Event_Start_RAS" name="Start RAS Realty Appraisal and Supervision">
      <bpmn:outgoing>Flow_18oxlan</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:sequenceFlow id="Flow_18oxlan" sourceRef="Event_Start_RAS" targetRef="Gateway_updateStatusSN" />
    <bpmn:parallelGateway id="Gateway_updateStatusSN">
      <bpmn:incoming>Flow_18oxlan</bpmn:incoming>
      <bpmn:outgoing>Flow_1jtdurw</bpmn:outgoing>
      <bpmn:outgoing>Flow_0igivc5</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:serviceTask id="Activity_mtgApplState_SN" name="updateStatus Appl SN" camunda:type="external" camunda:topic="mtgUpdateApplicationStatus">
      <bpmn:documentation>Zadost ceka na supervizi nemovitosti</bpmn:documentation>
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="nextApplicationState">SN</camunda:inputParameter>
          <camunda:inputParameter name="applId">${busApplId}</camunda:inputParameter>
          <camunda:inputParameter name="disableEmailNotification">1</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1jtdurw</bpmn:incoming>
      <bpmn:outgoing>Flow_0kxj3nm</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_mtgSubApplState_SN" name="updateStatus SubAppl SN" camunda:type="external" camunda:topic="mtgUpdateApplicationStatus">
      <bpmn:documentation>Zadost ceka na supervizi nemovitosti</bpmn:documentation>
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="nextApplicationState">SN</camunda:inputParameter>
          <camunda:inputParameter name="applId">${busSubApplId}</camunda:inputParameter>
          <camunda:inputParameter name="forceEmailNotification">1</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0igivc5</bpmn:incoming>
      <bpmn:outgoing>Flow_1umqg5s</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1jtdurw" sourceRef="Gateway_updateStatusSN" targetRef="Activity_mtgApplState_SN" />
    <bpmn:sequenceFlow id="Flow_0igivc5" sourceRef="Gateway_updateStatusSN" targetRef="Activity_mtgSubApplState_SN" />
    <bpmn:parallelGateway id="Gateway_updateStatusSN_join">
      <bpmn:incoming>Flow_0kxj3nm</bpmn:incoming>
      <bpmn:incoming>Flow_1umqg5s</bpmn:incoming>
      <bpmn:outgoing>Flow_0ysb4qp</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:sequenceFlow id="Flow_0kxj3nm" sourceRef="Activity_mtgApplState_SN" targetRef="Gateway_updateStatusSN_join" />
    <bpmn:sequenceFlow id="Flow_1umqg5s" sourceRef="Activity_mtgSubApplState_SN" targetRef="Gateway_updateStatusSN_join" />
    <bpmn:sequenceFlow id="Flow_0ysb4qp" sourceRef="Gateway_updateStatusSN_join" targetRef="Gateway_waitMessage" />
    <bpmn:eventBasedGateway id="Gateway_waitMessage">
      <bpmn:incoming>Flow_0ysb4qp</bpmn:incoming>
      <bpmn:outgoing>Flow_1abrju4</bpmn:outgoing>
      <bpmn:outgoing>Flow_1azq21c</bpmn:outgoing>
    </bpmn:eventBasedGateway>
    <bpmn:intermediateCatchEvent id="Event_rmlRltySupervisionDone" name="rmlRltySupervisionDone">
      <bpmn:incoming>Flow_1abrju4</bpmn:incoming>
      <bpmn:outgoing>Flow_0ks33a1</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_0ue6ez8" messageRef="Message_1962du0" />
    </bpmn:intermediateCatchEvent>
    <bpmn:sequenceFlow id="Flow_1abrju4" sourceRef="Gateway_waitMessage" targetRef="Event_rmlRltySupervisionDone" />
    <bpmn:intermediateCatchEvent id="Event_rmlHycApproval" name="rmlHycApproval">
      <bpmn:incoming>Flow_1azq21c</bpmn:incoming>
      <bpmn:outgoing>Flow_0xu9s7w</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_1w2tywt" messageRef="Message_20s2r3j" />
    </bpmn:intermediateCatchEvent>
    <bpmn:sequenceFlow id="Flow_1azq21c" sourceRef="Gateway_waitMessage" targetRef="Event_rmlHycApproval" />
    <bpmn:serviceTask id="Activity_UpdateSubAppl_SPS" name="UpdateSubAppl appr_sys_id = SPS" camunda:type="external" camunda:topic="mtgUpdateSubApplSystem">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="approvalSystem">SPS</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0ks33a1</bpmn:incoming>
      <bpmn:outgoing>Flow_0tyd53k</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0ks33a1" sourceRef="Event_rmlRltySupervisionDone" targetRef="Activity_UpdateSubAppl_SPS" />
    <bpmn:serviceTask id="Activity_UpdateSubAppl_HYC" name="UpdateSubAppl appr_sys_id = HYC" camunda:type="external" camunda:topic="mtgUpdateSubApplSystem">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="approvalSystem">HYC</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0xu9s7w</bpmn:incoming>
      <bpmn:outgoing>Flow_16mslvx</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_mtgApplState_ZPRC" name="updateStatus Appl ZPRC" camunda:type="external" camunda:topic="mtgUpdateApplicationStatus">
      <bpmn:documentation>Zadost zpracovavana v hypoclient</bpmn:documentation>
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="nextApplicationState">ZPRC</camunda:inputParameter>
          <camunda:inputParameter name="applId">${busApplId}</camunda:inputParameter>
          <camunda:inputParameter name="disableEmailNotification">1</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0vml8ig</bpmn:incoming>
      <bpmn:outgoing>Flow_0r35hlo</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_mtgSubApplState_ZPRC" name="updateStatus SubAppl ZPRC" camunda:type="external" camunda:topic="mtgUpdateApplicationStatus">
      <bpmn:documentation>Zadost zpracovavana v hypoclient</bpmn:documentation>
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="nextApplicationState">ZPRC</camunda:inputParameter>
          <camunda:inputParameter name="applId">${busSubApplId}</camunda:inputParameter>
          <camunda:inputParameter name="forceEmailNotification">1</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0wtd15v</bpmn:incoming>
      <bpmn:outgoing>Flow_1d6vvql</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0xu9s7w" sourceRef="Event_rmlHycApproval" targetRef="Activity_UpdateSubAppl_HYC" />
    <bpmn:sequenceFlow id="Flow_16mslvx" sourceRef="Activity_UpdateSubAppl_HYC" targetRef="Gateway_updateStatusZPRC" />
    <bpmn:sequenceFlow id="Flow_0wtd15v" sourceRef="Gateway_updateStatusZPRC" targetRef="Activity_mtgSubApplState_ZPRC" />
    <bpmn:sequenceFlow id="Flow_0vml8ig" sourceRef="Gateway_updateStatusZPRC" targetRef="Activity_mtgApplState_ZPRC" />
    <bpmn:parallelGateway id="Gateway_updateStatusZPRC">
      <bpmn:incoming>Flow_16mslvx</bpmn:incoming>
      <bpmn:outgoing>Flow_0wtd15v</bpmn:outgoing>
      <bpmn:outgoing>Flow_0vml8ig</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:parallelGateway id="Gateway_updateStatusZPRC_join">
      <bpmn:incoming>Flow_0r35hlo</bpmn:incoming>
      <bpmn:incoming>Flow_1d6vvql</bpmn:incoming>
      <bpmn:outgoing>Flow_0a2cg59</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:sequenceFlow id="Flow_0r35hlo" sourceRef="Activity_mtgApplState_ZPRC" targetRef="Gateway_updateStatusZPRC_join" />
    <bpmn:sequenceFlow id="Flow_1d6vvql" sourceRef="Activity_mtgSubApplState_ZPRC" targetRef="Gateway_updateStatusZPRC_join" />
    <bpmn:sequenceFlow id="Flow_0a2cg59" sourceRef="Gateway_updateStatusZPRC_join" targetRef="Event_TerminateProcess" />
    <bpmn:serviceTask id="Activity_uploadHycRealties" name="uploadHycApplication" camunda:type="external" camunda:topic="mtgUploadHycRealties">
      <bpmn:incoming>Flow_0tyd53k</bpmn:incoming>
      <bpmn:outgoing>Flow_1ndultf</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0tyd53k" sourceRef="Activity_UpdateSubAppl_SPS" targetRef="Activity_uploadHycRealties" />
    <bpmn:serviceTask id="Activity_getMtgApplicationWithApplicants" name="getMtgApplication with applicants" camunda:type="external" camunda:topic="mtgGetApplication">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="activeOnly">0</camunda:inputParameter>
          <camunda:inputParameter name="getApplicantIds">1</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1ndultf</bpmn:incoming>
      <bpmn:outgoing>Flow_0zjdtly</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1ndultf" sourceRef="Activity_uploadHycRealties" targetRef="Activity_getMtgApplicationWithApplicants" />
    <bpmn:callActivity id="Activity_SolveApplicant" name="solve applicant" calledElement="Solve_applicant">
      <bpmn:extensionElements>
        <camunda:in businessKey="#{execution.processBusinessKey}" />
        <camunda:in source="X-Correlation-Id" target="X-Correlation-Id" />
        <camunda:in source="applicantId" target="applicantId" />
        <camunda:in source="processTp" target="processTp" />
        <camunda:in source="processState" target="processState" />
        <camunda:in source="busApplId" target="busApplId" />
        <camunda:in source="applKey" target="applKey" />
        <camunda:in source="busSubApplId" target="busSubApplId" />
        <camunda:in source="subApplKey" target="subApplKey" />
        <camunda:in source="applicationState" target="applicationState" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0zjdtly</bpmn:incoming>
      <bpmn:outgoing>Flow_1sld0xt</bpmn:outgoing>
      <bpmn:multiInstanceLoopCharacteristics camunda:collection="${applicantIds}" camunda:elementVariable="applicantId" />
    </bpmn:callActivity>
    <bpmn:sequenceFlow id="Flow_0zjdtly" sourceRef="Activity_getMtgApplicationWithApplicants" targetRef="Activity_SolveApplicant" />
    <bpmn:serviceTask id="Activity_mtgApplState_ZDDS" name="updateStatus Appl ZDDS" camunda:type="external" camunda:topic="mtgUpdateApplicationStatus">
      <bpmn:documentation>Zadost odeslana do druheho scoringu</bpmn:documentation>
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="nextApplicationState">ZDDS</camunda:inputParameter>
          <camunda:inputParameter name="applId">${busApplId}</camunda:inputParameter>
          <camunda:inputParameter name="disableEmailNotification">1</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0fvtg0b</bpmn:incoming>
      <bpmn:outgoing>Flow_0bvsi2a</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_mtgSubApplState_ZDDS" name="updateStatus SubAppl ZDDS" camunda:type="external" camunda:topic="mtgUpdateApplicationStatus">
      <bpmn:documentation>Zadost odeslana do druheho scoringu</bpmn:documentation>
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="nextApplicationState">ZDDS</camunda:inputParameter>
          <camunda:inputParameter name="applId">${busSubApplId}</camunda:inputParameter>
          <camunda:inputParameter name="forceEmailNotification">1</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1mp2hax</bpmn:incoming>
      <bpmn:outgoing>Flow_0cjj3oe</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:parallelGateway id="Gateway_updateStatusZDDS">
      <bpmn:incoming>Flow_1sld0xt</bpmn:incoming>
      <bpmn:outgoing>Flow_0fvtg0b</bpmn:outgoing>
      <bpmn:outgoing>Flow_1mp2hax</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:parallelGateway id="Gateway_updateStatusZDDS_join">
      <bpmn:incoming>Flow_0bvsi2a</bpmn:incoming>
      <bpmn:incoming>Flow_0cjj3oe</bpmn:incoming>
      <bpmn:outgoing>Flow_1o3stvv</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:sequenceFlow id="Flow_1sld0xt" sourceRef="Activity_SolveApplicant" targetRef="Gateway_updateStatusZDDS" />
    <bpmn:sequenceFlow id="Flow_0fvtg0b" sourceRef="Gateway_updateStatusZDDS" targetRef="Activity_mtgApplState_ZDDS" />
    <bpmn:sequenceFlow id="Flow_1mp2hax" sourceRef="Gateway_updateStatusZDDS" targetRef="Activity_mtgSubApplState_ZDDS" />
    <bpmn:sequenceFlow id="Flow_0bvsi2a" sourceRef="Activity_mtgApplState_ZDDS" targetRef="Gateway_updateStatusZDDS_join" />
    <bpmn:sequenceFlow id="Flow_0cjj3oe" sourceRef="Activity_mtgSubApplState_ZDDS" targetRef="Gateway_updateStatusZDDS_join" />
    <bpmn:serviceTask id="callApproval_2nd_scoring" name="callMortgageApproval" camunda:type="external" camunda:topic="mtgCallApproval">
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="processPart">2ND_SCORING</camunda:inputParameter>
        </camunda:inputOutput>
        <camunda:properties>
          <camunda:property name="retry_config" value="NO" />
        </camunda:properties>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_1o3stvv</bpmn:incoming>
      <bpmn:outgoing>Flow_1t93t9g</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1o3stvv" sourceRef="Gateway_updateStatusZDDS_join" targetRef="callApproval_2nd_scoring" />
    <bpmn:endEvent id="Event_End">
      <bpmn:incoming>Flow_1t93t9g</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1t93t9g" sourceRef="callApproval_2nd_scoring" targetRef="Event_End" />
    <bpmn:endEvent id="Event_TerminateProcess" name="TerminateProcess">
      <bpmn:incoming>Flow_0a2cg59</bpmn:incoming>
      <bpmn:escalationEventDefinition id="EscalationEventDefinition_175dhp4" escalationRef="Escalation_0iiqp4j" />
    </bpmn:endEvent>
    <bpmn:textAnnotation id="TextAnnotation_0gahox3">
      <bpmn:text>zatím chybí plnění atributu appr_sys_id, který zatím na LAS chybí</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:association id="Association_1msnohd" associationDirection="None" sourceRef="Activity_UpdateSubAppl_SPS" targetRef="TextAnnotation_0gahox3" />
    <bpmn:association id="Association_0n34s2t" associationDirection="None" sourceRef="TextAnnotation_0gahox3" targetRef="Activity_UpdateSubAppl_HYC" />
  </bpmn:process>
  <bpmn:escalation id="Escalation_1502bkr" name="ZDZAM" escalationCode="ZDZAM" />
  <bpmn:message id="Message_1962du0" name="rmlRltySupervisionDone" />
  <bpmn:message id="Message_20s2r3j" name="rmlHycApproval" />
  <bpmn:escalation id="Escalation_2ih94rl" name="EZSO" escalationCode="EZSO" />
  <bpmn:escalation id="Escalation_2fssht6" name="TODO2" escalationCode="TODO2" />
  <bpmn:escalation id="Escalation_0iiqp4j" name="TerminateProcess" escalationCode="TerminateProcess" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="MortgageAppraisal">
      <bpmndi:BPMNShape id="Event_14dx6su_di" bpmnElement="Event_Start_RAS">
        <dc:Bounds x="182" y="102" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="160" y="145" width="84" height="40" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_17wpyly_di" bpmnElement="Gateway_updateStatusSN">
        <dc:Bounds x="265" y="95" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0xx71bw" bpmnElement="Activity_mtgApplState_SN">
        <dc:Bounds x="350" y="80" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0291kcv" bpmnElement="Activity_mtgSubApplState_SN" bioc:stroke="#0d4372" bioc:fill="#bbdefb" color:background-color="#bbdefb" color:border-color="#0d4372">
        <dc:Bounds x="350" y="200" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0w8ptkn" bpmnElement="Gateway_updateStatusSN_join">
        <dc:Bounds x="495" y="95" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_08c0tqh_di" bpmnElement="Gateway_waitMessage">
        <dc:Bounds x="585" y="95" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0uo2vox_di" bpmnElement="Event_rmlRltySupervisionDone">
        <dc:Bounds x="672" y="102" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="650" y="145" width="81" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1hrh5fn_di" bpmnElement="Event_rmlHycApproval">
        <dc:Bounds x="672" y="342" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="651" y="385" width="79" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_05v7y6d_di" bpmnElement="Activity_UpdateSubAppl_SPS" bioc:stroke="#6b3c00" bioc:fill="#ffe0b2" color:background-color="#ffe0b2" color:border-color="#6b3c00">
        <dc:Bounds x="750" y="80" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0nc49qz" bpmnElement="Activity_UpdateSubAppl_HYC" bioc:stroke="#6b3c00" bioc:fill="#ffe0b2" color:background-color="#ffe0b2" color:border-color="#6b3c00">
        <dc:Bounds x="750" y="320" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1y4wteu" bpmnElement="Activity_mtgApplState_ZPRC">
        <dc:Bounds x="1030" y="200" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_05m3osv" bpmnElement="Activity_mtgSubApplState_ZPRC">
        <dc:Bounds x="1030" y="320" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1a1y0fa_di" bpmnElement="Gateway_updateStatusZPRC">
        <dc:Bounds x="945" y="335" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1b9ezuq" bpmnElement="Gateway_updateStatusZPRC_join">
        <dc:Bounds x="1165" y="335" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_12n24bx" bpmnElement="Activity_uploadHycRealties">
        <dc:Bounds x="890" y="80" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_024tyyk" bpmnElement="Activity_getMtgApplicationWithApplicants">
        <dc:Bounds x="1030" y="80" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_12eve69" bpmnElement="Activity_SolveApplicant" color:background-color="#fff" color:border-color="#000">
        <dc:Bounds x="1170" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_04hv949" bpmnElement="Activity_mtgApplState_ZDDS">
        <dc:Bounds x="1400" y="80" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_0eb7lm9" bpmnElement="Activity_mtgSubApplState_ZDDS">
        <dc:Bounds x="1400" y="200" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1r5au6c" bpmnElement="Gateway_updateStatusZDDS">
        <dc:Bounds x="1315" y="95" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1qpvla5" bpmnElement="Gateway_updateStatusZDDS_join">
        <dc:Bounds x="1545" y="95" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1mtmx6j_di" bpmnElement="callApproval_2nd_scoring" color:background-color="#fff" color:border-color="#000">
        <dc:Bounds x="1630" y="80" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_06vds7e_di" bpmnElement="Event_End">
        <dc:Bounds x="1772" y="102" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_0qamkyw_di" bpmnElement="Event_TerminateProcess">
        <dc:Bounds x="1772" y="342" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1746" y="385" width="88" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="TextAnnotation_0gahox3_di" bpmnElement="TextAnnotation_0gahox3">
        <dc:Bounds x="750" y="199" width="99.99156545209178" height="82" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="Flow_18oxlan_di" bpmnElement="Flow_18oxlan">
        <di:waypoint x="218" y="120" />
        <di:waypoint x="265" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1jtdurw_di" bpmnElement="Flow_1jtdurw">
        <di:waypoint x="315" y="120" />
        <di:waypoint x="350" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0igivc5_di" bpmnElement="Flow_0igivc5">
        <di:waypoint x="290" y="145" />
        <di:waypoint x="290" y="240" />
        <di:waypoint x="350" y="240" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0kxj3nm_di" bpmnElement="Flow_0kxj3nm">
        <di:waypoint x="450" y="120" />
        <di:waypoint x="495" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1umqg5s_di" bpmnElement="Flow_1umqg5s">
        <di:waypoint x="450" y="240" />
        <di:waypoint x="520" y="240" />
        <di:waypoint x="520" y="145" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ysb4qp_di" bpmnElement="Flow_0ysb4qp">
        <di:waypoint x="545" y="120" />
        <di:waypoint x="585" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1abrju4_di" bpmnElement="Flow_1abrju4">
        <di:waypoint x="635" y="120" />
        <di:waypoint x="672" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1azq21c_di" bpmnElement="Flow_1azq21c">
        <di:waypoint x="610" y="145" />
        <di:waypoint x="610" y="360" />
        <di:waypoint x="672" y="360" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ks33a1_di" bpmnElement="Flow_0ks33a1">
        <di:waypoint x="708" y="120" />
        <di:waypoint x="750" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0xu9s7w_di" bpmnElement="Flow_0xu9s7w">
        <di:waypoint x="708" y="360" />
        <di:waypoint x="750" y="360" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_16mslvx_di" bpmnElement="Flow_16mslvx">
        <di:waypoint x="850" y="360" />
        <di:waypoint x="945" y="360" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0wtd15v_di" bpmnElement="Flow_0wtd15v">
        <di:waypoint x="995" y="360" />
        <di:waypoint x="1030" y="360" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0vml8ig_di" bpmnElement="Flow_0vml8ig">
        <di:waypoint x="970" y="335" />
        <di:waypoint x="970" y="240" />
        <di:waypoint x="1030" y="240" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0r35hlo_di" bpmnElement="Flow_0r35hlo">
        <di:waypoint x="1130" y="240" />
        <di:waypoint x="1190" y="240" />
        <di:waypoint x="1190" y="335" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1d6vvql_di" bpmnElement="Flow_1d6vvql">
        <di:waypoint x="1130" y="360" />
        <di:waypoint x="1165" y="360" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0a2cg59_di" bpmnElement="Flow_0a2cg59">
        <di:waypoint x="1215" y="360" />
        <di:waypoint x="1772" y="360" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0tyd53k_di" bpmnElement="Flow_0tyd53k">
        <di:waypoint x="850" y="120" />
        <di:waypoint x="890" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1ndultf_di" bpmnElement="Flow_1ndultf">
        <di:waypoint x="990" y="120" />
        <di:waypoint x="1030" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0zjdtly_di" bpmnElement="Flow_0zjdtly">
        <di:waypoint x="1130" y="120" />
        <di:waypoint x="1170" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1sld0xt_di" bpmnElement="Flow_1sld0xt">
        <di:waypoint x="1270" y="120" />
        <di:waypoint x="1315" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0fvtg0b_di" bpmnElement="Flow_0fvtg0b">
        <di:waypoint x="1365" y="120" />
        <di:waypoint x="1400" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1mp2hax_di" bpmnElement="Flow_1mp2hax">
        <di:waypoint x="1340" y="145" />
        <di:waypoint x="1340" y="240" />
        <di:waypoint x="1400" y="240" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0bvsi2a_di" bpmnElement="Flow_0bvsi2a">
        <di:waypoint x="1500" y="120" />
        <di:waypoint x="1545" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0cjj3oe_di" bpmnElement="Flow_0cjj3oe">
        <di:waypoint x="1500" y="240" />
        <di:waypoint x="1570" y="240" />
        <di:waypoint x="1570" y="145" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1o3stvv_di" bpmnElement="Flow_1o3stvv">
        <di:waypoint x="1595" y="120" />
        <di:waypoint x="1630" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1t93t9g_di" bpmnElement="Flow_1t93t9g">
        <di:waypoint x="1730" y="120" />
        <di:waypoint x="1772" y="120" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Association_1msnohd_di" bpmnElement="Association_1msnohd">
        <di:waypoint x="800" y="160" />
        <di:waypoint x="800" y="199" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Association_0n34s2t_di" bpmnElement="Association_0n34s2t">
        <di:waypoint x="800" y="281" />
        <di:waypoint x="800" y="320" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
