<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:modeler="http://camunda.org/schema/modeler/1.0" id="Definitions_1ghwu0b" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.27.0" modeler:executionPlatform="Camunda Platform" modeler:executionPlatformVersion="7.15.0">
  <bpmn:process id="RekoSolveApplicant" name="REKO Solve Applicant" isExecutable="true" camunda:historyTimeToLive="7" camunda:isStartableInTasklist="false">
    <bpmn:documentation>na vstupu je:
- correlationId
- application_id? - adbInstPtKey žadatele (HYPO)
- clientId? - siebelId klienta (CL)
- processTp
- applicationState?</bpmn:documentation>
    <bpmn:startEvent id="Event_Start">
      <bpmn:outgoing>Flow_1bgyism</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:serviceTask id="Activity_setPartyFromAppl" name="set Party From Appl" camunda:type="external" camunda:topic="rekoSetPartyFromAppl">
      <bpmn:documentation>volá CUS_EntParty-Update_1_2

vstup:
- PV partyUpdate</bpmn:documentation>
      <bpmn:extensionElements />
      <bpmn:incoming>SequenceFlow_needEditSblParty</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0cbl7be</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_uploadContactsFromAppl" name="upload Contacts From Appl" camunda:type="external" camunda:topic="rekoUploadContactsFromAppl">
      <bpmn:documentation>volá CUS_Contact-Upload_1_1

vstup:
- PV contactUpload ContactUploadDTO</bpmn:documentation>
      <bpmn:extensionElements />
      <bpmn:incoming>SequenceFlow_needUploadContacts</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1d1bpzz</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="Activity_checkPersonalData" name="check Personal Data" camunda:type="external" camunda:topic="rekoCheckPersonalDataDelegate">
      <bpmn:documentation>//todo now
//        1.poku je na loanApp - zustane
//        2.poku je souhlas beru z SBL (phone bez souhlasu)
//        3. pokud je na Rsts tak beru z toho.
        //synchronizuju LoanApp &gt; SBL
        //synchronizuju rsts &gt; SBL


vstup:
- PV loanApplication BuildingLoanApplicationDTO
- PV loanApplicationOwner BuildingLoanApplicationOwnerDTO
- PV party PartyDTO
- PV partyRsts BuildingPartyRstsDTO

výstup do PV:
- loanApplicationOwner BuildingLoanApplicationOwnerDTO
- needEditAdbParty
- partyUpdate UpdatePartyDto
- needEditSblParty
- contactUpload ContactUploadDTO
- needUploadContacts</bpmn:documentation>
      <bpmn:incoming>Flow_03mngil</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1pg8bwt</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="ExclusiveGateway_needUploadContacts_join">
      <bpmn:incoming>SequenceFlow_1d1bpzz</bpmn:incoming>
      <bpmn:incoming>FLow_DoNotUploadSBLContacts</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_09dzw5x</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="ExclusiveGateway_needUploadContacts" name="needUploadContacts" default="FLow_DoNotUploadSBLContacts">
      <bpmn:incoming>SequenceFlow_0vhycfg</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_needUploadContacts</bpmn:outgoing>
      <bpmn:outgoing>FLow_DoNotUploadSBLContacts</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:parallelGateway id="ExclusiveGateway_0xegfi0">
      <bpmn:incoming>SequenceFlow_09dzw5x</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_1gv2gdl</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_0cr8pj0</bpmn:incoming>
      <bpmn:outgoing>Flow_1wm3638</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:exclusiveGateway id="ExclusiveGateway_needEditAdbParty_join">
      <bpmn:incoming>Flow_Nebude_uprava_ADB_party</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_0v4wwho</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0cr8pj0</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="ExclusiveGateway_needEditSblParty_join">
      <bpmn:incoming>SequenceFlow_0cbl7be</bpmn:incoming>
      <bpmn:incoming>Flow_NebudeUpravaSBLparty</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_1gv2gdl</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="ExclusiveGateway_needEditAdbParty" name="needEditAdbParty" default="Flow_Nebude_uprava_ADB_party">
      <bpmn:incoming>SequenceFlow_0829gpm</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_needEditAdbParty</bpmn:outgoing>
      <bpmn:outgoing>Flow_Nebude_uprava_ADB_party</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:exclusiveGateway id="ExclusiveGateway_needEditSblParty" name="needEditSblParty" default="Flow_NebudeUpravaSBLparty">
      <bpmn:incoming>SequenceFlow_1put6rl</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_needEditSblParty</bpmn:outgoing>
      <bpmn:outgoing>Flow_NebudeUpravaSBLparty</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:parallelGateway id="ExclusiveGateway_1bdm3dz">
      <bpmn:incoming>SequenceFlow_1pg8bwt</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0vhycfg</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_0829gpm</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_1put6rl</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:parallelGateway id="ExclusiveGateway_0vdcw36">
      <bpmn:incoming>Flow_012q9q4</bpmn:incoming>
      <bpmn:incoming>Flow_0b0mr32</bpmn:incoming>
      <bpmn:incoming>Flow_0z4xp7y</bpmn:incoming>
      <bpmn:outgoing>Flow_03mngil</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:sequenceFlow id="SequenceFlow_needEditAdbParty" name="yes" sourceRef="ExclusiveGateway_needEditAdbParty" targetRef="Activity_setBuildingLoanAppl">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${needEditAdbParty}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_needEditSblParty" name="yes" sourceRef="ExclusiveGateway_needEditSblParty" targetRef="Activity_setPartyFromAppl">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${needEditSblParty}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_0cbl7be" sourceRef="Activity_setPartyFromAppl" targetRef="ExclusiveGateway_needEditSblParty_join" />
    <bpmn:sequenceFlow id="SequenceFlow_needUploadContacts" name="yes" sourceRef="ExclusiveGateway_needUploadContacts" targetRef="Activity_uploadContactsFromAppl">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${needUploadContacts}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_1d1bpzz" sourceRef="Activity_uploadContactsFromAppl" targetRef="ExclusiveGateway_needUploadContacts_join" />
    <bpmn:sequenceFlow id="SequenceFlow_1pg8bwt" sourceRef="Activity_checkPersonalData" targetRef="ExclusiveGateway_1bdm3dz" />
    <bpmn:sequenceFlow id="FLow_DoNotUploadSBLContacts" name="no" sourceRef="ExclusiveGateway_needUploadContacts" targetRef="ExclusiveGateway_needUploadContacts_join">
      <bpmn:extensionElements>
        <camunda:executionListener class="cz.equa.camapp.utils.RecorderExecutionListener" event="take" />
      </bpmn:extensionElements>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_09dzw5x" sourceRef="ExclusiveGateway_needUploadContacts_join" targetRef="ExclusiveGateway_0xegfi0" />
    <bpmn:sequenceFlow id="SequenceFlow_0vhycfg" sourceRef="ExclusiveGateway_1bdm3dz" targetRef="ExclusiveGateway_needUploadContacts" />
    <bpmn:sequenceFlow id="SequenceFlow_1gv2gdl" sourceRef="ExclusiveGateway_needEditSblParty_join" targetRef="ExclusiveGateway_0xegfi0" />
    <bpmn:sequenceFlow id="SequenceFlow_0cr8pj0" sourceRef="ExclusiveGateway_needEditAdbParty_join" targetRef="ExclusiveGateway_0xegfi0" />
    <bpmn:sequenceFlow id="Flow_Nebude_uprava_ADB_party" name="no" sourceRef="ExclusiveGateway_needEditAdbParty" targetRef="ExclusiveGateway_needEditAdbParty_join">
      <bpmn:extensionElements>
        <camunda:executionListener class="cz.equa.camapp.utils.RecorderExecutionListener" event="take" />
      </bpmn:extensionElements>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_NebudeUpravaSBLparty" name="no" sourceRef="ExclusiveGateway_needEditSblParty" targetRef="ExclusiveGateway_needEditSblParty_join">
      <bpmn:extensionElements>
        <camunda:executionListener class="cz.equa.camapp.utils.RecorderExecutionListener" event="take" />
      </bpmn:extensionElements>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_0829gpm" sourceRef="ExclusiveGateway_1bdm3dz" targetRef="ExclusiveGateway_needEditAdbParty" />
    <bpmn:sequenceFlow id="SequenceFlow_1put6rl" sourceRef="ExclusiveGateway_1bdm3dz" targetRef="ExclusiveGateway_needEditSblParty" />
    <bpmn:sequenceFlow id="Flow_1bgyism" sourceRef="Event_Start" targetRef="Gateway_19t3qkd" />
    <bpmn:endEvent id="Event_End">
      <bpmn:incoming>Flow_1wm3638</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="Activity_getRstsPartyDetail" name="get RSTS party detail" camunda:type="external" camunda:topic="rekoGetRstsPartyDetail">
      <bpmn:documentation>volá se mdc-custom-v1-getPartyInstanceDetail se source a target system = rsts_cibis

vstup:
- PV rstsClientId
- PV correlationId

výstup do PV:
- partyRsts BuildingPartyRstsDTO</bpmn:documentation>
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_0n2eqlb</bpmn:incoming>
      <bpmn:outgoing>Flow_012q9q4</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_012q9q4" sourceRef="Activity_getRstsPartyDetail" targetRef="ExclusiveGateway_0vdcw36" />
    <bpmn:serviceTask id="Activity_getSBLPartyDetail" name="get SBL Party Detail" camunda:type="external" camunda:topic="rekoGetSBLPartyDetail">
      <bpmn:documentation>volá TIF CUS_EntParty-GetDetail_2_2
načte klienta ze Siebelu do party

vstup:
- PV clientId

výstup:
- party BuildingPartyDTO
- partyId = siebelId</bpmn:documentation>
      <bpmn:extensionElements />
      <bpmn:incoming>Flow_054m4rh</bpmn:incoming>
      <bpmn:outgoing>Flow_0b0mr32</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0n2eqlb" sourceRef="Gateway_19t3qkd" targetRef="Activity_getRstsPartyDetail" />
    <bpmn:parallelGateway id="Gateway_19t3qkd">
      <bpmn:incoming>Flow_1bgyism</bpmn:incoming>
      <bpmn:outgoing>Flow_0n2eqlb</bpmn:outgoing>
      <bpmn:outgoing>Flow_054m4rh</bpmn:outgoing>
      <bpmn:outgoing>Flow_0g4uazx</bpmn:outgoing>
    </bpmn:parallelGateway>
    <bpmn:sequenceFlow id="Flow_054m4rh" sourceRef="Gateway_19t3qkd" targetRef="Activity_getSBLPartyDetail" />
    <bpmn:sequenceFlow id="Flow_0b0mr32" sourceRef="Activity_getSBLPartyDetail" targetRef="ExclusiveGateway_0vdcw36" />
    <bpmn:sequenceFlow id="Flow_03mngil" sourceRef="ExclusiveGateway_0vdcw36" targetRef="Activity_checkPersonalData" />
    <bpmn:serviceTask id="Activity_getBuildingLoanAppl" name="get Building Loan Appl" camunda:type="external" camunda:topic="rekoGetBuildingLoanAppl">
      <bpmn:documentation>-DTO zmena  loanapplication_primary_owner &gt;&gt; List &lt;prérson&gt; + je to jiny nez NewPersonDTO.
- las-application-v1.yaml

- isIncome = 0 - nenačítáme příjmy
- isObligation = 0 - nanačítáme závazky
- isOwner = 1 - načítáme hlavního žadatele
- requestedVariants = NONE - nenačítáme varianty

vstup:
- PV applKey
- PV busApplId
- PV correlationId
- PV clContextId

výstup ukládáme do PV:
- buildingLoanApplication jako BuildingLoanApplicationDTO
- loanApplicationOwner jako BuildingLoanApplicationOwnerDTO</bpmn:documentation>
      <bpmn:extensionElements>
        <camunda:inputOutput>
          <camunda:inputParameter name="isIncome">0</camunda:inputParameter>
          <camunda:inputParameter name="isObligation">0</camunda:inputParameter>
          <camunda:inputParameter name="isOwner">1</camunda:inputParameter>
          <camunda:inputParameter name="requestedVariants">NONE</camunda:inputParameter>
        </camunda:inputOutput>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0g4uazx</bpmn:incoming>
      <bpmn:outgoing>Flow_0z4xp7y</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_0g4uazx" sourceRef="Gateway_19t3qkd" targetRef="Activity_getBuildingLoanAppl" />
    <bpmn:sequenceFlow id="Flow_0z4xp7y" sourceRef="Activity_getBuildingLoanAppl" targetRef="ExclusiveGateway_0vdcw36" />
    <bpmn:sequenceFlow id="Flow_1wm3638" sourceRef="ExclusiveGateway_0xegfi0" targetRef="Event_End" />
    <bpmn:serviceTask id="Activity_setBuildingLoanAppl" name="set Building Loan Appl" camunda:type="external" camunda:topic="rekoSetBuildingLoanAppl">
      <bpmn:documentation>volá las-application-v1-setBuildingLoanAppl

vstup:
- PV buildingLoanApplication BuildingLoanApplicationDTO
- PV loanApplicationOwner BuildingLoanApplicationOwnerDTO
- PV applKey
- PV correlationId
- PV clContextId
</bpmn:documentation>
      <bpmn:extensionElements />
      <bpmn:incoming>SequenceFlow_needEditAdbParty</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0v4wwho</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="SequenceFlow_0v4wwho" sourceRef="Activity_setBuildingLoanAppl" targetRef="ExclusiveGateway_needEditAdbParty_join" />
    <bpmn:textAnnotation id="TextAnnotation_0hh2ce2">
      <bpmn:text>vstup:
busApplId = id žádosti
applKey = id žádosti
clientId = RB SBL partyId
rstsClientId = RSTS CIBIS partyId
X-Correlation-Id = correlationId
X-Cl-Context-Id = tenant</bpmn:text>
    </bpmn:textAnnotation>
    <bpmn:association id="Association_0duejw2" associationDirection="None" sourceRef="Event_Start" targetRef="TextAnnotation_0hh2ce2" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="RekoSolveApplicant">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="Event_Start">
        <dc:Bounds x="192" y="401" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activitiy_setPartyFromAppl_di" bpmnElement="Activity_setPartyFromAppl">
        <dc:Bounds x="950" y="80" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_10m5row_di" bpmnElement="Activity_uploadContactsFromAppl">
        <dc:Bounds x="950" y="279" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_1trnvmu_di" bpmnElement="Activity_checkPersonalData">
        <dc:Bounds x="640" y="379" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_09cxcgz_di" bpmnElement="ExclusiveGateway_needUploadContacts_join" isMarkerVisible="true">
        <dc:Bounds x="1055" y="394" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_0cjshfm_di" bpmnElement="ExclusiveGateway_needUploadContacts" isMarkerVisible="true">
        <dc:Bounds x="895" y="394" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="875" y="446" width="89" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_0xegfi0_di" bpmnElement="ExclusiveGateway_0xegfi0">
        <dc:Bounds x="1155" y="394" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_1pnmaoi_di" bpmnElement="ExclusiveGateway_needEditAdbParty_join" isMarkerVisible="true">
        <dc:Bounds x="1155" y="495" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_1dukr1a_di" bpmnElement="ExclusiveGateway_needEditSblParty_join" isMarkerVisible="true">
        <dc:Bounds x="1155" y="195" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_1d0ppoy_di" bpmnElement="ExclusiveGateway_needEditAdbParty" isMarkerVisible="true">
        <dc:Bounds x="795" y="495" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="698" y="513" width="90" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_0j1nvzc_di" bpmnElement="ExclusiveGateway_needEditSblParty" isMarkerVisible="true">
        <dc:Bounds x="795" y="195" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="700" y="213" width="86" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_1bdm3dz_di" bpmnElement="ExclusiveGateway_1bdm3dz">
        <dc:Bounds x="795" y="394" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_0vdcw36_di" bpmnElement="ExclusiveGateway_0vdcw36">
        <dc:Bounds x="545" y="394" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_1xc6nn1_di" bpmnElement="Event_End">
        <dc:Bounds x="1262" y="401" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1860" y="624" width="20" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1dl1al4" bpmnElement="Activity_getRstsPartyDetail">
        <dc:Bounds x="390" y="379" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1lysito" bpmnElement="Activity_getSBLPartyDetail">
        <dc:Bounds x="390" y="480" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1fgvbqc_di" bpmnElement="Gateway_19t3qkd">
        <dc:Bounds x="285" y="394" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="BPMNShape_1qfp0ws" bpmnElement="Activity_getBuildingLoanAppl">
        <dc:Bounds x="390" y="279" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Task_11hk17c_di" bpmnElement="Activity_setBuildingLoanAppl">
        <dc:Bounds x="950" y="580" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="TextAnnotation_0hh2ce2_di" bpmnElement="TextAnnotation_0hh2ce2">
        <dc:Bounds x="160" y="564" width="200" height="111" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0f8z964_di" bpmnElement="SequenceFlow_needEditAdbParty">
        <di:waypoint x="820" y="545" />
        <di:waypoint x="820" y="620" />
        <di:waypoint x="950" y="620" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="791" y="552" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0hq0ly8_di" bpmnElement="SequenceFlow_needEditSblParty">
        <di:waypoint x="820" y="195" />
        <di:waypoint x="820" y="120" />
        <di:waypoint x="950" y="120" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="791" y="163" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0cbl7be_di" bpmnElement="SequenceFlow_0cbl7be">
        <di:waypoint x="1050" y="122" />
        <di:waypoint x="1180" y="122" />
        <di:waypoint x="1180" y="195" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1hvz1f7_di" bpmnElement="SequenceFlow_needUploadContacts">
        <di:waypoint x="920" y="394" />
        <di:waypoint x="920" y="319" />
        <di:waypoint x="950" y="319" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="892" y="373" width="17" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1d1bpzz_di" bpmnElement="SequenceFlow_1d1bpzz">
        <di:waypoint x="1050" y="319" />
        <di:waypoint x="1080" y="319" />
        <di:waypoint x="1080" y="394" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1pg8bwt_di" bpmnElement="SequenceFlow_1pg8bwt">
        <di:waypoint x="740" y="419" />
        <di:waypoint x="795" y="419" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="FLow_DoNotUploadSBLContacts_di" bpmnElement="FLow_DoNotUploadSBLContacts">
        <di:waypoint x="945" y="419" />
        <di:waypoint x="1055" y="419" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="973" y="426" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_09dzw5x_di" bpmnElement="SequenceFlow_09dzw5x">
        <di:waypoint x="1105" y="419" />
        <di:waypoint x="1155" y="419" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0vhycfg_di" bpmnElement="SequenceFlow_0vhycfg">
        <di:waypoint x="845" y="419" />
        <di:waypoint x="895" y="419" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1gv2gdl_di" bpmnElement="SequenceFlow_1gv2gdl">
        <di:waypoint x="1180" y="245" />
        <di:waypoint x="1180" y="394" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0cr8pj0_di" bpmnElement="SequenceFlow_0cr8pj0">
        <di:waypoint x="1180" y="495" />
        <di:waypoint x="1180" y="444" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_Nebude_uprava_ADB_party_di" bpmnElement="Flow_Nebude_uprava_ADB_party">
        <di:waypoint x="845" y="520" />
        <di:waypoint x="1155" y="520" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="864" y="493" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_NebudeUpravaSBLparty_di" bpmnElement="Flow_NebudeUpravaSBLparty">
        <di:waypoint x="845" y="220" />
        <di:waypoint x="1155" y="220" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="864" y="233" width="13" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0829gpm_di" bpmnElement="SequenceFlow_0829gpm">
        <di:waypoint x="820" y="444" />
        <di:waypoint x="820" y="495" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1put6rl_di" bpmnElement="SequenceFlow_1put6rl">
        <di:waypoint x="820" y="394" />
        <di:waypoint x="820" y="245" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1bgyism_di" bpmnElement="Flow_1bgyism">
        <di:waypoint x="228" y="419" />
        <di:waypoint x="285" y="419" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_012q9q4_di" bpmnElement="Flow_012q9q4">
        <di:waypoint x="490" y="419" />
        <di:waypoint x="545" y="419" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0n2eqlb_di" bpmnElement="Flow_0n2eqlb">
        <di:waypoint x="335" y="419" />
        <di:waypoint x="390" y="419" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_054m4rh_di" bpmnElement="Flow_054m4rh">
        <di:waypoint x="310" y="444" />
        <di:waypoint x="310" y="520" />
        <di:waypoint x="390" y="520" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0b0mr32_di" bpmnElement="Flow_0b0mr32">
        <di:waypoint x="490" y="520" />
        <di:waypoint x="570" y="520" />
        <di:waypoint x="570" y="444" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_03mngil_di" bpmnElement="Flow_03mngil">
        <di:waypoint x="595" y="419" />
        <di:waypoint x="640" y="419" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0g4uazx_di" bpmnElement="Flow_0g4uazx">
        <di:waypoint x="310" y="394" />
        <di:waypoint x="310" y="319" />
        <di:waypoint x="390" y="319" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0z4xp7y_di" bpmnElement="Flow_0z4xp7y">
        <di:waypoint x="490" y="319" />
        <di:waypoint x="570" y="319" />
        <di:waypoint x="570" y="394" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1wm3638_di" bpmnElement="Flow_1wm3638">
        <di:waypoint x="1205" y="419" />
        <di:waypoint x="1262" y="419" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0v4wwho_di" bpmnElement="SequenceFlow_0v4wwho">
        <di:waypoint x="1050" y="620" />
        <di:waypoint x="1180" y="620" />
        <di:waypoint x="1180" y="545" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Association_0duejw2_di" bpmnElement="Association_0duejw2">
        <di:waypoint x="210" y="437" />
        <di:waypoint x="210" y="564" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>