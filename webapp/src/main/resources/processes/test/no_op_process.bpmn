<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" id="Definitions_0o3btg8" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.22.0">
  <bpmn:process id="NO_OP_PROCESS" name="NO_OP_PROCESS" isExecutable="true" camunda:historyTimeToLive="0">
    <bpmn:startEvent id="StartEvent_1">
      <bpmn:outgoing>SequenceFlow_0uefl3y</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:endEvent id="EndEvent_05y96cl">
      <bpmn:incoming>SequenceFlow_0uefl3y</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="SequenceFlow_0uefl3y" sourceRef="StartEvent_1" targetRef="EndEvent_05y96cl" />
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="NO_OP_PROCESS">
      <bpmndi:BPMNShape id="_BPMNShape_StartEvent_2" bpmnElement="StartEvent_1">
        <dc:Bounds x="179" y="79" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_05y96cl_di" bpmnElement="EndEvent_05y96cl">
        <dc:Bounds x="272" y="79" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0uefl3y_di" bpmnElement="SequenceFlow_0uefl3y">
        <di:waypoint x="215" y="97" />
        <di:waypoint x="272" y="97" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
