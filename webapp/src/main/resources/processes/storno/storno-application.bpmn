<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" id="Definitions_0ia8yfg" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.34.0">
  <bpmn:process id="Storno-application" isExecutable="true" camunda:historyTimeToLive="32">
    <bpmn:startEvent id="StartEvent_subprocess">
      <bpmn:outgoing>SequenceFlow_0hkn8rj</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:endEvent id="EndEvent_subprocess" name="End">
      <bpmn:extensionElements>
        <camunda:executionListener class="cz.equa.camapp.utils.RecorderExecutionListener" event="end" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0uhxrd0</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:serviceTask id="stornoApplication" name="Storno Application" camunda:delegateExpression="${notifyProcessDelegate}">
      <bpmn:extensionElements>
        <camunda:executionListener class="cz.equa.camapp.utils.RecorderExecutionListener" event="end" />
        <camunda:field name="notifyMessage">
          <camunda:string>stornoReceivedMessage</camunda:string>
        </camunda:field>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_processExists</bpmn:incoming>
      <bpmn:outgoing>Flow_1bx8ioi</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_doesProcessInstanceExist_join">
      <bpmn:incoming>Flow_1bx8ioi</bpmn:incoming>
      <bpmn:incoming>Flow_0rnnzrl</bpmn:incoming>
      <bpmn:outgoing>Flow_0uhxrd0</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="Check_Camunda_process_existence" name="Check Camunda process existence" camunda:delegateExpression="${checkExistenceOfCamundaProcessDelegate}">
      <bpmn:incoming>Flow_1bnnic6</bpmn:incoming>
      <bpmn:outgoing>Flow_0r17nlh</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="Gateway_doesProcessInstanceExist" name="Does Camunda process exist">
      <bpmn:incoming>Flow_0r17nlh</bpmn:incoming>
      <bpmn:outgoing>Flow_processExists</bpmn:outgoing>
      <bpmn:outgoing>Flow_processDoesNotExist</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="set_STR" name="set STR" camunda:delegateExpression="${updateApplicationDelegate}">
      <bpmn:extensionElements>
        <camunda:field name="nextApplicationState">
          <camunda:string>STR</camunda:string>
        </camunda:field>
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_processDoesNotExist</bpmn:incoming>
      <bpmn:outgoing>Flow_1a4eqvu</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="notifyMerchant" name="Notify Merchant" camunda:delegateExpression="${notifyMerchantForapplicationDelegate}">
      <bpmn:extensionElements>
        <camunda:executionListener class="cz.equa.camapp.utils.RecorderExecutionListener" event="end" />
      </bpmn:extensionElements>
      <bpmn:incoming>Flow_0u1h3tf</bpmn:incoming>
      <bpmn:outgoing>Flow_0rnnzrl</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:serviceTask id="extract_application_identifiers" name="extract application identifiers" camunda:delegateExpression="${extractApplIdentifiersDelegate}">
      <bpmn:incoming>SequenceFlow_0hkn8rj</bpmn:incoming>
      <bpmn:outgoing>Flow_1bnnic6</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="SequenceFlow_0hkn8rj" sourceRef="StartEvent_subprocess" targetRef="extract_application_identifiers" />
    <bpmn:sequenceFlow id="Flow_0uhxrd0" sourceRef="Gateway_doesProcessInstanceExist_join" targetRef="EndEvent_subprocess" />
    <bpmn:sequenceFlow id="Flow_processExists" name="Yes" sourceRef="Gateway_doesProcessInstanceExist" targetRef="stornoApplication">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${stornoApplicationCamProcessExists}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1bx8ioi" sourceRef="stornoApplication" targetRef="Gateway_doesProcessInstanceExist_join" />
    <bpmn:sequenceFlow id="Flow_0rnnzrl" sourceRef="notifyMerchant" targetRef="Gateway_doesProcessInstanceExist_join" />
    <bpmn:sequenceFlow id="Flow_1bnnic6" sourceRef="extract_application_identifiers" targetRef="Check_Camunda_process_existence" />
    <bpmn:sequenceFlow id="Flow_0r17nlh" sourceRef="Check_Camunda_process_existence" targetRef="Gateway_doesProcessInstanceExist" />
    <bpmn:sequenceFlow id="Flow_processDoesNotExist" name="No" sourceRef="Gateway_doesProcessInstanceExist" targetRef="set_STR">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${!stornoApplicationCamProcessExists}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="Flow_1a4eqvu" sourceRef="set_STR" targetRef="Gateway_isBSL" />
    <bpmn:serviceTask id="Activity_cibis_storno" name="CIBIS - kafka storno" camunda:type="external" camunda:topic="rekoSendKafkaCibisStorno">
      <bpmn:incoming>Flow_BSL</bpmn:incoming>
      <bpmn:outgoing>Flow_1gclfka</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="Flow_1gclfka" sourceRef="Activity_cibis_storno" targetRef="Gateway_isBSL_join" />
    <bpmn:exclusiveGateway id="Gateway_isBSL" default="Flow_0ws0pz0">
      <bpmn:incoming>Flow_1a4eqvu</bpmn:incoming>
      <bpmn:outgoing>Flow_BSL</bpmn:outgoing>
      <bpmn:outgoing>Flow_0ws0pz0</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_BSL" name="BSL_REA || BSL_DRA" sourceRef="Gateway_isBSL" targetRef="Activity_cibis_storno">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${applicationElement.busProdSubTp=="BSL_REA" || applicationElement.busProdSubTp=="BSL_DRA"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:exclusiveGateway id="Gateway_isBSL_join">
      <bpmn:incoming>Flow_1gclfka</bpmn:incoming>
      <bpmn:incoming>Flow_0ws0pz0</bpmn:incoming>
      <bpmn:outgoing>Flow_0u1h3tf</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="Flow_0u1h3tf" sourceRef="Gateway_isBSL_join" targetRef="notifyMerchant" />
    <bpmn:sequenceFlow id="Flow_0ws0pz0" sourceRef="Gateway_isBSL" targetRef="Gateway_isBSL_join" />
  </bpmn:process>
  <bpmn:message id="Message_0r26zl9" name="stornoReceivedMessage" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Storno-application">
      <bpmndi:BPMNShape id="Activity_0kqifrg_di" bpmnElement="stornoApplication">
        <dc:Bounds x="600" y="80" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_082iwd5_di" bpmnElement="Gateway_doesProcessInstanceExist" isMarkerVisible="true">
        <dc:Bounds x="515" y="175" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="583" y="190" width="77" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1nxpkil_di" bpmnElement="Check_Camunda_process_existence">
        <dc:Bounds x="370" y="160" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0zrnbgh_di" bpmnElement="extract_application_identifiers">
        <dc:Bounds x="230" y="160" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="StartEvent_1tgg4oo_di" bpmnElement="StartEvent_subprocess">
        <dc:Bounds x="152" y="182" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_14n8uji_di" bpmnElement="set_STR">
        <dc:Bounds x="600" y="240" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_1m8fejw_di" bpmnElement="Gateway_isBSL" isMarkerVisible="true">
        <dc:Bounds x="735" y="255" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_0hupunr_di" bpmnElement="Gateway_doesProcessInstanceExist_join" isMarkerVisible="true">
        <dc:Bounds x="1165" y="175" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_0vn1gyh_di" bpmnElement="EndEvent_subprocess">
        <dc:Bounds x="1252" y="182" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1261" y="225" width="20" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Gateway_01zutc9_di" bpmnElement="Gateway_isBSL_join" isMarkerVisible="true">
        <dc:Bounds x="940" y="255" width="50" height="50" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_1xk28q5_di" bpmnElement="notifyMerchant">
        <dc:Bounds x="1030" y="240" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Activity_0mtsvqu_di" bpmnElement="Activity_cibis_storno">
        <dc:Bounds x="810" y="320" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0hkn8rj_di" bpmnElement="SequenceFlow_0hkn8rj">
        <di:waypoint x="188" y="200" />
        <di:waypoint x="230" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0uhxrd0_di" bpmnElement="Flow_0uhxrd0">
        <di:waypoint x="1215" y="200" />
        <di:waypoint x="1252" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_03todyt_di" bpmnElement="Flow_processExists">
        <di:waypoint x="540" y="175" />
        <di:waypoint x="540" y="120" />
        <di:waypoint x="600" y="120" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="547" y="145" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1bx8ioi_di" bpmnElement="Flow_1bx8ioi">
        <di:waypoint x="700" y="120" />
        <di:waypoint x="1190" y="120" />
        <di:waypoint x="1190" y="175" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0rnnzrl_di" bpmnElement="Flow_0rnnzrl">
        <di:waypoint x="1130" y="280" />
        <di:waypoint x="1190" y="280" />
        <di:waypoint x="1190" y="225" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1bnnic6_di" bpmnElement="Flow_1bnnic6">
        <di:waypoint x="330" y="200" />
        <di:waypoint x="370" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0r17nlh_di" bpmnElement="Flow_0r17nlh">
        <di:waypoint x="470" y="200" />
        <di:waypoint x="515" y="200" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_14u4zu6_di" bpmnElement="Flow_processDoesNotExist">
        <di:waypoint x="540" y="225" />
        <di:waypoint x="540" y="280" />
        <di:waypoint x="600" y="280" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="549" y="249" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1a4eqvu_di" bpmnElement="Flow_1a4eqvu">
        <di:waypoint x="700" y="280" />
        <di:waypoint x="735" y="280" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1gclfka_di" bpmnElement="Flow_1gclfka">
        <di:waypoint x="910" y="360" />
        <di:waypoint x="965" y="360" />
        <di:waypoint x="965" y="305" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0t34tfo_di" bpmnElement="Flow_BSL">
        <di:waypoint x="760" y="305" />
        <di:waypoint x="760" y="360" />
        <di:waypoint x="810" y="360" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="731" y="373" width="58" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0u1h3tf_di" bpmnElement="Flow_0u1h3tf">
        <di:waypoint x="990" y="280" />
        <di:waypoint x="1030" y="280" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_0ws0pz0_di" bpmnElement="Flow_0ws0pz0">
        <di:waypoint x="785" y="280" />
        <di:waypoint x="940" y="280" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
