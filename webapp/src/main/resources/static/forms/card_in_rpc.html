<form role="form"
      id="variablesForm"
      name="variablesForm">

    <div class="row">
        <div class="col-xs-6">
            <div class="form-group">
                <label for="cardInRpc">Potvrz<PERSON>ji, že karta je založena v RPC</label>
                <input type="checkbox"
                       id="cardInRpc"
                       name="cardInRpc"
                       ng-model="cardInRpc"
                       class="form-control"/>
            </div>
            <p ng-if="cardInRpc" class="alert alert-danger">
                Karta je v RPC již vytvořená. Vyplnil/a jsem níže uvedené údaje.
            </p>
            <p ng-if="!cardInRpc" class="alert alert-danger">
                Karta v RPC není. Vytvořit kartu. Níže uvedené údaje budou přepsány.
            </p>

            <div class="form-group">
                <label for="accountNumber">Account number</label>
                <input id="accountNumber"
                       name="accountNumber"
                       class="form-control"
                       ng-model="newCard.accountNumber"/>
                <p ng-if="variablesForm.cardInRpc.$invalid" style="color: red">
                    10 znaků
                </p>
            </div>

            <div class="form-group">
                <label for="pan">PAN</label>
                <input type="text"
                       id="pan"
                       name="pan"
                       ng-model="newCard.pan"
                       class="form-control"/>
            </div>

            <div class="input-group">
                <label for="expirationDate">Expiration date</label>
                <input id="expirationDate"
                       name="expirationDate"
                       type="text"
                       ng-model="newCard.expirationDate"
                       class="form-control"/>
            </div>

            <div class="form-group">
                <label for="correlativeField">Correlative</label>
                <input id="correlativeField"
                       name="correlative"
                       ng-model="newCard.correlative"
                       class="form-control"/>
            </div>

            <p ng-if="!validData" class="alert alert-danger">
                Pokud je karta v RPC, je nutné zadat všechny údaje o ní.
            </p>

            <script cam-script type="text/form-script">
                debugger;
                camForm.on('form-loaded', function() {
                    camForm.variableManager.fetchVariable('newCard');
                });
                camForm.on('variables-fetched', function() {
                    $scope.newCard = $scope.getNewCardObject(camForm.variableManager.variableValue('newCard'));
                });
                camForm.on('variables-applied', function() {
                    //$scope.validate();
                });
                camForm.on('store', function() {
                    $scope.validate();
                    if ($scope.validData) {
                        camForm.variableManager.variable('newCard').value = $scope.getNewCardXml();
                    }
                });
                camForm.on('submit', function(e) {
                    $scope.validate();
                    if ($scope.validData) {
                        if (!$scope.variablesForm.cardInRpc.$viewValue) {
                            $scope.variablesForm.accountNumber.$modelValue = null;
                        }
                        camForm.variableManager.variable('newCard').value = $scope.getNewCardXml();
                    } else {
                        e.submitPrevented = true;
                    }
                });
                $scope.validate = function() {
                    var form = $scope.variablesForm;
                    console.log(form.cardInRpc.$viewValue);
                    if (form.cardInRpc.$viewValue) {
                        if (form.accountNumber.$modelValue == null || form.accountNumber.$modelValue == "") {
                            $scope.validData = undefined;
                            return;
                        }
                        if (form.pan.$modelValue == null || form.pan.$modelValue == "") {
                            $scope.validData = undefined;
                            return;
                        }
                        if (form.expirationDate.$modelValue == null || form.expirationDate.$modelValue == "") {
                            $scope.validData = undefined;
                            return;
                        }
                        if (form.correlative.$modelValue == null || form.correlative.$modelValue == "") {
                            $scope.validData = undefined;
                            return;
                        }
                        if (!form.accountNumber.$valid || !form.pan.$valid || !form.expirationDate.$valid || !form.correlative.$valid) {
                            $scope.validData = undefined;
                            return;
                        }
                    }
                    $scope.validData = true;
                }
                $scope.getNewCardXml = function() {
                    var form = $scope.variablesForm;
                    var xml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><cardDto><accountNumber>" + form.accountNumber.$modelValue + "</accountNumber><pan>" + form.pan.$modelValue + "</pan><expirationDate>" + form.expirationDate.$modelValue + "</expirationDate><correlative>" + form.correlative.$modelValue + "</correlative></cardDto>";
                    return xml;
                }
                $scope.getNewCardObject = function(xml) {
                    const parser = new DOMParser();
                    const xmlDoc = parser.parseFromString(xml, "text/xml");
                    const card = { accountNumber: xmlDoc?.getElementsByTagName("accountNumber")?.[0]?.childNodes?.[0]?.nodeValue || "",
                     pan: xmlDoc?.getElementsByTagName("pan")?.[0]?.childNodes?.[0]?.nodeValue || "",
                     expirationDate: xmlDoc?.getElementsByTagName("expirationDate")?.[0]?.childNodes?.[0]?.nodeValue || "",
                     correlative: xmlDoc?.getElementsByTagName("correlative")?.[0]?.childNodes?.[0]?.nodeValue || ""};
                    return card;
                }
            </script>
        </div>
    </div>
</form>