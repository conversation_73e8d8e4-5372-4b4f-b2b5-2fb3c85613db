<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="DEBUG" monitorInterval="30">
    <Appenders>
        <Console name="ConsoleAppender" target="SYSTEM_OUT" follow="true">
            <PatternLayout pattern="%highlight{%d{ISO8601} [%t] [C:%X{correlationId}, P:%X{processId}, U:%X{username}] %-5p %C - %m%n}"/>
        </Console>
    </Appenders>
    <Loggers>
        <Logger name="cz.equa.camapp" level="debug" additivity="false">
            <AppenderRef ref="ConsoleAppender"/>
        </Logger>
        
        <Logger name="cz.rb" level="debug" additivity="false">
            <AppenderRef ref="ConsoleAppender"/>
        </Logger>
        
        <!-- Enable this to see sql queries and other stuff -->
        <Logger name="org.camunda.bpm.client" level="debug" additivity="false">
            <AppenderRef ref="ConsoleAppender"/>
        </Logger>
        
        <!-- Logger name="cz.equa.camunda" level="debug" additivity="false">
            <AppenderRef ref="ConsoleAppender"/>
        </Logger-->
        
        <!-- Next two loggers prints task related queries. This could be commented out in the future. -->
        <Logger name="historicTask" level="debug" additivity="false">
            <AppenderRef ref="ConsoleAppender"/>
        </Logger>
        
        <Logger name="customTask" level="debug" additivity="false">
            <AppenderRef ref="ConsoleAppender"/>
        </Logger>
        
        <Root level="info" additivity="false">
            <AppenderRef ref="ConsoleAppender"/>
        </Root>
    </Loggers>
</Configuration>