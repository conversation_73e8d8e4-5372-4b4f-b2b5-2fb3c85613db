server.port: 9001
management:
  endpoints:
    web.exposure.include: health, info, prometheus, scheduledtasks
  endpoint:
    health:
      group.custom.show-components: always
      show-details: always
    prometheus:
      enabled: true
      cache.time-to-live: 10s
  health:
    mail.enabled: false
    livenessState.enabled: true
    readinessState.enabled: true
  security.enabled: false
  server:
    port: 8888
  info:
    java.enabled: true
    git:
      enabled: true
      mode: full
    build.enabled: true
    env.enabled: true
    os.enabled: true
# SFTP configuration
app:
  services:
    sftp:
      host: localhost
      port: 2222
      user: foo
      password: pass
      remote-directory: /upload
      path-prefix: /upload
      allow-unknown-keys: true
      enabled: true
      session-timeout: 30
##SMTP settings
smtp_from: <EMAIL>
bo_smtp_to: <EMAIL>
smtp_to_ho_card_maintenance: <EMAIL>
spring:
  application.name: camundaApplication
  jackson.date-format: yyyy-MM-dd'T'HH:mm:ss.SSSZ
  datasource:
    jdbcBatchProcessing: false
    url: jdbc:h2:mem:example-simple;MODE=Oracle;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
  h2:
    console:
      enabled: true
  jersey:
    application-path: /rest
    type: filter
  mail:
    host: czcrbp10.rb.cz
    port: 25
    properties.mail.smtp.auth: false
    properties.mail.smtp.starttls.enable: true
  freemarker.template-loader-path=classpath: /templates/
  freemarker.suffix: .ftl

camunda:
  bpm:
    database:
      type: h2
      jdbcBatchProcessing: false
    application:
      delete-upon-undeploy: false
      scan-for-process-definitions: false
      deploy-changed-only: true
      resume-previous-versions: true
      resume-previous-by: a value
    job-execution:
      enabled: true
      failed-job-retry-time-cycle: R1/PT5M
      core-pool-size: 20
      max-pool-size: 40
      queue-capacity: 10
    metrics:
      enabled: true
      db-reporter-activate: true
    admin-user:
      id: demo
      password: demo
    history-level: FULL
    #history-level: AUDIT
    default-serialization-format: application/json
    authorization:
      enabled: true
    webapp:
        application-path: /
    client:
      base-url: http://localhost:9001/rest/engine/default # the URL pointing to the Camunda Platform Runtime REST API
      lock-duration: 10000 # defines how many milliseconds the External Tasks are locked until they can be fetched again
      worker-id: ext-worker # Identifies the worker towards the Engine
      date-format: yyyy-MM-dd'T'HH:mm:ss.SSSZ
      subscriptions.worker-topic.include-extension-properties: true
      max-tasks: 10 # the maximum number of External Tasks to fetch in one request

storno.process.blocked: true
expiration.process.blocked: true
tif:
  configuration.path: ../tif/src/main/resources/ifconfig.xml
  client.username: CmnToBpm_pres
las.cz.rb.las.application.handler.url: http://localhost:9090/rbapl/las
las.cz.rb.las.sms.handler.url: http://localhost:9090/rbapl/las
las.cz.rb.las.operation.handler.url: http://localhost:9090/rbapl/las
las.cz.rb.las.approval.handler.url: http://localhost:9090/rbapl/las
las.cz.rb.las.parametrization.handler.url: http://localhost:9090/rbapl/las
las.cz.rb.had.partners.handler.url: http://localhost:9090/rbapl/had
las.cz.rb.rpsn.handler.url: http://localhost:9090/rbapl/LAS/rpsn/v1
las.cz.rb.cus.party.application.handler.url: http://localhost:9090/rbepi
las.cz.rb.cus.party.handler.url: http://localhost:9090/rbepi
las.cz.rb.cus.application.handler.url: http://localhost:9090/rbepi
las.cz.rb.cus.activity.handler.url: http://localhost:9090/rbepi
las.cz.rb.had.interestchange.handler.url: http://localhost:9090/rbapl/had
las.cz.rb.bai.loan.approval.url: http://localhost:9090/rbapl/had
las.cz.rb.bai.loan.approval.handler.url: http://localhost:9090/rbapl
las.cz.rb.ifr.card.processing.handler.url: http://localhost:9090/rbapl
las.cz.rb.ifr.message.handler.url: http://localhost:9090/rbapl
las.cz.rb.dms.document.handler.url: http://localhost:9090/rbepi
las.cz.rb.dms.document.v2.handler.url: http://localhost:9090/rbepi
ocr.cz.rb.v2.handler.url: http://localhost:9090/ocr
las.cz.rb.bai.account.handler.url: http://localhost:9090/rbapl
las.cz.rb.mut.external.handler.url: http://localhost:9090/rbapl
las.cz.rb.mdc.custom.handler.url: http://localhost:9090/rbapl
las.cz.rb.bai.currentaccount.handler.url: http://localhost:9090/rbapl
las.cz.rb.tif.ims.handler.url: http://localhost:9090/rbapl

defaultTimeZone: Europe/Prague

kong:
  endpoint:
    configuration:
      default:
        username: rb
        password: RB-EQUA2021
        connectionTimeout: 60000
        readTimeout: 60000
        connections: 50
      "[api-rb.cz/test]":
        username: user1
        password: pass1

healthCheck.maxWait: 5000
openid.endpoint.url: https://sso-web.sit1.equabank.loc/sso
magicLink.url: https://ucet.sit1.equabank.cz/podpis?accessKey=
refi.zip.pswd: "{DEFAULT:\"Default123pswd\",AIRBANK:\"AIRBANKpswd\",MBANK:\"MBANKpswd\",FIOBANK:\"FIOpswd\"}"
refi.file.location: "camunda/********"
reko.generate.document.rea.finalization.url: "https://www.rb.cz/test"
reko.generate.document.dra.finalization.url: "https://www.rb.cz/test"


springdoc:
  api-docs:
    path: /openapi
  swagger-ui:
    path: /docs

environment.id: SIT1
number.of.retries.to.get.gfo.id: 3
http:
  proxy:
    hostname: mwg.rb.cz
    port: 8079
    user-agent: CMN

ldap:
  url: ldaps://pdc-comm-1.comm.equabank.loc:636
  accept.untrusted.certificates: true
  allow.anonymous.login: true
  use.ssl: true
  security.authentication: simple
  base.dn:
  administrator:
    group: app_CAMUNDA_Support
  manager:
    dn: <EMAIL>
    password: Banka2014*
  user:
    search:
      base: OU=Accounts,DC=COMM,DC=equabank,DC=loc
      filter: (objectclass=person)
    attribute:
      id: sAMAccountName
      firstname: givenName
      lastname: sn
      email: userPrincipalName
  group:
    search:
      base: OU=AppGroups,OU=ServiceGroups,DC=COMM,DC=equabank,DC=loc
      filter: (objectclass=group)
    attribute:
      id: sAMAccountName
      name: name
      member: member

kafka:
    enabled: false
    broker.address: kafapl1ad1.rb.cz:9092,kafapl1ad2.rb.cz:9092
    ssl.enabled: false
    truststore.location: 
    truststore.password: 
    keystore.location: 
    keystore.password: 
    key.password: 
    schema.registry.url: none
    request.timout.ms: 30000
    retry.backoff.ms: 100
    reconnect:
        backoff:
            max.ms: 1000
            ms: 50

sec.cors.allowed-origins[0]: http://localhost:3000
sec.cors.allowed-origins[1]: http://camsupport.dev1.equabank.loc
sec.cors.allowed-methods[0]: GET
sec.cors.allowed-methods[1]: PUT
sec.cors.allowed-methods[2]: POST
sec.cors.allowed-methods[3]: DELETE
sec.cors.allowed-methods[4]: OPTIONS
sec.cors.allowed-headers[0]: "*"
sec.cors.allow-credentials: false
sec.cors.max-age: 3600

server.servlet.session.timeout: 3600

testing.mock.eshop.ftp: ********
camsupport.url: http://localhost:3000
loan:
  hypo.pubweb.url: https://pujcka.rb.cz
  approval.finalization.url: https://pujcka.rb.cz
  generate.email.X0000005.attached.document.id: 091g94ch8075fa4b
  document.download.file.path: test
  document.download.connect.timeout: 9600
  document.download.read.timeout: 9600

jobRetryPolicy: timeout,offline,SSLException,Connection pool shut down,TALEND file in use,NO INCIDENT

#  basic-auth:
#    username: demo
#    password: demo
worker.default:
  retries: 5
  retryPeriod: 100000

vip_party_ids: "********,********,1770253,634385,********"

backoffstrategy:
  initTime: 500
  factor: 2
  maxTime: 1000
