<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
                  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
                  xmlns:tns="http://camunda.equa.cz/ac"
                  targetNamespace="http://camunda.equa.cz/ac">
    <wsdl:types>
        <schema xmlns="http://www.w3.org/2001/XMLSchema">
            <import namespace="http://camunda.equa.cz/ac" schemaLocation="CloseAccountServiceSchema.xsd"/>
        </schema>
    </wsdl:types>
    <wsdl:message name="CommonServiceResponse">
        <wsdl:part element="tns:CommonServiceResponse" name="CommonServiceResponse"/>
    </wsdl:message>
    <wsdl:message name="StartProcessRequest">
        <wsdl:part element="tns:StartProcessRequest" name="StartProcessRequest"/>
    </wsdl:message>

    <wsdl:portType name="CloseAccountPort">
        <wsdl:operation name="StartProcess">
            <wsdl:input message="tns:StartProcessRequest" name="StartProcessRequest"/>
            <wsdl:output message="tns:CommonServiceResponse" name="CommonServiceResponse"/>
        </wsdl:operation>
    </wsdl:portType>
    <wsdl:binding name="CloseAccountPortSoap11" type="tns:CloseAccountPort">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <wsdl:operation name="StartProcess">
            <soap:operation soapAction=""/>
            <wsdl:input name="StartProcessRequest">
                <soap:body use="literal"/>
            </wsdl:input>
            <wsdl:output name="CommonServiceResponse">
                <soap:body use="literal"/>
            </wsdl:output>
        </wsdl:operation>
    </wsdl:binding>
    <wsdl:service name="CloseAccountPortService">
        <wsdl:port binding="tns:CloseAccountPortSoap11" name="CloseAccountPortSoap11">
            <soap:address location="http://localhost:9001/ac/ws"/>
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>