<?xml version="1.0" encoding="UTF-8"?>
<schema xmlns="http://www.w3.org/2001/XMLSchema"
		xmlns:tns="http://camunda.equa.cz/task"
		xmlns:lovs="http://www.equabank.cz/lovs"
		targetNamespace="http://camunda.equa.cz/task"
		elementFormDefault="qualified">
	<import namespace="http://www.equabank.cz/lovs" schemaLocation="LovPart.xsd"/>

	<complexType name="TaskRequestBaseType">
		<sequence>
			<element name="impersonatedUser" type="string"/>
		</sequence>
	</complexType>

	<complexType name="Task">
		<sequence>
			<!--            Camunda attributes-->
			<element minOccurs="0" name="assignee" type="string"/>
			<element minOccurs="0" name="caseInstanceId" type="string"/>
			<element minOccurs="0" name="delegationState" type="string"/>
			<element minOccurs="0" name="description" type="string"/>
			<element minOccurs="0" name="due" type="dateTime"/>
			<element minOccurs="0" name="followUp" type="dateTime"/>
			<element minOccurs="0" name="id" type="string"/>
			<element minOccurs="0" name="name" type="string"/>
      <element minOccurs="0" name="gfoClientId" type="string"/>
      <element minOccurs="0" name="cif" type="string"/>
			<element minOccurs="0" name="owner" type="string"/>
			<element minOccurs="0" name="parentTaskId" type="string"/>
			<element name="priority" type="int"/>
			<!--            Custom attributes-->
			<element name="busApplId" type="string"/>
			<element name="clientName" type="string"/>
			<element name="clientSurname" type="string"/>
			<element name="taskType" type="lovs:LovTicketTp"/>
			<element name="createDate" type="dateTime"/>
			<element name="taskState" type="lovs:LovTicketStat"/>
            <element minOccurs="0" name="taskName" type="string"/>
            <element minOccurs="0" name="folderId" type="string"/>
		</sequence>
	</complexType>

	<complexType name="GetTasksRequestType">
   		<complexContent>
			<extension base="tns:TaskRequestBaseType">
				<sequence>
					<element name="sortAttribute" minOccurs="0" type="tns:taskAttribute"/>
					<element name="sortOrder" minOccurs="0" type="tns:sortOrder"/>
					<element name="maxResults" minOccurs="0" type="int"/>
					<element name="firstResult" minOccurs="0" type="int"/>
					<element name="taskType" minOccurs="0" type="lovs:LovTicketTp"/>
					<element name="busApplId" type="string" minOccurs="0" />
				</sequence>
			</extension>
		</complexContent>
	</complexType>

	<element name="GetTasksRequest">
		<complexType>
			<complexContent>
				<extension base="tns:GetTasksRequestType">
					<sequence>
						<element name="taskState" minOccurs="0" maxOccurs="unbounded" type="lovs:LovTicketStat"/>
                        <element name="taskName" minOccurs="0" maxOccurs="1" type="string"/>
					</sequence>
				</extension>
			</complexContent>
		</complexType>
	</element>

	<element name="GetClosedTasksRequest">
		<complexType>
			<complexContent>
				<extension base="tns:GetTasksRequestType">
					<sequence>
						<element name="cif" type="string" minOccurs="0" />
						<element name="gfoClientId" type="string" minOccurs="0" />
						<element name="accountNumber" type="string" minOccurs="0" />
						<element name="createdFrom" type="date" minOccurs="0" />
						<element name="createdTo" type="date" minOccurs="0" />
						<element name="createdBy" type="string" minOccurs="0" />
						<element name="assignee" type="string" minOccurs="0" />
					</sequence>
				</extension>
			</complexContent>
		</complexType>
	</element>

	<simpleType name="taskAttribute">
		<restriction base="string">
			<enumeration value="busApplId"/>
			<enumeration value="clientName"/>
			<enumeration value="clientSurname"/>
			<enumeration value="taskType"/>
			<enumeration value="taskState"/>
			<enumeration value="createDate"/>
		</restriction>
	</simpleType>

	<simpleType name="sortOrder">
		<restriction base="string">
			<enumeration value="ASC"/>
			<enumeration value="DESC"/>
		</restriction>
	</simpleType>

	<element name="GetTasksResponse">
		<complexType>
			<sequence>
				<element name="CommonServiceResponse" type="tns:CommonServiceResponse"/>
				<element maxOccurs="unbounded" minOccurs="0" name="tasks" nillable="true" type="tns:Task"/>
				<element name="count" type="int"/>
			</sequence>
		</complexType>
	</element>

	<element name="AssignTaskRequest">
		<complexType>
			<complexContent>
				<extension base="tns:TaskRequestBaseType">
					<sequence>
						<element name="taskId" type="string"/>
						<element name="allowReassign" minOccurs="0" type="boolean"/>
					</sequence>
				</extension>
			</complexContent>
		</complexType>
	</element>

	<element name="UnassignTaskRequest">
		<complexType>
			<complexContent>
				<extension base="tns:TaskRequestBaseType">
					<sequence>
						<element name="taskId" type="string"/>
						<element name="newState" type="string"/>
					</sequence>
				</extension>
			</complexContent>
		</complexType>
	</element>

	<element name="UnassignTaskResponse">
		<complexType>
			<sequence>
				<element name="CommonServiceResponse" type="tns:CommonServiceResponse"/>
			</sequence>
		</complexType>
	</element>

	<element name="AssignTaskResponse">
		<complexType>
			<sequence>
				<element name="CommonServiceResponse" type="tns:CommonServiceResponse"/>
			</sequence>
		</complexType>
	</element>

	<element name="CompleteTaskRequest">
		<complexType>
			<complexContent>
				<extension base="tns:TaskRequestBaseType">
					<sequence>
						<element name="taskId" type="string"/>
						<choice>
							<element name="result" type="tns:completionType"/>
							<element name="resultGeneric" type="tns:completionResultType" />
						</choice>
					</sequence>
				</extension>
			</complexContent>
		</complexType>
	</element>

	<element name="CompleteTaskResponse">
		<complexType>
			<sequence>
				<element name="CommonServiceResponse" type="tns:CommonServiceResponse"/>
			</sequence>
		</complexType>
	</element>

	<simpleType name="completionType">
		<restriction base="string">
			<enumeration value="APPROVED"/>
			<enumeration value="DECLINED"/>
			<enumeration value="CANCELED"/>
			<enumeration value="ASSIGN_TO_BRANCH"/>
			<enumeration value="ASSIGN_TO_COURIER"/>
		</restriction>
	</simpleType>

	<complexType name="completionResultType">
		<sequence>
			<element name="taskVariables" type="tns:variable" maxOccurs="unbounded"/>
			<element name="processVariables" type="tns:variable" maxOccurs="unbounded"/>
		</sequence>
	</complexType>

	<complexType name="variable">
		<sequence>
			<element name="key" type="string" />
			<element name="value" type="string" />
		</sequence>
	</complexType>

	<element name="SetTaskStateRequest">
		<complexType>
			<complexContent>
				<extension base="tns:TaskRequestBaseType">
					<sequence>
						<element name="taskId" type="string"/>
						<element name="state" type="lovs:LovTicketStat"/>
						<element name="taskVariables" type="tns:variable" maxOccurs="unbounded" minOccurs="0"/>
					</sequence>
				</extension>
			</complexContent>
		</complexType>
	</element>

	<element name="SetTaskStateResponse">
		<complexType>
			<sequence>
				<element name="CommonServiceResponse" type="tns:CommonServiceResponse"/>
			</sequence>
		</complexType>
	</element>

	<element name="GetTaskRequest">
		<complexType>
			<complexContent>
				<extension base="tns:TaskRequestBaseType">
					<sequence>
						<element name="id" type="string"/>
					</sequence>
				</extension>
			</complexContent>
		</complexType>
	</element>

	<element name="GetTaskResponse">
		<complexType>
			<sequence>
				<element name="CommonServiceResponse" type="tns:CommonServiceResponse"/>
				<element name="Task" type="tns:Task"/>
			</sequence>
		</complexType>
	</element>

	<complexType name="CommonServiceResponse">
		<sequence>
			<element name="status" type="tns:ResponseCode"/>
			<element name="camundaBusinessKey" type="string"/>
			<element name="message" type="string" minOccurs="0"/>
		</sequence>
	</complexType>

	<simpleType name="ResponseCode">
		<restriction base="string">
			<enumeration value="OK"/>
			<enumeration value="NOK"/>
		</restriction>
	</simpleType>

</schema>