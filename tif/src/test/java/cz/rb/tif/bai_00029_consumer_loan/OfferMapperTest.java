package cz.rb.tif.bai_00029_consumer_loan;

import cz.equa.camapp.model.OfferDTO;
import cz.rb.services.consumerloan.getoffers_1_0._1.Res;
import org.junit.Test;

import static cz.rb.tif.Utils.getPodamFactoryWithLovs;
import static org.unitils.reflectionassert.ReflectionAssert.assertReflectionEquals;

public class OfferMapperTest {

    @Test
    public void testMapper() {
        Res.Offer offer = getPodamFactoryWithLovs().manufacturePojo(Res.Offer.class);
        OfferDTO offerDTO = ConsumerLoanMapper.INSTANCE.tifToDto(offer);
        Res.Offer offer2 = ConsumerLoanMapper.INSTANCE.dtoToTif(offerDTO);
        assertReflectionEquals(offer, offer2);
    }

    @Test
    public void testInverserMapper() {
        OfferDTO offerDTO = getPodamFactoryWithLovs().manufacturePojo(OfferDTO.class);
        Res.Offer offer = ConsumerLoanMapper.INSTANCE.dtoToTif(offerDTO);
        OfferDTO offerDTO2 = ConsumerLoanMapper.INSTANCE.tifToDto(offer);
        assertReflectionEquals(offerDTO, offerDTO2);
    }
}
