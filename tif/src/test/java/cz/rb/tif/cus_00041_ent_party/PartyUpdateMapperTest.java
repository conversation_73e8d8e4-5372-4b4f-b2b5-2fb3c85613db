package cz.rb.tif.cus_00041_ent_party;

import cz.equa.camapp.model.update_party.additional_info.AdditionalInfoDTO;
import cz.equa.camapp.model.update_party.basic_info.BasicInfoDTO;
import cz.rb.services.party.update_1_2._1.OpUpdate12;
import org.apache.commons.lang3.SerializationUtils;
import org.junit.Test;
import uk.co.jemos.podam.api.PodamFactory;

import java.time.LocalDate;

import static cz.rb.tif.Utils.getPodamFactoryWithLovs;
import static org.unitils.reflectionassert.ReflectionAssert.assertReflectionEquals;

public class PartyUpdateMapperTest {

    PodamFactory factory = getPodamFactoryWithLovs();

	@Test
	public void testEntPartyBasicInfoMapper() {
        OpUpdate12.BasicInfo basicInfo = createBasicInfoWithData();
        BasicInfoDTO basicInfoDTO = PartyUpdateMapper.INSTANCE.tifBasicInfoToDTO(basicInfo);
		OpUpdate12.BasicInfo basicInfo2 = PartyUpdateMapper.INSTANCE.dtoToTifBasicInfo(basicInfoDTO);
		assertReflectionEquals(basicInfo, basicInfo2);
	}
    
    @Test
	public void testEntPartyAdditionalInfoMapper() {
        OpUpdate12.AdditionalInfo additionalInfo = createAdditionalInfoWithData();
        AdditionalInfoDTO additionalInfoDTO = PartyUpdateMapper.INSTANCE.tifAdditionalInfoToDTO(additionalInfo);
		OpUpdate12.AdditionalInfo additionalInfo2 = PartyUpdateMapper.INSTANCE.dtoToTifAdditionalInfo(additionalInfoDTO);
		assertReflectionEquals(additionalInfo, additionalInfo2);
	}

	@Test
	public void testEntPartyBasicInfoDTOSerialization() {
		BasicInfoDTO basicInfo = factory.manufacturePojo(BasicInfoDTO.class);
		BasicInfoDTO clone = SerializationUtils.clone(basicInfo);
		assertReflectionEquals(basicInfo, clone);
	}
    
    @Test
	public void testEntPartyAdditionalInfoDTOSerialization() {
		AdditionalInfoDTO additionalInfoDTO = factory.manufacturePojo(AdditionalInfoDTO.class);
		AdditionalInfoDTO clone = SerializationUtils.clone(additionalInfoDTO);
		assertReflectionEquals(additionalInfoDTO, clone);
	}
    
    private OpUpdate12.BasicInfo createBasicInfoWithData() {
        OpUpdate12.BasicInfo basicInfo = new OpUpdate12.BasicInfo();
        OpUpdate12.BasicInfo.PrivateParty privateParty = new OpUpdate12.BasicInfo.PrivateParty();
        
        OpUpdate12.BasicInfo.PrivateParty.FirstName firstName = new OpUpdate12.BasicInfo.PrivateParty.FirstName();
        firstName.setValue("FirstName");
        privateParty.setFirstName(firstName);
        
        OpUpdate12.BasicInfo.PrivateParty.LastName lastName = new OpUpdate12.BasicInfo.PrivateParty.LastName();
        lastName.setValue("LastName");
        privateParty.setLastName(lastName);
        
        privateParty.setGender("Gender");
        
        OpUpdate12.BasicInfo.PrivateParty.BirthInfo birthInfo = new OpUpdate12.BasicInfo.PrivateParty.BirthInfo();
        OpUpdate12.BasicInfo.PrivateParty.BirthInfo.BirthCode birthCode = new OpUpdate12.BasicInfo.PrivateParty.BirthInfo.BirthCode();
        birthCode.setValue("birthCode");
        birthInfo.setBirthCode(birthCode);
        
        birthInfo.setBirthDate(LocalDate.MAX);
        
        OpUpdate12.BasicInfo.PrivateParty.BirthInfo.CityOfBirth cityOfBirth = new OpUpdate12.BasicInfo.PrivateParty.BirthInfo.CityOfBirth();
        cityOfBirth.setValue("cityOfBirth");
        birthInfo.setCityOfBirth(cityOfBirth);
        
        OpUpdate12.BasicInfo.PrivateParty.BirthInfo.CountryOfBirth countryOfBirth = new OpUpdate12.BasicInfo.PrivateParty.BirthInfo.CountryOfBirth();
        countryOfBirth.setValue("countryOfBirth");
        birthInfo.setCountryOfBirth(countryOfBirth);
        
        OpUpdate12.BasicInfo.PrivateParty.BirthInfo.PhoneyBirthCode phoneyBirthCode = new OpUpdate12.BasicInfo.PrivateParty.BirthInfo.PhoneyBirthCode();
        phoneyBirthCode.setValue("phoneyBirthCode");
        birthInfo.setPhoneyBirthCode(phoneyBirthCode);
        
        privateParty.setBirthInfo(birthInfo);
        
        OpUpdate12.BasicInfo.PrivateParty.BirthLastName birthLastName = new OpUpdate12.BasicInfo.PrivateParty.BirthLastName();
        birthLastName.setValue("birthLastName");
        privateParty.setBirthLastName(birthLastName);
        
        OpUpdate12.BasicInfo.PrivateParty.Citizenship citizenship = new OpUpdate12.BasicInfo.PrivateParty.Citizenship();
        citizenship.setValue("Cityzenship");
        privateParty.setCitizenship(citizenship);
        
        basicInfo.setPrivateParty(privateParty);
        return basicInfo;
    }
    
    private OpUpdate12.AdditionalInfo createAdditionalInfoWithData() {
        OpUpdate12.AdditionalInfo additionalInfo = new OpUpdate12.AdditionalInfo();
        
        OpUpdate12.AdditionalInfo.Taxation taxation = new OpUpdate12.AdditionalInfo.Taxation();
        
        OpUpdate12.AdditionalInfo.Taxation.TaxId taxId = new OpUpdate12.AdditionalInfo.Taxation.TaxId();
        taxId.setValue("taxId");
        taxation.setTaxId(taxId);
        
        OpUpdate12.AdditionalInfo.Taxation.TaxResidency taxResidency = new OpUpdate12.AdditionalInfo.Taxation.TaxResidency();
        taxResidency.setValue("taxResidency");
        taxation.setTaxResidency(taxResidency);
        
        additionalInfo.setTaxation(taxation);
        
        return additionalInfo;
    }
}