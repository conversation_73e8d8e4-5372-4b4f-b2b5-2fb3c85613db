<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns="http://rb.cz/cdm/dds/crm/cpa/P0001238/1.0"
          xmlns:xs="http://www.w3.org/2001/XMLSchema"
          targetNamespace="http://rb.cz/cdm/dds/crm/cpa/P0001238/1.0"
          elementFormDefault="qualified"
          attributeFormDefault="unqualified"
          version="1.0.0">
   <xs:element name="P0001238">
      <xs:complexType>
         <xs:sequence>
            <xs:element name="party">
               <xs:complexType>
                  <xs:sequence>
                     <xs:element name="basicInfo">
                        <xs:complexType>
                           <xs:sequence>
                              <xs:element name="titleBefore" minOccurs="0" maxOccurs="1">
                                 <xs:annotation>
                                    <xs:documentation>Title before the first name</xs:documentation>
                                 </xs:annotation>
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:maxLength value="50"/>
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="firstName" minOccurs="1" maxOccurs="1" nillable="false">
                                 <xs:annotation>
                                    <xs:documentation>Jan</xs:documentation>
                                 </xs:annotation>
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:maxLength value="80"/>
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="middleName" minOccurs="0" maxOccurs="1">
                                 <xs:annotation>
                                    <xs:documentation>Amos</xs:documentation>
                                 </xs:annotation>
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:maxLength value="80"/>
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="lastName" minOccurs="1" maxOccurs="1" nillable="false">
                                 <xs:annotation>
                                    <xs:documentation>Komenský</xs:documentation>
                                 </xs:annotation>
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:maxLength value="100"/>
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="titleBehind" minOccurs="0" maxOccurs="1">
                                 <xs:annotation>
                                    <xs:documentation>Title behind the surname</xs:documentation>
                                 </xs:annotation>
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:maxLength value="50"/>
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="birthDate" type="xs:date" minOccurs="0" maxOccurs="1"/>
                              <xs:element name="birthCode" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:maxLength value="20"/>
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                           </xs:sequence>
                        </xs:complexType>
                     </xs:element>
                     <xs:element name="contact">
                        <xs:complexType>
                           <xs:sequence>
                              <xs:element name="phoneNumber" type="xs:string" minOccurs="0" maxOccurs="1"/>
                              <xs:element name="address">
                                 <xs:complexType>
                                    <xs:sequence>
                                       <xs:element name="street" minOccurs="0" maxOccurs="1">
                                          <xs:annotation>
                                             <xs:documentation>Street name</xs:documentation>
                                          </xs:annotation>
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:maxLength value="200"/>
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="number" minOccurs="1" maxOccurs="1">
                                          <xs:annotation>
                                             <xs:documentation>Building number/street number</xs:documentation>
                                          </xs:annotation>
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:maxLength value="30"/>
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="city" minOccurs="1" maxOccurs="1">
                                          <xs:annotation>
                                             <xs:documentation>City name</xs:documentation>
                                          </xs:annotation>
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:maxLength value="50"/>
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="country" type="xs:string" minOccurs="0" maxOccurs="1">
                                          <xs:annotation>
                                             <xs:documentation>Country (String value - not code)</xs:documentation>
                                          </xs:annotation>
                                       </xs:element>
                                       <xs:element name="postalCode">
                                          <xs:annotation>
                                             <xs:documentation>ZIP code</xs:documentation>
                                          </xs:annotation>
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:maxLength value="30"/>
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                    </xs:sequence>
                                 </xs:complexType>
                              </xs:element>
                           </xs:sequence>
                        </xs:complexType>
                     </xs:element>
                  </xs:sequence>
               </xs:complexType>
            </xs:element>
            <xs:element name="loanData">
               <xs:annotation>
                  <xs:documentation>Subject of agreement
</xs:documentation>
               </xs:annotation>
               <xs:complexType>
                  <xs:sequence>
                     <xs:element name="product">
                        <xs:complexType>
                           <xs:sequence>
                              <xs:element name="agreementNumber"
                                          type="xs:string"
                                          minOccurs="1"
                                          maxOccurs="1">
                                 <xs:annotation>
                                    <xs:documentation>ID of contract</xs:documentation>
                                 </xs:annotation>
                              </xs:element>
                              <xs:element name="accDate" type="xs:date">
                                 <xs:annotation>
                                    <xs:documentation>akceptacni lhuta prijeti navrhu</xs:documentation>
                                 </xs:annotation>
                              </xs:element>
                              <xs:element name="creditworthiness"
                                          type="xs:string"
                                          minOccurs="1"
                                          maxOccurs="1"/>
                           </xs:sequence>
                        </xs:complexType>
                     </xs:element>
                     <xs:element name="installment">
                        <xs:complexType>
                           <xs:sequence>
                              <xs:element name="nonPurpLoan" type="xs:integer">
                                 <xs:annotation>
                                    <xs:documentation>neucelova cast pujcky</xs:documentation>
                                 </xs:annotation>
                              </xs:element>
                              <xs:element name="purpLoan" type="xs:integer">
                                 <xs:annotation>
                                    <xs:documentation>ucelova cast pujcky</xs:documentation>
                                 </xs:annotation>
                              </xs:element>
                              <xs:element name="loanAmount" type="xs:integer"/>
                              <xs:element name="installmentCount"
                                          type="xs:integer"
                                          minOccurs="1"
                                          maxOccurs="1"/>
                              <xs:element name="totalAmount"
                                          type="xs:integer"
                                          minOccurs="1"
                                          maxOccurs="1"/>
                              <xs:element name="IR" type="xs:decimal" minOccurs="1" maxOccurs="1">
                                 <xs:annotation>
                                    <xs:documentation>loan interest rate</xs:documentation>
                                 </xs:annotation>
                              </xs:element>
                              <xs:element name="rpsn" type="xs:decimal" minOccurs="1" maxOccurs="1"/>
                              <xs:element name="dailyFee" type="xs:decimal">
                                 <xs:annotation>
                                    <xs:documentation>denni urok v Kc</xs:documentation>
                                 </xs:annotation>
                              </xs:element>
                              <xs:element name="countAmount" type="xs:integer"/>
                           </xs:sequence>
                        </xs:complexType>
                     </xs:element>
                     <xs:element name="insurance" minOccurs="0" maxOccurs="1">
                        <xs:complexType>
                           <xs:sequence>
                              <xs:element name="insuranceFee" minOccurs="1" maxOccurs="1">
                                 <xs:annotation>
                                    <xs:documentation>monthly fee insurance</xs:documentation>
                                 </xs:annotation>
                                 <xs:simpleType>
                                    <xs:restriction base="xs:decimal">
                                       <xs:totalDigits value="18"/>
                                       <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="paymentNoIns" minOccurs="1" maxOccurs="1">
                                 <xs:annotation>
                                    <xs:documentation>total monthly payment without insurance</xs:documentation>
                                 </xs:annotation>
                                 <xs:simpleType>
                                    <xs:restriction base="xs:decimal">
                                       <xs:totalDigits value="18"/>
                                       <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="type" type="xs:string" minOccurs="1" maxOccurs="1">
                                 <xs:annotation>
                                    <xs:documentation>type of installment ("Balíček A")</xs:documentation>
                                 </xs:annotation>
                              </xs:element>
                              <xs:element name="ref" type="xs:boolean" minOccurs="1" maxOccurs="1">
                                 <xs:annotation>
                                    <xs:documentation>true; false</xs:documentation>
                                 </xs:annotation>
                              </xs:element>
                           </xs:sequence>
                        </xs:complexType>
                     </xs:element>
                     <xs:element name="currentAccount" minOccurs="0" maxOccurs="1">
                        <xs:complexType>
                           <xs:sequence minOccurs="0">
                              <xs:element name="account">
                                 <xs:complexType>
                                    <xs:sequence>
                                       <xs:element name="numberPart1" minOccurs="0" maxOccurs="1">
                                          <xs:annotation>
                                             <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                          </xs:annotation>
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:maxLength value="6"/>
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="numberPart2" minOccurs="1" maxOccurs="1">
                                          <xs:annotation>
                                             <xs:documentation>Base part of the bank account number</xs:documentation>
                                          </xs:annotation>
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:maxLength value="10"/>
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="bankCode" type="xs:string" minOccurs="1" maxOccurs="1"/>
                                    </xs:sequence>
                                 </xs:complexType>
                              </xs:element>
                           </xs:sequence>
                        </xs:complexType>
                     </xs:element>
                     <xs:element name="com" minOccurs="0" maxOccurs="unbounded">
                        <xs:annotation>
                           <xs:documentation>zavazek</xs:documentation>
                        </xs:annotation>
                        <xs:complexType>
                           <xs:sequence>
                              <xs:element name="comName" type="xs:string">
                                 <xs:annotation>
                                    <xs:documentation>jmeno zavazku
</xs:documentation>
                                 </xs:annotation>
                              </xs:element>
                              <xs:element name="creditor" type="xs:string">
                                 <xs:annotation>
                                    <xs:documentation>veritel</xs:documentation>
                                 </xs:annotation>
                              </xs:element>
                              <xs:element name="origLoan" type="xs:integer">
                                 <xs:annotation>
                                    <xs:documentation>puvodni vyse uveru</xs:documentation>
                                 </xs:annotation>
                              </xs:element>
                              <xs:element name="rePujcka" type="xs:integer"/>
                           </xs:sequence>
                        </xs:complexType>
                     </xs:element>
                     <xs:element name="primCom" minOccurs="0">
                        <xs:complexType>
                           <xs:sequence>
                              <xs:element name="comName" type="xs:string">
                                 <xs:annotation>
                                    <xs:documentation>jmeno zavazku
</xs:documentation>
                                 </xs:annotation>
                              </xs:element>
                              <xs:element name="creditor" type="xs:string">
                                 <xs:annotation>
                                    <xs:documentation>veritel</xs:documentation>
                                 </xs:annotation>
                              </xs:element>
                              <xs:element name="origLoan" type="xs:integer">
                                 <xs:annotation>
                                    <xs:documentation>puvodni vyse uveru</xs:documentation>
                                 </xs:annotation>
                              </xs:element>
                              <xs:element name="rePujcka" type="xs:integer"/>
                              <xs:element name="primAcc">
                                 <xs:complexType>
                                    <xs:sequence>
                                       <xs:element name="numberPart1" minOccurs="0" maxOccurs="1">
                                          <xs:annotation>
                                             <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                          </xs:annotation>
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:maxLength value="6"/>
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="numberPart2" minOccurs="1" maxOccurs="1">
                                          <xs:annotation>
                                             <xs:documentation>Base part of the bank account number</xs:documentation>
                                          </xs:annotation>
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:maxLength value="10"/>
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="bankCode" type="xs:string" minOccurs="1" maxOccurs="1"/>
                                       <xs:element name="var" type="xs:string" minOccurs="0">
                                          <xs:annotation>
                                             <xs:documentation>variabilni symbol
</xs:documentation>
                                          </xs:annotation>
                                       </xs:element>
                                       <xs:element name="spec" type="xs:string" minOccurs="0">
                                          <xs:annotation>
                                             <xs:documentation>specificky symbol</xs:documentation>
                                          </xs:annotation>
                                       </xs:element>
                                    </xs:sequence>
                                 </xs:complexType>
                              </xs:element>
                           </xs:sequence>
                        </xs:complexType>
                     </xs:element>
                  </xs:sequence>
               </xs:complexType>
            </xs:element>
            <xs:element name="agent" minOccurs="0" maxOccurs="1">
               <xs:annotation>
                  <xs:documentation>intermediary contract in the sense of the loan</xs:documentation>
               </xs:annotation>
               <xs:complexType>
                  <xs:sequence minOccurs="1" maxOccurs="1">
                     <xs:element name="agentName" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                           <xs:documentation>intermediary data</xs:documentation>
                        </xs:annotation>
                        <xs:simpleType>
                           <xs:restriction base="xs:string">
                              <xs:maxLength value="320"/>
                           </xs:restriction>
                        </xs:simpleType>
                     </xs:element>
                     <xs:element name="reqNo" type="xs:integer" minOccurs="1" maxOccurs="1"/>
                     <xs:element name="street" minOccurs="0" maxOccurs="1">
                        <xs:annotation>
                           <xs:documentation>Street name</xs:documentation>
                        </xs:annotation>
                        <xs:simpleType>
                           <xs:restriction base="xs:string">
                              <xs:maxLength value="200"/>
                           </xs:restriction>
                        </xs:simpleType>
                     </xs:element>
                     <xs:element name="number" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                           <xs:documentation>Building number/street number</xs:documentation>
                        </xs:annotation>
                        <xs:simpleType>
                           <xs:restriction base="xs:string">
                              <xs:maxLength value="30"/>
                           </xs:restriction>
                        </xs:simpleType>
                     </xs:element>
                     <xs:element name="postalCode">
                        <xs:annotation>
                           <xs:documentation>ZIP code</xs:documentation>
                        </xs:annotation>
                        <xs:simpleType>
                           <xs:restriction base="xs:string">
                              <xs:maxLength value="30"/>
                           </xs:restriction>
                        </xs:simpleType>
                     </xs:element>
                     <xs:element name="city" minOccurs="1" maxOccurs="1">
                        <xs:annotation>
                           <xs:documentation>City name</xs:documentation>
                        </xs:annotation>
                        <xs:simpleType>
                           <xs:restriction base="xs:string">
                              <xs:maxLength value="50"/>
                           </xs:restriction>
                        </xs:simpleType>
                     </xs:element>
                     <xs:element name="phoneNo" type="xs:string" minOccurs="1" maxOccurs="1"/>
                     <xs:element name="email" type="xs:string" minOccurs="1" maxOccurs="1"/>
                     <xs:element name="repre" minOccurs="0" maxOccurs="1">
                        <xs:complexType>
                           <xs:sequence>
                              <xs:element name="representation"
                                          type="xs:boolean"
                                          minOccurs="0"
                                          maxOccurs="1">
                                 <xs:annotation>
                                    <xs:documentation>true;false</xs:documentation>
                                 </xs:annotation>
                              </xs:element>
                              <xs:element name="repreName" minOccurs="1" maxOccurs="1">
                                 <xs:annotation>
                                    <xs:documentation>intermediary data</xs:documentation>
                                 </xs:annotation>
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:maxLength value="320"/>
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="repreSurname" minOccurs="1" maxOccurs="1">
                                 <xs:annotation>
                                    <xs:documentation>intermediary data</xs:documentation>
                                 </xs:annotation>
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:maxLength value="320"/>
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="street" minOccurs="0" maxOccurs="1">
                                 <xs:annotation>
                                    <xs:documentation>Street name</xs:documentation>
                                 </xs:annotation>
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:maxLength value="200"/>
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="number" minOccurs="1" maxOccurs="1">
                                 <xs:annotation>
                                    <xs:documentation>Building number/street number</xs:documentation>
                                 </xs:annotation>
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:maxLength value="30"/>
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="postalCode">
                                 <xs:annotation>
                                    <xs:documentation>ZIP code</xs:documentation>
                                 </xs:annotation>
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:maxLength value="30"/>
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="city" minOccurs="1" maxOccurs="1">
                                 <xs:annotation>
                                    <xs:documentation>City name</xs:documentation>
                                 </xs:annotation>
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:maxLength value="50"/>
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="phoneNo" type="xs:string" minOccurs="1" maxOccurs="1"/>
                              <xs:element name="email" type="xs:string" minOccurs="1" maxOccurs="1"/>
                           </xs:sequence>
                        </xs:complexType>
                     </xs:element>
                  </xs:sequence>
               </xs:complexType>
            </xs:element>
         </xs:sequence>
      </xs:complexType>
   </xs:element>
</xs:schema>
