<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2018 rel. 2 (x64) (http://www.altova.com) by <PERSON><PERSON><PERSON> (ProDocument Services, s.r.o.) -->
<xs:schema xmlns="http://rb.cz/cdm/dds/crm/cpa/X0000007/1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="http://rb.cz/cdm/dds/crm/cpa/X0000007/1.0" elementFormDefault="qualified" attributeFormDefault="unqualified" version="1.0.0">
	<xs:element name="X0000007">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="productType" type="xs:string" minOccurs="1" maxOccurs="1">
					<xs:annotation>
						<xs:documentation>kod produktu</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="emailData">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="reason" type="xs:string">
								<xs:annotation>
									<xs:documentation>duvod zamitnuti, bude chodit text</xs:documentation>
								</xs:annotation>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
				<xs:element name="busProdSubTpName" type="xs:string" minOccurs="0">
					<xs:annotation>
						<xs:documentation>název produktu</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="applTp" type="xs:string" minOccurs="0">
					<xs:annotation>
						<xs:documentation>Priznak jestli se jedna o top-up nebo novou zadost:
							CHANGE_PROD_INCR = navyseni;NEW_PROD = nova</xs:documentation>
					</xs:annotation>
				</xs:element>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
</xs:schema>
