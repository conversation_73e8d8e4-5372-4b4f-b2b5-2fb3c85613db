<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns="http://rb.cz/cdm/dds/crm/cpa/X0000014/1.0"
          xmlns:xs="http://www.w3.org/2001/XMLSchema"
          targetNamespace="http://rb.cz/cdm/dds/crm/cpa/X0000014/1.0"
          elementFormDefault="qualified"
          attributeFormDefault="unqualified"
          version="1.0.0">
   <xs:element name="X0000014">
      <xs:complexType>
         <xs:sequence>
            <xs:element name="orderInfo" minOccurs="0" maxOccurs="1">
               <xs:complexType>
                  <xs:sequence>
                     <xs:element name="eshopName" type="xs:string" minOccurs="0" maxOccurs="1"/>
                     <xs:element name="eshopURL" type="xs:string" minOccurs="0" maxOccurs="1"/>
                     <xs:element name="orderNo" type="xs:string" minOccurs="0" maxOccurs="1"/>
                     <xs:element name="buyDate" type="xs:date" minOccurs="0" maxOccurs="1"/>
                     <xs:element name="accessKey" type="xs:string" minOccurs="0" maxOccurs="1"/>
                  </xs:sequence>
               </xs:complexType>
            </xs:element>
            <xs:element name="paymentInfo">
               <xs:complexType>
                  <xs:sequence>
                     <xs:element name="VS" type="xs:string" minOccurs="0" maxOccurs="1"/>
                     <xs:element name="amountInfo">
                        <xs:complexType>
                           <xs:sequence>
                              <xs:element name="rejectionReason" type="xs:string"/>
                              <xs:element name="finalAmount"
                                          type="xs:decimal"
                                          minOccurs="0"
                                          maxOccurs="1"/>
                              <xs:element name="dueDate" type="xs:date" minOccurs="0" maxOccurs="1"/>
                              <xs:element name="account" minOccurs="0" maxOccurs="1">
                                 <xs:complexType>
                                    <xs:sequence>
                                       <xs:element name="numberPart1" minOccurs="0" maxOccurs="1">
                                          <xs:annotation>
                                             <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                          </xs:annotation>
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:maxLength value="6"/>
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="numberPart2">
                                          <xs:annotation>
                                             <xs:documentation>Base part of the bank account number</xs:documentation>
                                          </xs:annotation>
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:maxLength value="10"/>
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="bankCode" type="xs:string" minOccurs="0" maxOccurs="1">
                                          <xs:annotation>
                                             <xs:documentation>Part of the bank account number which identifies the bank</xs:documentation>
                                          </xs:annotation>
                                       </xs:element>
                                    </xs:sequence>
                                 </xs:complexType>
                              </xs:element>
                              <xs:element name="QRcode" type="xs:string" minOccurs="0" maxOccurs="1"/>
                           </xs:sequence>
                        </xs:complexType>
                     </xs:element>
                  </xs:sequence>
               </xs:complexType>
            </xs:element>
         </xs:sequence>
      </xs:complexType>
   </xs:element>
</xs:schema>
