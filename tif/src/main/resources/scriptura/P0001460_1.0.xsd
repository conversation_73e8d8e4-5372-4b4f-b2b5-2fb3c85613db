<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2018 rel. 2 (x64) (http://www.altova.com) by <PERSON><PERSON><PERSON> (ProDocument Services, s.r.o.) -->
<xs:schema xmlns="http://rb.cz/cdm/dds/crm/cpa/P0001460/1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="http://rb.cz/cdm/dds/crm/cpa/P0001460/1.0" elementFormDefault="qualified" attributeFormDefault="unqualified" version="1.0.0">
	<xs:element name="P0001460">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="subSegment" type="xs:string" minOccurs="0">
					<xs:annotation>
						<xs:documentation>segment klienta</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="repre">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="representation" type="xs:boolean">
								<xs:annotation>
									<xs:documentation>zastoupeni zprostredkovatelem</xs:documentation>
								</xs:annotation>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
				<xs:element name="party">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="personalData">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="birthCode" type="xs:string" minOccurs="0">
											<xs:annotation>
												<xs:documentation>rodne cislo</xs:documentation>
											</xs:annotation>
										</xs:element>
										<xs:element name="birthDate" type="xs:date" minOccurs="0"/>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
							<xs:element name="name">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="titlesBefore" type="xs:string" minOccurs="0">
											<xs:annotation>
												<xs:documentation>tituly před jménem</xs:documentation>
											</xs:annotation>
										</xs:element>
										<xs:element name="firstName" type="xs:string" nillable="false">
											<xs:annotation>
												<xs:documentation>křestní jméno</xs:documentation>
											</xs:annotation>
										</xs:element>
										<xs:element name="middleName" type="xs:string" minOccurs="0">
											<xs:annotation>
												<xs:documentation>druhé křestní jméno</xs:documentation>
											</xs:annotation>
										</xs:element>
										<xs:element name="lastName" type="xs:string" nillable="false">
											<xs:annotation>
												<xs:documentation>příjmení</xs:documentation>
											</xs:annotation>
										</xs:element>
										<xs:element name="titlesBehind" type="xs:string" minOccurs="0">
											<xs:annotation>
												<xs:documentation>tituly za jménem</xs:documentation>
											</xs:annotation>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
							<xs:element name="contacts">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="phoneNumber" type="xs:string" maxOccurs="1"/>
										<xs:element name="email" type="xs:string">
											<xs:annotation>
												<xs:documentation>kontaktní e-mail</xs:documentation>
											</xs:annotation>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
							<xs:element name="address">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="perm">
											<xs:annotation>
												<xs:documentation>Trvalá adresa</xs:documentation>
											</xs:annotation>
											<xs:complexType>
												<xs:sequence>
													<xs:element name="street" type="xs:string" minOccurs="0">
														<xs:annotation>
															<xs:documentation>ulice</xs:documentation>
														</xs:annotation>
													</xs:element>
													<xs:element name="number" type="xs:string">
														<xs:annotation>
															<xs:documentation>ulice číslo</xs:documentation>
														</xs:annotation>
													</xs:element>
													<xs:element name="postalCode" type="xs:string">
														<xs:annotation>
															<xs:documentation>PSČ</xs:documentation>
														</xs:annotation>
													</xs:element>
													<xs:element name="city" type="xs:string">
														<xs:annotation>
															<xs:documentation>město</xs:documentation>
														</xs:annotation>
													</xs:element>
													<xs:element name="country" type="xs:string" minOccurs="0">
														<xs:annotation>
															<xs:documentation>země</xs:documentation>
														</xs:annotation>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
										<xs:element name="contact">
											<xs:annotation>
												<xs:documentation>Kontaktní/zasílací adresa</xs:documentation>
											</xs:annotation>
											<xs:complexType>
												<xs:sequence>
													<xs:element name="street" type="xs:string" minOccurs="0">
														<xs:annotation>
															<xs:documentation>ulice</xs:documentation>
														</xs:annotation>
													</xs:element>
													<xs:element name="number" type="xs:string">
														<xs:annotation>
															<xs:documentation>ulice číslo</xs:documentation>
														</xs:annotation>
													</xs:element>
													<xs:element name="postalCode" type="xs:string">
														<xs:annotation>
															<xs:documentation>PSČ</xs:documentation>
														</xs:annotation>
													</xs:element>
													<xs:element name="city" type="xs:string">
														<xs:annotation>
															<xs:documentation>město</xs:documentation>
														</xs:annotation>
													</xs:element>
													<xs:element name="country" type="xs:string" minOccurs="0">
														<xs:annotation>
															<xs:documentation>země</xs:documentation>
														</xs:annotation>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
				<xs:element name="cardDetail">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="maskedNumber" type="xs:string">
								<xs:annotation>
									<xs:documentation>Číslo kreditní karty</xs:documentation>
								</xs:annotation>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
				<xs:element name="applDetails">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="toBePaidAmount" type="xs:decimal">
								<xs:annotation>
									<xs:documentation>Celková částka, kterou má Klient zaplatit</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="RPSN" type="xs:decimal"/>
							<xs:element name="reportDate" type="xs:date" minOccurs="0"/>
							<xs:element name="repaymentSchedule">
								<xs:annotation>
									<xs:documentation>Splátkový kalendář obsahující data pro 12 měsíců</xs:documentation>
								</xs:annotation>
								<xs:complexType>
									<xs:sequence>
										<xs:element name="month" maxOccurs="12">
											<xs:annotation>
												<xs:documentation>Splátková data obsahující data pro měsíc (12x)</xs:documentation>
											</xs:annotation>
											<xs:complexType>
												<xs:sequence>
													<xs:element name="repaymentAmount" type="xs:decimal">
														<xs:annotation>
															<xs:documentation>Výše splátky v CZK</xs:documentation>
														</xs:annotation>
													</xs:element>
													<xs:element name="repaidPrincipal" type="xs:decimal">
														<xs:annotation>
															<xs:documentation>Výše jistiny splacené ve splátce v CZK</xs:documentation>
														</xs:annotation>
													</xs:element>
													<xs:element name="repaidInterest" type="xs:decimal">
														<xs:annotation>
															<xs:documentation>Výše úroku splaceného ve splátce v CZK</xs:documentation>
														</xs:annotation>
													</xs:element>
													<xs:element name="remainingPrincipal" type="xs:decimal">
														<xs:annotation>
															<xs:documentation>Výše zbývající nesplacené jistiny po splátce v CZK</xs:documentation>
														</xs:annotation>
													</xs:element>
													<xs:element name="otherCosts" type="xs:decimal">
														<xs:annotation>
															<xs:documentation>Další náklady zahrnuté ve splátce (poplatky) v CZK</xs:documentation>
														</xs:annotation>
													</xs:element>
													<xs:element name="id" type="xs:integer">
														<xs:annotation>
															<xs:documentation>Pořadové číslo měsíce</xs:documentation>
														</xs:annotation>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
							<xs:element name="promo" type="xs:string">
								<xs:annotation>
									<xs:documentation>přiřazená promo akce</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="productType" type="xs:string">
								<xs:annotation>
									<xs:documentation>kod produktu</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="place" type="xs:string" minOccurs="0"/>
							<xs:element name="monthlyFee" type="xs:decimal">
								<xs:annotation>
									<xs:documentation>Měsiční poplatek za vedení karty</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="minCreditLimit" type="xs:decimal">
								<xs:annotation>
									<xs:documentation>Minimální produktový limit</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="interestRatePOS" type="xs:decimal">
								<xs:annotation>
									<xs:documentation>Úroková POS sazba</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="interestRateATM" type="xs:decimal">
								<xs:annotation>
									<xs:documentation>Úroková ATM sazba</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="creditLimit" type="xs:decimal">
								<xs:annotation>
									<xs:documentation>schválený limit</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="busProdSubTpName" type="xs:string">
								<xs:annotation>
									<xs:documentation>název produktu</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="busApplId" type="xs:string">
								<xs:annotation>
									<xs:documentation>číslo žádosti</xs:documentation>
								</xs:annotation>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
</xs:schema>
