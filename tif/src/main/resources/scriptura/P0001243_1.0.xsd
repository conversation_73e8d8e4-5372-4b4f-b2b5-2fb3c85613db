<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns="http://rb.cz/cdm/dds/crm/cpa/P0001243/1.0"
          xmlns:xs="http://www.w3.org/2001/XMLSchema"
          targetNamespace="http://rb.cz/cdm/dds/crm/cpa/P0001243/1.0"
          elementFormDefault="qualified"
          attributeFormDefault="unqualified"
          version="1.0.0">
   <xs:element name="P0001243">
      <xs:complexType>
         <xs:sequence>
            <xs:element name="companyInfo">
               <xs:complexType>
                  <xs:sequence>
                     <xs:element name="companyName" type="xs:string" minOccurs="1" maxOccurs="1"/>
                     <xs:element name="additionalInfo"
                                 type="xs:string"
                                 minOccurs="0"
                                 maxOccurs="1"/>
                     <xs:element name="address">
                        <xs:complexType>
                           <xs:sequence>
                              <xs:element name="street" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:maxLength value="200"/>
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="number" type="xs:string" minOccurs="0" maxOccurs="1"/>
                              <xs:element name="city" minOccurs="1" maxOccurs="1">
                                 <xs:annotation>
                                    <xs:documentation>City name</xs:documentation>
                                 </xs:annotation>
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:maxLength value="50"/>
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="postalCode" minOccurs="1" maxOccurs="1">
                                 <xs:annotation>
                                    <xs:documentation>ZIP code</xs:documentation>
                                 </xs:annotation>
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:maxLength value="30"/>
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                           </xs:sequence>
                        </xs:complexType>
                     </xs:element>
                  </xs:sequence>
               </xs:complexType>
            </xs:element>
            <xs:element name="clientInfo">
               <xs:complexType>
                  <xs:sequence>
                     <xs:element name="titleBefore" type="xs:string" minOccurs="0" maxOccurs="1"/>
                     <xs:element name="firstName" type="xs:string" minOccurs="1" maxOccurs="1"/>
                     <xs:element name="middleName" type="xs:string" minOccurs="0" maxOccurs="1"/>
                     <xs:element name="lastName" type="xs:string" minOccurs="1" maxOccurs="1"/>
                     <xs:element name="titleBehind" type="xs:string" minOccurs="0" maxOccurs="1"/>
                     <xs:element name="birthDate" type="xs:date" minOccurs="0" maxOccurs="1"/>
                     <xs:element name="birthCode" type="xs:string" minOccurs="0" maxOccurs="1"/>
                     <xs:element name="phoneNumber" type="xs:string"/>
                     <xs:element name="address">
                        <xs:complexType>
                           <xs:sequence>
                              <xs:element name="street" minOccurs="0" maxOccurs="1">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:maxLength value="200"/>
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="number" type="xs:string" minOccurs="0" maxOccurs="1"/>
                              <xs:element name="city" minOccurs="1" maxOccurs="1">
                                 <xs:annotation>
                                    <xs:documentation>City name</xs:documentation>
                                 </xs:annotation>
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:maxLength value="50"/>
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="postalCode" minOccurs="1" maxOccurs="1">
                                 <xs:annotation>
                                    <xs:documentation>ZIP code</xs:documentation>
                                 </xs:annotation>
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:maxLength value="30"/>
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="country" type="xs:string" minOccurs="0" maxOccurs="1"/>
                           </xs:sequence>
                        </xs:complexType>
                     </xs:element>
                  </xs:sequence>
               </xs:complexType>
            </xs:element>
            <xs:element name="productType" minOccurs="1" maxOccurs="1">
               <xs:complexType>
                  <xs:sequence>
                     <xs:element name="loanAmount" type="xs:integer" minOccurs="1" maxOccurs="1"/>
                     <xs:element name="overpaymentAccount">
                        <xs:complexType>
                           <xs:sequence>
                              <xs:element name="numberPart1" minOccurs="0" maxOccurs="1">
                                 <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                 </xs:annotation>
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:maxLength value="6"/>
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="numberPart2">
                                 <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                 </xs:annotation>
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:maxLength value="10"/>
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="bankCode" type="xs:string" minOccurs="1" maxOccurs="1"/>
                           </xs:sequence>
                        </xs:complexType>
                     </xs:element>
                  </xs:sequence>
               </xs:complexType>
            </xs:element>
         </xs:sequence>
      </xs:complexType>
   </xs:element>
</xs:schema>
