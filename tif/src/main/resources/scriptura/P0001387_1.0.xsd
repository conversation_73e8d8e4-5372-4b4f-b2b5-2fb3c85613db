<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2018 rel. 2 (x64) (http://www.altova.com) by <PERSON><PERSON><PERSON> (ProDocument Services, s.r.o.) -->
<xs:schema xmlns="http://rb.cz/dms/dds/CRD/P0001387/1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="http://rb.cz/dms/dds/CRD/P0001387/1.0" elementFormDefault="qualified" attributeFormDefault="unqualified" version="1.0.0">
	<xs:element name="P0001387">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="party">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="name">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="titlesBefore" minOccurs="0">
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:maxLength value="30"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="firstName">
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:maxLength value="50"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="middleName" minOccurs="0">
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:maxLength value="80"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="lastName">
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:maxLength value="50"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="titlesBehind" minOccurs="0">
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:maxLength value="30"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
				<xs:element name="applDetails">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="productType" type="xs:string">
								<xs:annotation>
									<xs:documentation>kod produktu</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="busProdSubTpName" type="xs:string">
								<xs:annotation>
									<xs:documentation>název produktu</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="busApplId" type="xs:string">
								<xs:annotation>
									<xs:documentation>číslo žádosti</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="creditLimit" type="xs:decimal">
								<xs:annotation>
									<xs:documentation>schválený limit</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="insurances">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="mandatory">
											<xs:annotation>
												<xs:documentation>Kolekce názvů pojištění v ceně karty</xs:documentation>
											</xs:annotation>
											<xs:complexType>
												<xs:sequence>
													<xs:element name="insuranceName" type="xs:string" minOccurs="0" maxOccurs="unbounded">
														<xs:annotation>
															<xs:documentation>Název pojištění</xs:documentation>
														</xs:annotation>
													</xs:element>
													<xs:element name="insuranceCode" type="xs:integer" minOccurs="0" maxOccurs="unbounded">
														<xs:annotation>
															<xs:documentation>Kód pojištění</xs:documentation>
														</xs:annotation>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
										<xs:element name="optional">
											<xs:complexType>
												<xs:sequence>
													<xs:element name="type" type="xs:string" minOccurs="0"/>
													<xs:element name="fee" type="xs:decimal" minOccurs="0"/>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
							<xs:element name="interestRatePOS" type="xs:decimal">
								<xs:annotation>
									<xs:documentation>Úroková sazba POS</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="interestRateATM" type="xs:decimal">
								<xs:annotation>
									<xs:documentation>Úroková sazba ATM</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="monthlyFee" type="xs:decimal">
								<xs:annotation>
									<xs:documentation>Měsiční poplatek za vedení karty</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="RPSN" type="xs:decimal"/>
							<xs:element name="toBePaidAmount" type="xs:decimal">
								<xs:annotation>
									<xs:documentation>Celková částka, kterou má Klient zaplatit</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="repaymentSchedule">
								<xs:annotation>
									<xs:documentation>Splátkový kalendář obsahující data pro 12 měsíců</xs:documentation>
								</xs:annotation>
								<xs:complexType>
									<xs:sequence>
										<xs:element name="month" maxOccurs="12">
											<xs:complexType>
												<xs:sequence>
													<xs:element name="id" type="xs:int">
														<xs:annotation>
															<xs:documentation>Pořadové číslo měsíce</xs:documentation>
														</xs:annotation>
													</xs:element>
													<xs:element name="repaymentAmount" type="xs:decimal">
														<xs:annotation>
															<xs:documentation>Výše splátky v CZK</xs:documentation>
														</xs:annotation>
													</xs:element>
													<xs:element name="repaidPrincipal" type="xs:decimal">
														<xs:annotation>
															<xs:documentation>Výše jistiny splacené ve splátce v CZK</xs:documentation>
														</xs:annotation>
													</xs:element>
													<xs:element name="repaidInterest" type="xs:decimal">
														<xs:annotation>
															<xs:documentation>Výše úroku splaceného ve splátce v CZK</xs:documentation>
														</xs:annotation>
													</xs:element>
													<xs:element name="remainingPrincipal" type="xs:decimal">
														<xs:annotation>
															<xs:documentation>Výše zbývající nesplacené jistiny po splátce v CZK</xs:documentation>
														</xs:annotation>
													</xs:element>
													<xs:element name="otherCosts" type="xs:decimal">
														<xs:annotation>
															<xs:documentation>Další náklady zahrnuté ve splátce (poplatky) v CZK</xs:documentation>
														</xs:annotation>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
							<xs:element name="reportDate" type="xs:date" minOccurs="0"/>
							<xs:element name="place" type="xs:string" minOccurs="0"/>
							<xs:element name="preContractInfoValidity" minOccurs="0">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="from" type="xs:date" minOccurs="0"/>
										<xs:element name="to" type="xs:date" minOccurs="0"/>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
							<xs:element name="applTp" type="xs:string">
								<xs:annotation>
									<xs:documentation>KK/CLIP</xs:documentation>
								</xs:annotation>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
				<xs:element name="repre" minOccurs="0">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="repreName" type="xs:string" minOccurs="0">
								<xs:annotation>
									<xs:documentation>jméno zprostředkovatele</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="repreSurname" type="xs:string" minOccurs="0">
								<xs:annotation>
									<xs:documentation>příjmení zprostředkovatele</xs:documentation>
								</xs:annotation>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
				<xs:element name="agent" minOccurs="0">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="agentName" type="xs:string" minOccurs="0"/>
							<xs:element name="agentStreet" type="xs:string" minOccurs="0"/>
							<xs:element name="agentPostalCode" type="xs:string" minOccurs="0"/>
							<xs:element name="agentCity" type="xs:string" minOccurs="0"/>
							<xs:element name="agentPhoneNumber" type="xs:string" minOccurs="0"/>
							<xs:element name="agentEmail" type="xs:string" minOccurs="0"/>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
				<xs:element name="subSegment" type="xs:string" minOccurs="0">
					<xs:annotation>
						<xs:documentation>segment klienta</xs:documentation>
					</xs:annotation>
				</xs:element>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
</xs:schema>
