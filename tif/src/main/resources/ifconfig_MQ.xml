<?xml version="1.0" encoding="utf-8"?>
<tcfj:configuration xmlns:tcfj="http://www.trask.cz/TIF/TCFJ/1.3">
	<application>
		<name>CMN</name>
		<level>TEST</level>
		<systemIdPrefix />
		<defaultClientUserName>l2</defaultClientUserName>
		<defaultServerUserName>l3</defaultServerUserName>
		<definitions>
			<validateDefinitions>false</validateDefinitions>
			<apiExpiration>6</apiExpiration>
			<!-- Definitions loaded from file -->
			<file>
				<definitionsDir>defs</definitionsDir>
			</file>
			<!-- Definitions loaded from database, 
      use the server connection when running within the database 
      or difene the connection to DB
      <database>
 				<serverConnection>true</serverConnection>

				<hostName>*********</hostName>
				<port>1523</port>
				<sid>buitest</sid>
				<userName>TIF_MDR</userName>
				<password>TIF_MDR</password>
			</database>
      -->
		</definitions>
		<monitoringPort>0</monitoringPort>
		<messageBodyEncoding>ISO-8859-2</messageBodyEncoding>
		<sensitiveValuesSecureStore>../tif/src/main/resources/sec/APS_TEST.securestore</sensitiveValuesSecureStore>
		<sensitiveValuesKeyStore>../tif/src/main/resources/sec/RFB_TEST.pwdstore</sensitiveValuesKeyStore>
		<callingContext>THREAD_LOCAL</callingContext>
		<maxRequestSize>0</maxRequestSize>
	</application>
	<log>
		<enabled>true</enabled>
		<enableToStdout>true</enableToStdout>
		<file>
			<applicationLog>
				<fileName>logs/dpc.log</fileName>
				<archive>logs/dapi/archive</archive>
				<level>INFO</level>
			</applicationLog>
			<requestLog>
				<fileName>logs/dpc_reqs.log</fileName>
				<archive>logs/dapi/archive</archive>
				<maxValue>0</maxValue>
				<dapiLevel>ALL_ALL_DATA</dapiLevel>
				<specialDapiLevels>
					<dapi>
						<name>GET_STATUS</name>
						<level>ALL_CUTOFF</level>
					</dapi>
					<dapi>
						<name>GET_IDENTITY</name>
						<level>ALL_CUTOFF</level>
					</dapi>
				</specialDapiLevels>
			</requestLog>
			<responseLog>
				<fileName>logs/dpc_resp.log</fileName>
				<archive>logs/dapi/archive</archive>
				<maxValue>0</maxValue>
				<dapiLevel>ALL_ALL_DATA</dapiLevel>
				<specialDapiLevels>
					<dapi>
						<name>GET_STATUS</name>
						<level>ALL_CUTOFF</level>
					</dapi>
					<dapi>
						<name>GET_IDENTITY</name>
						<level>ALL_CUTOFF</level>
					</dapi>
				</specialDapiLevels>
			</responseLog>
			<errorLog>
				<fileName>logs/dpc_err.log</fileName>
				<archive>logs/archive</archive>
				<maxValue>0</maxValue>
			</errorLog>
		</file>
		<!-- the logging can be made into a DB also 
    use the server connection when running within the database 
    or difene the connection to DB
    
    There are two alternatives to connect to DB, classical :
    
    <database>
 			<serverConnection>true</serverConnection>

			<hostName>*********</hostName>
			<port>1523</port>
			<sid>buitest</sid>
			<userName>TIF_MDR</userName>
			<password>TIF_MDR</password>
			<tableName>JAVA_LOG</tableName>
			<maxLogRecordSize>1024</maxLogRecordSize>
			<level>INFO</level>
			<dapiLevel>NO_LOGGING</dapiLevel>
		</database>
	
	Or by using connection string :
		    <database>
 			<serverConnection>true</serverConnection>
			<connectionURL>*******************************</connectionURL>
			<userName>TIF_MDR</userName>
			<password>TIF_MDR</password>
			<tableName>TCFJ_LOG</tableName>
			<maxLogRecordSize>1024</maxLogRecordSize>
			<level>INFO</level>
			<dapiLevel>NO_LOGGING</dapiLevel>
		</database>
    -->
		<buffer>
			<enabled>true</enabled>
			<bufferLength>20</bufferLength>
			<pattern>%-5p [%d{ISO8601}] [%-12t] - %m%nM</pattern>
			<application>true</application>
			<request>false</request>
			<response>false</response>
			<error>false</error>
		</buffer>
        <cl>
            <enabled>true</enabled>
            <specEnabled>true</specEnabled>
            <commonEnabled>true</commonEnabled>
            <endpoints>redis1-test.rb.cz:6379,redis2-test.rb.cz:6379</endpoints>
            <!-- DO NOT DELETE THIS COMMENT !!! -->
            <key>logstash</key>
            <password>fakeWord</password>
            <alwaysBatch>true</alwaysBatch>
            <batchSize>100</batchSize>
            <flushInterval>100</flushInterval>
            <queueSize>5000</queueSize>
            <purgeOnFailure>true</purgeOnFailure>
            <registerMBean>false</registerMBean>
            <layout>
                <locationInfo>false</locationInfo>
                <userFields>server_name:cmnsrl1at1.rb.cz,logSource:TCFJ.CMN,environment_id:ENV09,environment_short:TFX1,business_domain:technical,application:cmn,application_module:cmn,layer:app,layer_type:jboss,source_type:log4j,source_data_type:logs,retention_period:weekly,business_app:CMN,family:tif,family_type:tcfj,data_type:logs,tif_log_type:APP,tif_instance:T01_1</userFields>
            </layout>
        </cl>
	</log>
	<security>
		<providerClassName>com.ge.ufo.security.provider.aes.GESecurityProvider_AES</providerClassName>
		<keyStore>src/sec/global.securestore</keyStore>
		<passwordStore>src/sec/global.pwdstore</passwordStore>
		<createSignature>false</createSignature>
		<verifySignature>false</verifySignature>
	</security>
	<transport>
		<mq>
            <cacheReinitTimeout>30</cacheReinitTimeout>
            <cacheFailureTimeout>5</cacheFailureTimeout>
            <default>
              <ssl>
                <enabled>false</enabled>
                <trustStore>sec/truststore</trustStore>
                <keyStore>sec/keyStore</keyStore>
                <keyStorePassword>password</keyStorePassword>
                <cipherSuite>SSL_RSA_WITH_RC4_128_MD5</cipherSuite>
              </ssl>
            </default>
            <mqManager>
              <priority>10</priority>
              <useBindingMode>false</useBindingMode>
              <hostName>wmbd4d12.rb.cz</hostName>
              <port>1516</port>
              <queueManager>WMBD4D12</queueManager>
              <channel>TIF.CON.CMN.SVRCONN</channel>
              <ccsId>912</ccsId>
              <ssl>
                <enabled>false</enabled>
                <trustStore>sec/truststore</trustStore>
                <keyStore>sec/keyStore</keyStore>
                <keyStorePassword>password</keyStorePassword>
                <cipherSuite>SSL_RSA_WITH_RC4_128_MD5</cipherSuite>
              </ssl>
              <pooling>
                <maxSize>5</maxSize>
                <poolExhaustedAction>FAIL</poolExhaustedAction>
                <inactivityOptimization>
                  <enabled>false</enabled>
                  <inactiveTimeout>100</inactiveTimeout>
                  <connectionFreePeriod>50</connectionFreePeriod>
                </inactivityOptimization>
              </pooling>
            </mqManager>
        </mq>
	</transport>
	<garbageMessageCollector>
		<enabled>true</enabled>
		<runningPeriod>30</runningPeriod>
	</garbageMessageCollector>
	<errorHandling>
		<mqType>
			<reportQueue>CLF.ERR.MON</reportQueue>
		</mqType>
		<email>
			<smtpHost>localhost</smtpHost>
			<toAddress><EMAIL></toAddress>
			<fromAddress><EMAIL></fromAddress>
			<subject>DapiClient ErrorNotification</subject>
			<encoding>Windows-1250</encoding>
		</email>
	</errorHandling>
	<besim>
		<enabled>false</enabled>
		<name>BESIM</name>
    <database>
      <!-- use the server connection when running within the database 
        or difene the connection to DB
 			<serverConnection>true</serverConnection>
 			-->

			<hostName>localhost</hostName>
			<port>1521</port>
			<sid>ORADB</sid>
			<userName>user</userName>
			<password>pass</password>
			
			<!-- tuning of the connection numbers from version 5.5.4 -->
			<connMaxActive>3</connMaxActive>
			<connMaxIdle>3</connMaxIdle>
			<connMaxWait>-1</connMaxWait>
		</database>
	</besim>
	<router>
	  <!-- use the server connection when running within the database 
       or difene the connection to DB
 		<serverConnection>true</serverConnection>
 		-->
		<hostName>localhost</hostName>
		<port>1521</port>
		<sid>ORADB</sid>
		<userName>user</userName>
		<password>pass</password>
		<enabled>false</enabled>
		<environment>TEST</environment>
		<cacheDir>/tmp</cacheDir>
		<refreshEnabled>true</refreshEnabled>
		<refreshTime>60</refreshTime>
		<storedProcedureName>sp</storedProcedureName>
	</router>
	<!-- self registration to the TIF Topology -->
	<monitoring>
		<enabled>false</enabled>
		<envSubtype>LIVE</envSubtype>
		<applicationName>TST</applicationName>
	</monitoring>
</tcfj:configuration>
