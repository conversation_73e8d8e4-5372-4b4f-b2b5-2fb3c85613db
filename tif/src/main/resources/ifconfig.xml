<?xml version="1.0" encoding="utf-8"?>
<tcfj:configuration xmlns:tcfj="http://www.trask.cz/TIF/TCFJ/1.3">
	<application>
		<name>TST</name>
		<level>TEST</level>
		<systemIdPrefix />
		<defaultClientUserName>l2</defaultClientUserName>
		<defaultServerUserName>l3</defaultServerUserName>
		<definitions>
			<validateDefinitions>true</validateDefinitions>
			<apiExpiration>6</apiExpiration>
			<!-- Definitions loaded from file -->
			<file>
				<definitionsDir>../tif/src/main/resources/defs</definitionsDir>
			</file>
		</definitions>
		<monitoringPort>0</monitoringPort>
		<messageBodyEncoding>ISO-8859-2</messageBodyEncoding>
		<sensitiveValuesSecureStore>../tif/src/main/resources/sec/APS_TEST.securestore</sensitiveValuesSecureStore>
		<sensitiveValuesKeyStore>../tif/src/main/resources/sec/RFB_TEST.pwdstore</sensitiveValuesKeyStore>
		<callingContext>THREAD_LOCAL</callingContext>
		<maxRequestSize>0</maxRequestSize>
	</application>
	<log>
		<enabled>true</enabled>
		<enableToStdout>true</enableToStdout>
		<file>
			<applicationLog>
				<fileName>/tmp/tiflogs/dpc.log</fileName>
				<archive>/tmp/tiflogs/dapi/archive</archive>
				<level>DEBUG</level>
			</applicationLog>
			<requestLog>
				<fileName>/tmp/tiflogs/dpc_reqs.log</fileName>
				<archive>/tmp/tiflogs/dapi/archive</archive>
				<maxValue>0</maxValue>
				<dapiLevel>ALL_ALL_DATA</dapiLevel>
				<specialDapiLevels>
					<dapi>
						<name>GET_STATUS</name>
						<level>ALL_CUTOFF</level>
					</dapi>
					<dapi>
						<name>GET_IDENTITY</name>
						<level>ALL_CUTOFF</level>
					</dapi>
				</specialDapiLevels>
			</requestLog>
			<responseLog>
				<fileName>/tmp/tiflogs/dpc_resp.log</fileName>
				<archive>/tmp/tiflogs/dapi/archive</archive>
				<maxValue>0</maxValue>
				<dapiLevel>ALL_ALL_DATA</dapiLevel>
				<specialDapiLevels>
					<dapi>
						<name>GET_STATUS</name>
						<level>ALL_CUTOFF</level>
					</dapi>
					<dapi>
						<name>GET_IDENTITY</name>
						<level>ALL_CUTOFF</level>
					</dapi>
				</specialDapiLevels>
			</responseLog>
			<errorLog>
				<fileName>/tmp/tiflogs/dpc_err.log</fileName>
				<archive>/tmp/tiflogs/archive</archive>
				<maxValue>0</maxValue>
			</errorLog>
		</file>
		<buffer>
			<enabled>true</enabled>
			<bufferLength>20</bufferLength>
			<pattern>%-5p [%d{ISO8601}] [%-12t] - %m%nM</pattern>
			<application>true</application>
			<request>false</request>
			<response>false</response>
			<error>false</error>
		</buffer>
	</log>
	<security>
		<providerClassName>com.ge.ufo.security.provider.aes.GESecurityProvider_AES</providerClassName>
		<keyStore>src/sec/global.securestore</keyStore>
		<passwordStore>src/sec/global.pwdstore</passwordStore>
		<createSignature>false</createSignature>
		<verifySignature>false</verifySignature>
	</security>
	<transport>
		<!-- the transport can use also file simulation  -->
        <file>
			<workDir>/tmp/messages</workDir>
		</file>
	</transport>
	<garbageMessageCollector>
		<enabled>true</enabled>
		<runningPeriod>30</runningPeriod>
	</garbageMessageCollector>
	<errorHandling>
		<mqType>
			<reportQueue>CLF.ERR.MON</reportQueue>
		</mqType>
		<email>
			<smtpHost>localhost</smtpHost>
			<toAddress><EMAIL></toAddress>
			<fromAddress><EMAIL></fromAddress>
			<subject>DapiClient ErrorNotification</subject>
			<encoding>Windows-1250</encoding>
		</email>
	</errorHandling>
	<besim>
		<enabled>true</enabled>
		<name>BESIM</name>
        <xml> <!-- production defs are in the etc/docker of the webapp module -->
            <xmlDirectory>../tif/src/main/resources/defs/besim</xmlDirectory>
        </xml>
	</besim>
	<router>
	  <!-- use the server connection when running within the database 
       or difene the connection to DB
 		<serverConnection>true</serverConnection>
 		-->
		<hostName>localhost</hostName>
		<port>1521</port>
		<sid>ORADB</sid>
		<userName>user</userName>
		<password>pass</password>
		<enabled>false</enabled>
		<environment>TEST</environment>
		<cacheDir>/tmp</cacheDir>
		<refreshEnabled>true</refreshEnabled>
		<refreshTime>60</refreshTime>
		<storedProcedureName>sp</storedProcedureName>
	</router>
	<!-- self registration to the TIF Topology -->
	<monitoring>
		<enabled>false</enabled>
		<envSubtype>LIVE</envSubtype>
		<applicationName>TST</applicationName>
	</monitoring>
</tcfj:configuration>
