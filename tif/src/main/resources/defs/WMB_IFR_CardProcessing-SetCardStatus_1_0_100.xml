<?xml version="1.0" encoding="UTF-8"?>
<tif xmlns="http://trask.cz/tif/definitions/api">
   <api>
      <application>WMB</application>
      <name>IFR_CardProcessing-SetCardStatus_1_0</name>
      <version>100</version>
      <operation_type>active</operation_type>
      <documentation>The service sets card status</documentation>
      <encryption_enabled>true</encryption_enabled>
      <encryption_api_group>CARD</encryption_api_group>
      <message type="XML" version="3">
         <input_parameters>
            <parameter>
               <name>Request</name>
               <type name="XSD"
                     elementName="cardProcessingRequest"
                     ns="http://rb.cz/services/technical/IFR_CardProcessing/1.0"
                     location="./xsd/IFR_00014_CardProcessing_1.0.xsd"/>
               <required>true</required>
               <elementExtraInfoList>
                  <elementExtraInfo>
                     <elementPath>cardProcessingRequest/entityService/opSetCardStatus_1_0/pan</elementPath>
                     <encryption>
                        <enabled>true</enabled>
                        <group>CARD</group>
                     </encryption>
                  </elementExtraInfo>
               </elementExtraInfoList>
            </parameter>
         </input_parameters>
         <output_parameters>
            <parameter>
               <name>Response</name>
               <type name="XSD"
                     elementName="cardProcessingResponse"
                     ns="http://rb.cz/services/technical/IFR_CardProcessing/1.0"
                     location="./xsd/IFR_00014_CardProcessing_1.0.xsd"/>
               <required>true</required>
               <elementExtraInfoList>
                  <elementExtraInfo>
                     <elementPath>cardProcessingResponse/response/opSetCardStatus_1_0_res/cardList/card/pan</elementPath>
                     <encryption>
                        <enabled>true</enabled>
                        <group>CARD</group>
                     </encryption>
                  </elementExtraInfo>
               </elementExtraInfoList>
            </parameter>
         </output_parameters>
      </message>
   </api>
</tif>
