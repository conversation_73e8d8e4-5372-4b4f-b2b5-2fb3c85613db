<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:opgetcreditofferparam_1_3="http://rb.cz/services/entityService/paymentCard/GetCreditOfferParam_1_3"
           xmlns:xs="http://www.w3.org/2001/XMLSchema"
           targetNamespace="http://rb.cz/services/entityService/paymentCard/GetCreditOfferParam_1_3"
           elementFormDefault="qualified"
           attributeFormDefault="unqualified"
           version="1.0.0">
   <xs:element name="opGetCreditOfferParam_1_3">
      <xs:complexType>
         <xs:sequence>
            <xs:element name="offerParamSet">
               <xs:complexType>
                  <xs:choice>
                     <xs:element name="cardApplication">
                        <xs:complexType>
                           <xs:sequence>
                              <xs:element name="cardType">
                                 <xs:annotation>
                                    <xs:documentation>Attributes of credit card</xs:documentation>
                                 </xs:annotation>
                                 <xs:complexType>
                                    <xs:sequence>
                                       <xs:element name="cardProductType" type="xs:string">
                                          <xs:annotation>
                                             <xs:documentation>A product type means if a card is Maestro, Visa, Billa, etc. [CODELIST: CreditCardTypeCode]</xs:documentation>
                                          </xs:annotation>
                                       </xs:element>
                                    </xs:sequence>
                                 </xs:complexType>
                              </xs:element>
                              <xs:element name="totalLimit">
                                 <xs:annotation>
                                    <xs:documentation>A total limit of a loan</xs:documentation>
                                 </xs:annotation>
                                 <xs:complexType>
                                    <xs:sequence>
                                       <xs:element name="amount">
                                          <xs:annotation>
                                             <xs:documentation>Amount</xs:documentation>
                                          </xs:annotation>
                                          <xs:simpleType>
                                             <xs:restriction base="xs:decimal">
                                                <xs:totalDigits value="18"/>
                                                <xs:fractionDigits value="5"/>
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="currencyCode" type="xs:string" minOccurs="0">
                                          <xs:annotation>
                                             <xs:documentation>Currency of amount [CODELIST: Currency]</xs:documentation>
                                          </xs:annotation>
                                       </xs:element>
                                    </xs:sequence>
                                 </xs:complexType>
                              </xs:element>
                              <xs:element name="interestRate">
                                 <xs:annotation>
                                    <xs:documentation>An interest rate - in %</xs:documentation>
                                 </xs:annotation>
                                 <xs:simpleType>
                                    <xs:restriction base="xs:decimal">
                                       <xs:totalDigits value="10"/>
                                       <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="feeList">
                                 <xs:annotation>
                                    <xs:documentation>A list of fees</xs:documentation>
                                 </xs:annotation>
                                 <xs:complexType>
                                    <xs:sequence>
                                       <xs:element name="fee" maxOccurs="unbounded">
                                          <xs:complexType>
                                             <xs:sequence>
                                                <xs:element name="type" type="xs:string">
                                                   <xs:annotation>
                                                      <xs:documentation>A type of a fee [CODELIST: LoanFee]</xs:documentation>
                                                   </xs:annotation>
                                                </xs:element>
                                                <xs:element name="amount">
                                                   <xs:annotation>
                                                      <xs:documentation>An amount of a fee</xs:documentation>
                                                   </xs:annotation>
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:decimal">
                                                         <xs:totalDigits value="18"/>
                                                         <xs:fractionDigits value="5"/>
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="currencyCode" type="xs:string" minOccurs="0">
                                                   <xs:annotation>
                                                      <xs:documentation>A currency of a fee [CODELIST: Currency]</xs:documentation>
                                                   </xs:annotation>
                                                </xs:element>
                                             </xs:sequence>
                                          </xs:complexType>
                                       </xs:element>
                                    </xs:sequence>
                                 </xs:complexType>
                              </xs:element>
                              <xs:element name="instalmentCount" type="xs:integer">
                                 <xs:annotation>
                                    <xs:documentation>A count of instalments per year</xs:documentation>
                                 </xs:annotation>
                              </xs:element>
                           </xs:sequence>
                        </xs:complexType>
                     </xs:element>
                     <xs:element name="fundTransfer">
                        <xs:complexType>
                           <xs:sequence>
                              <xs:element name="cardIdentifier" maxOccurs="1">
                                 <xs:complexType>
                                    <xs:sequence>
                                       <xs:element name="identificationCode">
                                          <xs:annotation>
                                             <xs:documentation>A value of the identifier (type of the identifier is specified in the identificationType field)</xs:documentation>
                                          </xs:annotation>
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:maxLength value="64"/>
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="identificationType">
                                          <xs:annotation>
                                             <xs:documentation>Type of the code - core identifier or card hash [LOV: cardHash]</xs:documentation>
                                          </xs:annotation>
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:enumeration value="cardHash"/>
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                    </xs:sequence>
                                 </xs:complexType>
                              </xs:element>
                              <xs:element name="mprodCode" type="xs:string">
                                 <xs:annotation>
                                    <xs:documentation>MPROD code from product catalogue</xs:documentation>
                                 </xs:annotation>
                              </xs:element>
                              <xs:element name="amountToTransfer">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:decimal">
                                       <xs:totalDigits value="18"/>
                                       <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="currencyCode" type="xs:string">
                                 <xs:annotation>
                                    <xs:documentation>[CODELIST: Currency]</xs:documentation>
                                 </xs:annotation>
                              </xs:element>
                           </xs:sequence>
                        </xs:complexType>
                     </xs:element>
                     <xs:element name="instalmentProgram">
                        <xs:complexType>
                           <xs:sequence>
                              <xs:element name="primaryId">
                                 <xs:annotation>
                                    <xs:documentation>A transaction identifier from RPC</xs:documentation>
                                 </xs:annotation>
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:maxLength value="18"/>
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                           </xs:sequence>
                        </xs:complexType>
                     </xs:element>
                     <xs:element name="loanOnPhone">
                        <xs:complexType>
                           <xs:sequence>
                              <xs:element name="cardIdentifier">
                                 <xs:complexType>
                                    <xs:sequence>
                                       <xs:element name="identificationCode">
                                          <xs:annotation>
                                             <xs:documentation>A value of the identifier (type of the identifier is specified in the identificationType field)</xs:documentation>
                                          </xs:annotation>
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:maxLength value="64"/>
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="identificationType">
                                          <xs:annotation>
                                             <xs:documentation>Type of the code - core identifier or card hash [LOV: cardHash]</xs:documentation>
                                          </xs:annotation>
                                          <xs:simpleType>
                                             <xs:restriction base="xs:string">
                                                <xs:enumeration value="cardHash"/>
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                    </xs:sequence>
                                 </xs:complexType>
                              </xs:element>
                              <xs:element name="mprodCode" type="xs:string">
                                 <xs:annotation>
                                    <xs:documentation>MPROD code from product catalogue</xs:documentation>
                                 </xs:annotation>
                              </xs:element>
                              <xs:element name="amountToTransfer">
                                 <xs:simpleType>
                                    <xs:restriction base="xs:decimal">
                                       <xs:totalDigits value="18"/>
                                       <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="currencyCode" type="xs:string">
                                 <xs:annotation>
                                    <xs:documentation>[CODELIST: Currency]</xs:documentation>
                                 </xs:annotation>
                              </xs:element>
                           </xs:sequence>
                        </xs:complexType>
                     </xs:element>
                  </xs:choice>
               </xs:complexType>
            </xs:element>
         </xs:sequence>
      </xs:complexType>
   </xs:element>
   <xs:element name="opGetCreditOfferParam_1_3_res">
      <xs:complexType>
         <xs:sequence>
            <xs:element name="offerParamSet" minOccurs="0">
               <xs:complexType>
                  <xs:choice>
                     <xs:element name="cardApplication">
                        <xs:complexType>
                           <xs:sequence>
                              <xs:element name="rpsn">
                                 <xs:annotation>
                                    <xs:documentation>Roční procentní sazba nákladů. An equivalent for APR (Annual Percentage Rate) in Czech republic</xs:documentation>
                                 </xs:annotation>
                                 <xs:simpleType>
                                    <xs:restriction base="xs:decimal">
                                       <xs:totalDigits value="8"/>
                                       <xs:fractionDigits value="3"/>
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="applicationId">
                                 <xs:annotation>
                                    <xs:documentation>Unique identification of an application</xs:documentation>
                                 </xs:annotation>
                                 <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                       <xs:maxLength value="30"/>
                                    </xs:restriction>
                                 </xs:simpleType>
                              </xs:element>
                              <xs:element name="totalAmount">
                                 <xs:annotation>
                                    <xs:documentation>A sum of fees, instalments, interests which a client will pay together.</xs:documentation>
                                 </xs:annotation>
                                 <xs:complexType>
                                    <xs:sequence>
                                       <xs:element name="amount">
                                          <xs:annotation>
                                             <xs:documentation>Amount</xs:documentation>
                                          </xs:annotation>
                                          <xs:simpleType>
                                             <xs:restriction base="xs:decimal">
                                                <xs:totalDigits value="18"/>
                                                <xs:fractionDigits value="5"/>
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="currencyCode" type="xs:string" minOccurs="0">
                                          <xs:annotation>
                                             <xs:documentation>Currency of amount [CODELIST: Currency]</xs:documentation>
                                          </xs:annotation>
                                       </xs:element>
                                    </xs:sequence>
                                 </xs:complexType>
                              </xs:element>
                              <xs:element name="regularInstalmentAmount">
                                 <xs:annotation>
                                    <xs:documentation>9% instalment (9% from the total limit)</xs:documentation>
                                 </xs:annotation>
                                 <xs:complexType>
                                    <xs:sequence>
                                       <xs:element name="amount">
                                          <xs:annotation>
                                             <xs:documentation>Amount</xs:documentation>
                                          </xs:annotation>
                                          <xs:simpleType>
                                             <xs:restriction base="xs:decimal">
                                                <xs:totalDigits value="18"/>
                                                <xs:fractionDigits value="5"/>
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="currencyCode" type="xs:string" minOccurs="0">
                                          <xs:annotation>
                                             <xs:documentation>Currency of amount [CODELIST: Currency]</xs:documentation>
                                          </xs:annotation>
                                       </xs:element>
                                    </xs:sequence>
                                 </xs:complexType>
                              </xs:element>
                              <xs:element name="sumOfFees">
                                 <xs:annotation>
                                    <xs:documentation>A sum of fees which a client will pay</xs:documentation>
                                 </xs:annotation>
                                 <xs:complexType>
                                    <xs:sequence>
                                       <xs:element name="amount">
                                          <xs:annotation>
                                             <xs:documentation>Amount</xs:documentation>
                                          </xs:annotation>
                                          <xs:simpleType>
                                             <xs:restriction base="xs:decimal">
                                                <xs:totalDigits value="18"/>
                                                <xs:fractionDigits value="5"/>
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="currencyCode" type="xs:string" minOccurs="0">
                                          <xs:annotation>
                                             <xs:documentation>Currency of amount [CODELIST: Currency]</xs:documentation>
                                          </xs:annotation>
                                       </xs:element>
                                    </xs:sequence>
                                 </xs:complexType>
                              </xs:element>
                              <xs:element name="paymentSchedules">
                                 <xs:complexType>
                                    <xs:sequence>
                                       <xs:element name="paymentSchedule" minOccurs="1" maxOccurs="12">
                                          <xs:complexType>
                                             <xs:sequence>
                                                <xs:element name="monthNumber" minOccurs="1" maxOccurs="1">
                                                   <xs:annotation>
                                                      <xs:documentation>number representing the month</xs:documentation>
                                                   </xs:annotation>
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:integer">
                                                         <xs:minInclusive value="1"/>
                                                         <xs:maxInclusive value="12"/>
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="outstandingAmount" minOccurs="1" maxOccurs="1">
                                                   <xs:annotation>
                                                      <xs:documentation>Outstanding amount</xs:documentation>
                                                   </xs:annotation>
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:decimal">
                                                         <xs:totalDigits value="18"/>
                                                         <xs:fractionDigits value="5"/>
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="instalmentAmount">
                                                   <xs:annotation>
                                                      <xs:documentation>instalment Amount</xs:documentation>
                                                   </xs:annotation>
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:decimal">
                                                         <xs:totalDigits value="18"/>
                                                         <xs:fractionDigits value="5"/>
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="principalAmount">
                                                   <xs:annotation>
                                                      <xs:documentation>principal Amount</xs:documentation>
                                                   </xs:annotation>
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:decimal">
                                                         <xs:totalDigits value="18"/>
                                                         <xs:fractionDigits value="5"/>
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="interestAmount">
                                                   <xs:annotation>
                                                      <xs:documentation>interest Amount</xs:documentation>
                                                   </xs:annotation>
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:decimal">
                                                         <xs:totalDigits value="18"/>
                                                         <xs:fractionDigits value="5"/>
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="feeAmount">
                                                   <xs:annotation>
                                                      <xs:documentation>fee Amount</xs:documentation>
                                                   </xs:annotation>
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:decimal">
                                                         <xs:totalDigits value="18"/>
                                                         <xs:fractionDigits value="5"/>
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="currencyCode" type="xs:string" minOccurs="0">
                                                   <xs:annotation>
                                                      <xs:documentation>Currency of amount [CODELIST: Currency]</xs:documentation>
                                                   </xs:annotation>
                                                </xs:element>
                                             </xs:sequence>
                                          </xs:complexType>
                                       </xs:element>
                                    </xs:sequence>
                                 </xs:complexType>
                              </xs:element>
                           </xs:sequence>
                        </xs:complexType>
                     </xs:element>
                     <xs:element name="fundTransfer">
                        <xs:complexType>
                           <xs:sequence>
                              <xs:element name="salesPlan" type="xs:integer">
                                 <xs:annotation>
                                    <xs:documentation>Id of a sales plan</xs:documentation>
                                 </xs:annotation>
                              </xs:element>
                              <xs:element name="internalSalesPlan" type="xs:integer">
                                 <xs:annotation>
                                    <xs:documentation>Primary id of a sales plan</xs:documentation>
                                 </xs:annotation>
                              </xs:element>
                           </xs:sequence>
                        </xs:complexType>
                     </xs:element>
                     <xs:element name="instalmentProgramList">
                        <xs:complexType>
                           <xs:sequence>
                              <xs:element name="instalmentProgram" maxOccurs="unbounded">
                                 <xs:annotation>
                                    <xs:documentation>A sales plan for 12, 24 or another number of months (instalment count)</xs:documentation>
                                 </xs:annotation>
                                 <xs:complexType>
                                    <xs:sequence>
                                       <xs:element name="salesPlan" type="xs:integer">
                                          <xs:annotation>
                                             <xs:documentation>Id of a sales plan</xs:documentation>
                                          </xs:annotation>
                                       </xs:element>
                                       <xs:element name="internalSalesPlan" type="xs:integer">
                                          <xs:annotation>
                                             <xs:documentation>Primary id of a sales plan</xs:documentation>
                                          </xs:annotation>
                                       </xs:element>
                                       <xs:element name="totalAmount">
                                          <xs:annotation>
                                             <xs:documentation>A sum of fees, instalments, interests which a client will pay together.</xs:documentation>
                                          </xs:annotation>
                                          <xs:complexType>
                                             <xs:sequence>
                                                <xs:element name="amount">
                                                   <xs:annotation>
                                                      <xs:documentation>Amount</xs:documentation>
                                                   </xs:annotation>
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:decimal">
                                                         <xs:totalDigits value="18"/>
                                                         <xs:fractionDigits value="5"/>
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="currencyCode" type="xs:string" minOccurs="0">
                                                   <xs:annotation>
                                                      <xs:documentation>Currency of amount [CODELIST: Currency]</xs:documentation>
                                                   </xs:annotation>
                                                </xs:element>
                                             </xs:sequence>
                                          </xs:complexType>
                                       </xs:element>
                                       <xs:element name="firstInstalment">
                                          <xs:complexType>
                                             <xs:sequence>
                                                <xs:element name="instalmentAmount">
                                                   <xs:annotation>
                                                      <xs:documentation>First instalment amount</xs:documentation>
                                                   </xs:annotation>
                                                   <xs:complexType>
                                                      <xs:sequence>
                                                         <xs:element name="amount">
                                                            <xs:annotation>
                                                               <xs:documentation>Amount</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:simpleType>
                                                               <xs:restriction base="xs:decimal">
                                                                  <xs:totalDigits value="18"/>
                                                                  <xs:fractionDigits value="5"/>
                                                               </xs:restriction>
                                                            </xs:simpleType>
                                                         </xs:element>
                                                         <xs:element name="currencyCode" type="xs:string" minOccurs="0">
                                                            <xs:annotation>
                                                               <xs:documentation>Currency of amount [CODELIST: Currency]</xs:documentation>
                                                            </xs:annotation>
                                                         </xs:element>
                                                      </xs:sequence>
                                                   </xs:complexType>
                                                </xs:element>
                                                <xs:element name="date" type="xs:date">
                                                   <xs:annotation>
                                                      <xs:documentation>First instalment date</xs:documentation>
                                                   </xs:annotation>
                                                </xs:element>
                                             </xs:sequence>
                                          </xs:complexType>
                                       </xs:element>
                                       <xs:element name="regularInstalmentAmount">
                                          <xs:annotation>
                                             <xs:documentation>Regular instalment amount</xs:documentation>
                                          </xs:annotation>
                                          <xs:complexType>
                                             <xs:sequence>
                                                <xs:element name="amount">
                                                   <xs:annotation>
                                                      <xs:documentation>Amount</xs:documentation>
                                                   </xs:annotation>
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:decimal">
                                                         <xs:totalDigits value="18"/>
                                                         <xs:fractionDigits value="5"/>
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="currencyCode" type="xs:string" minOccurs="0">
                                                   <xs:annotation>
                                                      <xs:documentation>Currency of amount [CODELIST: Currency]</xs:documentation>
                                                   </xs:annotation>
                                                </xs:element>
                                             </xs:sequence>
                                          </xs:complexType>
                                       </xs:element>
                                       <xs:element name="lastInstalmentAmount">
                                          <xs:annotation>
                                             <xs:documentation>Next instalment amount</xs:documentation>
                                          </xs:annotation>
                                          <xs:complexType>
                                             <xs:sequence>
                                                <xs:element name="amount">
                                                   <xs:annotation>
                                                      <xs:documentation>Amount</xs:documentation>
                                                   </xs:annotation>
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:decimal">
                                                         <xs:totalDigits value="18"/>
                                                         <xs:fractionDigits value="5"/>
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="currencyCode" type="xs:string" minOccurs="0">
                                                   <xs:annotation>
                                                      <xs:documentation>Currency of amount [CODELIST: Currency]</xs:documentation>
                                                   </xs:annotation>
                                                </xs:element>
                                             </xs:sequence>
                                          </xs:complexType>
                                       </xs:element>
                                       <xs:element name="instalmentCount" type="xs:integer">
                                          <xs:annotation>
                                             <xs:documentation>A count of instalments</xs:documentation>
                                          </xs:annotation>
                                       </xs:element>
                                       <xs:element name="interestRate">
                                          <xs:annotation>
                                             <xs:documentation>Interest rate</xs:documentation>
                                          </xs:annotation>
                                          <xs:simpleType>
                                             <xs:restriction base="xs:decimal">
                                                <xs:totalDigits value="10"/>
                                                <xs:fractionDigits value="4"/>
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="rpsn">
                                          <xs:annotation>
                                             <xs:documentation>Roční procentní sazba nákladů. An equivalent for APR (Annual Percentage Rate) in Czech republic</xs:documentation>
                                          </xs:annotation>
                                          <xs:simpleType>
                                             <xs:restriction base="xs:decimal">
                                                <xs:totalDigits value="8"/>
                                                <xs:fractionDigits value="3"/>
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                    </xs:sequence>
                                 </xs:complexType>
                              </xs:element>
                           </xs:sequence>
                        </xs:complexType>
                     </xs:element>
                     <xs:element name="loanOnPhoneList">
                        <xs:complexType>
                           <xs:sequence>
                              <xs:element name="loanOnPhone" maxOccurs="unbounded">
                                 <xs:complexType>
                                    <xs:sequence>
                                       <xs:element name="salesPlan" type="xs:integer">
                                          <xs:annotation>
                                             <xs:documentation>Id of a sales plan</xs:documentation>
                                          </xs:annotation>
                                       </xs:element>
                                       <xs:element name="internalSalesPlan" type="xs:integer">
                                          <xs:annotation>
                                             <xs:documentation>Primary id of a sales plan</xs:documentation>
                                          </xs:annotation>
                                       </xs:element>
                                       <xs:element name="totalAmount">
                                          <xs:annotation>
                                             <xs:documentation>A sum of fees, instalments, interests which a client will pay together.</xs:documentation>
                                          </xs:annotation>
                                          <xs:complexType>
                                             <xs:sequence>
                                                <xs:element name="amount">
                                                   <xs:annotation>
                                                      <xs:documentation>Amount</xs:documentation>
                                                   </xs:annotation>
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:decimal">
                                                         <xs:totalDigits value="18"/>
                                                         <xs:fractionDigits value="5"/>
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="currencyCode" type="xs:string" minOccurs="0">
                                                   <xs:annotation>
                                                      <xs:documentation>Currency of amount [CODELIST: Currency]</xs:documentation>
                                                   </xs:annotation>
                                                </xs:element>
                                             </xs:sequence>
                                          </xs:complexType>
                                       </xs:element>
                                       <xs:element name="firstInstalment">
                                          <xs:complexType>
                                             <xs:sequence>
                                                <xs:element name="instalmentAmount">
                                                   <xs:annotation>
                                                      <xs:documentation>First instalment amount</xs:documentation>
                                                   </xs:annotation>
                                                   <xs:complexType>
                                                      <xs:sequence>
                                                         <xs:element name="amount">
                                                            <xs:annotation>
                                                               <xs:documentation>Amount</xs:documentation>
                                                            </xs:annotation>
                                                            <xs:simpleType>
                                                               <xs:restriction base="xs:decimal">
                                                                  <xs:totalDigits value="18"/>
                                                                  <xs:fractionDigits value="5"/>
                                                               </xs:restriction>
                                                            </xs:simpleType>
                                                         </xs:element>
                                                         <xs:element name="currencyCode" type="xs:string" minOccurs="0">
                                                            <xs:annotation>
                                                               <xs:documentation>Currency of amount [CODELIST: Currency]</xs:documentation>
                                                            </xs:annotation>
                                                         </xs:element>
                                                      </xs:sequence>
                                                   </xs:complexType>
                                                </xs:element>
                                                <xs:element name="date" type="xs:date">
                                                   <xs:annotation>
                                                      <xs:documentation>First instalment date</xs:documentation>
                                                   </xs:annotation>
                                                </xs:element>
                                             </xs:sequence>
                                          </xs:complexType>
                                       </xs:element>
                                       <xs:element name="regularInstalmentAmount">
                                          <xs:annotation>
                                             <xs:documentation>Regular instalment amount</xs:documentation>
                                          </xs:annotation>
                                          <xs:complexType>
                                             <xs:sequence>
                                                <xs:element name="amount">
                                                   <xs:annotation>
                                                      <xs:documentation>Amount</xs:documentation>
                                                   </xs:annotation>
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:decimal">
                                                         <xs:totalDigits value="18"/>
                                                         <xs:fractionDigits value="5"/>
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="currencyCode" type="xs:string" minOccurs="0">
                                                   <xs:annotation>
                                                      <xs:documentation>Currency of amount [CODELIST: Currency]</xs:documentation>
                                                   </xs:annotation>
                                                </xs:element>
                                             </xs:sequence>
                                          </xs:complexType>
                                       </xs:element>
                                       <xs:element name="lastInstalmentAmount">
                                          <xs:annotation>
                                             <xs:documentation>Next instalment amount</xs:documentation>
                                          </xs:annotation>
                                          <xs:complexType>
                                             <xs:sequence>
                                                <xs:element name="amount">
                                                   <xs:annotation>
                                                      <xs:documentation>Amount</xs:documentation>
                                                   </xs:annotation>
                                                   <xs:simpleType>
                                                      <xs:restriction base="xs:decimal">
                                                         <xs:totalDigits value="18"/>
                                                         <xs:fractionDigits value="5"/>
                                                      </xs:restriction>
                                                   </xs:simpleType>
                                                </xs:element>
                                                <xs:element name="currencyCode" type="xs:string" minOccurs="0">
                                                   <xs:annotation>
                                                      <xs:documentation>Currency of amount [CODELIST: Currency]</xs:documentation>
                                                   </xs:annotation>
                                                </xs:element>
                                             </xs:sequence>
                                          </xs:complexType>
                                       </xs:element>
                                       <xs:element name="instalmentCount" type="xs:integer">
                                          <xs:annotation>
                                             <xs:documentation>A count of instalments</xs:documentation>
                                          </xs:annotation>
                                       </xs:element>
                                       <xs:element name="interestRate">
                                          <xs:annotation>
                                             <xs:documentation>Interest rate</xs:documentation>
                                          </xs:annotation>
                                          <xs:simpleType>
                                             <xs:restriction base="xs:decimal">
                                                <xs:totalDigits value="10"/>
                                                <xs:fractionDigits value="4"/>
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                       <xs:element name="rpsn">
                                          <xs:annotation>
                                             <xs:documentation>Roční procentní sazba nákladů. An equivalent for APR (Annual Percentage Rate) in Czech republic</xs:documentation>
                                          </xs:annotation>
                                          <xs:simpleType>
                                             <xs:restriction base="xs:decimal">
                                                <xs:totalDigits value="8"/>
                                                <xs:fractionDigits value="3"/>
                                             </xs:restriction>
                                          </xs:simpleType>
                                       </xs:element>
                                    </xs:sequence>
                                 </xs:complexType>
                              </xs:element>
                           </xs:sequence>
                        </xs:complexType>
                     </xs:element>
                  </xs:choice>
               </xs:complexType>
            </xs:element>
            <xs:element name="resultInfo" minOccurs="0">
               <xs:complexType>
                  <xs:sequence>
                     <xs:element name="resultItem" maxOccurs="unbounded">
                        <xs:complexType>
                           <xs:sequence>
                              <xs:element name="code" type="xs:string">
                                 <xs:annotation>
                                    <xs:documentation>A code of the error/warning [CODELIST: ERROR_CODES]</xs:documentation>
                                 </xs:annotation>
                              </xs:element>
                              <xs:element name="description" type="xs:string">
                                 <xs:annotation>
                                    <xs:documentation>A description of the error/warning</xs:documentation>
                                 </xs:annotation>
                              </xs:element>
                           </xs:sequence>
                        </xs:complexType>
                     </xs:element>
                  </xs:sequence>
               </xs:complexType>
            </xs:element>
         </xs:sequence>
      </xs:complexType>
   </xs:element>
</xs:schema>
