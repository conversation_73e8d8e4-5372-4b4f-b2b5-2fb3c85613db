<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/services/paymentCard/GetTransactionList_1_9"
    version="1.0.0"
    xmlns:opgettransactionlist_1_9="http://rb.cz/services/paymentCard/GetTransactionList_1_9" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="opGetTransactionList_1_9">
        <xs:complexType>
            <xs:sequence>
                <xs:element maxOccurs="1" minOccurs="0" name="paging">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="1" minOccurs="1"
                                name="startIndex" type="xs:integer">
                                <xs:annotation>
                                    <xs:documentation>Index of the first item within the returned collection. Default value is 1.</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="maxResults" type="xs:integer">
                                <xs:annotation>
                                    <xs:documentation>Maximal number of returned collection items.</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="sorting">
                    <xs:annotation>
                        <xs:documentation>Sorting parameters</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="cardTransactionItems">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="cardTransactionItem">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="nullValues">
                                    <xs:annotation>
                                    <xs:documentation>Parameter defines where should be null values sorted in the returned list - at the beggining or at the end. - Begin/ End [LOV: Begin;End]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="Begin"/>

                                    <xs:enumeration value="End"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="sortType">
                                    <xs:annotation>
                                    <xs:documentation>Parameter defines whether is the sorting type ascending or descending. [LOV: Ascending;Descending]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="Ascending"/>

                                    <xs:enumeration value="Descending"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="sortAttribute">
                                    <xs:annotation>
                                    <xs:documentation>Sorting attribute: SettDate, OrigAmount, SettAmount - CC transactions [LOV: SettDate;SettAmount;OrigAmount;TransactionDate]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="SettDate"/>

                                    <xs:enumeration value="SettAmount"/>

                                    <xs:enumeration value="OrigAmount"/>

                                    <xs:enumeration value="TransactionDate"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="1"
                    name="sumOfRecords" type="xs:boolean">
                    <xs:annotation>
                        <xs:documentation>Record count - flag indicating if record count should be returned or not. True = count of records and records. False or the element is not sent = only records.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element minOccurs="0" name="summtn" type="xs:boolean">
                    <xs:annotation>
                        <xs:documentation>Summation - Flag indicating if summation should be returned in the response</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="1" name="schgCrit">
                    <xs:annotation>
                        <xs:documentation>Searching criteria</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="1" minOccurs="1" name="cardTp">
                                <xs:annotation>
                                    <xs:documentation>Card type - credit, debit [LOV: Credit;Debit]</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:enumeration value="Credit"/>
                                    <xs:enumeration value="Debit"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="equaTransaction" type="xs:boolean">
                                <xs:annotation>
                                    <xs:documentation>Flag whether to return also transactions migrated from Equa bank</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="1" name="txItm">
                                <xs:annotation>
                                    <xs:documentation>Transaction item</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="acctNb">
                                    <xs:annotation>
                                    <xs:documentation>Account number of a payment card</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    minOccurs="0" name="nbPart1">
                                    <xs:annotation>
                                    <xs:documentation>numberPart1 - Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="nbPart2">
                                    <xs:annotation>
                                    <xs:documentation>numberPart2 - Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="bkCd" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>bank code - part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="ccyCd" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>currencyCode - (main) currency code of the bank account</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="cardId">
                                    <xs:annotation>
                                    <xs:documentation>Card identifier - If this parameter is empty, every card transaction for the mentioned account number will be returned</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="idCd">
                                    <xs:annotation>
                                    <xs:documentation>Identification code - a value of the identifier (type of the identifier is specified in the "Identification type" field)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="64"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="idTp">
                                    <xs:annotation>
                                    <xs:documentation>Identificatio type - the meaning of the "Identification code" [LOV: cardHash;coreId1;coreId2]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="cardHash"/>

                                    <xs:enumeration value="coreId1"/>

                                    <xs:enumeration value="coreId2"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="txDt">
                                    <xs:annotation>
                                    <xs:documentation>Transaction date - an interval when a transaction was realized</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="fr" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Transaction date from</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="to" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Transaction date to</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="txAmt">
                                    <xs:annotation>
                                    <xs:documentation>Transaction amount - an interval of an original amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="fr" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Transaction amount from</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="to" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Transaction amount to</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="ccyCds">
                                    <xs:annotation>
                                    <xs:documentation>Currency codes - currencies of original amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    name="ccyCd" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency code [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0"
                                    name="excl" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Exclude: False - The transactions are searched in the mentioned currency. True - The transactions are searched in the currencies which are not mentioned in this complex element.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="acctgAmt">
                                    <xs:annotation>
                                    <xs:documentation>Accounting amount - an interval of an accounted amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="fr" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Accounting amount from</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="to" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Accounting amount to</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="ccyCde" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency code [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="schTxt">
                                    <xs:annotation>
                                    <xs:documentation>Merchant description - description of a transaction</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="240"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="cdtCardTx">
                                    <xs:annotation>
                                    <xs:documentation>Credit card transaction - parameters for credit card transactions</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="acctgDt">
                                    <xs:annotation>
                                    <xs:documentation>Accounting date - an interval when a transaction was accounted</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="fr" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Accounting date from</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="to" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Accounting date to</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="txTps">
                                    <xs:annotation>
                                    <xs:documentation>Transaction types - a list of transaction types</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1"
                                    name="txTp" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Transaction type - a type of a credit card transaction - POS, ATM, ATM storno, etc. [CODELIST: CCMovementType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="isHold" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Flag whether to inlcude holds in the returned list. Not selected = return holds AND transactions. False = return ONLY transactions. True = return ONLY holds.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="instlmtPrgm" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Instalment program - information about an availability of an instalment program for a transaction: True - required; False or the element is not send - not required</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="txFltr">
                                    <xs:annotation>
                                    <xs:documentation>choice of account movement in witch TRS looking for data</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="tp">
                                    <xs:annotation>
                                    <xs:documentation>type of transaction by which to filter [LOV: MOVEMENT_TYPE;TRANSACTION_CATEGORY]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="MOVEMENT_TYPE"/>

                                    <xs:enumeration value="TRANSACTION_CATEGORY"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="cdes">
                                    <xs:annotation>
                                    <xs:documentation>list of transaction codes</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded" name="cde">
                                    <xs:annotation>
                                    <xs:documentation>transaction code</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="mrchntNm">
                                    <xs:annotation>
                                    <xs:documentation>Name of the merchant e.g. Tesco</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="note">
                                    <xs:annotation>
                                    <xs:documentation>A custom note provided by client to mark transaction with note.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="254"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="byPFMCtgy">
                                    <xs:annotation>
                                    <xs:documentation>Search by PFM category ID.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="1000"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="balRfrsh" type="xs:boolean">
                                <xs:annotation>
                                    <xs:documentation>Balance refresh: Y - this element manages refreshing a credit card balances and transactions/holds.</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element minOccurs="0" name="lvlOfDtl">
                    <xs:annotation>
                        <xs:documentation>Level of detail requested in response. Possible values: "full" (all attributes), "basic" (only attributes for simple list). If the value is not present in the request, then providing system by default returns data as for "full". [LOV: full;basic]</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:enumeration value="full"/>
                            <xs:enumeration value="basic"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="opGetTransactionList_1_9_res">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="paging">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element minOccurs="0" name="startIndex" type="xs:integer">
                                <xs:annotation>
                                    <xs:documentation>Index of the first item within the returned collection. Default value is 1</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element minOccurs="0" name="endIndex" type="xs:integer">
                                <xs:annotation>
                                    <xs:documentation>Index of the last item within the returned collection</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element minOccurs="0"
                                name="endOfRecords" type="xs:boolean">
                                <xs:annotation>
                                    <xs:documentation>Flag indicating end of the records</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0"
                    name="totalSumOfRecords" type="xs:integer">
                    <xs:annotation>
                        <xs:documentation>Number of a returned records</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element minOccurs="0" name="summtn">
                    <xs:annotation>
                        <xs:documentation>Summation - sums of amounts</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded" name="sum">
                                <xs:annotation>
                                    <xs:documentation>Sum of amounts</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="tp">
                                    <xs:annotation>
                                    <xs:documentation>Type - defines what transactions are added to the sum: "Credit" - contains sum of credit transactions (numebr 0 or greater), "Debit" - contains sum of debit transactions (number 0 or lower) [LOV: Credit;Debit]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:enumeration value="Credit"/>
                                    <xs:enumeration value="Debit"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="sum" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Sum of transaction amounts for respective type and currency</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="ccy" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of sum of transaction amounts
</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="cnt" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Count - count of transactions for respective type and currency</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="cardTx">
                    <xs:annotation>
                        <xs:documentation>Card transaction</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:choice>
                            <xs:element maxOccurs="1" minOccurs="1" name="cdtCardTxs">
                                <xs:annotation>
                                    <xs:documentation>Credit card transactions</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="cdtCardTx">
                                    <xs:annotation>
                                    <xs:documentation>Credit card transaction</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="txId">
                                    <xs:annotation>
                                    <xs:documentation>Transaction id - transaction identifier from Transaction system</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="pmryId" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Primary id - Transaction identifier from RPC</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="txCd" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Transaction code - Rubric code</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="txTp" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Credit card transaction type [CODELIST: CCMovementType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="txAmt" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Transaction amount - Original amount</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="txCcy" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Transaction currency - Currency of original amount [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="acctgAmt" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Accounting amount</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="acctgCcy" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of accounting amount [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="cardIds">
                                    <xs:annotation>
                                    <xs:documentation>Card identifiers</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="cardId">
                                    <xs:annotation>
                                    <xs:documentation>Card identifier</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="idCd">
                                    <xs:annotation>
                                    <xs:documentation>Identification code - a value of the identifier (type of the identifier is specified in the "Identification type" field)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="64"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="idTp">
                                    <xs:annotation>
                                    <xs:documentation>Identification type - the meaning of the "Identification code" [LOV: cardHash;coreId1;coreId2]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="cardHash"/>

                                    <xs:enumeration value="coreId1"/>

                                    <xs:enumeration value="coreId2"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="mskdNb" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Masked number - secret card number</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="txDt" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Transaction date - Date when the transaction has been realized by the customer.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="acctgDt" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Accounting date - Date when the transaction has been accounted.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="rfrshDt" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Refresh date - A date when available balance was refreshed</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="mrchntDesc">
                                    <xs:annotation>
                                    <xs:documentation>Merchant description - description of a transaction</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="2000"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="txCdDesc" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Transaction code description - description of a card hold code (description of a rubrick code)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="instlmtPrgm" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Instalment program: True - an instalment program for the transaction is available; False - not available</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="note">
                                    <xs:annotation>
                                    <xs:documentation>A custom note provided by client to mark transaction with note.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="254"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="ctgyPFM">
                                    <xs:annotation>
                                    <xs:documentation>PFM transaction category name.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="ctgyPFMId" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>PFM transaction category ID.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="direction" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Direction of transaction - Debit/Credit</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="shopId">
                                    <xs:annotation>
                                    <xs:documentation>Unique id of the shop.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="23"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="1" name="dbtCardTxs">
                                <xs:annotation>
                                    <xs:documentation>Debit card transactions</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="dbtCardTx">
                                    <xs:annotation>
                                    <xs:documentation>Debit card transaction</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="mvmntId">
                                    <xs:annotation>
                                    <xs:documentation>Identification of a transaction for maintenance ('E' = Equa, without prefix = TS)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="txCtgy" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Transaction type according to MCH codelist TransactionCategory</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="mvmntTp" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Transaction type according to MCH codelist MovementType</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="txAmt" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Transaction amount - original amount</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="txCcy" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Transaction currency - currency of original amount [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="migAcctNb">
                                    <xs:annotation>
                                    <xs:documentation>Migrated bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="acctgAmt" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Accounting amount</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="acctgCcy" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Accounting currency - currency of accounting amount [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="cardIds">
                                    <xs:annotation>
                                    <xs:documentation>Card identifiers</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="cardId">
                                    <xs:annotation>
                                    <xs:documentation>Card identifier</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="idCd">
                                    <xs:annotation>
                                    <xs:documentation>Identification code - a value of the identifier (type of the identifier is specified in the "Identification type" field)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="64"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="idTp">
                                    <xs:annotation>
                                    <xs:documentation>Identification type - the meaning of the "Identification code" [LOV: cardHash;coreId1;coreId2]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="cardHash"/>

                                    <xs:enumeration value="coreId1"/>

                                    <xs:enumeration value="coreId2"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="mskdNb" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Masked number - secret card number</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="embsdNm">
                                    <xs:annotation>
                                    <xs:documentation>Embossed name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="25"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="txDt" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Transaction date - date when the transaction has been realized by the customer</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="acctgDt" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Accounting date - date when the transaction has been accounted.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="mrchntDesc">
                                    <xs:annotation>
                                    <xs:documentation>Merchant description - description of transaction</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="2000"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="authstnCd">
                                    <xs:annotation>
                                    <xs:documentation>Authorisation code</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="note">
                                    <xs:annotation>
                                    <xs:documentation>A custom note provided by client to mark transaction with note.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="254"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="ctgyPFM">
                                    <xs:annotation>
                                    <xs:documentation>PFM transaction category name.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="ctgyPFMId" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>PFM transaction category ID.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="shopId">
                                    <xs:annotation>
                                    <xs:documentation>Unique id of the shop.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="23"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:choice>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
