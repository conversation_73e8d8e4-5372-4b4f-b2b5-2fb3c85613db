<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/services/entityService/paymentCard/CreatePreApp_1_0"
    version="1.0.0"
    xmlns:opcreatepreapp_1_0="http://rb.cz/services/entityService/paymentCard/CreatePreApp_1_0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="opCreatePreApp_1_0">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="preApplication">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element minOccurs="0" name="offerId">
                                <xs:annotation>
                                    <xs:documentation>Unique identification of an offer</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="512"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="partyId">
                                <xs:annotation>
                                    <xs:documentation>Party Id (Siebel identifier)</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element minOccurs="1" name="requestedAmount">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="amount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Amount requested by client.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of amount [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="opCreatePreApp_1_0_res">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="1" name="applicationId">
                    <xs:annotation>
                        <xs:documentation>Unique identification of an application</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:maxLength value="30"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
