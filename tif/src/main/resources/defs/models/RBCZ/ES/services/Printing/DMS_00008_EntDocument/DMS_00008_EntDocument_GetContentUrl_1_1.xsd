<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/services/entityService/document/GetContentUrl_1_1"
    version="1.0.0"
    xmlns:opgetcontenturl_1_1="http://rb.cz/services/entityService/document/GetContentUrl_1_1" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="opGetContentUrl_1_1">
        <xs:complexType>
            <xs:sequence>
                <xs:element maxOccurs="1" minOccurs="1" name="documentID">
                    <xs:annotation>
                        <xs:documentation>The unique identifier of the document assigned by the system Documentum </xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:maxLength value="16"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="versionID">
                    <xs:annotation>
                        <xs:documentation>Document version identifier</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:maxLength value="32"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="contentFormat">
                    <xs:annotation>
                        <xs:documentation>File type extension to identify the content of the document format (doc, pdf, xls, ...)</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:maxLength value="400"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="signed" type="xs:boolean">
                    <xs:annotation>
                        <xs:documentation>Flag indicating if electronic signature is required. </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0"
                    name="documentMetadataRequested" type="xs:boolean">
                    <xs:annotation>
                        <xs:documentation>True - additional metadata (except metadata included directly in response root) are requested and will be returned in response in complex additionalMetadata; 
False or NULL - additional metadata (except metadata included directly in response root) are not requested and complex additionalMetadata will not be returned;</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="opGetContentUrl_1_1_res">
        <xs:complexType>
            <xs:sequence>
                <xs:element maxOccurs="1" minOccurs="1" name="documentContentUrl">
                    <xs:annotation>
                        <xs:documentation>Document content URI stored on shared storage</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:maxLength value="2048"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="documentMetadata">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="1" minOccurs="1" name="documentId">
                                <xs:annotation>
                                    <xs:documentation>The unique identifier of the document assigned by the system Documentum (dctmId)</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="16"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="externalId">
                                <xs:annotation>
                                    <xs:documentation>External identification of the document. Unique ID generated by other application then DMS (documentId in DMS). In case of no externalId exist the document is identified by internal DMS ID (dctmId)</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="16"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="1"
                                name="documentClass" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>[CODELIST: DocumentManagementDocumentClass]</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="channelCode" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Identification of the channel which has been request sent. [CODELIST: CHANNEL_CODES]</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="metadata">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="documentName">
                                    <xs:annotation>
                                    <xs:documentation>Name of the document</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="53"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="parties">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="party">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="role">
                                    <xs:annotation>
                                    <xs:documentation>[LOV: Client;Representative]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="Client"/>

                                    <xs:enumeration value="Representative"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="partyId">
                                    <xs:annotation>
                                    <xs:documentation>Party Id (Siebel identifier)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="partyMDItems">
                                    <xs:complexType>

                                    <xs:sequence
                                    maxOccurs="unbounded" minOccurs="1">
                                    <xs:choice>

                                    <xs:element name="item">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="code">
                                    <xs:annotation>
                                    <xs:documentation>[LOV: birthCode;clientMiddleName;clientName;clientRemark;clientSurname;companyId;companyName;midasNumber;positionType]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="birthCode"/>

                                    <xs:enumeration value="clientMiddleName"/>

                                    <xs:enumeration value="clientName"/>

                                    <xs:enumeration value="clientRemark"/>

                                    <xs:enumeration value="clientSurname"/>

                                    <xs:enumeration value="companyId"/>

                                    <xs:enumeration value="companyName"/>

                                    <xs:enumeration value="midasNumber"/>

                                    <xs:enumeration value="positionType"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="value" type="xs:string"/>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:choice>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="generic">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="unbounded" minOccurs="1">
                                    <xs:choice>
                                    <xs:element name="item">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="code">
                                    <xs:annotation>
                                    <xs:documentation>[LOV: accountNumber;agreementNumber;author;cardNumber;contentFormat;documentState;documentTypeCode;documentTypeName;esdoPlace;esdoPlaceType;esdoState;evidenceGroup;externalCreator;localId;newDocumentState;productGroup;template;validState;versionId;applicationId;loanId]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="accountNumber"/>

                                    <xs:enumeration value="agreementNumber"/>

                                    <xs:enumeration value="author"/>

                                    <xs:enumeration value="cardNumber"/>

                                    <xs:enumeration value="contentFormat"/>

                                    <xs:enumeration value="documentState"/>

                                    <xs:enumeration value="documentTypeCode"/>

                                    <xs:enumeration value="documentTypeName"/>

                                    <xs:enumeration value="esdoPlace"/>

                                    <xs:enumeration value="esdoPlaceType"/>

                                    <xs:enumeration value="esdoState"/>

                                    <xs:enumeration value="evidenceGroup"/>

                                    <xs:enumeration value="externalCreator"/>

                                    <xs:enumeration value="localId"/>

                                    <xs:enumeration value="newDocumentState"/>

                                    <xs:enumeration value="productGroup"/>

                                    <xs:enumeration value="template"/>

                                    <xs:enumeration value="validState"/>

                                    <xs:enumeration value="versionId"/>

                                    <xs:enumeration value="applicationId"/>

                                    <xs:enumeration value="loanId"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="value" type="xs:string"/>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="dateitem">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="code">
                                    <xs:annotation>
                                    <xs:documentation>[LOV: creationDate;dateOfLastChange;expiryDate;expiryDateOfCertificate;globalCreationDate;readDate]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="creationDate"/>

                                    <xs:enumeration value="dateOfLastChange"/>

                                    <xs:enumeration value="expiryDate"/>

                                    <xs:enumeration value="expiryDateOfCertificate"/>

                                    <xs:enumeration value="globalCreationDate"/>

                                    <xs:enumeration value="readDate"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="value" type="xs:dateTime"/>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="boolItem">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="code">
                                    <xs:annotation>
                                    <xs:documentation>[LOV: validFlag;isPrinted]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="validFlag"/>

                                    <xs:enumeration value="isPrinted"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="value" type="xs:boolean"/>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:choice>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
