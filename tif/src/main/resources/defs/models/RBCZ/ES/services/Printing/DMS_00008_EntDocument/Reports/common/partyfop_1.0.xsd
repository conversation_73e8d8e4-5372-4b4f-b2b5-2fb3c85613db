<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/dms/dds/partydata/partyfop/1.0"
    xmlns="http://rb.cz/dms/dds/partydata/partyfop/1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="partyfop">
        <xs:complexType>
            <xs:sequence maxOccurs="1" minOccurs="1">
                <xs:element maxOccurs="1" minOccurs="1" name="client">
                    <xs:complexType>
                        <xs:sequence maxOccurs="1" minOccurs="1">
                            <xs:element maxOccurs="1" minOccurs="1"
                                name="partyId" nillable="false">
                                <xs:annotation>
                                    <xs:documentation>Unique identification of the party</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="1" name="commercial">
                                <xs:complexType>
                                    <xs:sequence maxOccurs="1" minOccurs="1">
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="companyName" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Company name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="taxId" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>A Value added tax identification number - i.e. "DIČ"</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="registrationNumber" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Company identification number - i.e. "IČ"</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="country" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>country</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="legalForm" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Commercial client Legal Forms</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="dateOfEstablishment"
                                    nillable="false" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Date of establishment</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="taxationResidency" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>A taxation residency</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="birthInfo">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="birthCode" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Czech Birth Code - only for Czech citizens</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="20"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="birthDate"
                                    nillable="false" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Date of birth</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="birthPlace" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Place of birth</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="cityOfBirth"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>City of birth</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="countryOfBirth"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Country of birth</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="futureProductOwner" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Future product owner</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="productOwner"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Product owner</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="contacts">
                                <xs:complexType>
                                    <xs:sequence maxOccurs="1" minOccurs="1">
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="addresses">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="prefferedAddressId"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Preferred address ID</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="address">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="srcReferenceId" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>A unique ID of the address</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="type" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Mail Address type (Residency or Mail1-9) [CODELIST: GENERAL_MAILTYPE]
 [CODELIST: GENERAL_MAILTYPE]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="preferred"
                                    nillable="false" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Preferred address Y/N</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="addressee"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A mail addressee</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="street"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A street name</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="number"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A building number/street number</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="part"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A part of the city</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="city"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A city name</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="postalCode"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A ZIP code</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="country" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>[CODELIST: GENERAL_CNTR]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="classification" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A classification - home/work [CODELIST: UNI_CONTCLASSIF]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="additionalInfo"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Additional Info</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="phones">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="phone">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="srcReferenceId" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>A unique ID of the contact</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="name" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>User definable contact name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="type" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Siebel technical field differentiating records in the same physical database table (LANDLINE_NUMBER, MOBILE_NUMBER, FAX_NUMBER, EMAIL_ADDRESS) [CODELIST: RB_COMM_MEDIUM_TYPE] [CODELIST: RB_COMM_MEDIUM_TYPE]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="preferred"
                                    nillable="false" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>A preferred contact</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="number" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>A contact number.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="numberType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Contact number type (FAX1.., MOBILE1.., PHONE1..)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="classification" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A classification - home/work [CODELIST: UNI_CONTCLASSIF]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="comment" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>A contact comment</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="undeliverableFlag"
                                    nillable="false" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Undeliverable flag</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="emails">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="email">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="srcReferenceId" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>A unique ID of the contact</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="name" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>User definable contact name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="type" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Siebel technical field differentiating records in the same physical database table (LANDLINE_NUMBER, MOBILE_NUMBER, FAX_NUMBER, EMAIL_ADDRESS) [CODELIST: RB_COMM_MEDIUM_TYPE] [CODELIST: RB_COMM_MEDIUM_TYPE]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="preferred"
                                    nillable="false" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>A preferred contact</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="address" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>A contact address (email, URL ..)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="100"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="emailType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Email type (EMAIL1....)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="classification" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A classification - home/work [CODELIST: UNI_CONTCLASSIF]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="comment" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>A contact comment</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="undeliverableFlag"
                                    nillable="false" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Undeliverable flag</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="enterpreneur">
                                <xs:complexType>
                                    <xs:sequence maxOccurs="1" minOccurs="1">
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="partyId" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Party Id (Siebel identifier)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="firstName" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>First name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="80"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="middleNames" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Middle name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="80"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="lastName" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Last name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="100"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="titleBefore" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Titles in front of the name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="titleBehind" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Titles behind the name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="gender" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Gender</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="citizenship" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Citizenship</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="birthInfo">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="birthCode" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Czech Birth Code - only for Czech citizens</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="20"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="birthDate"
                                    nillable="false" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Date of birth</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="birthPlace" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Place of birth</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="cityOfBirth"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>City of birth</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="countryOfBirth"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Country of birth</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="productOwner"
                                    nillable="false" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Product owner</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="futureProductOwner"
                                    nillable="false" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Future product owner</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="amlPEPFlg" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Flag, if person is politicaly exposed</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="amlPEPCheck">
                                    <xs:annotation>
                                    <xs:documentation>Confirmation of AML PEP flag. (SBL LOV RB_AML_PEP_CHECK contained values "Confirmed; Disclaimed"</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="consents">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="consent">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    name="consentPurpose" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Suffix of LIC code RB_CONSENTS_PURPOSE (because it consist of two = prefix and suffix which identifies
the concrete purpose) [CODELIST: ConsentPurpose]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="consentCategory" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Prefix of LIC code RB_CONSENTS_PURPOSE (because it consist of two = prefix and suffix which identifies
the concrete purpose) [CODELIST: ConsentCategory]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="status" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Status of the consent (approved, not approved,...) [CODELIST: ConsentStatus]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="sourceSystem" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Code of the source system, where the consent has been collected 
(branch office, internet banking, smartphone application,...). Usefull if we need to find out when and
where consent has been completed. [CODELIST: Component]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="dateOfLastUpdate" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Contains date and time of the last update of a complex element consent</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="contacts">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="addresses">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="prefferedAddressId"
                                    nillable="false" type="xs:string"/>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="address">
                                    <xs:complexType>

                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="srcReferenceId" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>A unique ID of the address</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="type" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Mail Address type (Residency or Mail1-9) [CODELIST: GENERAL_MAILTYPE]
 [CODELIST: GENERAL_MAILTYPE]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="preferred"
                                    nillable="false" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Preferred address Y/N</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="addressee"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A mail addressee</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="street"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A street name</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="number"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A building number/street number</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="part"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A part of the city</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="city"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A city name</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="postalCode"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A ZIP code</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="country" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Country [CODELIST: GENERAL_CNTR]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="classification" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A classification - home/work [CODELIST: UNI_CONTCLASSIF]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="additionalInfo"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Additional info</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="phones">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="phone">
                                    <xs:complexType>

                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="srcReferenceId" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>A unique ID of the contact</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="name" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>User definable contact name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="type" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Siebel technical field differentiating records in the same physical database table (LANDLINE_NUMBER, MOBILE_NUMBER, FAX_NUMBER, EMAIL_ADDRESS) [CODELIST: RB_COMM_MEDIUM_TYPE] [CODELIST: RB_COMM_MEDIUM_TYPE]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="preferred"
                                    nillable="false" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>A preferred contact</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="number" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>A contact number.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="numberType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Contact number type (FAX1.., MOBILE1.., PHONE1..)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="classification" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A classification - home/work [CODELIST: UNI_CONTCLASSIF]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="comment" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>A contact comment</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="undeliverableFlag"
                                    nillable="false" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Undeliverable flag</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="emails">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="email">
                                    <xs:complexType>

                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="srcReferenceId" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>A unique ID of the contact</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="name" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>User definable contact name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="type" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Siebel technical field differentiating records in the same physical database table (LANDLINE_NUMBER, MOBILE_NUMBER, FAX_NUMBER, EMAIL_ADDRESS) [CODELIST: RB_COMM_MEDIUM_TYPE] [CODELIST: RB_COMM_MEDIUM_TYPE]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="preferred"
                                    nillable="false" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>A preferred contact</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="address" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>A contact address (email, URL ..)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="100"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="emailType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Email type (EMAIL1....)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="classification" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A classification - home/work [CODELIST: UNI_CONTCLASSIF]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="comment" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>A contact comment</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="undeliverableFlag"
                                    nillable="false" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Undeliverable flag</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="documents">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="document">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="documentId"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Document identifier</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="documentType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Identification Document type [CODELIST: UNI_PIDOCT]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="validity">
                                    <xs:complexType>

                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="validFrom"
                                    nillable="false" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Duration from date</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="validTo"
                                    nillable="false" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Duration to date</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="issuer"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Issuer</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="issuerCountry" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Country of issuer [CODELIST: GENERAL_CNTR]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="unacceptable"
                                    nillable="false" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Unacceptable flag</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="preferredDocument" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Preferred document</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="taxResidencies">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="taxResidency">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="taxationResidency" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>A taxation residency</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="taxIdentificationNumber" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>A taxation identification number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="taxResidencies">
                                <xs:complexType>
                                    <xs:sequence maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="taxResidency">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="taxationResidency" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>A taxation residency</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="taxIdentificationNumber" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>A taxation identification number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="employee">
                    <xs:complexType>
                        <xs:sequence maxOccurs="1" minOccurs="1">
                            <xs:element maxOccurs="1" minOccurs="1"
                                name="staffId" nillable="false">
                                <xs:annotation>
                                    <xs:documentation>Employee id of the person who has opened the investment ID</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="1"
                                name="firstName" nillable="false">
                                <xs:annotation>
                                    <xs:documentation>First name</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="80"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="1"
                                name="lastName" nillable="false">
                                <xs:annotation>
                                    <xs:documentation>Last name</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="100"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="jobDescription" nillable="false">
                                <xs:annotation>
                                    <xs:documentation>Description of job</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="75"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="organizationUnit">
                                <xs:complexType>
                                    <xs:sequence maxOccurs="1" minOccurs="1">
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="name" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Name of organization</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="100"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="department" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Department name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="residence">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="street" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Name of the street</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="200"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="number" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Street number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="part" nillable="false">
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="city" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Name of the city</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="200"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="postalCode" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Postal code</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="country" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Name of the country</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="200"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="brand" nillable="false">
                    <xs:annotation>
                        <xs:documentation>type of graffic style [LOV: FWR;RB]</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:enumeration value="FWR"/>
                            <xs:enumeration value="RB"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0"
                    name="operationType" nillable="false">
                    <xs:annotation>
                        <xs:documentation>type of the operation - new/update [LOV: New;Update] [LOV: New;Update]</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:enumeration value="New"/>
                            <xs:enumeration value="Update"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0"
                    name="authorizationToken" nillable="false" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Authorization token</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="technicalMetadata">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element minOccurs="0" name="documentId">
                                <xs:annotation>
                                    <xs:documentation>The unique identifier of the document assigned by the system Documentum (dctmId)</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="16"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element minOccurs="0" name="externalDocumentId">
                                <xs:annotation>
                                    <xs:documentation>External identification of the document. Unique ID generated by other application then DMS (documentId in DMS). In case of no externalId exist the document is identified by internal DMS ID (dctmId)</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="16"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element minOccurs="0" name="delivery">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element minOccurs="1"
                                    name="deliveryChannel" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>delivery channel of the document [CODELIST: FavouredConfirmationDeliveryChannel]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:choice minOccurs="0">
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1"
                                    name="email" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>E-mail address of the report recipient</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    name="fax" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>fax of the report recipient</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1" name="addresses">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="address">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    minOccurs="0" name="addressee">
                                    <xs:annotation>
                                    <xs:documentation>Mail addressee</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="additionalInfo">
                                    <xs:annotation>
                                    <xs:documentation>Additional address info</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="street">
                                    <xs:annotation>
                                    <xs:documentation>Street name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="200"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="number">
                                    <xs:annotation>
                                    <xs:documentation>Building number/street number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="city">
                                    <xs:annotation>
                                    <xs:documentation>City name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="postalCode">
                                    <xs:annotation>
                                    <xs:documentation>ZIP code</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0"
                                    name="country" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>[CODELIST: GENERAL_CNTR]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:choice>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element minOccurs="0"
                                name="reportLanguage" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>language of the report, default is czech [CODELIST: LanguageDMS]</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element minOccurs="0" name="fileName" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>name of the generated report</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="fileFormatProperties">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="fileType">
                                    <xs:annotation>
                                    <xs:documentation>Statement file type

abo (1 | 2) (XX | AV) ( c | n) 
abo (1 = ediční, 2 = vnitřní ) (XX = není AV, AV = je AV) 
(c = v Doplňující údaj je Counter Acc Name | n = v Doplňující údaj je Note ) 
abo1XXc ABO (ediční formát) bez AV pole s názvem protistrany (v poli Doplňující údaj) 
abo1XXn ABO (ediční formát) bez AV pole s poznámkou (v poli Doplňující údaj) 
abo2XXc ABO (vnitřní formát) bez AV pole s názvem protistrany (v poli Doplňující údaj) 
abo2XXn ABO (vnitřní formát) bez AV pole s poznámkou (v poli Doplňující údaj) 
abo1AVc ABO (ediční formát) s AV polem s názvem protistrany (v poli Doplňující údaj) 
abo1AVn ABO (ediční formát) s AV polem s poznámkou (v poli Doplňující údaj) 
abo2AVc ABO (vnitřní formát) s AV polem s názvem protistrany (v poli Doplňující údaj) 
abo2AVn ABO (vnitřní formát) s AV polem s poznámkou (v poli Doplňující údaj)  [LOV: abo1XXc;abo1XXn;abo2XXc;abo2XXn;abo1AVc;abo1AVn;abo2AVc;abo2AVn;csv;gemini1;gemini2;jpg;pdf;PDFA-1a;PDFA-1b;xml;quest]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:enumeration value="abo1XXc"/>
                                    <xs:enumeration value="abo1XXn"/>
                                    <xs:enumeration value="abo2XXc"/>
                                    <xs:enumeration value="abo2XXn"/>
                                    <xs:enumeration value="abo1AVc"/>
                                    <xs:enumeration value="abo1AVn"/>
                                    <xs:enumeration value="abo2AVc"/>
                                    <xs:enumeration value="abo2AVn"/>
                                    <xs:enumeration value="csv"/>
                                    <xs:enumeration value="gemini1"/>
                                    <xs:enumeration value="gemini2"/>
                                    <xs:enumeration value="jpg"/>
                                    <xs:enumeration value="pdf"/>
                                    <xs:enumeration value="PDFA-1a"/>
                                    <xs:enumeration value="PDFA-1b"/>
                                    <xs:enumeration value="xml"/>
                                    <xs:enumeration value="quest"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="addParams">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="addParam">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="code">
                                    <xs:annotation>
                                    <xs:documentation>[LOV: dpi]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="dpi"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="value" type="xs:string"/>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element minOccurs="0" name="attachments">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="attachment">
                                    <xs:annotation>
                                    <xs:documentation>list of possible attachments for a reprt</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="type">
                                    <xs:annotation>
                                    <xs:documentation>any document can be attached based on the documentId or an url (url link to open the document in DMS) [LOV: documentId;url]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="documentId"/>

                                    <xs:enumeration value="url"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="value" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>value of documentId or url</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="documentName" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>name of the attachment</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="place" nillable="false">
                                <xs:annotation>
                                    <xs:documentation>City of the branch where the report is generated</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="80"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="timestamp">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="reportDate"
                                    nillable="false" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Date on which this report is generated</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="reportTime"
                                    nillable="false" type="xs:time">
                                    <xs:annotation>
                                    <xs:documentation>Time on which this report is generated</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
