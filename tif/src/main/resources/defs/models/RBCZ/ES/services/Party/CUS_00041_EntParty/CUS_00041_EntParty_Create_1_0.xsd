<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/services/party/Create_1_0/1.0"
    version="1.0.0"
    xmlns:opcreate_1_0="http://rb.cz/services/party/Create_1_0/1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="opCreate_1_0">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="skipIdentification" type="xs:boolean">
                    <xs:annotation>
                        <xs:documentation>True - finding and identification of best existing party record is skipped, and a new party records is forced to create.
False or not exist - there is attempt to find and identify the best party record for given criteria, if found - its partyId is returned and new record is not created. If not found - a new party records will be created.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="basicInfo">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="1" minOccurs="0" name="partyName">
                                <xs:annotation>
                                    <xs:documentation>Composed Name (full name)</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="256"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="partyType">
                                <xs:annotation>
                                    <xs:documentation>Party type - Private or Commercial</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element minOccurs="0" name="isClient" type="xs:boolean">
                                <xs:annotation>
                                    <xs:documentation>Party is client (product owner)</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element minOccurs="0" name="isVIP" type="xs:boolean">
                                <xs:annotation>
                                    <xs:documentation>Party is VIP</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="marketingConsent" type="xs:boolean">
                                <xs:annotation>
                                    <xs:documentation>The flag which indikates, if there is an approval for marketing and other interations with this party 
It is the main one and it could be divided into many partial approvals" </xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="customerSince" type="xs:date">
                                <xs:annotation>
                                    <xs:documentation>Customer since date</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="preferredLanguage" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Preferred language [CODELIST: Language]</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:choice>
                                <xs:element name="privateParty">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="titlesBefore">
                                    <xs:annotation>
                                    <xs:documentation>Titles in front of the name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="firstName">
                                    <xs:annotation>
                                    <xs:documentation>First name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="middleNames">
                                    <xs:annotation>
                                    <xs:documentation>Set of Middle names</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="lastName">
                                    <xs:annotation>
                                    <xs:documentation>Last name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="birthLastName">
                                    <xs:annotation>
                                    <xs:documentation>Birth last name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="titlesBehind">
                                    <xs:annotation>
                                    <xs:documentation>Titles behind the name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="vocative">
                                    <xs:annotation>
                                    <xs:documentation>Vocative - 5. case (Citizenship AND Prefered language = "Czech") (for example Nováku, Gábo, Jarolíme, Klápště) ; in other case Last name (McGregor, Campbell); Calculated</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="gender" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Gender [CODELIST: PRV_GENDER]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="title">
                                    <xs:annotation>
                                    <xs:documentation>Title - Calculated from Gender</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="birthInfo">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="birthDate" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Date of birth</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="birthCode">
                                    <xs:annotation>
                                    <xs:documentation>Czech Birth Code - only for Czech citizens</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="11"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="phoneyBirthCode">
                                    <xs:annotation>
                                    <xs:documentation>Phoney birth code - code used like RČ in case when RČ is not available (e .g. person is not Czech or Slovak citizen and has not have czech birth code)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="countryOfBirth" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Country of birth [CODELIST: GENERAL_CNTR]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="cityOfBirth">
                                    <xs:annotation>
                                    <xs:documentation>City of birth</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="citizenship" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Citizenship [CODELIST: GENERAL_CNTR]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="secondaryCitizenships">
                                    <xs:annotation>
                                    <xs:documentation>Secondary citizenships</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="citizenship">
                                    <xs:annotation>
                                    <xs:documentation>Secondary citizenship [CODELIST: GENERAL_CNTR]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="pepFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Politically exposed person flag</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="taxResidency" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Personal income tax and other taxes [CODELIST: GENERAL_CNTR]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element name="commercialParty">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="companyName">
                                    <xs:annotation>
                                    <xs:documentation>Company name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="companyNameSuffix">
                                    <xs:annotation>
                                    <xs:documentation>Used for additional text printed in the contract after the company name and residence</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="taxId">
                                    <xs:annotation>
                                    <xs:documentation>Tax identification number - i.e. "DIČ"</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="registrationNumber">
                                    <xs:annotation>
                                    <xs:documentation>Company identification number - i.e. "IČ"</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="phoneyRegistrationNumber" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Pseudo registration number</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="legalForm" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Legal Form [CODELIST: ACC_LEGFORM]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="legalStatus" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Basic legal form [CODELIST: ACC_BLEGFORM]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="registeredCompanyName">
                                    <xs:annotation>
                                    <xs:documentation>Company name registered - received from Albertina system</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="registeredCountry" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Country of commercial registration [CODELIST: GENERAL_CNTR]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="selfEmployedPerson">
                                    <xs:annotation>
                                    <xs:documentation>Persons details for FOP</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="birthInfo">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="birthDate" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Date of birth</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="birthCode">
                                    <xs:annotation>
                                    <xs:documentation>Czech Birth Code - only for Czech citizens</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="11"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0" name="phoneyBirthCode">
                                    <xs:annotation>
                                    <xs:documentation>Phoney birth code - code used like RČ in case when RČ is not available (e .g. person is not Czech or Slovak citizen and has not have czech birth code)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="countryOfBirth" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Country of birth [CODELIST: GENERAL_CNTR]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="cityOfBirth">
                                    <xs:annotation>
                                    <xs:documentation>City of birth</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                            </xs:choice>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="contacts">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="1" minOccurs="0" name="addresses">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="address">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    minOccurs="0" name="srcReferenceId">
                                    <xs:annotation>
                                    <xs:documentation>Unique identification of the address</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="addressName">
                                    <xs:annotation>
                                    <xs:documentation>User definable Mail Address name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="addressType">
                                    <xs:annotation>
                                    <xs:documentation>Mail address type (Residency or Mail1-9) [CODELIST: GENERAL_MAILTYPE]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="1" name="addressee">
                                    <xs:annotation>
                                    <xs:documentation>Mail addressee</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="additionalInfo">
                                    <xs:annotation>
                                    <xs:documentation>Additional address info</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="street">
                                    <xs:annotation>
                                    <xs:documentation>Street name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="200"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="1" name="number">
                                    <xs:annotation>
                                    <xs:documentation>Building number/street number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="city">
                                    <xs:annotation>
                                    <xs:documentation>City name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="postalCode">
                                    <xs:annotation>
                                    <xs:documentation>ZIP code</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="country">
                                    <xs:annotation>
                                    <xs:documentation>Country [CODELIST: GENERAL_CNTR]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="preferred" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Preferred address Y/N</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="classification">
                                    <xs:annotation>
                                    <xs:documentation>Classification - home/work [CODELIST: PRV_CONTCLASSIF]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="comment">
                                    <xs:annotation>
                                    <xs:documentation>Mail address comment</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="phones">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="phone">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    minOccurs="0" name="srcReferenceId">
                                    <xs:annotation>
                                    <xs:documentation>Unique identification of the entity in source system</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="contactName">
                                    <xs:annotation>
                                    <xs:documentation>User definable contact name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="contactType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Siebel technical field differentiating records in the same physical database table (LANDLINE_NUMBER, MOBILE_NUMBER, FAX_NUMBER, EMAIL_ADDRESS) [CODELIST: RB_COMM_MEDIUM_TYPE]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="number">
                                    <xs:annotation>
                                    <xs:documentation>A contact number.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="preferred" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>A preferred contact</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="classification">
                                    <xs:annotation>
                                    <xs:documentation>A classification - home/work [CODELIST: PRV_CONTCLASSIF]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="comment">
                                    <xs:annotation>
                                    <xs:documentation>A contact comment</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="emails">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="email">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    minOccurs="0" name="srcReferenceId">
                                    <xs:annotation>
                                    <xs:documentation>Unique identification of the entity in source system</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="contactName">
                                    <xs:annotation>
                                    <xs:documentation>User definable contact name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="contactType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Siebel technical field differentiating records in the same physical database table (LANDLINE_NUMBER, MOBILE_NUMBER, FAX_NUMBER, EMAIL_ADDRESS) [CODELIST: RB_COMM_MEDIUM_TYPE]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="address">
                                    <xs:annotation>
                                    <xs:documentation>A contact address (email, URL ..)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="100"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="preferred" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>A preferred contact</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="classification">
                                    <xs:annotation>
                                    <xs:documentation>A classification - home/work [CODELIST: PRV_CONTCLASSIF]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="comment">
                                    <xs:annotation>
                                    <xs:documentation>A contact comment</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="urls">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="url">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    minOccurs="0" name="srcReferenceId">
                                    <xs:annotation>
                                    <xs:documentation>Unique identification of the entity in source system</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="contactName">
                                    <xs:annotation>
                                    <xs:documentation>User definable contact name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="90"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="contactType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Siebel technical field differentiating records in the same physical database table (LANDLINE_NUMBER, MOBILE_NUMBER, FAX_NUMBER, EMAIL_ADDRESS) [CODELIST: RB_COMM_MEDIUM_TYPE]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="address">
                                    <xs:annotation>
                                    <xs:documentation>A contact address (email, URL ..)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="100"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="preferred" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>A preferred contact</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="classification" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A classification - home/work [CODELIST: PRV_CONTCLASSIF]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="comment">
                                    <xs:annotation>
                                    <xs:documentation>A contact comment</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="documents">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded"
                                minOccurs="1" name="document">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element minOccurs="0" name="srcReferenceId">
                                    <xs:annotation>
                                    <xs:documentation>Unique identification of the entity in source system</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="documentId">
                                    <xs:annotation>
                                    <xs:documentation>Document identifier</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="documentType">
                                    <xs:annotation>
                                    <xs:documentation>Identification Document type [CODELIST: UNI_PIDOCT]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="validity">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    minOccurs="0"
                                    name="validFrom" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Valid from date</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="validTo" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Valid to date</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="issuer">
                                    <xs:annotation>
                                    <xs:documentation>Issuer</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="100"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="issuerCountry">
                                    <xs:annotation>
                                    <xs:documentation>Country of issuer [CODELIST: GENERAL_CNTR]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="primaryDocument" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Primary document flag</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="comment">
                                    <xs:annotation>
                                    <xs:documentation>Comment</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="networkBank">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="nwbCustomerId">
                                <xs:annotation>
                                    <xs:documentation>NWB_ID of party</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="15"/>
                                    <xs:minLength value="1"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="networkBankId">
                                <xs:annotation>
                                    <xs:documentation>"TBSK" ... etc.</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:minLength value="1"/>
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="segmentNwb">
                                <xs:annotation>
                                    <xs:documentation>Segment NWB</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:minLength value="1"/>
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0"
                    name="registryCheck" type="xs:boolean">
                    <xs:annotation>
                        <xs:documentation>Simulates pressing the button "Registers" to pass the eligibility check if requested.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="opCreate_1_0_res">
        <xs:complexType>
            <xs:sequence>
                <xs:element maxOccurs="1" minOccurs="1" name="partyId">
                    <xs:annotation>
                        <xs:documentation>Party Id (Siebel identifier)</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:maxLength value="15"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="created" type="xs:dateTime"/>
                <xs:element maxOccurs="1" minOccurs="0"
                    name="isIdentified" type="xs:boolean">
                    <xs:annotation>
                        <xs:documentation>Technical flag (WMB logic) - if MDC return PartyID (MDC found partyID by requested data from consumer), value is TRUE
else if SBL create new partyID, value is FALSE</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
