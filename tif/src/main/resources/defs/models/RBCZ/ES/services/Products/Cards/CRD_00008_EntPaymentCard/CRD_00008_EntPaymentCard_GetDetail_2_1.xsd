<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2013 sp1 (http://www.altova.com) by Raiffeisenbank a.s. (Raiffeisenbank a.s.) -->
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/services/entityService/paymentCard/GetDetail_2_1"
    version="1.0.0"
    xmlns:opgetdetail_2_1="http://rb.cz/services/entityService/paymentCard/GetDetail_2_1" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="opGetDetail_2_1">
        <xs:complexType>
            <xs:sequence>
                <xs:element maxOccurs="1" minOccurs="1" name="requestedDetail">
                    <xs:annotation>
                        <xs:documentation>If no requested details are specified it is returned basic information about credit cards</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:choice>
                            <xs:element name="creditDetails">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="cardIdentifier">
                                    <xs:annotation>
                                    <xs:documentation>An identifier of the required card</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="identificationCode">
                                    <xs:annotation>
                                    <xs:documentation>Value of the identifier (type of the identifier is specified in the identificationType field)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="64"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="identificationType">
                                    <xs:annotation>
                                    <xs:documentation>Type of the code - core identifier or card hash [LOV: cardHash;coreId1;coreId2]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="cardHash"/>

                                    <xs:enumeration value="coreId1"/>

                                    <xs:enumeration value="coreId2"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="addServ" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>If insurances of credit card or bonus points of credit card is required</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="debitDetails">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="cardIdentifier">
                                    <xs:annotation>
                                    <xs:documentation>An identifier of the required card</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="identificationCode">
                                    <xs:annotation>
                                    <xs:documentation>Value of the identifier (type of the identifier is specified in the identificationType field)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="64"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="identificationType">
                                    <xs:annotation>
                                    <xs:documentation>Type of the code - core identifier or card hash [LOV: cardHash;coreId1;coreId2]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="cardHash"/>

                                    <xs:enumeration value="coreId1"/>

                                    <xs:enumeration value="coreId2"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:choice>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="opGetDetail_2_1_res">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="bankAccount">
                    <xs:annotation>
                        <xs:documentation>A detail of a bank account (credit or current)</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element minOccurs="0" name="partyId">
                                <xs:annotation>
                                    <xs:documentation>Party Id - account owner</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="accountNumber">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number. The length is 35 because it is not a canonical type and it is as an identifier in backend system for credit type.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="35"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of the bank account [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element minOccurs="0" name="creditAccountDetail">
                                <xs:annotation>
                                    <xs:documentation>A detail of a credit bank acount</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element minOccurs="0"
                                    name="accountStatus" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A status of a credit card [CODELIST: CreditCardAccountStatus]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="balances">
                                    <xs:annotation>
                                    <xs:documentation>A list of balances</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="balance">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    name="balanceCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A type of a balance [CODELIST: BALANCE_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="amount" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>An amount of a balance</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A currency code of a balance [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="refreshDate" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>A date when available balance was refreshed</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="instalment">
                                    <xs:annotation>
                                    <xs:documentation>Instalment information</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="accountNumber">
                                    <xs:annotation>
                                    <xs:documentation>A bank account for the instalment</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0"
                                    name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0"
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of the bank account [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="instalmentAmount">
                                    <xs:annotation>
                                    <xs:documentation>Minimal amount of an instalment</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    name="amount" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Amount of money</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency code of the amount [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="symbols">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    minOccurs="0" name="variable">
                                    <xs:annotation>
                                    <xs:documentation>Variable symbol</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0" name="constant">
                                    <xs:annotation>
                                    <xs:documentation>Constant symbol</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0" name="specific">
                                    <xs:annotation>
                                    <xs:documentation>Specific symbol</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="date" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Date of a payment</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="statementInfo">
                                    <xs:annotation>
                                    <xs:documentation>A statement information</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="lastStatementDate" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>A date of the last statement</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="nextStatementDate" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>A date of the next statement</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="limits">
                                    <xs:annotation>
                                    <xs:documentation>Limits</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="limit">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="limitType">
                                    <xs:annotation>
                                    <xs:documentation>A limit type - general, POS, ATM [LOV: General;ATM;POS]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="General"/>

                                    <xs:enumeration value="ATM"/>

                                    <xs:enumeration value="POS"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="maxAmount">
                                    <xs:annotation>
                                    <xs:documentation>An amount of a limit</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    name="amount" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Amount of money</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency code of the amount [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="1"
                                    name="period" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A period of the limit [CODELIST: FrequencyType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="bonusInfo">
                                    <xs:annotation>
                                    <xs:documentation>Bonus information</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    minOccurs="0"
                                    name="bonusType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Bonus type. According to this element a titles in a statement or whereever are displayed. [CODELIST: BonusType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="bonusAvailability" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>If a bonus program is available via Avaya for this card</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="bonusPoints">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="bonusPoint">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="bonusCode">
                                    <xs:annotation>
                                    <xs:documentation>A code of a meaning of the amount  [LOV: AVAILABLE_BONUS;CURRENT_DRAWED_BONUS;CURRENT_BONUS;TOTAL_BONUS;GAINED_GROUP_1;GAINED_GROUP_2;UNSPENT_FROM_PREVIOUS_PERIOD;AVAILABLE_BONUS_EOC;CURRENT_DRAWED_BONUS_EOC;CURRENT_BONUS_EOC;GAINED_GROUP_1_EOC;GAINED_GROUP_2_EOC]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="AVAILABLE_BONUS"/>

                                    <xs:enumeration value="CURRENT_DRAWED_BONUS"/>

                                    <xs:enumeration value="CURRENT_BONUS"/>

                                    <xs:enumeration value="TOTAL_BONUS"/>

                                    <xs:enumeration value="GAINED_GROUP_1"/>

                                    <xs:enumeration value="GAINED_GROUP_2"/>

                                    <xs:enumeration value="UNSPENT_FROM_PREVIOUS_PERIOD"/>

                                    <xs:enumeration value="AVAILABLE_BONUS_EOC"/>

                                    <xs:enumeration value="CURRENT_DRAWED_BONUS_EOC"/>

                                    <xs:enumeration value="CURRENT_BONUS_EOC"/>

                                    <xs:enumeration value="GAINED_GROUP_1_EOC"/>

                                    <xs:enumeration value="GAINED_GROUP_2_EOC"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="amount">
                                    <xs:annotation>
                                    <xs:documentation>Amount of bonus points</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:fractionDigits value="2"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element minOccurs="0" name="cardDetail">
                    <xs:annotation>
                        <xs:documentation>A card detail</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="cardIdentifiers">
                                <xs:annotation>
                                    <xs:documentation>A collection of a card identifiers</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="cardIdentifier">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="identificationCode">
                                    <xs:annotation>
                                    <xs:documentation>A value of the identifier (type of the identifier is specified in the identificationType field)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="64"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="identificationType">
                                    <xs:annotation>
                                    <xs:documentation>Type of the code - core identifier or card hash [LOV: cardHash;coreId1;coreId2]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="cardHash"/>

                                    <xs:enumeration value="coreId1"/>

                                    <xs:enumeration value="coreId2"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="maskedNumber" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>A masked payment card number</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element minOccurs="0" name="primaryCard" type="xs:boolean">
                                <xs:annotation>
                                    <xs:documentation>Flag if a card is primary or not</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element minOccurs="0" name="embossedName">
                                <xs:annotation>
                                    <xs:documentation>A title which is printed at a payment card</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element minOccurs="0" name="paymentCardHolder">
                                <xs:annotation>
                                    <xs:documentation>Information about a card holder</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element minOccurs="0" name="partyId">
                                    <xs:annotation>
                                    <xs:documentation>Party Id (Siebel identifier)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="name">
                                    <xs:annotation>
                                    <xs:documentation>A name of a card holder</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="titlesBefore">
                                    <xs:annotation>
                                    <xs:documentation>Titles in front of the name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="firstName">
                                    <xs:annotation>
                                    <xs:documentation>First name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="lastName">
                                    <xs:annotation>
                                    <xs:documentation>Last name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="titlesBehind">
                                    <xs:annotation>
                                    <xs:documentation>Titles behind the name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="validity">
                                <xs:annotation>
                                    <xs:documentation>A validity interval</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="validFrom" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Duration from date</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="validTo" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Duration to date</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="setting">
                                <xs:annotation>
                                    <xs:documentation>Settings of a payment card</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element minOccurs="0"
                                    name="usageAfterDeath" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>A flag if a card holder can use  a payment card after a death of a bank account owner. The account owner is not a card holder.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="renewalCard">
                                    <xs:annotation>
                                    <xs:documentation>Information about a card renewal</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="automaticRenewal" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Flag, if a client wants to set an automatic renewal of a card.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="eCommerce">
                                    <xs:annotation>
                                    <xs:documentation>eCommerce</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="activated" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>The e-commerce feature is activated (true/false).</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="setUpAvailable" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Client has E-commerce set up available (turn on/turn off).</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="limits">
                                    <xs:annotation>
                                    <xs:documentation>Limits</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="limit">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="limitType">
                                    <xs:annotation>
                                    <xs:documentation>A limit type [LOV: General;ATM;POS]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="General"/>

                                    <xs:enumeration value="ATM"/>

                                    <xs:enumeration value="POS"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="maxAmount">
                                    <xs:annotation>
                                    <xs:documentation>An amount of the limit</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    name="amount" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Amount of money</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency code of the amount [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0"
                                    name="period" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A period of the limit [CODELIST: FrequencyType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="balances">
                                <xs:annotation>
                                    <xs:documentation>A list of balances</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="balance">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="balanceCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A type of a balance [CODELIST: BALANCE_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="amount" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>An amount of a balance</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A currency code of a balance [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="refreshDate" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>A date when available balance was refreshed</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element minOccurs="0" name="deliveryInfo">
                                <xs:annotation>
                                    <xs:documentation>Delivery information</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="deliveryType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Type of a delivery - a client address or a branch - A, B [CODELIST: DistributionChannel]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="branchCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Identification of a branch [CODELIST: Branch]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="contacts">
                                    <xs:annotation>
                                    <xs:documentation>A contact addresses</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="contact">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="name">
                                    <xs:annotation>
                                    <xs:documentation>Addressee</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="firstName">
                                    <xs:annotation>
                                    <xs:documentation>First name or a name of a company</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="lastName">
                                    <xs:annotation>
                                    <xs:documentation>Last name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element name="address">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="addressName">
                                    <xs:annotation>
                                    <xs:documentation>User definable Mail Address name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="addressType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Mail address type (Residency or Mail1-9) [CODELIST: GENERAL_MAILTYPE]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element name="city">
                                    <xs:annotation>
                                    <xs:documentation>City name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="postalCode">
                                    <xs:annotation>
                                    <xs:documentation>ZIP code</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="country" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Country [CODELIST: GENERAL_CNTR]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="replacementCardIdentifier">
                                <xs:annotation>
                                    <xs:documentation>If the card from the request is replaced, there will be an identifier of the replacement card.</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="identificationCode">
                                    <xs:annotation>
                                    <xs:documentation>A value of the identifier (type of the identifier is specified in the identificationType field)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="64"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="identificationType">
                                    <xs:annotation>
                                    <xs:documentation>Type of the code - core identifier or card hash [LOV: cardHash;coreId1;coreId2]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:enumeration value="cardHash"/>
                                    <xs:enumeration value="coreId1"/>
                                    <xs:enumeration value="coreId2"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element minOccurs="0" name="creditCard">
                                <xs:annotation>
                                    <xs:documentation>Attributes of debit card</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="cardProductType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A product type means if a card is Maestro, Visa, Billa, etc. [CODELIST: CreditCardTypeCode]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="status" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A status of a credit card - active, blocked, etc.... [CODELIST: CREDIT_CARD_STATUS_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="insurances">
                                    <xs:annotation>
                                    <xs:documentation>A list of insurances</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    name="insurance" type="xs:string"/>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="creditInfo">
                                    <xs:annotation>
                                    <xs:documentation>Details about credit info service</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="creditInfoType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A type of Kredit Info service [CODELIST: ?? CreditInfoType ?]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="number">
                                    <xs:annotation>
                                    <xs:documentation>A phone number where an SMS is sent</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element minOccurs="0" name="debitCard">
                                <xs:annotation>
                                    <xs:documentation>Attributes of debit card</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="cardProductType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A debit card type code [CODELIST: DebitCardTypeCode]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="status" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A debit card status - active, closed, blocked etc [CODELIST: DEBIT_CARD_STATUS_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="insurances">
                                    <xs:annotation>
                                    <xs:documentation>A list of insurances</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="insurance">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    name="insurancePurposeCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Insurance purpose code [CODELIST: INSURANCE_PURPOSE_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="insuranceProductCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Insurance product code. [CODELIST: INSURANCE_PRODUCT_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="resultInfo">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded"
                                minOccurs="1" name="resultItem">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="code" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A code of the error/warning [CODELIST: ERROR_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="description" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A description of the error/warning</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
