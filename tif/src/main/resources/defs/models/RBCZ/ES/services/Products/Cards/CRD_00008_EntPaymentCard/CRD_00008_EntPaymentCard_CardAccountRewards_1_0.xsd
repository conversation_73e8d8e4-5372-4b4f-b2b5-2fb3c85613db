<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/services/entityService/paymentCard/CardAccountRewards_1_0"
    version="1.0.0"
    xmlns:opcardaccountrewards_1_0="http://rb.cz/services/entityService/paymentCard/CardAccountRewards_1_0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="opCardAccountRewards_1_0">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="accountNumber">
                    <xs:annotation>
                        <xs:documentation>Account number of a payment card</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="numberPart2">
                                <xs:annotation>
                                    <xs:documentation>Base part of the bank account number. The length is 35 because it is not a canonical type and it is as an identifier in backend system for credit type.</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="35"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element name="rewardAmount">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="amount">
                                <xs:annotation>
                                    <xs:documentation>Amount</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:decimal">
                                    <xs:totalDigits value="18"/>
                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element minOccurs="0"
                                name="currencyCode" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Currency of amount [CODELIST: Currency]</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element name="rewardType" type="xs:integer">
                    <xs:annotation>
                        <xs:documentation>Reward type. Value 1 = Spend and get bonus application sent from internet banking (other possible values may come in the future)</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="opportunityId">
                    <xs:annotation>
                        <xs:documentation>opportunity ID (from CRM)</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:maxLength value="15"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="opCardAccountRewards_1_0_res">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
</xs:schema>
