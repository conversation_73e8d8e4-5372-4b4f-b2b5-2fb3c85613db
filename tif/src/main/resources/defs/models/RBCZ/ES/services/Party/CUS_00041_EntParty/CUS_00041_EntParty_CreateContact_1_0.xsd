<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/services/party/CreateContact_1_0/1.0"
    version="1.0.0"
    xmlns:opcreatecontact_1_0="http://rb.cz/services/party/CreateContact_1_0/1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="opCreateContact_1_0">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="partyId">
                    <xs:annotation>
                        <xs:documentation>Party Id (Siebel identifier)</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:maxLength value="15"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="address">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="1" minOccurs="0" name="addressName">
                                <xs:annotation>
                                    <xs:documentation>User definable Mail Address name</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="90"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="addressee">
                                <xs:annotation>
                                    <xs:documentation>Mail addressee</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="additionalInfo">
                                <xs:annotation>
                                    <xs:documentation>Additional address info</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="street">
                                <xs:annotation>
                                    <xs:documentation>Street name</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="200"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="number">
                                <xs:annotation>
                                    <xs:documentation>Building number/street number</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="city">
                                <xs:annotation>
                                    <xs:documentation>City name</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="postalCode">
                                <xs:annotation>
                                    <xs:documentation>ZIP code</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="country" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Country [CODELIST: GENERAL_CNTR]</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="preferred" type="xs:boolean">
                                <xs:annotation>
                                    <xs:documentation>Preferred address Y/N</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="classification" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Classification - home/work [CODELIST: PRV_CONTCLASSIF]</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="comment">
                                <xs:annotation>
                                    <xs:documentation>Mail address comment</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="addressPointId">
                                <xs:annotation>
                                    <xs:documentation>Uniq ID of delivered address by RUIAN.</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="dataQualityVerified" type="xs:boolean">
                                <xs:annotation>
                                    <xs:documentation>Flag if address check by data quality system (Purity).</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="modifiedAfterVerification" type="xs:boolean">
                                <xs:annotation>
                                    <xs:documentation>Flag if address found and modified after data quality check.</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="email">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="1" minOccurs="1" name="contactName">
                                <xs:annotation>
                                    <xs:documentation>User definable contact name</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="90"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="1"
                                name="contactType" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Siebel technical field differentiating records in the same physical database table (LANDLINE_NUMBER, MOBILE_NUMBER, FAX_NUMBER, EMAIL_ADDRESS) [CODELIST: RB_COMM_MEDIUM_TYPE]</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="1" name="address">
                                <xs:annotation>
                                    <xs:documentation>A contact address (email, URL ..)</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="100"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="addressType">
                                <xs:annotation>
                                    <xs:documentation>Email address type (EMAIL1, EMAIL2,...)</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="1"
                                name="preferred" type="xs:boolean">
                                <xs:annotation>
                                    <xs:documentation>A preferred contact</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="classification" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>A classification - home/work [CODELIST: PRV_CONTCLASSIF]</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="comment">
                                <xs:annotation>
                                    <xs:documentation>A contact comment</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="qualityScore" type="xs:integer">
                                <xs:annotation>
                                    <xs:documentation>The Score defines a quality of the contact. Higher score means worse quality. The Score comes from Purity.</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="qualityExplanationCode">
                                <xs:annotation>
                                    <xs:documentation>Explanation codes defines a quality defects of the contact. Codes comes from Purity.</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="phone">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="1" minOccurs="1" name="contactName">
                                <xs:annotation>
                                    <xs:documentation>User definable contact name</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="90"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="1"
                                name="contactType" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Siebel technical field differentiating records in the same physical database table (LANDLINE_NUMBER, MOBILE_NUMBER, FAX_NUMBER, EMAIL_ADDRESS) [CODELIST: RB_COMM_MEDIUM_TYPE]</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="1" name="number">
                                <xs:annotation>
                                    <xs:documentation>A contact number.</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="numberType">
                                <xs:annotation>
                                    <xs:documentation>Contact number type (FAX1.., MOBILE1.., PHONE1..)</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="1"
                                name="preferred" type="xs:boolean">
                                <xs:annotation>
                                    <xs:documentation>A preferred contact</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="classification" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>A classification - home/work [CODELIST: PRV_CONTCLASSIF]</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="comment">
                                <xs:annotation>
                                    <xs:documentation>A contact comment</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="qualityScore" type="xs:integer">
                                <xs:annotation>
                                    <xs:documentation>The Score defines a quality of the contact. Higher score means worse quality. The Score comes from Purity.</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="qualityExplanationCode">
                                <xs:annotation>
                                    <xs:documentation>Explanation codes defines a quality defects of the contact. Codes comes from Purity.</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="opCreateContact_1_0_res">
        <xs:complexType>
            <xs:sequence>
                <xs:element maxOccurs="1" minOccurs="0" name="address">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="trgReferenceId">
                                <xs:annotation>
                                    <xs:documentation>Uniq ID of created entity in SBL</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="email">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="trgReferenceId">
                                <xs:annotation>
                                    <xs:documentation>Uniq ID of created entity in SBL</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="phone">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="trgReferenceId">
                                <xs:annotation>
                                    <xs:documentation>Uniq ID of created entity in SBL</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="resultItems">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded"
                                minOccurs="1" name="resultItem">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="code" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A code of the error/warning [CODELIST: ERROR_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="description">
                                    <xs:annotation>
                                    <xs:documentation>A description of the error/warning</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="2000"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
