<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/services/entityService/domesticPayment/GetSinglePOList_1_3/1.0"
    version="1.0.0"
    xmlns:opgetsinglepolist_1_3="http://rb.cz/services/entityService/domesticPayment/GetSinglePOList_1_3/1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="opGetSinglePOList_1_3">
        <xs:complexType>
            <xs:sequence>
                <xs:element maxOccurs="1" minOccurs="0" name="paging">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="startIndex" type="xs:integer">
                                <xs:annotation>
                                    <xs:documentation>Index of the first item within the returned collection. Default value is 1</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="maxResults" type="xs:integer">
                                <xs:annotation>
                                    <xs:documentation>Maximal number of returned collection items</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element minOccurs="0" name="countLimit" type="xs:integer">
                                <xs:annotation>
                                    <xs:documentation>Count limit - maximal border to which is required exact record count to be returned.  If value of "Count limit" in the request is not present then in response will not be present value of "Record count".</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="sorting">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="1" minOccurs="1" name="paymentOrderItems">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="paymentOrderItem">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="nullValues">
                                    <xs:annotation>
                                    <xs:documentation>Parameter defines where should be null values sorted in the returned list - at the beggining or at the end. [LOV: Begin;End]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="Begin"/>

                                    <xs:enumeration value="End"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="sortType">
                                    <xs:annotation>
                                    <xs:documentation>Parameter defines whether is the sorting type ascending or descending. [LOV: Ascending;Descending]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="Ascending"/>

                                    <xs:enumeration value="Descending"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="sortAttribute">
                                    <xs:annotation>
                                    <xs:documentation>[LOV: Amount;DueDate]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="Amount"/>

                                    <xs:enumeration value="DueDate"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="1" name="schgCrit">
                    <xs:annotation>
                        <xs:documentation>Searching criteria</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:choice>
                                <xs:element name="curAcctNb">
                                    <xs:annotation>
                                    <xs:documentation>Current account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element minOccurs="0" name="nbPart1">
                                    <xs:annotation>
                                    <xs:documentation>numberPart1 - Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="nbPart2">
                                    <xs:annotation>
                                    <xs:documentation>numberPart2 - Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="bkCd" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>bank code - part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="ccyCd" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>currencyCode - (main) currency code of the bank account</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element name="termDpstNb">
                                    <xs:annotation>
                                    <xs:documentation>Term deposit number</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element minOccurs="0" name="nbPart1">
                                    <xs:annotation>
                                    <xs:documentation>numberPart1 - Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="nbPart2">
                                    <xs:annotation>
                                    <xs:documentation>numberPart2 - Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="bkCd" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>bank code - part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="ccyCd" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>currencyCode - (main) currency code of the bank account</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="1" name="dpstNb">
                                    <xs:annotation>
                                    <xs:documentation>Number of deposit</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                            </xs:choice>
                            <xs:choice maxOccurs="1" minOccurs="0">
                                <xs:element maxOccurs="1" minOccurs="1" name="stgPmtOrdr">
                                    <xs:annotation>
                                    <xs:documentation>Standing payment order</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="trgReferenceId">
                                    <xs:annotation>
                                    <xs:documentation>Unique identification of the entity from provider point of view</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element maxOccurs="1" minOccurs="1" name="clctn">
                                    <xs:annotation>
                                    <xs:documentation>Collection Mandate Identifier used by TRS
</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="trgReferenceId">
                                    <xs:annotation>
                                    <xs:documentation>Unique identification of the entity from provider point of view</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element maxOccurs="1" minOccurs="1" name="SIPO">
                                    <xs:annotation>
                                    <xs:documentation>SIPO Mandate Identifier (used by TRS)
</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="trgReferenceId">
                                    <xs:annotation>
                                    <xs:documentation>Unique identification of the entity from provider point of view</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                            </xs:choice>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="txTp" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Trasaction type [CODELIST: TransactionType]</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="orgtrMsg">
                                <xs:annotation>
                                    <xs:documentation>Originator message</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="140"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element minOccurs="0" name="ctrPty">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element minOccurs="0" name="acctNb">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    minOccurs="0" name="nbPart1">
                                    <xs:annotation>
                                    <xs:documentation>numberPart1 - Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="nbPart2">
                                    <xs:annotation>
                                    <xs:documentation>numberPart2 - Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="bkCd" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>bank code - part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="pmtAmt">
                                <xs:annotation>
                                    <xs:documentation>Payment amount</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="ccyCd" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency code</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="amtFr">
                                    <xs:annotation>
                                    <xs:documentation>Amount from
</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:decimal">
                                    <xs:totalDigits value="22"/>
                                    <xs:fractionDigits value="7"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="amtTo">
                                    <xs:annotation>
                                    <xs:documentation>Amount to</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:decimal">
                                    <xs:totalDigits value="22"/>
                                    <xs:fractionDigits value="7"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="stss">
                                <xs:annotation>
                                    <xs:documentation>Statuses</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="0" name="sts" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Status - result of payment realization [CODELIST: PaymentStatus]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="intrvl">
                                <xs:annotation>
                                    <xs:documentation>Interval</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="dtFr" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Date from</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="dtTo" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Date to</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element minOccurs="0" name="symbs">
                                <xs:annotation>
                                    <xs:documentation>Transaction symbols - variable, constant, specific (ISO-20022 abbrev: symbs)</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:annotation>
                                    <xs:documentation>Variable symbol</xs:documentation>
                                    </xs:annotation>
                                    <xs:element minOccurs="0" name="varbl">
                                    <xs:annotation>
                                    <xs:documentation>Variable symbol (ISO-20022 abbrev: varbl)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="cst">
                                    <xs:annotation>
                                    <xs:documentation>Constant symbol (ISO-20022 abbrev: cst)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="spcfc">
                                    <xs:annotation>
                                    <xs:documentation>Specific symbol of the transaction (ISO-20022 abbrev: spcfc)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="fullTxtSch">
                                <xs:annotation>
                                    <xs:documentation>Full text search - string to be searched across several attributes (full text)</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element minOccurs="0" name="lvlOfDtl">
                    <xs:annotation>
                        <xs:documentation>Level of detail requested in response. Possible values: "full" (all attributes), "basic" (only attributes for simple list), "count" (only record count without data) [LOV: full;basic;count]</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:enumeration value="full"/>
                            <xs:enumeration value="basic"/>
                            <xs:enumeration value="count"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="opGetSinglePOList_1_3_res">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="paging">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="startIndex" type="xs:integer">
                                <xs:annotation>
                                    <xs:documentation>Index of the first item within the returned collection. Default value is 1</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="endIndex" type="xs:integer">
                                <xs:annotation>
                                    <xs:documentation>Index of the last item within the returned collection</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="endOfRecords" type="xs:boolean">
                                <xs:annotation>
                                    <xs:documentation>Flag indicating end of the records</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element minOccurs="0" name="recordCount" type="xs:integer">
                                <xs:annotation>
                                    <xs:documentation>Total count of records meeting searching conditions. Used by consumer application for calculation of number of pages to display.</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element minOccurs="0"
                                name="countLimitOverRun" type="xs:boolean">
                                <xs:annotation>
                                    <xs:documentation>Flag indicating that the count limit was overrun and returned record count is not exact (returned value of the count limit as count but the real count is greater). </xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="pmtOrdrs">
                    <xs:annotation>
                        <xs:documentation>Payment orders
ISO-20022 abbreviation: pmtOrdrs</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded"
                                minOccurs="1" name="pmtOrdr">
                                <xs:annotation>
                                    <xs:documentation>Payment order (ISO-20022 abbrev: pmtOrdr)</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="id">
                                    <xs:annotation>
                                    <xs:documentation>Identification</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="trgReferenceId">
                                    <xs:annotation>
                                    <xs:documentation>Unique identification of the entity from provider point of view</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="created" type="xs:dateTime"/>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="dpstNb" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>number of term deposit (ISO-20022 abbrev: dpstNb)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="txCtgy" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Transaction type according to MCH codelist TransactionCategory</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="mvmntTp" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Transaction type according to MCH codelist MovementType </xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="stgPmtOrdr">
                                    <xs:annotation>
                                    <xs:documentation>Standing payment order (ISO-20022 abbrev: stgPmtOrdr)</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="trgReferenceId">
                                    <xs:annotation>
                                    <xs:documentation>Unique identification of the entity from provider point of view</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="txTp" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Transaction type (ISO-20022 abbrev: txTp) [CODELIST: TransactionType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="ordrOrgtr">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    minOccurs="0" name="acctNm">
                                    <xs:annotation>
                                    <xs:documentation>bank account name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="140"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="acctNb">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    minOccurs="0" name="nbPart1">
                                    <xs:annotation>
                                    <xs:documentation>numberPart1 - Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="nbPart2">
                                    <xs:annotation>
                                    <xs:documentation>numberPart2 - Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="bkCd" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>bank code - part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="ccyCd" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>currencyCode - (main) currency code of the bank account</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="msg">
                                    <xs:annotation>
                                    <xs:documentation>message</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="140"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="ctrPty">
                                    <xs:annotation>
                                    <xs:documentation>Counterparty (ISO-20022 abbrev: ctrPty)</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    minOccurs="0" name="acctNm">
                                    <xs:annotation>
                                    <xs:documentation>bank account name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="140"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="acctNb">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    minOccurs="0" name="nbPart1">
                                    <xs:annotation>
                                    <xs:documentation>numberPart1 - Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="nbPart2">
                                    <xs:annotation>
                                    <xs:documentation>numberPart2 - Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="bkCd" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>bank code - part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0"
                                    name="ccyCd" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>currencyCode - (main) currency code of the bank account</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="msg">
                                    <xs:annotation>
                                    <xs:documentation>message</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="140"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="pmtAmt">
                                    <xs:annotation>
                                    <xs:documentation>Payment amount (ISO-20022 abbrev: pmtAmt)</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="amt" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>amount of money</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="ccy" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>currency of the amount - ISO code</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="prcg">
                                    <xs:annotation>
                                    <xs:documentation>Processing (ISO-20022 abbrev: prcg)</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="dueDt" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Due date of the payment order (ISO-20022 abbrev: dueDt)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="xpryDt" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Expiry date (ISO-20022 abbrev: xpryDt) - Date when the payment order expires</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="valDt" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Value date (ISO-20022 abbrev: valDt) - Datum valuty. Ridi, jaky kurz se pouzije, a jak se uroci.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="symbs">
                                    <xs:annotation>
                                    <xs:documentation>Transaction symbols - variable, constant, specific (ISO-20022 abbrev: symbs)</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:annotation>
                                    <xs:documentation>Variable symbol</xs:documentation>
                                    </xs:annotation>
                                    <xs:element
                                    minOccurs="0" name="varbl">
                                    <xs:annotation>
                                    <xs:documentation>Variable symbol (ISO-20022 abbrev: varbl)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="cst">
                                    <xs:annotation>
                                    <xs:documentation>Constant symbol (ISO-20022 abbrev: cst)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="spcfc">
                                    <xs:annotation>
                                    <xs:documentation>Specific symbol of the transaction (ISO-20022 abbrev: spcfc)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="flags">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="allowExpressPayment" type="xs:boolean"/>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="editable" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>If the payment order is editable, value is true.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="cretdBySys" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Created by system (ISO-20022 abbrev: cretdBySys) - flag if the transaction was created by system</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="realizInf">
                                    <xs:annotation>
                                    <xs:documentation>Realization info (abbrev: realizInf)</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    minOccurs="1"
                                    name="sts" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Payment status (ISO-20022 abbrev: sts) [CODELIST: PaymentStatus]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="desc" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>description of realization status (abbrev: desc)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="rjctnRsn" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Rejection reason (ISO-20022 abbrev: rjctnRsn) - Reason why transaction wasn't realized [CODELIST: RejectionReason]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="lastRealiz" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Last realization (abbrev: lastRealiz)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="realizAmt">
                                    <xs:annotation>
                                    <xs:documentation>Realized amount (abbrev: realizAmt)</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    name="amt" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>amount of money</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="ccy" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>currency of the amount - ISO code</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element minOccurs="0" name="rsltInf">
                    <xs:annotation>
                        <xs:documentation>Result info (ISO-20022 abbrev: rsltInf)</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded" name="rsltItm">
                                <xs:annotation>
                                    <xs:documentation>Result item (ISO-20022 abbrev: rsltItm)</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="cd" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A code of the error/warning [CODELIST: ERROR_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="desc">
                                    <xs:annotation>
                                    <xs:documentation>A description of the error/warning</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="2000"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
