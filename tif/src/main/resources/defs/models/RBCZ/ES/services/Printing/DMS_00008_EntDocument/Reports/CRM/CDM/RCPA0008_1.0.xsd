<?xml version="1.0" encoding="utf-8"?>
<!-- edited with XMLSpy v2013 sp1 (http://www.altova.com) by Raiffeisenbank a.s. (Raiffeisenbank a.s.) -->
<!--
Xml Schema generated by NetLT EAExtensionManager plugin 'XSD Composer ver. 2.1.6 [31.1.2012 11:25' ]

Important!!!

Automatically generated from EA model, don't update manually!!!

-->
<xs:schema xmlns:rcpa0008="http://rb.cz/cdm/dds/crm/cpa/RCPA0008/1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="http://rb.cz/cdm/dds/crm/cpa/RCPA0008/1.0" version="1.0.2">
	<xs:element name="RCPA0008">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="client" minOccurs="1" maxOccurs="1">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="private" minOccurs="1" maxOccurs="1">
								<xs:annotation>
									<xs:documentation>[RCPA0008_4] </xs:documentation>
								</xs:annotation>
								<xs:complexType>
									<xs:sequence>
										<xs:element name="address" minOccurs="1" maxOccurs="1">
											<xs:complexType>
												<xs:sequence>
													<xs:element name="mailing" minOccurs="1" maxOccurs="1">
														<xs:annotation>
															<xs:documentation>[RCPA0008_33] Private Party Mail Address where 
(Preferred IS SET)</xs:documentation>
														</xs:annotation>
														<xs:complexType>
															<xs:sequence>
																<xs:element name="additionalAddressInfo" minOccurs="0" maxOccurs="1">
																	<xs:annotation>
																		<xs:documentation>[RCPA0008_36] Raiffeisenbank, a.s.</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="255"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="addressee" minOccurs="1" maxOccurs="1">
																	<xs:annotation>
																		<xs:documentation>[RCPA0008_35] Ing., RNDr. Jan Brixi, CSc., PhD.</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="255"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="city" minOccurs="1" maxOccurs="1">
																	<xs:annotation>
																		<xs:documentation>[RCPA0008_39] Praha 4</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="200"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="country" minOccurs="1" maxOccurs="1">
																	<xs:annotation>
																		<xs:documentation>[RCPA0008_41] Česká Republika</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="200"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="number" minOccurs="1" maxOccurs="1">
																	<xs:annotation>
																		<xs:documentation>[RCPA0008_38] 2a/1714</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="50"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="postalCode" minOccurs="1" maxOccurs="1">
																	<xs:annotation>
																		<xs:documentation>[RCPA0008_40] 140 11</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="30"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="street" minOccurs="0" maxOccurs="1">
																	<xs:annotation>
																		<xs:documentation>[RCPA0008_37] Hvězdova</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="200"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
													<xs:element name="primary" minOccurs="1" maxOccurs="1">
														<xs:annotation>
															<xs:documentation>[RCPA0008_25] where (Mail Address Type = "RESIDENCY")</xs:documentation>
														</xs:annotation>
														<xs:complexType>
															<xs:sequence>
																<xs:element name="additionalAddressInfo" minOccurs="0" maxOccurs="1">
																	<xs:annotation>
																		<xs:documentation>[RCPA0008_27] palác Myslbek</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="255"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="city" minOccurs="1" maxOccurs="1">
																	<xs:annotation>
																		<xs:documentation>[RCPA0008_30] Praha 1</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="200"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="country" minOccurs="1" maxOccurs="1">
																	<xs:annotation>
																		<xs:documentation>[RCPA0008_32] Česká Republika</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="200"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="number" minOccurs="1" maxOccurs="1">
																	<xs:annotation>
																		<xs:documentation>[RCPA0008_29] 17/1234</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="50"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="postalCode" minOccurs="1" maxOccurs="1">
																	<xs:annotation>
																		<xs:documentation>[RCPA0008_31] 110 17</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="30"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="street" minOccurs="0" maxOccurs="1">
																	<xs:annotation>
																		<xs:documentation>[RCPA0008_28] Na Příkopě</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="200"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
										<xs:element name="contact" minOccurs="0" maxOccurs="1">
											<xs:annotation>
												<xs:documentation>[RCPA0008_54] where (Preferred IS SET)</xs:documentation>
											</xs:annotation>
											<xs:complexType>
												<xs:sequence>
													<xs:element name="preferred" minOccurs="0" maxOccurs="1">
														<xs:annotation>
															<xs:documentation>[RCPA0008_46] where (Preferred IS SET)</xs:documentation>
														</xs:annotation>
														<xs:complexType>
															<xs:sequence>
																<xs:element name="email" minOccurs="0" maxOccurs="1">
																	<xs:annotation>
																		<xs:documentation>[RCPA0008_55] <EMAIL></xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="150"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="fax" minOccurs="0" maxOccurs="1">
																	<xs:annotation>
																		<xs:documentation>[RCPA0008_51] +420111222444
Fax</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="50"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="fixLine" minOccurs="0" maxOccurs="1">
																	<xs:annotation>
																		<xs:documentation>[RCPA0008_43] +420222111333
Phone number</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="25"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="mobile" minOccurs="0" maxOccurs="1">
																	<xs:annotation>
																		<xs:documentation>[RCPA0008_47] +420603603603
Mobile</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="20"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
										<xs:element name="partyID" minOccurs="1" maxOccurs="1">
											<xs:annotation>
												<xs:documentation>[RCPA0008_5] Party ID of the Party</xs:documentation>
											</xs:annotation>
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:maxLength value="50"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="personalForm" minOccurs="1" maxOccurs="1">
											<xs:complexType>
												<xs:sequence>
													<xs:element name="birthCode" minOccurs="0" maxOccurs="1">
														<xs:annotation>
															<xs:documentation>[RCPA0008_15] ######/####</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:string">
																<xs:maxLength value="20"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
													<xs:element name="birthDate" type="xs:date" minOccurs="1" maxOccurs="1">
														<xs:annotation>
															<xs:documentation>[RCPA0008_11] 30.01.1962</xs:documentation>
														</xs:annotation>
													</xs:element>
													<xs:element name="birthPlace" minOccurs="1" maxOccurs="1">
														<xs:annotation>
															<xs:documentation>[RCPA0008_14] calculated from City of Birth and Country of birth</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:string">
																<xs:maxLength value="255"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
													<xs:element name="citizenship" minOccurs="1" maxOccurs="1">
														<xs:annotation>
															<xs:documentation>[RCPA0008_13] </xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:string">
																<xs:maxLength value="30"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
													<xs:element name="firstName" minOccurs="1" maxOccurs="1">
														<xs:annotation>
															<xs:documentation>[RCPA0008_7] Jan</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:string">
																<xs:maxLength value="80"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
													<xs:element name="futureProductOwner" minOccurs="0">
														<xs:simpleType>
															<xs:restriction base="xs:string">
																<xs:maxLength value="30"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
													<xs:element name="gender" minOccurs="1" maxOccurs="1">
														<xs:annotation>
															<xs:documentation>[RCPA0008_12] Muž/Žena</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:string">
																<xs:maxLength value="30"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
													<xs:element name="identification" minOccurs="0" maxOccurs="1">
														<xs:complexType>
															<xs:sequence>
																<xs:element name="identificationPaper" minOccurs="1" maxOccurs="unbounded">
																	<xs:annotation>
																		<xs:documentation>[RCPA0008_17] MULTIPLE
All documents valid at the moment being and not unacceptable.</xs:documentation>
																	</xs:annotation>
																	<xs:complexType>
																		<xs:sequence>
																			<xs:element name="authority" minOccurs="0" maxOccurs="1">
																				<xs:annotation>
																					<xs:documentation>[RCPA0008_21] Obecní úřad v Plánici</xs:documentation>
																				</xs:annotation>
																				<xs:simpleType>
																					<xs:restriction base="xs:string">
																						<xs:maxLength value="100"/>
																					</xs:restriction>
																				</xs:simpleType>
																			</xs:element>
																			<xs:element name="expiryDate" type="xs:date" minOccurs="1" maxOccurs="1">
																				<xs:annotation>
																					<xs:documentation>[RCPA0008_23] 01.01.2010</xs:documentation>
																				</xs:annotation>
																			</xs:element>
																			<xs:element name="issueDate" type="xs:date" minOccurs="1" maxOccurs="1">
																				<xs:annotation>
																					<xs:documentation>[RCPA0008_22] 01.01.2000</xs:documentation>
																				</xs:annotation>
																			</xs:element>
																			<xs:element name="issuedCountry" minOccurs="1" maxOccurs="1">
																				<xs:annotation>
																					<xs:documentation>[RCPA0008_20] Czech Republic, …</xs:documentation>
																				</xs:annotation>
																				<xs:simpleType>
																					<xs:restriction base="xs:string">
																						<xs:maxLength value="200"/>
																					</xs:restriction>
																				</xs:simpleType>
																			</xs:element>
																			<xs:element name="paperNo" minOccurs="1" maxOccurs="1">
																				<xs:annotation>
																					<xs:documentation>[RCPA0008_19] identification number</xs:documentation>
																				</xs:annotation>
																				<xs:simpleType>
																					<xs:restriction base="xs:string">
																						<xs:maxLength value="100"/>
																					</xs:restriction>
																				</xs:simpleType>
																			</xs:element>
																			<xs:element name="type" minOccurs="1" maxOccurs="1">
																				<xs:annotation>
																					<xs:documentation>[RCPA0008_18] one of specified LOV type</xs:documentation>
																				</xs:annotation>
																				<xs:simpleType>
																					<xs:restriction base="xs:string">
																						<xs:maxLength value="100"/>
																					</xs:restriction>
																				</xs:simpleType>
																			</xs:element>
																		</xs:sequence>
																	</xs:complexType>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
													<xs:element name="middleNames" minOccurs="0" maxOccurs="1">
														<xs:annotation>
															<xs:documentation>[RCPA0008_8] Xaver Josef Petr</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:string">
																<xs:maxLength value="80"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
													<xs:element name="surName" minOccurs="1" maxOccurs="1">
														<xs:annotation>
															<xs:documentation>[RCPA0008_9] Brixi</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:string">
																<xs:maxLength value="100"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
													<xs:element name="titleBefore" minOccurs="0" maxOccurs="1">
														<xs:annotation>
															<xs:documentation>[RCPA0008_6] Ing., RNDr.</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:string">
																<xs:maxLength value="50"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
													<xs:element name="titleBehind" minOccurs="0" maxOccurs="1">
														<xs:annotation>
															<xs:documentation>[RCPA0008_10] CSc., PhD.</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:string">
																<xs:maxLength value="50"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
										<xs:element name="taxResidencies" minOccurs="0">
											<xs:complexType>
												<xs:sequence>
													<xs:element name="taxResidency" maxOccurs="unbounded">
														<xs:complexType>
															<xs:sequence>
																<xs:element name="taxationResidency">
																	<xs:annotation>
																		<xs:documentation>[RCPA0014_100] </xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="30"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="taxIdentificationNumber">
																	<xs:annotation>
																		<xs:documentation>[RCPA0014_101] </xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="50"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
										<xs:element name="representatives" minOccurs="1" maxOccurs="1">
											<xs:complexType>
												<xs:sequence>
													<xs:element name="representative" minOccurs="0" maxOccurs="unbounded">
														<xs:annotation>
															<xs:documentation>[RCPA0008_58] List of Related Parties, which have hierarchical relation with the type GUARDIAN or PARENT
Multiple</xs:documentation>
														</xs:annotation>
														<xs:complexType>
															<xs:sequence>
																<xs:element name="firstName" minOccurs="1" maxOccurs="1">
																	<xs:annotation>
																		<xs:documentation>[RCPA0008_64] </xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="80"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="middleNames" minOccurs="0" maxOccurs="1">
																	<xs:annotation>
																		<xs:documentation>[RCPA0008_65] </xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="80"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="partyID" minOccurs="1" maxOccurs="1">
																	<xs:annotation>
																		<xs:documentation>[RCPA0008_62] </xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="50"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="surName" minOccurs="1" maxOccurs="1">
																	<xs:annotation>
																		<xs:documentation>[RCPA0008_66] </xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="100"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="titleBefore" minOccurs="0" maxOccurs="1">
																	<xs:annotation>
																		<xs:documentation>[RCPA0008_63] </xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="50"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="titleBehind" minOccurs="0" maxOccurs="1">
																	<xs:annotation>
																		<xs:documentation>[RCPA0008_67] </xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="50"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
				<xs:element name="employee" minOccurs="1" maxOccurs="1">
					<xs:annotation>
						<xs:documentation>[RCPA0008_68] currently logged on worker who sends the request to DMS</xs:documentation>
					</xs:annotation>
					<xs:complexType>
						<xs:sequence>
							<xs:element name="firstName" minOccurs="1" maxOccurs="1">
								<xs:annotation>
									<xs:documentation>[RCPA0008_70] </xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:string">
										<xs:maxLength value="80"/>
									</xs:restriction>
								</xs:simpleType>
							</xs:element>
							<xs:element name="jobDescription" minOccurs="1" maxOccurs="1">
								<xs:annotation>
									<xs:documentation>[RCPA0008_72] </xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:string">
										<xs:maxLength value="75"/>
									</xs:restriction>
								</xs:simpleType>
							</xs:element>
							<xs:element name="organizationUnit" minOccurs="1" maxOccurs="1">
								<xs:annotation>
									<xs:documentation>[RCPA0008_73] name derived from primary Position</xs:documentation>
								</xs:annotation>
								<xs:complexType>
									<xs:sequence>
										<xs:element name="residence" minOccurs="1" maxOccurs="1">
											<xs:complexType>
												<xs:sequence>
													<xs:element name="city" minOccurs="1" maxOccurs="1">
														<xs:annotation>
															<xs:documentation>[RCPA0008_74] City of residence of Org Unit</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:string">
																<xs:maxLength value="200"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
							<xs:element name="surName" minOccurs="1" maxOccurs="1">
								<xs:annotation>
									<xs:documentation>[RCPA0008_71] </xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:string">
										<xs:maxLength value="100"/>
									</xs:restriction>
								</xs:simpleType>
							</xs:element>
							<xs:element name="staffId">
								<xs:simpleType>
									<xs:restriction base="xs:string">
										<xs:maxLength value="50"/>
									</xs:restriction>
								</xs:simpleType>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
				<xs:element name="reportDate" type="xs:date" minOccurs="1" maxOccurs="1">
					<xs:annotation>
						<xs:documentation>[RCPA0008_3] Date on which this document was generated</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="reportLanguage" type="xs:string" minOccurs="0">
					<xs:annotation>
						<xs:documentation>language of the report [CODELIST: LanguageDMS]</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="externalDocumentId" minOccurs="0">
					<xs:simpleType>
						<xs:restriction base="xs:string"/>
					</xs:simpleType>
				</xs:element>
				<xs:element name="technicalProperties" minOccurs="0">
					<xs:complexType>
						<xs:choice>
							<xs:element name="csv" type="xs:anyType"/>
							<xs:element name="jpg">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="dpi" type="xs:integer"/>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
						</xs:choice>
					</xs:complexType>
				</xs:element>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
</xs:schema>
