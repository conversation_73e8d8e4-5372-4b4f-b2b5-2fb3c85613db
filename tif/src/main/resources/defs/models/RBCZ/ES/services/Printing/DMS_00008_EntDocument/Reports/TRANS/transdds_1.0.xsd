<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/dms/dds/trans/transdds/1.0"
    xmlns="http://rb.cz/dms/dds/trans/transdds/1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="transdds">
        <xs:complexType>
            <xs:sequence>
                <xs:element maxOccurs="1" minOccurs="0" name="email" nillable="false">
                    <xs:annotation>
                        <xs:documentation>E-mail address of the report recipient</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:maxLength value="50"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="place" nillable="false">
                    <xs:annotation>
                        <xs:documentation>City of the branch where the report is generated</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:maxLength value="80"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0"
                    name="reportDate" nillable="false" type="xs:date">
                    <xs:annotation>
                        <xs:documentation>Date on which this report is generated</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0"
                    name="reportTime" nillable="false" type="xs:time"/>
                <xs:element maxOccurs="1" minOccurs="0"
                    name="reportLanguage" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Language code that is configured in the application that launched the service call [CODELIST: Language]</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element minOccurs="0" name="externalDocumentId"
                    nillable="false" type="xs:string"/>
                <xs:element maxOccurs="1" minOccurs="0"
                    name="technicalProperties" nillable="false">
                    <xs:complexType>
                        <xs:choice>
                            <xs:element maxOccurs="1" minOccurs="1"
                                name="csv" nillable="false" type="xs:anyType">
                                <xs:annotation>
                                    <xs:documentation>Csv</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="1"
                                name="jpg" nillable="false">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="dpi" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Dpi</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:integer">
                                    <xs:fractionDigits value="0"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:choice>
                    </xs:complexType>
                </xs:element>
                <xs:choice>
                    <xs:element name="payerAccount" nillable="false">
                        <xs:complexType>
                            <xs:sequence>
                                <xs:element name="accountNumber" nillable="false">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element minOccurs="0"
                                    name="name" nillable="false">
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="numberPart1" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="numberPart2" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="bankCode"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies particular bank. [CODELIST: BANK_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="currencyCode"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>[CODELIST: DOMESTIC_PAYMENT_ORDER_CURRENCY_CODES  ]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element minOccurs="0"
                                    name="accountType" nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>[CODELIST: AccountType]</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                            </xs:sequence>
                        </xs:complexType>
                    </xs:element>
                    <xs:element name="payerTermDeposit">
                        <xs:complexType>
                            <xs:sequence>
                                <xs:element name="termDepositNumber" nillable="false">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element minOccurs="0"
                                    name="name" nillable="false">
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="numberPart1" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="numberPart2" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="bankCode"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies particular bank. [CODELIST: BANK_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="currencyCode"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>[CODELIST: DOMESTIC_PAYMENT_ORDER_CURRENCY_CODES  ]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="depositNumber">
                                    <xs:annotation>
                                    <xs:documentation>Number of deposit</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                            </xs:sequence>
                        </xs:complexType>
                    </xs:element>
                </xs:choice>
                <xs:element minOccurs="0" name="cardType">
                    <xs:complexType>
                        <xs:choice>
                            <xs:element name="creditCardTypeCode" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>[CODELIST: CreditCardTypeCode]</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="debitCardTypeCode" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>[CODELIST: DebitCardTypeCode]</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:choice>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="paymentCardHolder">
                    <xs:annotation>
                        <xs:documentation>A name of a card holder</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="1" minOccurs="0" name="titlesBefore">
                                <xs:annotation>
                                    <xs:documentation>Titles in front of the name</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="firstName">
                                <xs:annotation>
                                    <xs:documentation>First name</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="lastName">
                                <xs:annotation>
                                    <xs:documentation>Last name</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="titlesBehind">
                                <xs:annotation>
                                    <xs:documentation>Titles behind the name</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element minOccurs="0" name="interval" nillable="false">
                    <xs:complexType>
                        <xs:sequence minOccurs="0">
                            <xs:element minOccurs="0" name="validFrom"
                                nillable="false" type="xs:date">
                                <xs:annotation>
                                    <xs:documentation>Contract duration from date.</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element minOccurs="0" name="validTo"
                                nillable="false" type="xs:date">
                                <xs:annotation>
                                    <xs:documentation>Contract duration till date.</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="1"
                    name="transactionDetails" nillable="false">
                    <xs:complexType>
                        <xs:choice>
                            <xs:element maxOccurs="unbounded"
                                minOccurs="1" name="transactionDetail" nillable="false">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="transactionDate"
                                    nillable="false" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>realization date of the transaction</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="accountingDate"
                                    nillable="false" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>settlement date of the transaction</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="transactionAmount" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>original (requested) amount and ccy</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="currencyCode"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency code of the currency folder. [CODELIST: DOMESTIC_PAYMENT_ORDER_CURRENCY_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="amount"
                                    nillable="false" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Amount</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="accountingAmount" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>settlement amount and ccy</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="currencyCode"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency code of the currency folder. [CODELIST: DOMESTIC_PAYMENT_ORDER_CURRENCY_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="amount"
                                    nillable="false" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Amount</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="paymentCardNumber"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>card number hash</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:choice>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="accountTransaction" nillable="false">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="idMovement">
                                    <xs:annotation>
                                    <xs:documentation>Identification of a transaction for maintenance (prefix 'A' = authorization, 'E' = Equa, without prefix = TS)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="accountNumber">
                                    <xs:annotation>
                                    <xs:documentation>Migrated bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the migrated bank account number (used in case account was migrated to TS)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the migrated bank account number (used in case account was migrated to TS)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="bankCode">
                                    <xs:annotation>
                                    <xs:documentation>Bank code of the migrated account (used in case account was migrated to TS)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="typeCode"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Transaction type code [CODELIST: TRANSACTION_TYPE_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="originatorMessage" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Message of the originator of the transaction.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="240"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="counterParty" nillable="false">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="accountName" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Account Name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="140"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="accountNumber" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Bank account number of the counter party.</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="numberPart1" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="numberPart2" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="bankCode"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies particular bank. [CODELIST: BANK_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="message" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Delivered information.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="140"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0"
                                    name="internalAccount" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Internal account</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="direction" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Direction of transaction. [LOV: All;Credit;Debit] [LOV: All;Credit;Debit]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="All"/>

                                    <xs:enumeration value="Credit"/>

                                    <xs:enumeration value="Debit"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="symbols" nillable="false">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="variable" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Variable symbol</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="constant" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Constant symbol</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="specific" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Specific symbol</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="fees" nillable="false">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="changeFee" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>fee for a change</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="amount"
                                    nillable="false" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Amount of money.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="currencyCode"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency code. [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="messageFee" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>fee for a message (sms)</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="amount"
                                    nillable="false" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Amount of money.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="currencyCode"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency code. [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="transactionFee" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>fee for a transaction</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="amount"
                                    nillable="false" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Amount of money.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="currencyCode"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency code. [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="feeDetails" nillable="false">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1"
                                    name="feeDetail" nillable="false">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="ordinalNumber"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Index of sorting items (items must be in same order)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="amount"
                                    nillable="false" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Amount of the transaction item.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of the transaction item. [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="realizationDate"
                                    nillable="false" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Date of the realization.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="initialAmount" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Initial amount and its currency code.</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="amount"
                                    nillable="false" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Amount</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0"
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of the transaction item. [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="exchangeRate"
                                    nillable="false" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Exchange Rate</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="shortDescription"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A short description of the transaction item.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="description"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A description of the transaction item.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0"
                                    name="actionTypeCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A type of the client´s action (eg. transfer, payment, standing payment, ..). [CODELIST: ACTION_TYPE_CODE]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0"
                                    name="bundelItemCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>[CODELIST: BundelItemCode]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="flags" nillable="false">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="allowRepeatTransaction"
                                    nillable="false" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>flag if the transaction can be repeated</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="allowRefundTransaction"
                                    nillable="false" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>flag if the transaction can be refunded</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="transactionCategory"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Transaction Category [CODELIST: TransactionCategory]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="movementType"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Movement Type [CODELIST: MovementType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="actionTypeCode"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A type of the client´s action (eg. transfer, payment, standing payment, ..). [CODELIST: ACTION_TYPE_CODE]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="creditCardTransaction" nillable="false">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="transactionCode"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Rubric code [CODELIST: CCMovementCodes]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="transactionType"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Credti card transaction/hold type - Rubrick code [CODELIST: CCMovementType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="transactionDescription" type="xs:string"/>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="description" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Debit info</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="2000"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="cardId"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Unique identification from processing (target) system point of view</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="merchantName">
                                    <xs:annotation>
                                    <xs:documentation>Merchant name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="transactionCity">
                                    <xs:annotation>
                                    <xs:documentation>Transaction address city</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="transactionNote">
                                    <xs:annotation>
                                    <xs:documentation>Transaction client note</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="debitCardTransaction" nillable="false">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="transactionId" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Unique identifier for transaction</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="description" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Debit info</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="2000"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="cardId"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Unique identification from processing (target) system point of view</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="transactionType"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Transaction type code [CODELIST: TRANSACTION_TYPE_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="embossName" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>embossed name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="25"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="transactionCategory"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Transaction Category [CODELIST: TransactionCategory]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="movementType"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Movement Type [CODELIST: MovementType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="actionTypeCode"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>action type code [CODELIST: ACTION_TYPE_CODE]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="authorizationCode" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Authorization code</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="merchantName">
                                    <xs:annotation>
                                    <xs:documentation>Merchant name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="transactionCity">
                                    <xs:annotation>
                                    <xs:documentation>Transaction address city</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="transactionNote">
                                    <xs:annotation>
                                    <xs:documentation>Transaction client note</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:choice>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="unbounded" name="data">
                                <xs:annotation>
                                    <xs:documentation>All information about transaction are in parameter data divided by pipelines.Output parameters in data are in following order: idmovement|numberpart1|numberpart2|bankcode|amount|currencycode|realizationdate|valuta|originatormessage|contranumberpart1|contranumberpart2|contrabankcode|contraaccname|contraaccinternal|contramessage|direction|varsymbol|constsymbol|specsymbol|messagefee|chargefee|changefee|canberepeated|canberefunded|amountinrequiredcurrency|requiredcurrencycode|paymentcardnumbersecret|note|merchantname|shopcity|transactioncategorycodemch|movementtypecodemch</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="2000"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                        </xs:choice>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
