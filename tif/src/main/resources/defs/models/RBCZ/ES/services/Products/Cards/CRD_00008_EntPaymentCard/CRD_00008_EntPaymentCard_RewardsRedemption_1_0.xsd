<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/services/entityService/paymentCard/RewardsRedemption_1_0"
    version="1.0.0"
    xmlns:oprewardsredemption_1_0="http://rb.cz/services/entityService/paymentCard/RewardsRedemption_1_0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="opRewardsRedemption_1_0">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="partyId">
                    <xs:annotation>
                        <xs:documentation>Party Id - who initiates the request</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:maxLength value="15"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element name="accountNumber">
                    <xs:annotation>
                        <xs:documentation>Account number from which bonus points are spent.</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="1" minOccurs="0" name="numberPart1">
                                <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="numberPart2">
                                <xs:annotation>
                                    <xs:documentation>Base part of the bank account number. The length is 35 because it is not a canonical type and it is as an identifier in backend system for credit type.
</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="35"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element minOccurs="0" name="bankCode" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank [CODELIST: BANK_CODES]</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element minOccurs="0"
                                name="currencyCode" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Currency of the bank account [CODELIST: Currency]</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element name="amount">
                    <xs:annotation>
                        <xs:documentation>Amount of bonus points which are required.</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:decimal">
                            <xs:fractionDigits value="2"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="opRewardsRedemption_1_0_res">
        <xs:complexType>
            <xs:sequence>
                <xs:element maxOccurs="1" minOccurs="1" name="bonusPoints">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded"
                                minOccurs="1" name="bonusPoint">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="bonusCode">
                                    <xs:annotation>
                                    <xs:documentation>A code of a meaning of the amount  [LOV: AVAILABLE_BONUS;CURRENT_DRAWED_BONUS;CURRENT_BONUS;TOTAL_BONUS;GAINED_GROUP_1;GAINED_GROUP_2;UNSPENT_FROM_PREVIOUS_PERIOD]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:enumeration value="AVAILABLE_BONUS"/>
                                    <xs:enumeration value="CURRENT_DRAWED_BONUS"/>
                                    <xs:enumeration value="CURRENT_BONUS"/>
                                    <xs:enumeration value="TOTAL_BONUS"/>
                                    <xs:enumeration value="GAINED_GROUP_1"/>
                                    <xs:enumeration value="GAINED_GROUP_2"/>
                                    <xs:enumeration value="UNSPENT_FROM_PREVIOUS_PERIOD"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="amount">
                                    <xs:annotation>
                                    <xs:documentation>Amount of bonus points</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:decimal">
                                    <xs:fractionDigits value="2"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
