<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/services/entityService/paymentCard/Activate_1_3"
    version="1.0.0"
    xmlns:opactivate_1_3="http://rb.cz/services/entityService/paymentCard/Activate_1_3" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="opActivate_1_3">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="identification">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="srcReferenceId">
                                <xs:annotation>
                                    <xs:documentation>Unique identification of the entity from consumer point of view</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element name="cardType">
                    <xs:annotation>
                        <xs:documentation>[LOV: Credit;Debit]</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:enumeration value="Credit"/>
                            <xs:enumeration value="Debit"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element name="cardIdentifier">
                    <xs:annotation>
                        <xs:documentation>An identifier of the required card</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="identificationCode">
                                <xs:annotation>
                                    <xs:documentation>Value of the identifier (type of the identifier is specified in the identificationType field)</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="64"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="identificationType">
                                <xs:annotation>
                                    <xs:documentation>Type of the code - core identifier or card hash [LOV: cardHash;coreId1;coreId2]</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:enumeration value="cardHash"/>
                                    <xs:enumeration value="coreId1"/>
                                    <xs:enumeration value="coreId2"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="cardStatus">
                                <xs:annotation>
                                    <xs:documentation>Status of the new card. (1 - Aktive, W - Aktive for mobile wallets) [LOV: 1;W]</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:enumeration value="1"/>
                                    <xs:enumeration value="W"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element name="accountNumber">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="1" minOccurs="0" name="numberPart1">
                                <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="numberPart2">
                                <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element minOccurs="0" name="bankCode" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank [CODELIST: BANK_CODES]</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element name="authorizationSignatures">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded" name="authorizationSignature">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:annotation>
                                    <xs:documentation>funguje lovMultipleTranslate</xs:documentation>
                                    </xs:annotation>
                                    <xs:element name="applicationId" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Identification of the channel which has been request sent.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="frontEndApplicationId" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Modul of the application</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="channelCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Identification of the channel which has been request sent. [CODELIST: CHANNEL_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="signatureDate" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Date of authorization</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="opActivate_1_3_res">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="identification">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="srcReferenceId">
                                <xs:annotation>
                                    <xs:documentation>Unique identification of the entity from consumer point of view</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element minOccurs="0" name="cardIdentifier">
                    <xs:annotation>
                        <xs:documentation>An identifier of the required card</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="identificationCode">
                                <xs:annotation>
                                    <xs:documentation>Value of the identifier (type of the identifier is specified in the identificationType field)</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="64"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="identificationType">
                                <xs:annotation>
                                    <xs:documentation>Type of the code - core identifier or card hash [LOV: cardHash;coreId1;coreId2]</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:enumeration value="cardHash"/>
                                    <xs:enumeration value="coreId1"/>
                                    <xs:enumeration value="coreId2"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element minOccurs="0" name="creditCardNewStatus" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Credit card new status  received from RPC after processing change of status [CODELIST: CreditCardStatus]</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="resultInfo">
                    <xs:annotation>
                        <xs:documentation>If there is accountNumbers in the request</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded"
                                minOccurs="1" name="resultItem">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="code" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A code of the error/warning [CODELIST: ERROR_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="description" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A description of the error/warning</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
