<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2017 (http://www.altova.com) by <PERSON> (Raiffeisenbank a.s.) -->
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/services/party/GetDetail_1_7/1.0"
    version="1.0.0"
    xmlns:opgetdetail_1_7="http://rb.cz/services/party/GetDetail_1_7/1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="opGetDetail_1_7">
        <xs:complexType>
            <xs:sequence>
                <xs:element maxOccurs="1" minOccurs="1" name="identificationCode">
                    <xs:annotation>
                        <xs:documentation>value of the identifier (type of the identifier is specified in the identificationType field)</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:maxLength value="30"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="1" name="identificationType">
                    <xs:annotation>
                        <xs:documentation>type of the identifier [LOV: PARTYID;MID;BIRTHCODE;REGNUM]</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:enumeration value="PARTYID"/>
                            <xs:enumeration value="MID"/>
                            <xs:enumeration value="BIRTHCODE"/>
                            <xs:enumeration value="REGNUM"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="requestedDetail">
                    <xs:annotation>
                        <xs:documentation>#IF not requested anything returs basicInfo</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:choice>
                            <xs:element maxOccurs="1" minOccurs="1"
                                name="allDetails" type="xs:boolean">
                                <xs:annotation>
                                    <xs:documentation>flag - get all available details or not</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:sequence>
                                <xs:element maxOccurs="1" minOccurs="0" name="additionalInfo">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="addInfo" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>flag to get additional info for private/commercial party</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element maxOccurs="1" minOccurs="0" name="consents">
                                    <xs:annotation>
                                    <xs:documentation>Currently not used. The service does not provide consets.</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="consents" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>flag if list of consents is required to be retrieved.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element maxOccurs="1" minOccurs="0" name="cares">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="care" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>flag if care detail is required</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="displayInEBanking" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>flag if care can be displayed in Ebanking</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="type" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Party Care type - PRV_PCARET, ACC_PCARET [CODELIST: UNI_PCARET]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element maxOccurs="1" minOccurs="0" name="categories">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="category" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>flag if category detail is required</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="type" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A party category type [CODELIST: UNI_PCATT]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element maxOccurs="1" minOccurs="0" name="contacts">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="contact" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>flag if contact detail is required</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="cardDeliveryAddress" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>This flag actually not used (for card delivery address is necessary send accouont number)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="accountNumber">
                                    <xs:annotation>
                                    <xs:documentation>if fulfilled then return addresses for credit card delivery connected with this account</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank [CODELIST: BANK_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element minOccurs="0" name="checks">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="check">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="type">
                                    <xs:annotation>
                                    <xs:documentation>[LOV: CBLClient;CBLDocument;FATCA/CRS]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="CBLClient"/>

                                    <xs:enumeration value="CBLDocument"/>

                                    <xs:enumeration value="FATCA/CRS"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:choice minOccurs="0">
                                    <xs:element name="cblDocument">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    minOccurs="1"
                                    name="identificationDocType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Type of the identification document. [CODELIST: UNI_PIDOCT]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="identificationDocId">
                                    <xs:annotation>
                                    <xs:documentation>Unique identification of the entity.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:choice>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element maxOccurs="1" minOccurs="0" name="documents">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="document" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>flag if document detail is required</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element maxOccurs="1" minOccurs="0" name="identifiers">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="identifier" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>flag if document detail is required</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element maxOccurs="1" minOccurs="0" name="relations">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="relation" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>flag if the relations detail is required</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="relatedPartyId">
                                    <xs:annotation>
                                    <xs:documentation>related PartyId - Non mandatory second parameter to filter only those relations of PartyId that are related to relatedPartyId</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element maxOccurs="1" minOccurs="0" name="treasuryDetails">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="treasuryDetail" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>flag if the treasury detail is required</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                            </xs:sequence>
                        </xs:choice>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="opGetDetail_1_7_res">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="basicInfo">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="partyId">
                                <xs:annotation>
                                    <xs:documentation>Party Id (Siebel identifier)</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="partyShortName">
                                <xs:annotation>
                                    <xs:documentation>A Shortcut for the Party, calculated by Siebel</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="partyName">
                                <xs:annotation>
                                    <xs:documentation>Composed Name (full name)</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="256"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="partyType">
                                <xs:annotation>
                                    <xs:documentation>Party type - Private or Commercial</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="partyStatus" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Party Status [CODELIST: GENERAL_PSTAT]</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="statusDate" type="xs:dateTime">
                                <xs:annotation>
                                    <xs:documentation>Date and time of the last update of Party Status.</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="isClient" type="xs:boolean">
                                <xs:annotation>
                                    <xs:documentation>Party is client (product owner)</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="isVIP" type="xs:boolean">
                                <xs:annotation>
                                    <xs:documentation>Party is VIP</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="isEligible" type="xs:boolean">
                                <xs:annotation>
                                    <xs:documentation>Customer eligible</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="customerSince" type="xs:date">
                                <xs:annotation>
                                    <xs:documentation>Customer since date</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="customerTill" type="xs:date">
                                <xs:annotation>
                                    <xs:documentation>Day when Party "leaves the bank" - all products has been closed.</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="leavingReason">
                                <xs:annotation>
                                    <xs:documentation>Reason for leaving the bank</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="preferredLanguage" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Preferred language [CODELIST: Language]</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:choice>
                                <xs:element name="privateParty">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="titlesBefore">
                                    <xs:annotation>
                                    <xs:documentation>Titles in front of the name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="firstName">
                                    <xs:annotation>
                                    <xs:documentation>First name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="middleNames">
                                    <xs:annotation>
                                    <xs:documentation>Set of Middle names</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="lastName">
                                    <xs:annotation>
                                    <xs:documentation>Last name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="birthLastName">
                                    <xs:annotation>
                                    <xs:documentation>Birth last name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="titlesBehind">
                                    <xs:annotation>
                                    <xs:documentation>Titles behind the name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="vocative">
                                    <xs:annotation>
                                    <xs:documentation>Vocative - 5. case (Citizenship AND Prefered language = "Czech") (for example Nováku, Gábo, Jarolíme, Klápště) ; in other case Last name (McGregor, Campbell); Calculated</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="gender" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Gender [CODELIST: PRV_GENDER]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="title">
                                    <xs:annotation>
                                    <xs:documentation>Title - Calculated from Gender</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="birthInfo">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="birthDate" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Date of birth</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="birthCode">
                                    <xs:annotation>
                                    <xs:documentation>Czech Birth Code - only for Czech citizens</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="11"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="phoneyBirthCode">
                                    <xs:annotation>
                                    <xs:documentation>Phoney birth code - code used like RČ in case when RČ is not available (e .g. person is not Czech or Slovak citizen and has not have czech birth code)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="countryOfBirth" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Country of birth [CODELIST: GENERAL_CNTR]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="cityOfBirth">
                                    <xs:annotation>
                                    <xs:documentation>City of birth</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="minor" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Minor - first time calculated; initial value derived from the Date of birth (if age under 18 then =Y)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="isStaff" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Flag if private party record is RBCZ employee</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="citizenship" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Citizenship [CODELIST: GENERAL_CNTR]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="secondaryCitizenships">
                                    <xs:annotation>
                                    <xs:documentation>Secondary citizenships</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="citizenship">
                                    <xs:annotation>
                                    <xs:documentation>Secondary citizenship [CODELIST: GENERAL_CNTR]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element name="commercialParty">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="companyName">
                                    <xs:annotation>
                                    <xs:documentation>Company name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="companyNameSuffix">
                                    <xs:annotation>
                                    <xs:documentation>Used for additional text printed in the contract after the company name and residence</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="taxId">
                                    <xs:annotation>
                                    <xs:documentation>Tax identification number - i.e. "DIČ"</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="registrationNumber">
                                    <xs:annotation>
                                    <xs:documentation>Company identification number - i.e. "IČ"</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="birthCode">
                                    <xs:annotation>
                                    <xs:documentation>Birt code - only for FOP</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="11"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="legalForm" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Legal Form [CODELIST: ACC_LEGFORM]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="legalStatus" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Basic legal form [CODELIST: ACC_BLEGFORM]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="registeredCompanyName">
                                    <xs:annotation>
                                    <xs:documentation>Company name registered - received from Albertina system</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="registeredCountry" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Country of commercial registration [CODELIST: GENERAL_CNTR]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                            </xs:choice>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="additionalInfo">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="1" minOccurs="0" name="taxation">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="taxResidency" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A taxation residency [CODELIST: GENERAL_CNTR]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="taxId" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Tax identification number - ID relates to the Taxation residency country (for both type of party - private/commercila)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="taxOffice" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Tax office - from Albertina</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="beneficialStatement" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>The value will be true for Party who presented "Beneficial owner statement" document and has Tax domicile &lt;&gt; Czech Republic.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="beneficialOwner">
                                    <xs:annotation>
                                    <xs:documentation>Identification of the beneficial owner</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="fatca">
                                    <xs:annotation>
                                    <xs:documentation>Info about The Foreign Account Tax Compliance Act, cross border tax compliance used primary for US taxpayers.</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    minOccurs="0"
                                    name="signatureDate" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Date when the FATCA document form was signed.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="documentation" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Type of FATCA form document e.g. W8BEN. [CODELIST: FatcaDocumentation]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="actionCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Code of action (process step) which needs to be taken to comply with FATCA. Typically used to guide Account Managers throught the process of FATCA compliance. [CODELIST: FatcaActionCode]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="crs">
                                    <xs:annotation>
                                    <xs:documentation>Info about Common Reporting Standard (developed by OECD) used to share tax information between foreign countries.</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    minOccurs="0"
                                    name="signatureDate" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Date when CRS was signed.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="statusCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Status of CRS e.g. Not-Reportable (N), Reportable (R), etc. [CODELIST: CrsStatusCode]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="taxResidencyDetail">
                                    <xs:annotation>
                                    <xs:documentation>Info about tax residency for CRS.</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    minOccurs="0"
                                    name="country" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Country of tax residency [CODELIST: GENERAL_CNTR]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0"
                                    name="sourceCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Source from which tax residency was obtained e.g. "Prohlásila společ. za majitele", "Self Certification", etc. [CODELIST: CrsTaxResidenceSourceCode]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="rating">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="approvedRating">
                                    <xs:annotation>
                                    <xs:documentation>Approved (Final) Rating</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="balanceSheetDate" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Rating: Balance sheet date</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="countryOfRisk" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Country of risk [CODELIST: GENERAL_CNTR]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="countryRating">
                                    <xs:annotation>
                                    <xs:documentation>Country rating</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="riskClassification">
                                    <xs:annotation>
                                    <xs:documentation>Rating: Risk classification</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="ratingDateofApproval" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Date of Rating (Approval date)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="ratingModel">
                                    <xs:annotation>
                                    <xs:documentation>Rating: Model-Code</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="20"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="totalSales" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Rating: Risk classification</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="aml">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="amlResult" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>AML Alert</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="amlRiskRate" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>AML Risk rating</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="fisaFound" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>AML - FISA</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="pepFound" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>AML - PEP</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="cbl">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="cblCheckDate" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Date and time of last CBL check</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="cblCheckStatus" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>CBL check status</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="cblCheckResult" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>CBL check result</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="cblCheckUrL" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>CBL check result-link</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="defaults">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="hasDefaultFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Default</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="defaultCause1" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Default Cause 1</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="defaultCause2">
                                    <xs:annotation>
                                    <xs:documentation>Default Cause 2</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="20"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="defaultStartDate" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Default Start Date</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="defaultEndDate" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Default End Date</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="defaultEndCause" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Default End Cause</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="defaultClassification1" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Default Classification 1</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="defaultClassification2" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Default Classification 2</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="defaultCode1" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Default Indicator Code 1</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="defaultCode2" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Default Indicator Code 2</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="crossDefaultFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Cross default</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="crossDefaultNWU" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Cross default NWU</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="crossDefaultStartDate" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Cross default start date</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="variousFlags">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="thirdPartyClientFlag"
                                    nillable="false" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Third party client indication (X-sell not allowed)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="disableMailings" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Do not send direct mail</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="dontCall" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Do not call from CC</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="availedTradeServicesFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Availed Trade Services</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="bankAlertsDisabledFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Bank-originated alerts disabled</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="callCentreEnabledFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Call centre enabled</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="confirmationPageSignedFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Confirmation Page signed</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="doNotCallActivationFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Do not call activation</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="doNotCallBranchFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Do not call from branch</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="doNotDedupFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Do not deduplicate</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="doNotOfferLoansFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Do not offer active loans</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="eBankingenabledFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>E-banking enabled</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="faxEnabledFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Fax Enabled </xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="foreignCollAllowedFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Foreign collection allowed</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="homebankingEnabledFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Homebanking enabled</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="iblRegisteredFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>IBL registered</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="idDocPresentedFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>ID Document presented</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="nonResidentFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Non-Resident</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="swiftEnabledFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Swift enabled</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="serviceToSell" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Service to Sell</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="toBeDeleted" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>To be deleted</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="unacceptableFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Unacceptable Flag</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="paymentManualHandedFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Payment manual handed over, some booklet handed over to customer</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element minOccurs="0" name="segment" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Segment of a client [CODELIST: UNI_SEGM]</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element minOccurs="0" name="subSegment" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Subsegment of a client [CODELIST: UNI_SUBSEGM]</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="createdDate" type="xs:dateTime">
                                <xs:annotation>
                                    <xs:documentation>Date when the Customer Party relationship has been created.</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="dateOfConfirmSheet" type="xs:dateTime">
                                <xs:annotation>
                                    <xs:documentation>Date of Party Confirmation sheet signature</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="crmCodeGroup">
                                <xs:annotation>
                                    <xs:documentation>CRM code group</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="20"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="fraud" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Fraud</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="gams" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>GAMS [CODELIST: GENERAL_GAMS]</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="gccName" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Name of primary GCC for the customer</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="gccTopLevelName" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Top Level Of GCC (name)</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="gccPartyGroupName" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Top Level Of GCC (CoCuNuT ID)</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="gracePeriodInsufFunds" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Grace period of insufficient funds</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="crmIndustryCode" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>CRM code (industry code)</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="oenaceCategory" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>OENACE Category</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="oenaceDetail" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>OENACE Detail</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="pam" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Primary account manager</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="pamName">
                                <xs:annotation>
                                    <xs:documentation>Primary account manager full name</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="100"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="preferredBankAlertsChannel" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Preferred channel for Bank-defined alerts</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="preferredMarketingChannel" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Preferred Marketing Channel</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="primarySOL" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Primary SOL</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="productCustomersWantsUse" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Product the customer plans to use</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="successorId" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Successor ID</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="primaryRelManager" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Primary Relationship manager</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="careTypeRelManager" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Type of care for relationship manager [CODELIST: UNI_PCARET]</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="esa95" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>ESA95/ESA2010 (SNA) -  area of client's business focus</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="tigerCustomerType" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Customer Type in TIGER [CODELIST: PRV_COCUNUTT]</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="tigerCustomerStatus" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Status of Customer in TIGER</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="internalRiskLimit" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>RZB group Internal Limit (EUR mio)</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="sourceOfFunds" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Source of funds (Type of income - main source of income for client.)</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:choice>
                                <xs:element maxOccurs="1" minOccurs="1" name="privateParty">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="directChannelUsername" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Direct channels username unique within Private separately. Will be set up in the eBanking module. Unique id for Private user.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="primaryHousehold" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Name of household client is in</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="smsOTP" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>SMS OTP, from eBanking - authentication and certification mobile number</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="gsmFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Indication, if customer uses GSM banking. Filled from ebanking.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element maxOccurs="1" minOccurs="1" name="commercialParty">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="registrationCourt">
                                    <xs:annotation>
                                    <xs:documentation>Registration Court</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="certificateId">
                                    <xs:annotation>
                                    <xs:documentation>ID of the certificate of incorporation into Commercial register or Entrepreneurs Register</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="ancestorId" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Party ID of the ancestor's record; for company splitting purpose.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="dateFormed" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Date of company origination - data from Albertina</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="terminationDate" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Date of termination</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="terminationReason" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Reason of termination</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="tradesLicensingOffice" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Trades Licensing Office - data from Albertina</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="cityInhabitants" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Number of inhabitants of the city - data from Albertina</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="czNaceCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>CZ NACE code - Rating of companies based on areas. Related to statistical office</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="localOfficeFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Local Office / Local Branch - for marking regional party offices etc. not commercially registered or political parties</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="sepaDirectDebitFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>SEPA direct debit enabled</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="taxPurposesFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Permanent establishment for tax purposes</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                            </xs:choice>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element minOccurs="0" name="consents" type="xs:anyType">
                    <xs:annotation>
                        <xs:documentation>List of consents - Currently not used. The service does not provide consets.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="contacts">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="1" minOccurs="0" name="addresses">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="address">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="addressId">
                                    <xs:annotation>
                                    <xs:documentation>Unique identification of the address</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="addressName">
                                    <xs:annotation>
                                    <xs:documentation>User definable Mail Address name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="90"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="sblAddressSlot" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Mail address type (RESIDENCY or MAIL1 - MAIL9)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="addressee">
                                    <xs:annotation>
                                    <xs:documentation>Mail addressee</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="additionalInfo">
                                    <xs:annotation>
                                    <xs:documentation>Additional address info</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="street">
                                    <xs:annotation>
                                    <xs:documentation>Street name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="200"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="number">
                                    <xs:annotation>
                                    <xs:documentation>Building number/street number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="city">
                                    <xs:annotation>
                                    <xs:documentation>City name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="postalCode">
                                    <xs:annotation>
                                    <xs:documentation>ZIP code</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="country" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Country [CODELIST: GENERAL_CNTR]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="1"
                                    name="isResidency" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Is residency address, values True/False</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="isPreferred" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Preferred address True/ False</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="undeliverableFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Undeliverable flag. If the flag is set to "Y" the address is marked as undeliverable. Otherwise the address is ok.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="classification" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A classification - home/work [CODELIST: UNI_CONTCLASSIF]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="comment">
                                    <xs:annotation>
                                    <xs:documentation>Mail address comment</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="addressPointId">
                                    <xs:annotation>
                                    <xs:documentation>Uniq ID of delivered address by RUIAN.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="dataQualityVerified" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Flag if address check by data quality system (Purity).</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="modifiedAfterVerification" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Flag if address found and modified after data quality check.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="phones">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="phone">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="contactId">
                                    <xs:annotation>
                                    <xs:documentation>Unique identification of the contact (email address or phone number)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="contactName">
                                    <xs:annotation>
                                    <xs:documentation>User definable contact name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="90"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="contactType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Siebel technical field differentiating records in the same physical database table (LANDLINE_NUMBER, MOBILE_NUMBER, FAX_NUMBER, EMAIL_ADDRESS) [CODELIST: RB_COMM_MEDIUM_TYPE]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="number">
                                    <xs:annotation>
                                    <xs:documentation>A contact number.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberType">
                                    <xs:annotation>
                                    <xs:documentation>Contact number type (FAX1.., MOBILE1.., PHONE1..)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="isPreferred" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>A preferred contact</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="undeliverableFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Undeliverable flag</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="classification" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A classification - home/work [CODELIST: UNI_CONTCLASSIF]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="comment">
                                    <xs:annotation>
                                    <xs:documentation>A contact comment</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="qualityScore" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>The Score defines a quality of the contact. Higher score means worse quality. The Score comes from Purity.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="qualityExplanationCode">
                                    <xs:annotation>
                                    <xs:documentation>Explanation codes defines a quality defects of the contact. Codes comes from Purity.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="emails">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="email">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="contactId">
                                    <xs:annotation>
                                    <xs:documentation>Unique identification of the contact (email address or phone number)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="contactName">
                                    <xs:annotation>
                                    <xs:documentation>User definable contact name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="90"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="contactType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Siebel technical field differentiating records in the same physical database table (LANDLINE_NUMBER, MOBILE_NUMBER, FAX_NUMBER, EMAIL_ADDRESS) [CODELIST: RB_COMM_MEDIUM_TYPE]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="address">
                                    <xs:annotation>
                                    <xs:documentation>A contact address (email, URL ..)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="100"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="addressType">
                                    <xs:annotation>
                                    <xs:documentation>Email address type (EMAIL1, EMAIL2,...)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="isPreferred" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>A preferred contact</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="undeliverableFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Undeliverable flag</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="classification" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A classification - home/work [CODELIST: UNI_CONTCLASSIF]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="comment">
                                    <xs:annotation>
                                    <xs:documentation>A contact comment</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="qualityScore" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>The Score defines a quality of the contact. Higher score means worse quality. The Score comes from Purity.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="qualityExplanationCode">
                                    <xs:annotation>
                                    <xs:documentation>Explanation codes defines a quality defects of the contact. Codes comes from Purity.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="urls">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="url">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="srcReferenceId">
                                    <xs:annotation>
                                    <xs:documentation>Unique identification of the entity in source system</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="contactName">
                                    <xs:annotation>
                                    <xs:documentation>User definable contact name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="90"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="contactType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Siebel technical field differentiating records in the same physical database table (LANDLINE_NUMBER, MOBILE_NUMBER, FAX_NUMBER, EMAIL_ADDRESS) [CODELIST: RB_COMM_MEDIUM_TYPE]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="address">
                                    <xs:annotation>
                                    <xs:documentation>A contact address (email, URL ..)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="100"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="addressType">
                                    <xs:annotation>
                                    <xs:documentation>Email address type (EMAIL1, EMAIL2,...)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="preferred" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>A preferred contact</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="undeliverableFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Undeliverable flag</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="classification" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A classification - home/work [CODELIST: PRV_CONTCLASSIF]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="comment">
                                    <xs:annotation>
                                    <xs:documentation>A contact comment</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="qualityScore" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>The Score defines a quality of the contact. Higher score means worse quality. The Score comes from Purity.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="qualityExplanationCode">
                                    <xs:annotation>
                                    <xs:documentation>Explanation codes defines a quality defects of the contact. Codes comes from Purity.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="cares">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded"
                                minOccurs="1" name="care">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="primaryCare" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>flag if the care is primary or not</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="careType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>[CODELIST: UNI_PCARET]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="firstName">
                                    <xs:annotation>
                                    <xs:documentation>Worker First name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="lastName">
                                    <xs:annotation>
                                    <xs:documentation>Worker Last name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="photoUrl">
                                    <xs:annotation>
                                    <xs:documentation>URL address to the employee´s photo file</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="2000"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="division">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="divisionName">
                                    <xs:annotation>
                                    <xs:documentation>Organization unit name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="100"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="divisionSOL">
                                    <xs:annotation>
                                    <xs:documentation>Organization unit SOL</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="parentDivisionId" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Parent Division Id - Siebel foreign key to record of Organization Unit entity which to which position belongs [CODELIST: Branch]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="position">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="positionId">
                                    <xs:annotation>
                                    <xs:documentation>Positon Id - Siebel ROW_ID</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="positionNumber">
                                    <xs:annotation>
                                    <xs:documentation>Position serial number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="positionType">
                                    <xs:annotation>
                                    <xs:documentation>Position type name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="parentPositionId">
                                    <xs:annotation>
                                    <xs:documentation>Parent Position Id - Siebel foreign key to Parent Position</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="userId">
                                    <xs:annotation>
                                    <xs:documentation>User ID of format "CZA…". Joined field from Worker.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="contacts">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="phones">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="0" name="phone">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="contactType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Siebel technical field differentiating records in the same physical database table (LANDLINE_NUMBER, MOBILE_NUMBER, FAX_NUMBER, EMAIL_ADDRESS) [CODELIST: RB_COMM_MEDIUM_TYPE]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="number">
                                    <xs:annotation>
                                    <xs:documentation>A contact number.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="comment">
                                    <xs:annotation>
                                    <xs:documentation>A contact comment</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="emails">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="0" name="email">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="address">
                                    <xs:annotation>
                                    <xs:documentation>A contact address (email, URL ..)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="100"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="comment">
                                    <xs:annotation>
                                    <xs:documentation>A contact comment</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="displayInEBanking" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Displayed in Ebanking</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="comment">
                                    <xs:annotation>
                                    <xs:documentation>comment</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="categories">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded"
                                minOccurs="1" name="category">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="type" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Type of a category [CODELIST: UNI_PCATT]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="validity">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="validFrom" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Valid from, predefault value [Now]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="validTo" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Valid to</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="value">
                                    <xs:annotation>
                                    <xs:documentation>Value of a category - free text</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="100"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="categorization" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Value of a category [CODELIST: UNI_CTGZATION]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="comment">
                                    <xs:annotation>
                                    <xs:documentation>Comment</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element minOccurs="0" name="checks">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded" name="check">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="type">
                                    <xs:annotation>
                                    <xs:documentation>[LOV: CBLClient;CBLDocument;FATCA/CRS]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:enumeration value="CBLClient"/>
                                    <xs:enumeration value="CBLDocument"/>
                                    <xs:enumeration value="FATCA/CRS"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:choice minOccurs="0">
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="cbl">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    minOccurs="0" name="metadata">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    minOccurs="0"
                                    name="lastChecked" type="xs:date"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="comments">
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="2000"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:choice minOccurs="0">
                                    <xs:element name="cblClient">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="output">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    minOccurs="0"
                                    name="status" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>[CODELIST: GENERAL_CBLSTAT]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="result">
                                    <xs:annotation>
                                    <xs:documentation>Result of cbl Check
Values:
0 - OK/ V pořádku
1 - NOT OK/ Zamítnut
2 - NEEDS APPROVAL/ Vyžaduje schválení </xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="cblResultUrl" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>CBL check result-link</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0"
                                    name="pepFound" type="xs:boolean"/>

                                    <xs:element
                                    minOccurs="0"
                                    name="fisaFound" type="xs:boolean"/>

                                    <xs:element
                                    minOccurs="0"
                                    name="isirFound" type="xs:boolean"/>

                                    <xs:element
                                    minOccurs="0"
                                    name="iblFound" type="xs:boolean"/>

                                    <xs:element
                                    minOccurs="0"
                                    name="riskRating" type="xs:string"/>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="cblDocument">
                                    <xs:annotation>
                                    <xs:documentation>If not exists and type "CBL Document" was requested - it means: all existing CBL Document checks are requested to return.</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="input">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="trgReferenceId">
                                    <xs:annotation>
                                    <xs:documentation>Unique identification of the entity in target system</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="identificationDocId">
                                    <xs:annotation>
                                    <xs:documentation>Unique identification of the entity.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="1"
                                    name="identificationDocType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Type of the identification document. [CODELIST: UNI_PIDOCT]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element name="output">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="cblCheckStatus" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>CBL check status [CODELIST: GENERAL_CBLSTAT]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="result">
                                    <xs:annotation>
                                    <xs:documentation>Result of cbl Check
Values:
0 - OK/ V pořádku
1 - NOT OK/ Zamítnut
2 - NEEDS APPROVAL/ Vyžaduje schválení </xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="crsFatca">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    minOccurs="0" name="input">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="selfCertification" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Date of customer's self certification form.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="docEvidenceExpiry" type="xs:date"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="enhancedReview" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Date when relationship manager inquiry completed (and paper record search).</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0" name="fatca">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    minOccurs="0"
                                    name="documentation" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The data field includes a list of forms/documentary evidence a client may present which trigger a FATCA status change. Examples: 'W9' : W9 form and waiver are provided; 'W8BEN' : W8-BEN  is provided [CODELIST: FatcaDocumentation]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0"
                                    name="docProvidedOn" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>CRM has to fill in date when documentation has been provided by the client</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0" name="output">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    minOccurs="0" name="crs">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="status">
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="entityBusinessStatusCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>[CODELIST: CrsBusinessEntityStatus]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="entityBusinessStatusRO">
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="accountType">
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="actionCode">
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="actionExpiry" type="xs:date"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="activityCreation" type="xs:date"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="activityDue" type="xs:date"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="cureFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>This flag overrules all indicias except the tax residences defined by self-certification.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="3"
                                    minOccurs="0" name="taxResidencyDetail">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="taxResidency" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A taxation residency [CODELIST: GENERAL_CNTR]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="sourceCode">
                                    <xs:annotation>
                                    <xs:documentation>Reference to the source of the tax residence [CODELIST: CrsTaxResidenceSourceCode]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="20"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="identificationNumber">
                                    <xs:annotation>
                                    <xs:documentation>Tax Identification Number for tax residency</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="actionDetail">
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="status">
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0" name="fatca">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="status">
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0"
                                    name="documentation" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The data field includes a list of forms/documentary evidence a client may present which trigger a FATCA status change. Examples: 'W9' : W9 form and waiver are provided; 'W8BEN' : W8-BEN  is provided [CODELIST: FatcaDocumentation]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0"
                                    name="docProvidedOn" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>CRM has to fill in date when documentation has been provided by the client</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="highBalanceReviewCalc">
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="customerStatus">
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="accountType">
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="actionDetail">
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="actionCreated" type="xs:date"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="dueDateCalc" type="xs:date"/>

                                    <xs:element
                                    minOccurs="0"
                                    name="actionDone" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>FATCA data: "Datum FATCA dokončeno"/"FATCA done date"</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0"
                                    name="actionDoneFlag" type="xs:boolean"/>

                                    <xs:element
                                    minOccurs="0" name="taxResidencyDetail">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="sourceCode">
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="actionCode">
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0"
                                    name="TinType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>FATCA data: "Zdroj DR"/"Source TR" [CODELIST: FatcaClassificationOther]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="Tin">
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:choice>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:choice>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="documents">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded"
                                minOccurs="0" name="document">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="srcReferenceId">
                                    <xs:annotation>
                                    <xs:documentation>Unique identification of the entity in source system</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="documentId">
                                    <xs:annotation>
                                    <xs:documentation>Document identifier</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="documentType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Identification Document type [CODELIST: UNI_PIDOCT]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="validity">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="validFrom" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Valid from date</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="validTo" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Valid to date</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="issuer">
                                    <xs:annotation>
                                    <xs:documentation>Issuer</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="100"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="issuerCountry" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Country of issuer [CODELIST: GENERAL_CNTR]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="cblCheckStatus" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>CBL check status [CODELIST: GENERAL_CBLSTAT]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="cblCheckDate" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>CBL check date</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="unacceptable" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Unacceptable flag</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="primaryDocument" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Primary document flag</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="comment">
                                    <xs:annotation>
                                    <xs:documentation>Comment</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="groups" type="xs:anyType"/>
                <xs:element maxOccurs="1" minOccurs="0" name="identifiers">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded"
                                minOccurs="1" name="identifier">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="identificationCode">
                                    <xs:annotation>
                                    <xs:documentation>A value of the identifier (type of the identifier is specified in the identificationType field)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="identificationType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>[CODELIST: UNI_PIDT]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="unique" type="xs:boolean"/>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="validFrom" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Validity of identifier from</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="validTo" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Validity of identifier from</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element minOccurs="0" name="notes">
                    <xs:annotation>
                        <xs:documentation>Notes. Currrently in the service available only for Private party. (In master system available for both Private and Commercial party.)</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded"
                                minOccurs="1" name="note">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="noteId">
                                    <xs:annotation>
                                    <xs:documentation>Id of note (SBL row id)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="note">
                                    <xs:annotation>
                                    <xs:documentation>Note to customer </xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="5000"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="type">
                                    <xs:annotation>
                                    <xs:documentation>Type of note - value from picklist </xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="status">
                                    <xs:annotation>
                                    <xs:documentation>Currently  not used ,but is shown on gui .</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="relations">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded"
                                minOccurs="1" name="relation">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="relatedPartyId">
                                    <xs:annotation>
                                    <xs:documentation>Related Party Id (Siebel identifier)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="relatedPartyType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Related Party Type</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="relationType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Party Relation type [CODELIST: UNI_RELATIONSHIP]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="behalfAction" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Can act on behalf [CODELIST: GENERAL_ACTONBEHALF]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="relationValue" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Relation Value - usually percentage of the influence like ownership, share, etc.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="pgMembershipReason" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>P-G Membership reason - Filled from ISOR for GCC membership [CODELIST: GCC_MEMBERSHIP_REASON]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="gccGroupType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>GCC group type - Joined field GCC group type. Used only for GCC group membership [CODELIST: ACC_GCCCATEG]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="relationComment" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Comment</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="qualifiedRelationFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Calculated field indicating whether the relationship is of type "Qualified relationship" of "Relationship hierarchy" in Siebel. - Set to "Y" if the relationship is coming from the link between Account and Contact. Set to "N" if the relationship is coming from Party Relationship To BC.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="resultInfo">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded"
                                minOccurs="1" name="resultItem">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="resultCode">
                                    <xs:annotation>
                                    <xs:documentation>0=OK, 4=warning, 8=error [LOV: 0;4;8]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:enumeration value="0"/>
                                    <xs:enumeration value="4"/>
                                    <xs:enumeration value="8"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="code" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A code of the error/warning [CODELIST: ERROR_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="description">
                                    <xs:annotation>
                                    <xs:documentation>A description of the error/warning</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="2000"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="treasuryDetails">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="1" minOccurs="0" name="investmentProfile">
                                <xs:annotation>
                                    <xs:documentation>Investment profile</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="proportionalityTest">
                                    <xs:annotation>
                                    <xs:documentation>Test of proportionality which have section INV_FUNDS, INV_CERTIFICATES, SHARES, BONDS, RIGHTS, ASSET_MGMT which can have value number as points. </xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="0" name="instrumentEvaluation">
                                    <xs:annotation>
                                    <xs:documentation>Set of instrument</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="instrument">
                                    <xs:annotation>
                                    <xs:documentation>Investment profile - proportionalpart (Investor 1 - 6). Value "UNKNOWN" is as some part which ins´t include. [LOV: INV_FUNDS;INV_CERTIFICATES;SHARES;BONDS;RIGHTS;ASSET_MGMT;UNKNOWN]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="INV_FUNDS"/>

                                    <xs:enumeration value="INV_CERTIFICATES"/>

                                    <xs:enumeration value="SHARES"/>

                                    <xs:enumeration value="BONDS"/>

                                    <xs:enumeration value="RIGHTS"/>

                                    <xs:enumeration value="ASSET_MGMT"/>

                                    <xs:enumeration value="UNKNOWN"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="value" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Number as points</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="suitabilityTest">
                                    <xs:annotation>
                                    <xs:documentation>Suitability test have three parts advisory, investmentHorizon and assetManagement.</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="advisory">
                                    <xs:annotation>
                                    <xs:documentation>Advisory can have value INAPPROPRIATE, CONSERVATIVE, BALANCED, DYNAMIC, AGGRESIVE and UNKNOWN for value which isn´t include.  [LOV: INAPPROPRIATE;CONSERVATIVE;BALANCED;DYNAMIC;AGGRESIVE;UNKNOWN]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="INAPPROPRIATE"/>

                                    <xs:enumeration value="CONSERVATIVE"/>

                                    <xs:enumeration value="BALANCED"/>

                                    <xs:enumeration value="DYNAMIC"/>

                                    <xs:enumeration value="AGGRESIVE"/>

                                    <xs:enumeration value="UNKNOWN"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="investmentHorizon" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Investment Horizon have value numbers as years.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="assetManagement">
                                    <xs:annotation>
                                    <xs:documentation>Asset Management have same value as advisory: INAPPROPRIATE, CONSERVATIVE, BALANCED, DYNAMIC, AGGRESIVE and UNKNOWN for value which isn´t include.  [LOV: INAPPROPRIATE;CONSERVATIVE;BALANCED;DYNAMIC;AGGRESIVE;UNKNOWN]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="INAPPROPRIATE"/>

                                    <xs:enumeration value="CONSERVATIVE"/>

                                    <xs:enumeration value="BALANCED"/>

                                    <xs:enumeration value="DYNAMIC"/>

                                    <xs:enumeration value="AGGRESIVE"/>

                                    <xs:enumeration value="UNKNOWN"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="derivativeProportionalityTest">
                                    <xs:annotation>
                                    <xs:documentation>Derivative proportionality test can have value FULLYINEXPERIENCED, INEXPERIENCED, EXPERIENCED, VERYEXPERIENCED and UNKNOWN. [LOV: FULLYINEXPERIENCED;INEXPERIENCED;EXPERIENCED;VERYEXPERIENCED;UNKNOWN]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:enumeration value="FULLYINEXPERIENCED"/>
                                    <xs:enumeration value="INEXPERIENCED"/>
                                    <xs:enumeration value="EXPERIENCED"/>
                                    <xs:enumeration value="VERYEXPERIENCED"/>
                                    <xs:enumeration value="UNKNOWN"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="lastUpdateDate" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Date of the last update of the investment questionnaire</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="expirationDate" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>The expiration date of the investment questionnaire</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
