<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/dms/dds/K0000796/1.0"
    xmlns="http://rb.cz/dms/dds/K0000796/1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="K0000796">
        <xs:annotation>
            <xs:documentation>Smlouva o poskytování bankovních služeb </xs:documentation>
        </xs:annotation>
        <xs:complexType>
            <xs:sequence>
                <xs:element maxOccurs="1" minOccurs="0" name="technicalMetadata">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element minOccurs="0" name="documentId">
                                <xs:annotation>
                                    <xs:documentation>The unique identifier of the document assigned by the system Documentum (dctmId)</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="16"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element minOccurs="0" name="externalDocumentId">
                                <xs:annotation>
                                    <xs:documentation>External identification of the document. Unique ID generated by other application then DMS (documentId in DMS). In case of no externalId exist the document is identified by internal DMS ID (dctmId)</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="16"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element minOccurs="0" name="delivery">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element minOccurs="1"
                                    name="deliveryChannel" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>delivery channel of the document [CODELIST: FavouredConfirmationDeliveryChannel]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:choice minOccurs="0">
                                    <xs:element minOccurs="1" name="emails">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="email">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    minOccurs="0"
                                    name="sender" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>sender of email (From)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1"
                                    name="receiver" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>E-mail address of the report recipient (To)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="0"
                                    name="carbonCopy" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>carbon copy to receiver (Cc)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="0"
                                    name="blindCarbonCopy" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>blind carbon copy to receiver (Bcc)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0"
                                    name="subject" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>subject of email</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0"
                                    name="text" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>text of email </xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="additionalText" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>additional text for email </xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0" name="attachments">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded" name="attachment">
                                    <xs:annotation>
                                    <xs:documentation>list of possible attachments for a reprt</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="type">
                                    <xs:annotation>
                                    <xs:documentation>any document can be attached based on the documentId or an url (url link to open the document in DMS) [LOV: documentId;url]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="documentId"/>

                                    <xs:enumeration value="url"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="value" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>value of documentId or url</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0"
                                    name="documentName" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>name of the attachment</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="fax" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>fax of the report recipient</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="addresses">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="address">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    minOccurs="0" name="addressee">
                                    <xs:annotation>
                                    <xs:documentation>Mail addressee</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="additionalInfo">
                                    <xs:annotation>
                                    <xs:documentation>Additional address info</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="street">
                                    <xs:annotation>
                                    <xs:documentation>Street name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="200"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="number">
                                    <xs:annotation>
                                    <xs:documentation>Building number/street number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="city">
                                    <xs:annotation>
                                    <xs:documentation>City name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="postalCode">
                                    <xs:annotation>
                                    <xs:documentation>ZIP code</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0"
                                    name="country" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>[CODELIST: GENERAL_CNTR]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:choice>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element minOccurs="0"
                                name="reportLanguage" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>language of the report, default is czech [CODELIST: LanguageDMS]</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element minOccurs="0" name="fileName" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>name of the generated report</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="fileFormatProperties">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="fileType">
                                    <xs:annotation>
                                    <xs:documentation>Statement file type

abo (1 | 2) (XX | AV) ( c | n) 
abo (1 = ediční, 2 = vnitřní ) (XX = není AV, AV = je AV) 
(c = v Doplňující údaj je Counter Acc Name | n = v Doplňující údaj je Note ) 
abo1XXc ABO (ediční formát) bez AV pole s názvem protistrany (v poli Doplňující údaj) 
abo1XXn ABO (ediční formát) bez AV pole s poznámkou (v poli Doplňující údaj) 
abo2XXc ABO (vnitřní formát) bez AV pole s názvem protistrany (v poli Doplňující údaj) 
abo2XXn ABO (vnitřní formát) bez AV pole s poznámkou (v poli Doplňující údaj) 
abo1AVc ABO (ediční formát) s AV polem s názvem protistrany (v poli Doplňující údaj) 
abo1AVn ABO (ediční formát) s AV polem s poznámkou (v poli Doplňující údaj) 
abo2AVc ABO (vnitřní formát) s AV polem s názvem protistrany (v poli Doplňující údaj) 
abo2AVn ABO (vnitřní formát) s AV polem s poznámkou (v poli Doplňující údaj)  [LOV: abo1XXc;abo1XXn;abo2XXc;abo2XXn;abo1AVc;abo1AVn;abo2AVc;abo2AVn;csv;gemini1;gemini2;jpg;pdf;PDFA-1a;PDFA-1b;xml;quest]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:enumeration value="abo1XXc"/>
                                    <xs:enumeration value="abo1XXn"/>
                                    <xs:enumeration value="abo2XXc"/>
                                    <xs:enumeration value="abo2XXn"/>
                                    <xs:enumeration value="abo1AVc"/>
                                    <xs:enumeration value="abo1AVn"/>
                                    <xs:enumeration value="abo2AVc"/>
                                    <xs:enumeration value="abo2AVn"/>
                                    <xs:enumeration value="csv"/>
                                    <xs:enumeration value="gemini1"/>
                                    <xs:enumeration value="gemini2"/>
                                    <xs:enumeration value="jpg"/>
                                    <xs:enumeration value="pdf"/>
                                    <xs:enumeration value="PDFA-1a"/>
                                    <xs:enumeration value="PDFA-1b"/>
                                    <xs:enumeration value="xml"/>
                                    <xs:enumeration value="quest"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="addParams">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="addParam">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="code">
                                    <xs:annotation>
                                    <xs:documentation>[LOV: dpi]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="dpi"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="value" type="xs:string"/>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element minOccurs="0" name="attachments">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="attachment">
                                    <xs:annotation>
                                    <xs:documentation>list of possible attachments for a reprt</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="type">
                                    <xs:annotation>
                                    <xs:documentation>any document can be attached based on the documentId or an url (url link to open the document in DMS) [LOV: documentId;url]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="documentId"/>

                                    <xs:enumeration value="url"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="value" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>value of documentId or url</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="documentName" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>name of the attachment</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="place" nillable="false">
                                <xs:annotation>
                                    <xs:documentation>City of the branch where the report is generated</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="80"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="timestamp">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="reportDate"
                                    nillable="false" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Date on which this report is generated</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="reportTime"
                                    nillable="false" type="xs:time">
                                    <xs:annotation>
                                    <xs:documentation>Time on which this report is generated</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element name="party">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="basicInfo">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="partyId">
                                    <xs:annotation>
                                    <xs:documentation>Party Id (Siebel identifier)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="firstName" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Jan</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="80"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="middleNames">
                                    <xs:annotation>
                                    <xs:documentation>Amos</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="80"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="lastName" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Komenský</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="100"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="titleBefore">
                                    <xs:annotation>
                                    <xs:documentation>Title before the first name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="titleBehind">
                                    <xs:annotation>
                                    <xs:documentation>Title behind the surname</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="birthCode">
                                    <xs:annotation>
                                    <xs:documentation>Czech Birth Code</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="11"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="birthDate" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Date of birth</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="contact">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="phoneNumber" type="xs:string"/>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="email" type="xs:string"/>
                                    <xs:element name="addresses">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="address">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="addressType">
                                    <xs:annotation>
                                    <xs:documentation>address type (R - residential, C - contact) [LOV: R;C]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="R"/>

                                    <xs:enumeration value="C"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0" name="addressee">
                                    <xs:annotation>
                                    <xs:documentation>Mail addressee</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="additionalInfo">
                                    <xs:annotation>
                                    <xs:documentation>Additional address info</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="street">
                                    <xs:annotation>
                                    <xs:documentation>Street name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="200"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="number">
                                    <xs:annotation>
                                    <xs:documentation>Building number/street number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="city">
                                    <xs:annotation>
                                    <xs:documentation>City name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="country" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Country (String value - not code)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element name="postalCode">
                                    <xs:annotation>
                                    <xs:documentation>ZIP code</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="1" name="employee">
                    <xs:annotation>
                        <xs:documentation>worker signing/approving the document on behalf of RBCZ</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence maxOccurs="1" minOccurs="1">
                            <xs:element maxOccurs="1" minOccurs="1" name="staffId">
                                <xs:annotation>
                                    <xs:documentation>employee CZA id</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="firstName">
                                <xs:annotation>
                                    <xs:documentation>First name</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="80"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="middleNames">
                                <xs:annotation>
                                    <xs:documentation>Middle names</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="80"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="1" name="lastName">
                                <xs:annotation>
                                    <xs:documentation>Last name
</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="100"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="titleBefore">
                                <xs:annotation>
                                    <xs:documentation>Title before the first name</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="titleBehind">
                                <xs:annotation>
                                    <xs:documentation>Title behind the surname</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="role" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>role of employee (ie: Bankovní poradce)</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="city">
                                <xs:annotation>
                                    <xs:documentation>City of residence of Org Unit</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="200"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="inCity" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>In city (Praze, Berouně .... atd)</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="representative">
                    <xs:annotation>
                        <xs:documentation>representative (deputy)</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="representativeRole" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Role of representative (ie: "Rodič")</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element minOccurs="1"
                                name="prefferedIdNumber" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Number/Code of preffered ID document ("123456878 CZ")</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element minOccurs="1" name="IdType" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Type of ID ("Občanský průkaz")</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="basicInfo">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="firstName" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Jan</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="80"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="middleNames">
                                    <xs:annotation>
                                    <xs:documentation>Amos</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="80"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="lastName" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Komenský</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="100"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="titleBefore">
                                    <xs:annotation>
                                    <xs:documentation>Title before the first name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="titleBehind">
                                    <xs:annotation>
                                    <xs:documentation>Title behind the surname</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="birthCode">
                                    <xs:annotation>
                                    <xs:documentation>Czech Birth Code</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="11"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="birthDate" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Date of birth</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="contact">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="phoneNumber" type="xs:string"/>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="email" type="xs:string"/>
                                    <xs:element name="addresses">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="address">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="addressType">
                                    <xs:annotation>
                                    <xs:documentation>address type (R - residential, C - contact) [LOV: R;C]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="R"/>

                                    <xs:enumeration value="C"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0" name="addressee">
                                    <xs:annotation>
                                    <xs:documentation>Mail addressee</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="additionalInfo">
                                    <xs:annotation>
                                    <xs:documentation>Additional address info</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="street">
                                    <xs:annotation>
                                    <xs:documentation>Street name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="200"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="number">
                                    <xs:annotation>
                                    <xs:documentation>Building number/street number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="city">
                                    <xs:annotation>
                                    <xs:documentation>City name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="country" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Country (String value - not code)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0" name="postalCode">
                                    <xs:annotation>
                                    <xs:documentation>ZIP code</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element name="subjectOfAgreement">
                    <xs:annotation>
                        <xs:documentation>Subject of agreement
</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="1" minOccurs="1" name="bankAccount">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="bankAccountFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Flag - is bankAccount part of this agreement? True/false</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="bankAccountData">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="productDescription" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Product description ("eKonto KOMPLET")</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="account">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="currencies">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="currency">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="currencyType">
                                    <xs:annotation>
                                    <xs:documentation>Main or the other currencies of the account [LOV: MAIN;OTHER]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="MAIN"/>

                                    <xs:enumeration value="OTHER"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="currencyDescription" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency (value - not code) ("CZK - Česká koruna")</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="reportParam">
                                    <xs:annotation>
                                    <xs:documentation>Report parameters - Parametry výpisu</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    name="frequency" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>frequency ("Měsíčně")</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="channel" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Channel ("Internetové bankovnictví")</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="1" name="nonInterestReserve">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="nonInterestReserveFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Flag - is noninterest reserve part of this agreement? True/false</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="1" name="savingAccount">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="savingAccountFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Flag - is saving account part of this agreement? True/false</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="0" name="savingAccountData">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="productDescription" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Product description ("eKonto KOMPLET")</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="account">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="currencies">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="currency">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="currencyType">
                                    <xs:annotation>
                                    <xs:documentation>Main or the other currencies of the account [LOV: MAIN;OTHER]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="MAIN"/>

                                    <xs:enumeration value="OTHER"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="currencyDescription" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency (value - not code) ("CZK - Česká koruna")</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="reportParam">
                                    <xs:annotation>
                                    <xs:documentation>Report parameters - Parametry výpisu</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    name="frequency" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>frequency ("Měsíčně")</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="channel" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Channel ("Internetové bankovnictví")</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="1" name="directBanking">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="directBankingFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Flag - is saving directBanking part of this agreement? True/false</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="directBankingUsers">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="directBankingUser">
                                    <xs:annotation>
                                    <xs:documentation>User of direct  banking</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="sequence" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Sequence for order of printing on the template.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="clientNumber" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Client number </xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="directBankingUserFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Is user of the direct banking also account owner? (Yes = True)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0" name="disponentInfo">
                                    <xs:annotation>
                                    <xs:documentation>Disponent name</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="firstName" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Jan</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="80"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="middleNames">
                                    <xs:annotation>
                                    <xs:documentation>Amos</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="80"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="lastName" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Komenský</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="100"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="titleBefore">
                                    <xs:annotation>
                                    <xs:documentation>Title before the first name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="titleBehind">
                                    <xs:annotation>
                                    <xs:documentation>Title behind the surname</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="birthCode">
                                    <xs:annotation>
                                    <xs:documentation>Czech Birth Code</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="11"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="birthDate" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Date of birth</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element name="authMethods">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="authMethod">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="methodType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Auth method (MEK, OEK, MTOKEN)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="oekKeyId" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Personal electronic key (Osobní elektronický klíč s evidenčním číslem)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="tokenPhoneNumber" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>"+420 602 756 857"</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="1" name="debitCards">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="debitCardFlag" type="xs:boolean"/>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="0" name="debitCardData">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="sequence" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Sequence for order of printing on the template.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="debitCardType">
                                    <xs:annotation>
                                    <xs:documentation>[LOV: STICKER;ELECTRONIC;EMBOSS]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="STICKER"/>

                                    <xs:enumeration value="ELECTRONIC"/>

                                    <xs:enumeration value="EMBOSS"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="expressDelivery" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Express deliver of the card? (Yes = True)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="debitCardDescription" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Description ("Bezkontaktní nálepka MC", ......)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="cardHolderName" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Name of the Card Holder (Printed on card) ("Joe Doe")</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="limits">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    name="posLimit" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>limit amount of cashless payments per week</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="atmLimit" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>limit amount of cash payments per week</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="totalLimit" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>limit amount of all payments per week</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="insuranceList">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="insurance">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="insuranceType">
                                    <xs:annotation>
                                    <xs:documentation>[LOV: TRAVEL;ABUSE]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="TRAVEL"/>

                                    <xs:enumeration value="ABUSE"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="insuranceFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Flag - True, insurance is agreed</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="variation" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Type of insurrance (description) ("K4 - základ")</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="cardUser">
                                    <xs:annotation>
                                    <xs:documentation>Card user</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    name="cardUserFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Is account owner also user of card? (Yes - True)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0" name="basicInfo">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="firstName" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Jan</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="80"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="middleNames">
                                    <xs:annotation>
                                    <xs:documentation>Amos</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="80"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="lastName" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Komenský</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="100"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="titleBefore">
                                    <xs:annotation>
                                    <xs:documentation>Title before the first name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="titleBehind">
                                    <xs:annotation>
                                    <xs:documentation>Title behind the surname</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="birthCode">
                                    <xs:annotation>
                                    <xs:documentation>Czech Birth Code</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="11"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="birthDate" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Date of birth</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0" name="contact">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="phoneNumber" type="xs:string"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="email" type="xs:string"/>

                                    <xs:element name="addresses">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="address">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="addressType">
                                    <xs:annotation>
                                    <xs:documentation>address type (R - residential, C - contact) [LOV: R;C]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="R"/>

                                    <xs:enumeration value="C"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0" name="addressee">
                                    <xs:annotation>
                                    <xs:documentation>Mail addressee</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="additionalInfo">
                                    <xs:annotation>
                                    <xs:documentation>Additional address info</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="street">
                                    <xs:annotation>
                                    <xs:documentation>Street name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="200"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="number">
                                    <xs:annotation>
                                    <xs:documentation>Building number/street number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="city">
                                    <xs:annotation>
                                    <xs:documentation>City name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="country" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Country (String value - not code)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0" name="postalCode">
                                    <xs:annotation>
                                    <xs:documentation>ZIP code</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
