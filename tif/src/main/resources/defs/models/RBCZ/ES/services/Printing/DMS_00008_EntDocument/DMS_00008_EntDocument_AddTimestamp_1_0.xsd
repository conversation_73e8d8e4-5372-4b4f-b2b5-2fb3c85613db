<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/services/entityService/document/AddTimestamp_1_0"
    version="1.0.0"
    xmlns:opaddtimestamp_1_0="http://rb.cz/services/entityService/document/AddTimestamp_1_0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="opAddTimestamp_1_0">
        <xs:complexType>
            <xs:sequence>
                <xs:element maxOccurs="1" minOccurs="1" name="documentId">
                    <xs:annotation>
                        <xs:documentation>unique ID of the document (bar code)</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:maxLength value="16"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="1" name="format">
                    <xs:annotation>
                        <xs:documentation>File type extension to identify the content of the document format (doc, pdf, xls, ...)</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:maxLength value="400"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="opAddTimestamp_1_0_res">
        <xs:complexType>
            <xs:sequence>
                <xs:element maxOccurs="1" minOccurs="0"
                    name="globalExpiration" type="xs:dateTime">
                    <xs:annotation>
                        <xs:documentation>Expiry date of document, content all signatures and timestamps.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
