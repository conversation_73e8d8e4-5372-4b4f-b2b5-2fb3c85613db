<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/services/entityService/paymentCard/GetTransactionDetail_1_2"
    version="1.0.0"
    xmlns:opgettransactiondetail_1_2="http://rb.cz/services/entityService/paymentCard/GetTransactionDetail_1_2" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="opGetTransactionDetail_1_2">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="accountNumber">
                    <xs:annotation>
                        <xs:documentation>Account number of a payment card</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="1" minOccurs="0" name="numberPart1">
                                <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="numberPart2">
                                <xs:annotation>
                                    <xs:documentation>Base part of the bank account number. The length is 35 because it is not a canonical type and it is as an identifier in backend system for credit type.</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="35"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="bankCode" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element name="transactionIdentifier">
                    <xs:complexType>
                        <xs:choice>
                            <xs:element name="primaryId" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Transaction identifier from RPC</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="transactionId">
                                <xs:annotation>
                                    <xs:documentation>Transaction Identifier</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                        </xs:choice>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="opGetTransactionDetail_1_2_res">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="cardTransaction">
                    <xs:annotation>
                        <xs:documentation>A detail of a required card transaction</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="transactionId">
                                <xs:annotation>
                                    <xs:documentation>Transaction identifier from Transaction system</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element minOccurs="0" name="primaryId" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Transaction identifier from RPC</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="transactionAmount" type="xs:decimal">
                                <xs:annotation>
                                    <xs:documentation>Original amount</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="transactionCurrency" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Currency of original amount [CODELIST: Currency]</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element minOccurs="0"
                                name="accountingAmount" type="xs:decimal">
                                <xs:annotation>
                                    <xs:documentation>Accounting amount</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element minOccurs="0"
                                name="accountingCurrency" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Currency of accounting amount [CODELIST: Currency]</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="cardIdentifiers">
                                <xs:annotation>
                                    <xs:documentation>Card identifiers</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="cardIdentifier">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="identificationCode">
                                    <xs:annotation>
                                    <xs:documentation>A value of the identifier (type of the identifier is specified in the identificationType field)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="64"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="identificationType">
                                    <xs:annotation>
                                    <xs:documentation>The meaning of the identificationCode [LOV: cardHash;coreId1;coreId2]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="cardHash"/>

                                    <xs:enumeration value="coreId1"/>

                                    <xs:enumeration value="coreId2"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="maskedNumber" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Secret card number</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element minOccurs="0" name="embossedName">
                                <xs:annotation>
                                    <xs:documentation>Embossed name</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="25"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="transactionDate" type="xs:dateTime">
                                <xs:annotation>
                                    <xs:documentation>Date when the transaction has been realized by the customer.</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="accountingDate" type="xs:dateTime">
                                <xs:annotation>
                                    <xs:documentation>Date when the transaction has been accounted.</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element minOccurs="0" name="merchantDescription">
                                <xs:annotation>
                                    <xs:documentation>Description of a transaction</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="2000"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element minOccurs="0" name="note">
                                <xs:annotation>
                                    <xs:documentation>A custom note provided by client to mark transaction with note.</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="254"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element minOccurs="0" name="shopId">
                                <xs:annotation>
                                    <xs:documentation>Unique id of the shop.</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="23"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:choice>
                                <xs:element name="creditCardTransaction">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="transactionType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Credit card transaction type [CODELIST: CCMovementType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="transCodeDescription" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Description of a card hold code (description of a rubrick code)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="transactionCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Rubric code</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element name="debitCardTransaction">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="movementType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Transaction type according to MCH codelist MovementType</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="transactionCategory" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Transaction type according to MCH codelist TransactionCategory</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="authorizationCode">
                                    <xs:annotation>
                                    <xs:documentation>Authorization code</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                            </xs:choice>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
