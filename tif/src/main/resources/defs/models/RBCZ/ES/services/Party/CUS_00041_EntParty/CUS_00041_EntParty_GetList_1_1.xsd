<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/services/party/GetList_1_1/1.0"
    version="1.0.0"
    xmlns:opgetlist_1_1="http://rb.cz/services/party/GetList_1_1/1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="opGetList_1_1">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="source">
                    <xs:annotation>
                        <xs:documentation>There are two sources of party data. It is possible to select one of them. If not selected, default source=CRM system. [LOV: CRM system;Master data center]</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:enumeration value="CRM system"/>
                            <xs:enumeration value="Master data center"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element minOccurs="0" name="recordsReturnedMax" type="xs:integer">
                    <xs:annotation>
                        <xs:documentation>Maximal number of returned records. If more records are returned, just indication of this state and number of found records are returned.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="searchingCriteria">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element minOccurs="0" name="basicInfo">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element minOccurs="0" name="partyId">
                                    <xs:annotation>
                                    <xs:documentation>Party Id (Siebel identifier)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="partyType">
                                    <xs:annotation>
                                    <xs:documentation>Party type [LOV: Private;Commercial]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:enumeration value="Private"/>
                                    <xs:enumeration value="Commercial"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:choice minOccurs="0">
                                    <xs:element minOccurs="1" name="privateParty">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="firstName">
                                    <xs:annotation>
                                    <xs:documentation>First name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="lastName">
                                    <xs:annotation>
                                    <xs:documentation>Last name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="partyName">
                                    <xs:annotation>
                                    <xs:documentation>Composed name for Private (Last name First name)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="gender" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Gender [CODELIST: PRV_GENDER]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="birthInfo">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="birthDate" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Date of birth</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="birthCode">
                                    <xs:annotation>
                                    <xs:documentation>Czech birth code - only for Czech citizens or phoney birth code - for other personas</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="11"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element minOccurs="1" name="commercialParty">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="companyName">
                                    <xs:annotation>
                                    <xs:documentation>Company name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="registrationNumber">
                                    <xs:annotation>
                                    <xs:documentation>Company identification number - i.e. "IČ" or phoney company identification number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:choice>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="contacts">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="address">
                                    <xs:annotation>
                                    <xs:documentation>Only residence address search is available</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    minOccurs="0" name="city">
                                    <xs:annotation>
                                    <xs:documentation>City name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="street">
                                    <xs:annotation>
                                    <xs:documentation>Street name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="200"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="number">
                                    <xs:annotation>
                                    <xs:documentation>Building number/street number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="email">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="address" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>An email address</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="phone">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="number">
                                    <xs:annotation>
                                    <xs:documentation>A contact number.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="identifier">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="identificationType">
                                    <xs:annotation>
                                    <xs:documentation>type of the identifier [LOV: MID]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:enumeration value="MID"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="identificationCode">
                                    <xs:annotation>
                                    <xs:documentation>value of the identifier (type of the identifier is specified in the identificationType field)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element minOccurs="0" name="care">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="userId">
                                    <xs:annotation>
                                    <xs:documentation>User ID of format "CZA…". Joined field from Worker.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="userRelationship" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>User relationship to returned parties.
Values:
"MYPORTFOLIO" - Portfolio osoby. Zobrazí se osoby, u kterých je péče uživatele označena jako primární.
"MYTEAMPARTIES" - osoby mého týmu. Zobrazí se osoby, u kterých jsem já nebo někdo z mých podřízených (pokud mám podřízené) v primární péči.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element minOccurs="0" name="relatedProducts">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element minOccurs="0" name="accountNumber">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="paymentCard">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    minOccurs="0"
                                    name="maskedNumber" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A masked payment card number</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="validity">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    minOccurs="0" name="validTo">
                                    <xs:annotation>
                                    <xs:documentation>Payment card valid to, format "MM/YY"</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="paymentCardHolder">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    minOccurs="0"
                                    name="name" type="xs:string"/>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="opGetList_1_1_res">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="maxRecordsExceeded" type="xs:boolean">
                    <xs:annotation>
                        <xs:documentation>True if count of searched records &gt; recordsReturnedMax
False if count of searched records &lt;= recordsReturnedMax</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="foundRecords">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded" name="party">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="basicInfo">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    minOccurs="0" name="partyId">
                                    <xs:annotation>
                                    <xs:documentation>Party Id (Siebel identifier)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="100"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="partyName">
                                    <xs:annotation>
                                    <xs:documentation>Composed Name (full name)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="256"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="partyType">
                                    <xs:annotation>
                                    <xs:documentation>Party type [LOV: Private;Commercial]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="Private"/>

                                    <xs:enumeration value="Commercial"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="partyStatus" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Party Status [CODELIST: GENERAL_PSTAT]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="isClient" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Party is client (product owner)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="isEligible" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Customer eligible</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:choice minOccurs="0">
                                    <xs:element name="privateParty">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="firstName">
                                    <xs:annotation>
                                    <xs:documentation>First name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="lastName">
                                    <xs:annotation>
                                    <xs:documentation>Last name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="birthInfo">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="birthDate" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Date of birth</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="birthCode">
                                    <xs:annotation>
                                    <xs:documentation>Czech Birth Code - only for Czech citizens</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="citizenship" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Citizenship [CODELIST: GENERAL_CNTR]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="commercialParty">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="companyName">
                                    <xs:annotation>
                                    <xs:documentation>Company name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="registrationNumber">
                                    <xs:annotation>
                                    <xs:documentation>Company identification number - i.e. "IČ" or phoney company identification number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="legalForm" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Legal Form [CODELIST: ACC_LEGFORM]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="legalStatus" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Basic legal form [CODELIST: ACC_BLEGFORM]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="registeredCountry" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Country of commercial registration [CODELIST: GENERAL_CNTR]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0" name="selfEmployedPerson">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="birthInfo">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="birthCode">
                                    <xs:annotation>
                                    <xs:documentation>Czech Birth Code - only for Czech citizens</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="11"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:choice>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="contacts">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    minOccurs="0" name="addresses">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="2" name="address">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    minOccurs="0"
                                    name="addressType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Mail address type (RESIDENCY or MAIL1-9) [CODELIST: GENERAL_MAILTYPE]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="1"
                                    name="isResidency" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Is residency address, values True/False</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="1"
                                    name="isPreferred" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Is preferred contact, values True/False</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0" name="city">
                                    <xs:annotation>
                                    <xs:documentation>City name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="street">
                                    <xs:annotation>
                                    <xs:documentation>Street name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0" name="number">
                                    <xs:annotation>
                                    <xs:documentation>Building number/street number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="130"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="preferredPhoneNumber">
                                    <xs:annotation>
                                    <xs:documentation>A contact number.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="130"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="preferredEmailAddress">
                                    <xs:annotation>
                                    <xs:documentation>A contact address (email, URL ..)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="130"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="additionalInfo">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    minOccurs="0"
                                    name="segment" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Segment of a client [CODELIST: UNI_SEGM]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="subSegment" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Subsegment of a client [CODELIST: UNI_SUBSEGM]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
