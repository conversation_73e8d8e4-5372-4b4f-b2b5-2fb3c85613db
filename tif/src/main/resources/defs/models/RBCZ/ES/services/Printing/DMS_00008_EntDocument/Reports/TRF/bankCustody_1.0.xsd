<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/dms/dds/trf/bankCustody/1.0"
    version="1.0.0"
    xmlns:rtrfccs="http://rb.cz/dms/dds/trf/bankCustody/1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="bankCustody">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="technicalMetadata">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element minOccurs="0" name="documentId">
                                <xs:annotation>
                                    <xs:documentation>The unique identifier of the document assigned by the system Documentum (dctmId)</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="16"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element minOccurs="0"
                                name="externalDocumentId" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>External identification of the document. Unique ID generated by other application then DMS (bar code on the document). In case of no externalId exist the document is identified by internal DMS ID (dctmId)</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element minOccurs="0" name="delivery">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element minOccurs="1"
                                    name="deliveryChannel" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>delivery channel of the document [CODELIST: FavouredConfirmationDeliveryChannel]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:choice minOccurs="0">
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1"
                                    name="email" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>E-mail address of the report recipient</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    name="fax" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>fax of the report recipient</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:choice>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element minOccurs="0"
                                name="reportLanguage" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>language of the report, default is czech [CODELIST: LanguageDMS]</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element minOccurs="0" name="fileName" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>name of the generated report</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element minOccurs="0" name="fileFormatProperties">
                                <xs:complexType>
                                    <xs:choice>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="csv"
                                    nillable="false" type="xs:anyType"/>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="jpg" nillable="false">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="dpi" nillable="false">
                                    <xs:simpleType>

                                    <xs:restriction base="xs:integer">

                                    <xs:fractionDigits value="0"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="pdf">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="type">
                                    <xs:annotation>
                                    <xs:documentation>type of PDF; defaul = no pdfA [LOV: PDFA-1a;PDFA-1b;default]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="PDFA-1a"/>

                                    <xs:enumeration value="PDFA-1b"/>

                                    <xs:enumeration value="default"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:choice>
                                </xs:complexType>
                            </xs:element>
                            <xs:element minOccurs="0" name="attachments">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="attachment">
                                    <xs:annotation>
                                    <xs:documentation>list of possible attachments for a reprt</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="type">
                                    <xs:annotation>
                                    <xs:documentation>any document can be attached based on the documentId or an url (url link to open the document in DMS) [LOV: documentId;url]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="documentId"/>

                                    <xs:enumeration value="url"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="value" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>value of documentId or url</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="documentName" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>name of the attachment</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="place" nillable="false">
                                <xs:annotation>
                                    <xs:documentation>City of the branch where the report is generated</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="80"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="timestamp">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="reportDate"
                                    nillable="false" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Date on which this report is generated</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="reportTime"
                                    nillable="false" type="xs:time">
                                    <xs:annotation>
                                    <xs:documentation>Time on which this report is generated</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element name="provider">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="partyId">
                                <xs:annotation>
                                    <xs:documentation>Party Id (Siebel identifier)</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="commercialParty">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="companyName">
                                    <xs:annotation>
                                    <xs:documentation>Company name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="companyNameSuffix">
                                    <xs:annotation>
                                    <xs:documentation>Used for additional text printed in the contract after the company name and residence</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="taxId">
                                    <xs:annotation>
                                    <xs:documentation>Tax identification number - i.e. "DIČ"</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="registrationNumber">
                                    <xs:annotation>
                                    <xs:documentation>Company identification number - i.e. "IČ"</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="birthCode">
                                    <xs:annotation>
                                    <xs:documentation>Birt code - only for FOP</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="11"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="legalForm" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Legal Form [CODELIST: ACC_LEGFORM]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="legalStatus" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Basic legal form [CODELIST: ACC_BLEGFORM]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="registeredCompanyName">
                                    <xs:annotation>
                                    <xs:documentation>Company name registered - received from Albertina system</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="registeredCountry" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Country of commercial registration [CODELIST: GENERAL_CNTR]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element name="collateralCard">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="collateraCardId" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Plní se IDKZ -cislo transakce z ISOR</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="collateralGroup" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>[CODELIST: CollateralGroup]</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="collateralType" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>[CODELIST: CollateralType]</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element name="employees">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded"
                                minOccurs="1" name="employee" nillable="false">
                                <xs:annotation>
                                    <xs:documentation>worker signing/approving the document on behalf of RBCZ</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence maxOccurs="1" minOccurs="1">
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="firstName" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>First name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="80"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="middleNames" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Middle names</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="80"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="lastName" nillable="false">
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="100"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="titleBefore" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Title before the first name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="titleBehind" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Title behind the surname</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="jobDescription" nillable="false">
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="75"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="organizationUnit" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>name derived from primary Position</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="residence" nillable="false">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="city" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>City of residence of Org Unit</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="200"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="staffId" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>employee CZA id</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
