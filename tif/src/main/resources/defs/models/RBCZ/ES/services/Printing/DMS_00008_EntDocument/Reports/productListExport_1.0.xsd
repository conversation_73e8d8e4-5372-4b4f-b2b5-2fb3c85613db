<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/dms/dds/productListExport/1.0"
    xmlns="http://rb.cz/dms/dds/productListExport/1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="productListExport">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="technicalMetadata">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element minOccurs="0" name="documentId">
                                <xs:annotation>
                                    <xs:documentation>The unique identifier of the document assigned by the system Documentum (dctmId)</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="16"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element minOccurs="0" name="externalDocumentId">
                                <xs:annotation>
                                    <xs:documentation>External identification of the document. Unique ID generated by other application then DMS (documentId in DMS). In case of no externalId exist the document is identified by internal DMS ID (dctmId)</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="16"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element minOccurs="0" name="delivery">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element minOccurs="1"
                                    name="deliveryChannel" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>delivery channel of the document [CODELIST: FavouredConfirmationDeliveryChannel]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:choice minOccurs="0">
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1"
                                    name="email" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>E-mail address of the report recipient</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    name="fax" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>fax of the report recipient</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1" name="addresses">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="address">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    minOccurs="0" name="addressee">
                                    <xs:annotation>
                                    <xs:documentation>Mail addressee</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="additionalInfo">
                                    <xs:annotation>
                                    <xs:documentation>Additional address info</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="street">
                                    <xs:annotation>
                                    <xs:documentation>Street name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="200"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="number">
                                    <xs:annotation>
                                    <xs:documentation>Building number/street number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="city">
                                    <xs:annotation>
                                    <xs:documentation>City name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="postalCode">
                                    <xs:annotation>
                                    <xs:documentation>ZIP code</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0"
                                    name="country" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>[CODELIST: GENERAL_CNTR]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:choice>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element minOccurs="0"
                                name="reportLanguage" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>language of the report, default is czech [CODELIST: LanguageDMS]</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element minOccurs="0" name="fileName" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>name of the generated report</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="fileFormatProperties">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="fileType">
                                    <xs:annotation>
                                    <xs:documentation>Statement file type

abo (1 | 2) (XX | AV) ( c | n) 
abo (1 = ediční, 2 = vnitřní ) (XX = není AV, AV = je AV) 
(c = v Doplňující údaj je Counter Acc Name | n = v Doplňující údaj je Note ) 
abo1XXc ABO (ediční formát) bez AV pole s názvem protistrany (v poli Doplňující údaj) 
abo1XXn ABO (ediční formát) bez AV pole s poznámkou (v poli Doplňující údaj) 
abo2XXc ABO (vnitřní formát) bez AV pole s názvem protistrany (v poli Doplňující údaj) 
abo2XXn ABO (vnitřní formát) bez AV pole s poznámkou (v poli Doplňující údaj) 
abo1AVc ABO (ediční formát) s AV polem s názvem protistrany (v poli Doplňující údaj) 
abo1AVn ABO (ediční formát) s AV polem s poznámkou (v poli Doplňující údaj) 
abo2AVc ABO (vnitřní formát) s AV polem s názvem protistrany (v poli Doplňující údaj) 
abo2AVn ABO (vnitřní formát) s AV polem s poznámkou (v poli Doplňující údaj)  [LOV: abo1XXc;abo1XXn;abo2XXc;abo2XXn;abo1AVc;abo1AVn;abo2AVc;abo2AVn;csv;gemini1;gemini2;jpg;pdf;PDFA-1a;PDFA-1b;xml;quest]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:enumeration value="abo1XXc"/>
                                    <xs:enumeration value="abo1XXn"/>
                                    <xs:enumeration value="abo2XXc"/>
                                    <xs:enumeration value="abo2XXn"/>
                                    <xs:enumeration value="abo1AVc"/>
                                    <xs:enumeration value="abo1AVn"/>
                                    <xs:enumeration value="abo2AVc"/>
                                    <xs:enumeration value="abo2AVn"/>
                                    <xs:enumeration value="csv"/>
                                    <xs:enumeration value="gemini1"/>
                                    <xs:enumeration value="gemini2"/>
                                    <xs:enumeration value="jpg"/>
                                    <xs:enumeration value="pdf"/>
                                    <xs:enumeration value="PDFA-1a"/>
                                    <xs:enumeration value="PDFA-1b"/>
                                    <xs:enumeration value="xml"/>
                                    <xs:enumeration value="quest"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="addParams">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="addParam">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="code">
                                    <xs:annotation>
                                    <xs:documentation>[LOV: dpi]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="dpi"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="value" type="xs:string"/>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element minOccurs="0" name="attachments">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="attachment">
                                    <xs:annotation>
                                    <xs:documentation>list of possible attachments for a reprt</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="type">
                                    <xs:annotation>
                                    <xs:documentation>any document can be attached based on the documentId or an url (url link to open the document in DMS) [LOV: documentId;url]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="documentId"/>

                                    <xs:enumeration value="url"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="value" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>value of documentId or url</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="documentName" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>name of the attachment</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="place" nillable="false">
                                <xs:annotation>
                                    <xs:documentation>City of the branch where the report is generated</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="80"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="timestamp">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="reportDate"
                                    nillable="false" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Date on which this report is generated</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="reportTime"
                                    nillable="false" type="xs:time">
                                    <xs:annotation>
                                    <xs:documentation>Time on which this report is generated</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:choice>
                    <xs:element maxOccurs="1" minOccurs="1" name="loans">
                        <xs:complexType>
                            <xs:sequence>
                                <xs:element maxOccurs="unbounded" name="loan">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element minOccurs="0"
                                    name="productType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Product type</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="companyName" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Client or owner name</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="contractNumber" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Contract number</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="dueDate" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Due or maturity date</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="totalAmount">
                                    <xs:annotation>
                                    <xs:documentation>Total loan amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    minOccurs="1" name="amount">
                                    <xs:annotation>
                                    <xs:documentation>Amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="1"
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of amount [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="balance">
                                    <xs:annotation>
                                    <xs:documentation>Actual loan balance</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="amount">
                                    <xs:annotation>
                                    <xs:documentation>Amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of amount [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="status" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Loan status</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="overdue" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Overdue indicator</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                            </xs:sequence>
                        </xs:complexType>
                    </xs:element>
                    <xs:element maxOccurs="1" minOccurs="1" name="bankGuarenteesIssued">
                        <xs:complexType>
                            <xs:sequence>
                                <xs:element maxOccurs="unbounded" name="bankGuarantee">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element minOccurs="0"
                                    name="productType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Product type</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="companyName" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Client or owner name</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="referenceNumber" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Reference number</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="beneficiant" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Name of the receiver</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="dealDate" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Deal date</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="validToDate" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Valid to date</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="status" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Bank guarantee status</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="amount">
                                    <xs:annotation>
                                    <xs:documentation>Amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="amount">
                                    <xs:annotation>
                                    <xs:documentation>Amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of amount [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="facility" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Part of the facility</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                            </xs:sequence>
                        </xs:complexType>
                    </xs:element>
                    <xs:element maxOccurs="1" minOccurs="1" name="bankGuarenteesReceived">
                        <xs:complexType>
                            <xs:sequence>
                                <xs:element maxOccurs="unbounded" name="bankGuarantee">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element minOccurs="0"
                                    name="productType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Product type</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="companyName" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Client or owner name</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="referenceNumber" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Reference number</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="issuerName" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Issuer of bank guarantee</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="adviceDate" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Advice date</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="validToDate" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Valid to date</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="status" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Bank guarantee status</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="amount">
                                    <xs:annotation>
                                    <xs:documentation>Amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="amount">
                                    <xs:annotation>
                                    <xs:documentation>Amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of amount [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                            </xs:sequence>
                        </xs:complexType>
                    </xs:element>
                    <xs:element maxOccurs="1" minOccurs="1" name="letterOfCreditImport">
                        <xs:complexType>
                            <xs:sequence>
                                <xs:element maxOccurs="unbounded" name="letterOfCredit">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element minOccurs="0"
                                    name="productType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Product type</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="companyName" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Client or owner name</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="referenceNumber" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Reference number</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="beneficiant" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Beneficiant</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="dealDate" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Deal date</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="dueDate" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Due date</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="validToDate" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Valid to date</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="status" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Letter Of Credit Status</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="amount">
                                    <xs:annotation>
                                    <xs:documentation>Amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="amount">
                                    <xs:annotation>
                                    <xs:documentation>Amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of amount [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="maxAmount">
                                    <xs:annotation>
                                    <xs:documentation>Max amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="amount">
                                    <xs:annotation>
                                    <xs:documentation>Amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of amount [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="facility" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Part of the facility</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                            </xs:sequence>
                        </xs:complexType>
                    </xs:element>
                    <xs:element maxOccurs="1" minOccurs="1" name="letterOfCreditExport">
                        <xs:complexType>
                            <xs:sequence>
                                <xs:element maxOccurs="unbounded" name="letterOfCredit">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element minOccurs="0"
                                    name="productType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Product type</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="companyName" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Client or owner name</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="referenceNumber" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Reference number</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="issuerName" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Issuer name</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="status" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Letter Of Credit Status</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="adviceDate" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Advice date</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="validToDate" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Valid to date</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="amount">
                                    <xs:annotation>
                                    <xs:documentation>Amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="amount">
                                    <xs:annotation>
                                    <xs:documentation>Amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of amount [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                            </xs:sequence>
                        </xs:complexType>
                    </xs:element>
                    <xs:element maxOccurs="1" minOccurs="1" name="documentaryCollectionImport">
                        <xs:complexType>
                            <xs:sequence>
                                <xs:element maxOccurs="unbounded" name="documentaryCollection">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element minOccurs="0"
                                    name="productType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Product type</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="companyName" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Client or owner name</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="referenceNumber" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Reference number</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="senderName" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Sender name</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="status" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Documentary Collection Status</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="dealDate" type="xs:date"/>
                                    <xs:element minOccurs="0" name="amount">
                                    <xs:annotation>
                                    <xs:documentation>Amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="amount">
                                    <xs:annotation>
                                    <xs:documentation>Amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of amount [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                            </xs:sequence>
                        </xs:complexType>
                    </xs:element>
                    <xs:element maxOccurs="1" minOccurs="1" name="documentaryCollectionExport">
                        <xs:complexType>
                            <xs:sequence>
                                <xs:element maxOccurs="unbounded" name="documentaryCollection">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element minOccurs="0"
                                    name="productType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Product type</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="companyName" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Client or owner name</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="referenceNumber" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Reference number</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="receiverName" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Receiver name</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="adviceDate" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Advice date</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="status" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Documentary Collection Status</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="amount">
                                    <xs:annotation>
                                    <xs:documentation>Amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="amount">
                                    <xs:annotation>
                                    <xs:documentation>Amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of amount [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                            </xs:sequence>
                        </xs:complexType>
                    </xs:element>
                </xs:choice>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
