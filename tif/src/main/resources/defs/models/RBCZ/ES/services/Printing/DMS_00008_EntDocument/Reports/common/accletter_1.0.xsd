<?xml version="1.0" encoding="UTF-8"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:ns1="http://rb.cz/dms/dds/accletter/1.0" targetNamespace="http://rb.cz/dms/dds/accletter/1.0" elementFormDefault="qualified" attributeFormDefault="unqualified" version="1.0.6">
	<xs:element name="accletter">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="email" minOccurs="0" maxOccurs="unbounded">
					<xs:annotation>
						<xs:documentation>E-mail address of the report recipient</xs:documentation>
					</xs:annotation>
					<xs:simpleType>
						<xs:restriction base="xs:string">
							<xs:maxLength value="50"/>
						</xs:restriction>
					</xs:simpleType>
				</xs:element>
				<xs:element name="place" minOccurs="0" maxOccurs="1">
					<xs:annotation>
						<xs:documentation>City of the branch where the report is generated</xs:documentation>
					</xs:annotation>
					<xs:simpleType>
						<xs:restriction base="xs:string">
							<xs:maxLength value="80"/>
						</xs:restriction>
					</xs:simpleType>
				</xs:element>
				<xs:element name="reportDate" type="xs:date" minOccurs="1" maxOccurs="1">
					<xs:annotation>
						<xs:documentation>Date on which this report is generated</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="reportTime" type="xs:time" minOccurs="0" maxOccurs="1">
					<xs:annotation>
						<xs:documentation>Time on which this report is generated</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="reportLanguage" type="xs:string" minOccurs="0">
					<xs:annotation>
						<xs:documentation>[CODELIST: LanguageDMS]</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="deliveryChannel" type="xs:string" minOccurs="0">
					<xs:annotation>
						<xs:documentation>[CODELIST: FavouredConfirmationDeliveryChannel]</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="externalDocumentId" minOccurs="0">
					<xs:annotation>
						<xs:documentation>ID generated by another system, its own unique code</xs:documentation>
					</xs:annotation>
					<xs:simpleType>
						<xs:restriction base="xs:string">
							<xs:maxLength value="32"/>
						</xs:restriction>
					</xs:simpleType>
				</xs:element>
				<xs:element name="documentId" type="xs:string" minOccurs="0">
					<xs:annotation>
						<xs:documentation>documentID generated by DMS</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="esdoTracked" type="xs:boolean" minOccurs="0"/>
				<xs:element name="controlledPrint" type="xs:boolean" minOccurs="0"/>
				<xs:element name="technicalProperties" minOccurs="0">
					<xs:complexType>
						<xs:choice>
							<xs:element name="csv" type="xs:anyType" nillable="false" minOccurs="1" maxOccurs="1"/>
							<xs:element name="jpg" nillable="false" minOccurs="1" maxOccurs="1">
								<xs:complexType>
									<xs:sequence minOccurs="1" maxOccurs="1">
										<xs:element name="dpi" nillable="false" minOccurs="1" maxOccurs="1">
											<xs:simpleType>
												<xs:restriction base="xs:integer">
													<xs:fractionDigits value="0"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
							<xs:element name="pdf">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="type">
											<xs:annotation>
												<xs:documentation>type of PDF; defaul = no pdfA [LOV: PDFA-1a;PDFA-1b;default]</xs:documentation>
											</xs:annotation>
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:enumeration value="PDFA-1a"/>
													<xs:enumeration value="PDFA-1b"/>
													<xs:enumeration value="default"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
						</xs:choice>
					</xs:complexType>
				</xs:element>
				<xs:element name="acceptanceLetter">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="contract">
								<xs:annotation>
									<xs:documentation>Accepted contract/proposal</xs:documentation>
								</xs:annotation>
								<xs:complexType>
									<xs:sequence>
										<xs:element name="docNumber" type="xs:string">
											<xs:annotation>
												<xs:documentation>Number of contract (proposal)</xs:documentation>
											</xs:annotation>
										</xs:element>
										<xs:element name="docDate" type="xs:date">
											<xs:annotation>
												<xs:documentation>Date when contract/proposal was made</xs:documentation>
											</xs:annotation>
										</xs:element>
										<xs:element name="approvalDate" type="xs:date" minOccurs="0" maxOccurs="1">
											<xs:annotation>
												<xs:documentation>Date when contract/proposal was approved</xs:documentation>
											</xs:annotation>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
							<xs:element name="currencyCode" type="xs:string" minOccurs="1">
								<xs:annotation>
									<xs:documentation>Requested product currency code [CODELIST: Currency]</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="amount" minOccurs="0">
								<xs:annotation>
									<xs:documentation>Loan total amount requested</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:decimal">
										<xs:totalDigits value="18"/>
										<xs:fractionDigits value="5"/>
									</xs:restriction>
								</xs:simpleType>
							</xs:element>
							<xs:element name="interestRate" minOccurs="0">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="totalRate">
											<xs:annotation>
												<xs:documentation>Total interest rate</xs:documentation>
											</xs:annotation>
											<xs:simpleType>
												<xs:restriction base="xs:decimal">
													<xs:totalDigits value="10"/>
													<xs:fractionDigits value="4"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="period" type="xs:string">
											<xs:annotation>
												<xs:documentation>Interest period [CODELIST: FrequencyType]</xs:documentation>
											</xs:annotation>
										</xs:element>
										<xs:element name="float" minOccurs="0">
											<xs:annotation>
												<xs:documentation>Filled only for float type of interest rate</xs:documentation>
											</xs:annotation>
											<xs:complexType>
												<xs:sequence>
													<xs:element name="baseRate">
														<xs:annotation>
															<xs:documentation>Base interest rate (PRIBOR, LIBOR, ...)</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:decimal">
																<xs:totalDigits value="10"/>
																<xs:fractionDigits value="4"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
													<xs:element name="baseRateType" type="xs:string">
														<xs:annotation>
															<xs:documentation>Type of base rate (PRIBOR-1M, PRIBOR-3M, ...)</xs:documentation>
														</xs:annotation>
													</xs:element>
													<xs:element name="rate">
														<xs:annotation>
															<xs:documentation>Added rate. TotalRate = baseRate + rate</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:decimal">
																<xs:totalDigits value="10"/>
																<xs:fractionDigits value="4"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
							<xs:element name="instalment" minOccurs="0">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="count" type="xs:integer">
											<xs:annotation>
												<xs:documentation>Instalment count</xs:documentation>
											</xs:annotation>
										</xs:element>
										<xs:element name="amount">
											<xs:annotation>
												<xs:documentation>Instalment amount</xs:documentation>
											</xs:annotation>
											<xs:simpleType>
												<xs:restriction base="xs:decimal">
													<xs:totalDigits value="18"/>
													<xs:fractionDigits value="5"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="period" type="xs:string">
											<xs:annotation>
												<xs:documentation>Instalment period [CODELIST: FrequencyType]</xs:documentation>
											</xs:annotation>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
							<xs:element name="accountNumber" minOccurs="0">
								<xs:annotation>
									<xs:documentation>Account to which product is related to</xs:documentation>
								</xs:annotation>
								<xs:complexType>
									<xs:sequence>
										<xs:element name="numberPart1" minOccurs="0" maxOccurs="1">
											<xs:annotation>
												<xs:documentation>Prefix part of the bank account number</xs:documentation>
											</xs:annotation>
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:maxLength value="6"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="numberPart2">
											<xs:annotation>
												<xs:documentation>Base part of the bank account number</xs:documentation>
											</xs:annotation>
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:maxLength value="10"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="bankCode" type="xs:string">
											<xs:annotation>
												<xs:documentation>Part of the bank account number which identifies the bank</xs:documentation>
											</xs:annotation>
										</xs:element>
										<xs:element name="currencyCode" type="xs:string">
											<xs:annotation>
												<xs:documentation>Currency of the bank account [CODELIST: Currency]</xs:documentation>
											</xs:annotation>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
							<xs:element name="address" minOccurs="0">
								<xs:annotation>
									<xs:documentation>Contact address for requested product instance</xs:documentation>
								</xs:annotation>
								<xs:complexType>
									<xs:sequence>
										<xs:element name="srcReferenceId" minOccurs="0">
											<xs:annotation>
												<xs:documentation>Unique identification of the address</xs:documentation>
											</xs:annotation>
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:maxLength value="15"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="addressee">
											<xs:annotation>
												<xs:documentation>Mail addressee</xs:documentation>
											</xs:annotation>
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:maxLength value="255"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="additionalInfo" minOccurs="0" maxOccurs="1">
											<xs:annotation>
												<xs:documentation>Additional address info</xs:documentation>
											</xs:annotation>
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:maxLength value="255"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="street" minOccurs="0" maxOccurs="1">
											<xs:annotation>
												<xs:documentation>Street name</xs:documentation>
											</xs:annotation>
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:maxLength value="200"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="number">
											<xs:annotation>
												<xs:documentation>Building number/street number</xs:documentation>
											</xs:annotation>
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:maxLength value="30"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="city">
											<xs:annotation>
												<xs:documentation>City name</xs:documentation>
											</xs:annotation>
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:maxLength value="50"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="postalCode">
											<xs:annotation>
												<xs:documentation>ZIP code</xs:documentation>
											</xs:annotation>
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:maxLength value="30"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="country" type="xs:string">
											<xs:annotation>
												<xs:documentation>Country [CODELIST: GENERAL_CNTR]</xs:documentation>
											</xs:annotation>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
							<xs:element name="owner">
								<xs:annotation>
									<xs:documentation>Client </xs:documentation>
								</xs:annotation>
								<xs:complexType>
									<xs:sequence>
										<xs:element name="partyId">
											<xs:annotation>
												<xs:documentation>Party Id (Siebel identifier)</xs:documentation>
											</xs:annotation>
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:maxLength value="15"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="privateParty" minOccurs="0">
											<xs:complexType>
												<xs:sequence>
													<xs:element name="titlesBefore" minOccurs="0" maxOccurs="1">
														<xs:annotation>
															<xs:documentation>Titles in front of the name</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:string">
																<xs:maxLength value="30"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
													<xs:element name="firstName" minOccurs="0" maxOccurs="1">
														<xs:annotation>
															<xs:documentation>First name</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:string">
																<xs:maxLength value="50"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
													<xs:element name="lastName" minOccurs="0" maxOccurs="1">
														<xs:annotation>
															<xs:documentation>Last name</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:string">
																<xs:maxLength value="50"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
													<xs:element name="titlesBehind" minOccurs="0" maxOccurs="1">
														<xs:annotation>
															<xs:documentation>Titles behind the name</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:string">
																<xs:maxLength value="30"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
													<xs:element name="birthInfo" minOccurs="0">
														<xs:complexType>
															<xs:sequence>
																<xs:element name="birthDate" type="xs:date" minOccurs="0" maxOccurs="1">
																	<xs:annotation>
																		<xs:documentation>Date of birth</xs:documentation>
																	</xs:annotation>
																</xs:element>
																<xs:element name="birthCode" minOccurs="0" maxOccurs="1">
																	<xs:annotation>
																		<xs:documentation>Czech Birth Code</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="11"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
													<xs:element name="citizenship" type="xs:string" minOccurs="0" maxOccurs="1">
														<xs:annotation>
															<xs:documentation>Citizenship [CODELIST: GENERAL_CNTR]</xs:documentation>
														</xs:annotation>
													</xs:element>
													<xs:element name="gender" type="xs:string" minOccurs="0">
														<xs:annotation>
															<xs:documentation>gender [CODELIST: PRV_GENDER]</xs:documentation>
														</xs:annotation>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
										<xs:element name="residenceAddress" minOccurs="0">
											<xs:complexType>
												<xs:sequence>
													<xs:element name="street" minOccurs="1" maxOccurs="1">
														<xs:annotation>
															<xs:documentation>street and number</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:string">
																<xs:maxLength value="200"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
													<xs:element name="city">
														<xs:annotation>
															<xs:documentation>City name</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:string">
																<xs:maxLength value="50"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
													<xs:element name="postalCode">
														<xs:annotation>
															<xs:documentation>ZIP code</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:string">
																<xs:maxLength value="30"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
													<xs:element name="country" type="xs:string">
														<xs:annotation>
															<xs:documentation>Country [CODELIST: GENERAL_CNTR]</xs:documentation>
														</xs:annotation>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
										<xs:element name="accountOwnerType" minOccurs="1" maxOccurs="1">
											<xs:annotation>
												<xs:documentation>This element will be used as a condition on official letter creation in the SCR system. Based on the value sent in the accountOwnerType, SCR will be generating a little different text in the letter (PSD2, GDPR regulations) and also will use different contact information. [LOV: FO;FOP;PO]</xs:documentation>
											</xs:annotation>
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:enumeration value="FO"/>
													<xs:enumeration value="FOP"/>
													<xs:enumeration value="PO"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
				<xs:element name="employee" minOccurs="0" maxOccurs="1">
					<xs:annotation>
						<xs:documentation>worker signing/approving the document on behalf of RBCZ</xs:documentation>
					</xs:annotation>
					<xs:complexType>
						<xs:sequence minOccurs="1" maxOccurs="1">
							<xs:element name="firstName" minOccurs="0" maxOccurs="1">
								<xs:annotation>
									<xs:documentation>First name</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:string">
										<xs:maxLength value="80"/>
									</xs:restriction>
								</xs:simpleType>
							</xs:element>
							<xs:element name="middleNames" minOccurs="0" maxOccurs="1">
								<xs:annotation>
									<xs:documentation>Middle names</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:string">
										<xs:maxLength value="80"/>
									</xs:restriction>
								</xs:simpleType>
							</xs:element>
							<xs:element name="lastName" minOccurs="0" maxOccurs="1">
								<xs:simpleType>
									<xs:restriction base="xs:string">
										<xs:maxLength value="100"/>
									</xs:restriction>
								</xs:simpleType>
							</xs:element>
							<xs:element name="titleBefore" minOccurs="0" maxOccurs="1">
								<xs:annotation>
									<xs:documentation>Title before the first name</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:string">
										<xs:maxLength value="50"/>
									</xs:restriction>
								</xs:simpleType>
							</xs:element>
							<xs:element name="titleBehind" minOccurs="0" maxOccurs="1">
								<xs:annotation>
									<xs:documentation>Title behind the surname</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:string">
										<xs:maxLength value="50"/>
									</xs:restriction>
								</xs:simpleType>
							</xs:element>
							<xs:element name="jobDescription" minOccurs="0" maxOccurs="1">
								<xs:simpleType>
									<xs:restriction base="xs:string">
										<xs:maxLength value="75"/>
									</xs:restriction>
								</xs:simpleType>
							</xs:element>
							<xs:element name="organizationUnit" minOccurs="0" maxOccurs="1">
								<xs:annotation>
									<xs:documentation>name derived from primary Position</xs:documentation>
								</xs:annotation>
								<xs:complexType>
									<xs:sequence minOccurs="1" maxOccurs="1">
										<xs:element name="residence" minOccurs="1" maxOccurs="1">
											<xs:complexType>
												<xs:sequence minOccurs="1" maxOccurs="1">
													<xs:element name="city" minOccurs="1" maxOccurs="1">
														<xs:annotation>
															<xs:documentation>City of residence of Org Unit</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:string">
																<xs:maxLength value="200"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
							<xs:element name="staffId" minOccurs="1" maxOccurs="1">
								<xs:annotation>
									<xs:documentation>employee CZA id</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:string">
										<xs:maxLength value="50"/>
									</xs:restriction>
								</xs:simpleType>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
</xs:schema>
