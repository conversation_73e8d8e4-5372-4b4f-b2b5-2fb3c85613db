<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/services/entityService/document/GetHistory_1_2"
    version="1.0.0"
    xmlns:opgethistory_1_2="http://rb.cz/services/entityService/document/GetHistory_1_2" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="opGetHistory_1_2">
        <xs:complexType>
            <xs:sequence>
                <xs:choice>
                    <xs:element maxOccurs="1" minOccurs="1" name="documentId">
                        <xs:annotation>
                            <xs:documentation>The unique identifier of the document assigned by the system Documentum </xs:documentation>
                        </xs:annotation>
                        <xs:simpleType>
                            <xs:restriction base="xs:string">
                                <xs:maxLength value="16"/>
                            </xs:restriction>
                        </xs:simpleType>
                    </xs:element>
                    <xs:element maxOccurs="1" minOccurs="1" name="externalId">
                        <xs:annotation>
                            <xs:documentation>External identification of the document. Unique ID generated by other application then DMS (documentId in DMS). In case of no externalId exist the document is identified by internal DMS ID (dctmId)</xs:documentation>
                        </xs:annotation>
                        <xs:simpleType>
                            <xs:restriction base="xs:string">
                                <xs:maxLength value="32"/>
                            </xs:restriction>
                        </xs:simpleType>
                    </xs:element>
                </xs:choice>
                <xs:element name="requestedDetail">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:choice>
                                <xs:element name="allDetails" type="xs:boolean"/>
                                <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="historyDetail">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="history" type="xs:boolean"/>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="historyPkiDetail">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="historyPki" type="xs:boolean"/>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="versionListDetail">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="versionList" type="xs:boolean"/>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                </xs:sequence>
                            </xs:choice>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="opGetHistory_1_2_res">
        <xs:complexType>
            <xs:sequence>
                <xs:element maxOccurs="1" minOccurs="0" name="historyResult">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded"
                                minOccurs="1" name="history">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="documentId">
                                    <xs:annotation>
                                    <xs:documentation>The unique identifier of the document assigned by the system DMS (dctmId)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="16"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="date" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>date of state change
</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="signPeriodExpiryDate" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Termination date, i.e. deadline applied on the document SM state. It is used  in a Paperless process for so called Published states when either client or bank should sign or confirm the document until this deadline.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="documentSmStateDescription">
                                    <xs:annotation>
                                    <xs:documentation>Description of actual document SM status (rb_d_abstract.sm_current_state).</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="64"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="documentSmState">
                                    <xs:annotation>
                                    <xs:documentation>Document state - code of the state (codebook of states - State Machine - rb_t_state_definition, describes workflows.)
rb_d_abstract.sm_state_id</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="32"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="performer" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>user that invokes change of the document</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="reasonText" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>freetext description of the change</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="resultItems">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="errorList">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="errorCode"
                                    nillable="false" type="xs:string"/>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="errorDesc"
                                    nillable="false" type="xs:string"/>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="0"
                                    name="errorID"
                                    nillable="false" type="xs:string"/>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="historyPkiResult">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded"
                                minOccurs="1" name="historyPki">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="documentId">
                                    <xs:annotation>
                                    <xs:documentation>The unique identifier of the document assigned by the system DMS (dctmId)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="16"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="reqDate" type="xs:dateTime"/>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="validState" type="xs:string"/>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="smState" type="xs:string"/>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="pkiOperation" type="xs:string"/>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="versionNumber" type="xs:string"/>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="pkiDescription" type="xs:string"/>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="isFinished" type="xs:boolean"/>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="resultItems">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="errorList">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="errorCode"
                                    nillable="false" type="xs:string"/>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="errorDesc"
                                    nillable="false" type="xs:string"/>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="0"
                                    name="errorID"
                                    nillable="false" type="xs:string"/>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="versionListResult">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded"
                                minOccurs="1" name="versionList">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="documentName">
                                    <xs:annotation>
                                    <xs:documentation>Name of document (dm_sysobject.object_name).</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="versionLabel"
                                    nillable="false" type="xs:string"/>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="versionNumber"
                                    nillable="false" type="xs:string"/>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="creationDate"
                                    nillable="false" type="xs:dateTime"/>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="hasContent"
                                    nillable="false" type="xs:boolean"/>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="hasRendition"
                                    nillable="false" type="xs:boolean"/>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="isCurrent"
                                    nillable="false" type="xs:boolean"/>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="deletedDocument"
                                    nillable="false" type="xs:boolean"/>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="resultItems">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="errorList">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="errorCode"
                                    nillable="false" type="xs:string"/>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="errorDesc"
                                    nillable="false" type="xs:string"/>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="0"
                                    name="errorID"
                                    nillable="false" type="xs:string"/>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
