<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/services/entityService/paymentCard/CreateDebitApp_1_0"
    version="1.0.0"
    xmlns:opcreatedebitapp_1_0="http://rb.cz/services/entityService/paymentCard/CreateDebitApp_1_0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="opCreateDebitApp_1_0">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="identification">
                    <xs:annotation>
                        <xs:documentation>Request identifier</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="srcReferenceId">
                                <xs:annotation>
                                    <xs:documentation>Unique identification of the entity from consumer point of view</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element name="application">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="currentAccount">
                                <xs:annotation>
                                    <xs:documentation>A current bank account number</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of the bank account [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="embossedName1" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Embossed name of a card holder</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element minOccurs="0"
                                name="embossedName2" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>In case that a payment card is a corporate card, there is a name of the company</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="applicant">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="person">
                                    <xs:annotation>
                                    <xs:documentation>Information about a card holder</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="titlesBefore">
                                    <xs:annotation>
                                    <xs:documentation>Titles in front of the name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="firstName">
                                    <xs:annotation>
                                    <xs:documentation>First name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="lastName">
                                    <xs:annotation>
                                    <xs:documentation>Last name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="titlesBehind">
                                    <xs:annotation>
                                    <xs:documentation>Titles behind the name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="gender" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Gender [CODELIST: PRV_GENDER]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="birthInfo">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="birthDate" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Date of birth</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="birthCode">
                                    <xs:annotation>
                                    <xs:documentation>Czech Birth Code - only for Czech citizens</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="11"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="citizenship" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Citizenship [CODELIST: GENERAL_CNTR]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="partyId">
                                    <xs:annotation>
                                    <xs:documentation>Party Id (Siebel identifier) of the payment card holder</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="contact">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="addresses">
                                    <xs:annotation>
                                    <xs:documentation>Addresses</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded" name="address">
                                    <xs:annotation>
                                    <xs:documentation>Address</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="addressName">
                                    <xs:annotation>
                                    <xs:documentation>User definable Mail Address name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="addressType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Mail address type (Residency or Mail1-9) [CODELIST: GENERAL_MAILTYPE]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element name="city">
                                    <xs:annotation>
                                    <xs:documentation>City name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="postalCode">
                                    <xs:annotation>
                                    <xs:documentation>ZIP code</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="20"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="country" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Country [CODELIST: GENERAL_CNTR]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="fixLine">
                                    <xs:annotation>
                                    <xs:documentation>A telephone number - international selection + space + number (9 digits)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="mobile">
                                    <xs:annotation>
                                    <xs:documentation>A mobile telephone number - international selection + space + number (9 digits)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="email">
                                    <xs:annotation>
                                    <xs:documentation>An e-mail</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="100"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="idPaper">
                                    <xs:annotation>
                                    <xs:documentation>Identification paper</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="documentType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A type of an identity document [CODELIST: UNI_PIDOCT]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="documentNo" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Identity document number</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="authority" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>An authority which issues an identity document</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="issueDate" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>An issue date of an identity document</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="expiryDate" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>When an identity document expires.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="cardProductType" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Card product type code [CODELIST: DebitCardTypeCode]</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="setting">
                                <xs:annotation>
                                    <xs:documentation>Setting of a payment card</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element minOccurs="0"
                                    name="usageAfterDeath" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>A flag if a card holder can use  a payment card after a death of a bank account owner. The account owner is not a card holder.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="insurances">
                                    <xs:annotation>
                                    <xs:documentation>A list of insurances</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="insurance">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    name="insurancePurposeCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Insurance purpose code [CODELIST: INSURANCE_PURPOSE_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="insuranceProductCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Insurance product code. [CODELIST: INSURANCE_PRODUCT_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="eCommerce">
                                    <xs:annotation>
                                    <xs:documentation>eCommerce</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="activated" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>The e-commerce feature is activated (true/false).</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="limits">
                                    <xs:annotation>
                                    <xs:documentation>Limits</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="limit">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="limitType">
                                    <xs:annotation>
                                    <xs:documentation>A limit type [LOV: General;ATM;POS]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="General"/>

                                    <xs:enumeration value="ATM"/>

                                    <xs:enumeration value="POS"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="maxAmount">
                                    <xs:annotation>
                                    <xs:documentation>An amount of the limit</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    name="amount" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Amount of money</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0"
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency code of the amount [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0"
                                    name="period" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Period of the limit validity [CODELIST: FrequencyType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="expressDelivery" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Express delivery - flag indicating whether is requeired or not</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="processCardOffline" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The card will not be promoted to RPC synchronously via the online WFQ, but by PADRON</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="deliveryInfo">
                                <xs:annotation>
                                    <xs:documentation>Delivery information</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="deliveryType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Distribution channel for debit card - branch or holder address or company address (B/A/F). [CODELIST: DistributionChannel]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="pinDeliveryType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Distribution channel for the pin of debit card - Internet banking or branch or holder address or company address (I/B/A/F). If the value is not filled then the PIN is distributed via the same distribution channel as debit card. [CODELIST: DistributionChannel]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="branchCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A branch identification where a payment card has to be delivered [CODELIST: Branch]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="contractDocId" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>DMS identification of the contract document</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element name="authorizationSignatures">
                    <xs:annotation>
                        <xs:documentation>Authorization signatures</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded" name="authorizationSignature">
                                <xs:annotation>
                                    <xs:documentation>An authorization signature</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:annotation>
                                    <xs:documentation>funguje lovMultipleTranslate</xs:documentation>
                                    </xs:annotation>
                                    <xs:element name="applicationId" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Identification of the channel which has been request sent.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="frontEndApplicationId" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Modul of the application</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="channelCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Identification of the channel which has been request sent. [CODELIST: CHANNEL_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="signatureDate" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Date of authorization</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="opCreateDebitApp_1_0_res">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="identification">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="srcReferenceId">
                                <xs:annotation>
                                    <xs:documentation>Unique identification of the entity from consumer point of view</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element minOccurs="0" name="trgReferenceId">
                                <xs:annotation>
                                    <xs:documentation>Unique identification of the entity from provider point of view</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element minOccurs="0" name="requestStatus" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>How finished the application request [CODELIST: ApplicationStatus]</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element minOccurs="0" name="resultInfo">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded" name="resultItem">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="code" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A code of the error/warning [CODELIST: ERROR_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="description" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A description of the error/warning</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
