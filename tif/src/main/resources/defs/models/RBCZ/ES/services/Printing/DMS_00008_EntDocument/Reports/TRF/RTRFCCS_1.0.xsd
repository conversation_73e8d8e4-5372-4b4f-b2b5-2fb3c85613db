<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2013 sp1 (http://www.altova.com) by Raiffeisenbank a.s. (Raiffeisenbank a.s.) -->
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/dms/dds/trf/ccs/RTRFCCS/1.0"
    xmlns="http://rb.cz/dms/dds/trf/ccs/RTRFCCS/1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="rtrfccs">
        <xs:complexType>
            <xs:sequence>
                <xs:element maxOccurs="1" minOccurs="0" name="client">
                    <xs:annotation>
                        <xs:documentation>Client party detail information</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence maxOccurs="1" minOccurs="1">
                            <xs:element maxOccurs="1" minOccurs="0" name="commercialRegistration">
                                <xs:complexType>
                                    <xs:sequence maxOccurs="1" minOccurs="1">
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="commercialName">
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="contact">
                                <xs:complexType>
                                    <xs:sequence maxOccurs="1" minOccurs="1">
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="preferred">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="0" name="email">
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="150"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="fax">
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="fixLine">
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="25"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="mobile">
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="20"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="personalForm">
                                <xs:annotation>
                                    <xs:documentation>Personal form  information of the party</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence maxOccurs="1" minOccurs="1">
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="firstName">
                                    <xs:annotation>
                                    <xs:documentation>First name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="80"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="middleNames">
                                    <xs:annotation>
                                    <xs:documentation>Middle names</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="80"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="surName">
                                    <xs:annotation>
                                    <xs:documentation>Surname</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="100"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="titleBefore">
                                    <xs:annotation>
                                    <xs:documentation>Title before the first name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="titleBehind">
                                    <xs:annotation>
                                    <xs:documentation>Title behind the surname</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="address">
                                <xs:annotation>
                                    <xs:documentation>Client addresses</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence maxOccurs="1" minOccurs="1">
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="mailing">
                                    <xs:annotation>
                                    <xs:documentation>Party mailing address</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="street">
                                    <xs:annotation>
                                    <xs:documentation>Name of the street</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="200"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="number">
                                    <xs:annotation>
                                    <xs:documentation>Street number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="part">
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="city">
                                    <xs:annotation>
                                    <xs:documentation>Name of the city</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="200"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="postalCode">
                                    <xs:annotation>
                                    <xs:documentation>Postal code</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="country">
                                    <xs:annotation>
                                    <xs:documentation>Name of the country</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="200"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="contract">
                    <xs:complexType>
                        <xs:sequence maxOccurs="1" minOccurs="1">
                            <xs:element maxOccurs="1" minOccurs="0" name="address">
                                <xs:complexType>
                                    <xs:sequence maxOccurs="1" minOccurs="1">
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="mailing">
                                    <xs:annotation>
                                    <xs:documentation>Party mailing address</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="street">
                                    <xs:annotation>
                                    <xs:documentation>Name of the street</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="200"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="number">
                                    <xs:annotation>
                                    <xs:documentation>Street number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="city">
                                    <xs:annotation>
                                    <xs:documentation>Name of the city</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="200"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="postalCode">
                                    <xs:annotation>
                                    <xs:documentation>Postal code</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="country">
                                    <xs:annotation>
                                    <xs:documentation>Name of the country</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="200"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="contact">
                                <xs:complexType>
                                    <xs:sequence maxOccurs="1" minOccurs="1">
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="preferred">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="0" name="email">
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="150"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="fax">
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="fixLine">
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="25"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="mobile">
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="20"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="personalForm">
                                <xs:complexType>
                                    <xs:sequence maxOccurs="1" minOccurs="1">
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="firstName">
                                    <xs:annotation>
                                    <xs:documentation>First name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="80"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="surName">
                                    <xs:annotation>
                                    <xs:documentation>Surname</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="100"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="titleBefore">
                                    <xs:annotation>
                                    <xs:documentation>Title before the first name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="titleBehind">
                                    <xs:annotation>
                                    <xs:documentation>Title behind the surname</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="personNumber">
                                    <xs:annotation>
                                    <xs:documentation>employee identification number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="email">
                    <xs:annotation>
                        <xs:documentation>E-mail address of the report recipient</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:maxLength value="50"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="place">
                    <xs:annotation>
                        <xs:documentation>City of the branch where the report is generated</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:maxLength value="80"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="1"
                    name="reportDate" type="xs:date">
                    <xs:annotation>
                        <xs:documentation>Date on which this report is generated</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0"
                    name="reportTime" type="xs:time"/>
                <xs:element maxOccurs="1" minOccurs="0" name="reportLanguage">
                    <xs:annotation>
                        <xs:documentation>language of the report [LOV: English;German;Czech] [LOV: English;German;Czech]</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:enumeration value="English"/>
                            <xs:enumeration value="German"/>
                            <xs:enumeration value="Czech"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0"
                    name="deliveryChannel" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>preferred delivery channel [CODELIST: FavouredConfirmationDeliveryChannel]</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="1" name="confirmation">
                    <xs:complexType>
                        <xs:sequence maxOccurs="1" minOccurs="1">
                            <xs:element maxOccurs="1" minOccurs="1"
                                name="confirmationId" type="xs:string"/>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="transactionId" type="xs:string"/>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="uti" type="xs:string"/>
                            <xs:element maxOccurs="1" minOccurs="1"
                                name="confirmationType" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>type of the confirmation which is displayed on the report, e.g. kofirmace, close-out, storno</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="1"
                                name="confirmationName" type="xs:string"/>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="transactionType" type="xs:string"/>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="confirmationDate" type="xs:date"/>
                            <xs:element maxOccurs="1" minOccurs="1"
                                name="dealDate" type="xs:date"/>
                            <xs:element minOccurs="0" name="margin">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="percentageAmount" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Percentage amount of margin. Value between 0 and 1 including decimal.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="absoluteAmount">
                                    <xs:annotation>
                                    <xs:documentation>Absolute amount of margin.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:decimal">
                                    <xs:totalDigits value="18"/>
                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of absolute amount [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="1" name="swap">
                    <xs:complexType>
                        <xs:sequence maxOccurs="1" minOccurs="1">
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="buyerParty" type="xs:string"/>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="sellerParty" type="xs:string"/>
                            <xs:element maxOccurs="1" minOccurs="1"
                                name="startDate" type="xs:date"/>
                            <xs:element maxOccurs="1" minOccurs="1"
                                name="maturityDate" type="xs:date"/>
                            <xs:element maxOccurs="1" minOccurs="0" name="notionalAmount">
                                <xs:complexType>
                                    <xs:sequence maxOccurs="1" minOccurs="1">
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="currency" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The currency in which an amount is denominated. [CODELIST: DOMESTIC_PAYMENT_ORDER_CURRENCY_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="amount">
                                    <xs:annotation>
                                    <xs:documentation>The positive monetary quantity in currency units.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:decimal">
                                    <xs:totalDigits value="22"/>
                                    <xs:fractionDigits value="7"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="fixedRates">
                                <xs:complexType>
                                    <xs:sequence maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="fixedRate">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="payerReciever">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="payerParty" type="xs:string"/>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="dayCountFraction">
                                    <xs:annotation>
                                    <xs:documentation>[LOV: 30/360;30E/360;ACT/360;ACT/365;ACT/365 (JPY);ACT/ACT] [LOV: 30/360;30E/360;ACT/360;ACT/365;ACT/365 (JPY);ACT/ACT]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="30/360"/>

                                    <xs:enumeration value="30E/360"/>

                                    <xs:enumeration value="ACT/360"/>

                                    <xs:enumeration value="ACT/365"/>

                                    <xs:enumeration value="ACT/365 (JPY)"/>

                                    <xs:enumeration value="ACT/ACT"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="notionalAmount">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="currency" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The currency in which an amount is denominated. [CODELIST: DOMESTIC_PAYMENT_ORDER_CURRENCY_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="amount">
                                    <xs:annotation>
                                    <xs:documentation>The positive monetary quantity in currency units.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="22"/>

                                    <xs:fractionDigits value="7"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="interestAmount">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="currency" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The currency in which an amount is denominated. [CODELIST: DOMESTIC_PAYMENT_ORDER_CURRENCY_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="amount">
                                    <xs:annotation>
                                    <xs:documentation>The positive monetary quantity in currency units.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:fractionDigits value="7"/>

                                    <xs:totalDigits value="22"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="fixedRate" type="xs:decimal"/>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="minimalFixedRate" type="xs:decimal"/>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="maximalFixedRate" type="xs:decimal"/>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="0" name="barrier">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="barrierName" type="xs:string"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="barrierType">
                                    <xs:annotation>
                                    <xs:documentation>This specifies whether the option becomes effective ("knock-in") or is annulled ("knock-out") when the respective barrier event occurs. [LOV: Knockin;Knockout] [LOV: Knockin;Knockout]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="Knockin"/>

                                    <xs:enumeration value="Knockout"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="direction">
                                    <xs:annotation>
                                    <xs:documentation>This specifies whether the barrier direction is "Up" or "Down"; that is, that a barrier event occurs if the spot rate is at or above the trigger rate, or at or below the trigger rate during the period of observation of an american barrier, or at the times of observation of a discrete or european barrier. [LOV: Down;Up] [LOV: Down;Up]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="Down"/>

                                    <xs:enumeration value="Up"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="quotedCurrencyPair">
                                    <xs:annotation>
                                    <xs:documentation>Defines the two currencies for an FX trade and the quotation relationship between the two currencies.</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>

                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="currency1" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The first currency specified when a pair of currencies is to be evaluated. [CODELIST: DOMESTIC_PAYMENT_ORDER_CURRENCY_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="currency2" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The second currency specified when a pair of currencies is to be evaluated. [CODELIST: DOMESTIC_PAYMENT_ORDER_CURRENCY_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="quoteBasis">
                                    <xs:annotation>
                                    <xs:documentation>The method by which the exchange rate is quoted. [LOV: Currency1PerCurrency2;Currency2PerCurrency1] [LOV: Currency1PerCurrency2;Currency2PerCurrency1]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="Currency1PerCurrency2"/>

                                    <xs:enumeration value="Currency2PerCurrency1"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="triggerRate" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>The market rate is observed relative to the trigger rate, and if it is found to be on the predefined side of (above or below) the trigger rate, a trigger event is deemed to have occurred.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="paymentSchedule">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="calculationPeriod">
                                    <xs:complexType>

                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="duration" type="xs:string"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="startDate" type="xs:date"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="endDate" type="xs:date"/>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="settlementDate" type="xs:date"/>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="floatingRates">
                                <xs:complexType>
                                    <xs:sequence maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="floatingRate">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="payerReciever">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="payerParty" type="xs:string"/>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="dayCountFraction">
                                    <xs:annotation>
                                    <xs:documentation>[LOV: 30/360;30E/360;ACT/360;ACT/365;ACT/365 (JPY);ACT/ACT] [LOV: 30/360;30E/360;ACT/360;ACT/365;ACT/365 (JPY);ACT/ACT]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="30/360"/>

                                    <xs:enumeration value="30E/360"/>

                                    <xs:enumeration value="ACT/360"/>

                                    <xs:enumeration value="ACT/365"/>

                                    <xs:enumeration value="ACT/365 (JPY)"/>

                                    <xs:enumeration value="ACT/ACT"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="notionalAmount">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="currency" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The currency in which an amount is denominated. [CODELIST: DOMESTIC_PAYMENT_ORDER_CURRENCY_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="amount">
                                    <xs:annotation>
                                    <xs:documentation>The positive monetary quantity in currency units.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="22"/>

                                    <xs:fractionDigits value="7"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="interestAmount">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="currency" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The currency in which an amount is denominated. [CODELIST: DOMESTIC_PAYMENT_ORDER_CURRENCY_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="amount">
                                    <xs:annotation>
                                    <xs:documentation>The positive monetary quantity in currency units.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="22"/>

                                    <xs:fractionDigits value="7"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="floatingRateType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>[CODELIST: BaseRateType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="witholdingTaxRate" type="xs:decimal"/>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="spread" type="xs:decimal"/>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="settlementInterestRate" type="xs:decimal"/>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="0" name="barrier">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="barrierName" type="xs:string"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="barrierType">
                                    <xs:annotation>
                                    <xs:documentation>This specifies whether the option becomes effective ("knock-in") or is annulled ("knock-out") when the respective barrier event occurs. [LOV: Knockin;Knockout] [LOV: Knockin;Knockout]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="Knockin"/>

                                    <xs:enumeration value="Knockout"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="direction">
                                    <xs:annotation>
                                    <xs:documentation>This specifies whether the barrier direction is "Up" or "Down"; that is, that a barrier event occurs if the spot rate is at or above the trigger rate, or at or below the trigger rate during the period of observation of an american barrier, or at the times of observation of a discrete or european barrier. [LOV: Down;Up] [LOV: Down;Up]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="Down"/>

                                    <xs:enumeration value="Up"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="quotedCurrencyPair">
                                    <xs:annotation>
                                    <xs:documentation>Defines the two currencies for an FX trade and the quotation relationship between the two currencies.</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>

                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="currency1" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The first currency specified when a pair of currencies is to be evaluated. [CODELIST: DOMESTIC_PAYMENT_ORDER_CURRENCY_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="currency2" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The second currency specified when a pair of currencies is to be evaluated. [CODELIST: DOMESTIC_PAYMENT_ORDER_CURRENCY_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="quoteBasis">
                                    <xs:annotation>
                                    <xs:documentation>The method by which the exchange rate is quoted. [LOV: Currency1PerCurrency2;Currency2PerCurrency1] [LOV: Currency1PerCurrency2;Currency2PerCurrency1]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="Currency1PerCurrency2"/>

                                    <xs:enumeration value="Currency2PerCurrency1"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="triggerRate" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>The market rate is observed relative to the trigger rate, and if it is found to be on the predefined side of (above or below) the trigger rate, a trigger event is deemed to have occurred.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="paymentSchedule">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="fixingDate" type="xs:date"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="calculationPeriod">
                                    <xs:complexType>

                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="duration" type="xs:string"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="startDate" type="xs:date"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="endDate" type="xs:date"/>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="settlementDate" type="xs:date"/>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="cashSettlement">
                                <xs:complexType>
                                    <xs:sequence maxOccurs="1" minOccurs="1">
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="fixedRates">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="fixedRate">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="payerReciever">
                                    <xs:complexType>

                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="payerParty" type="xs:string"/>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="notionalAmount">
                                    <xs:complexType>

                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="currency" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The currency in which an amount is denominated. [CODELIST: DOMESTIC_PAYMENT_ORDER_CURRENCY_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="amount">
                                    <xs:annotation>
                                    <xs:documentation>The positive monetary quantity in currency units.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:fractionDigits value="7"/>

                                    <xs:totalDigits value="22"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="fixedRate" type="xs:decimal"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="minimalFixedRate" type="xs:decimal"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="maximalFixedRate" type="xs:decimal"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="fixedAmount">
                                    <xs:complexType>

                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="currency" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The currency in which an amount is denominated. [CODELIST: DOMESTIC_PAYMENT_ORDER_CURRENCY_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="amount">
                                    <xs:annotation>
                                    <xs:documentation>The positive monetary quantity in currency units.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="22"/>

                                    <xs:fractionDigits value="7"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="netFixedAmount">
                                    <xs:complexType>

                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="currency" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The currency in which an amount is denominated. [CODELIST: DOMESTIC_PAYMENT_ORDER_CURRENCY_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="amount">
                                    <xs:annotation>
                                    <xs:documentation>The positive monetary quantity in currency units.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:fractionDigits value="7"/>

                                    <xs:totalDigits value="22"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="witholdingTax">
                                    <xs:complexType>

                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="currency" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The currency in which an amount is denominated. [CODELIST: DOMESTIC_PAYMENT_ORDER_CURRENCY_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="amount">
                                    <xs:annotation>
                                    <xs:documentation>The positive monetary quantity in currency units.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="22"/>

                                    <xs:fractionDigits value="7"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="calculationPeriod">
                                    <xs:complexType>

                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="duration" type="xs:string"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="startDate" type="xs:date"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="endDate" type="xs:date"/>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="settlementDate" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>The date on which settlement is scheduled to occur</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="floatingRates">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="floatingRate">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="payerReciever">
                                    <xs:complexType>

                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="payerParty" type="xs:string"/>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="notionalAmount">
                                    <xs:complexType>

                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="currency" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The currency in which an amount is denominated. [CODELIST: DOMESTIC_PAYMENT_ORDER_CURRENCY_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="amount">
                                    <xs:annotation>
                                    <xs:documentation>The positive monetary quantity in currency units.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:fractionDigits value="7"/>

                                    <xs:totalDigits value="22"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="floatingRateType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>[CODELIST: BaseRateType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0"
                                    name="resetFloatingRate" type="xs:decimal"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="floatingAmount">
                                    <xs:complexType>

                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="currency" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The currency in which an amount is denominated. [CODELIST: DOMESTIC_PAYMENT_ORDER_CURRENCY_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="amount">
                                    <xs:annotation>
                                    <xs:documentation>The positive monetary quantity in currency units.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="22"/>

                                    <xs:fractionDigits value="7"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="netFloatingAmount">
                                    <xs:complexType>

                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="currency" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The currency in which an amount is denominated. [CODELIST: DOMESTIC_PAYMENT_ORDER_CURRENCY_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="amount">
                                    <xs:annotation>
                                    <xs:documentation>The positive monetary quantity in currency units.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="22"/>

                                    <xs:fractionDigits value="7"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="witholdingTax">
                                    <xs:complexType>

                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="currency" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The currency in which an amount is denominated. [CODELIST: DOMESTIC_PAYMENT_ORDER_CURRENCY_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="amount">
                                    <xs:annotation>
                                    <xs:documentation>The positive monetary quantity in currency units.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:fractionDigits value="7"/>

                                    <xs:totalDigits value="22"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="spread" type="xs:decimal"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="calculationPeriod">
                                    <xs:complexType>

                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="duration" type="xs:string"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="startDate" type="xs:date"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="endDate" type="xs:date"/>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="settlementInterestRate" type="xs:decimal"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="settlementDate" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>The date on which settlement is scheduled to occur</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="paymentSchedules">
                                <xs:complexType>
                                    <xs:sequence maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="paymentSchedule">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="payerReciever">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="payerParty" type="xs:string"/>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="paymentType">
                                    <xs:annotation>
                                    <xs:documentation>[LOV: Fixed;Float] [LOV: Fixed;Float]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="Fixed"/>

                                    <xs:enumeration value="Float"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="fixingDate" type="xs:date"/>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="calculationPeriod">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="startDate" type="xs:date"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="endDate" type="xs:date"/>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="notionalAmount">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="currency" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The currency in which an amount is denominated. [CODELIST: DOMESTIC_PAYMENT_ORDER_CURRENCY_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="amount">
                                    <xs:annotation>
                                    <xs:documentation>The positive monetary quantity in currency units.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="22"/>

                                    <xs:fractionDigits value="7"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="notionalExchangeDate" type="xs:date"/>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="fixedRate" type="xs:decimal"/>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="floatingRateType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>[CODELIST: BaseRateType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="spread">
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="22"/>

                                    <xs:fractionDigits value="7"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="settlementDate" type="xs:date"/>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="barrier">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="barrierName" type="xs:string"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="barrierType">
                                    <xs:annotation>
                                    <xs:documentation>This specifies whether the option becomes effective ("knock-in") or is annulled ("knock-out") when the respective barrier event occurs. [LOV: Knockin;Knockout] [LOV: Knockin;Knockout]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="Knockin"/>

                                    <xs:enumeration value="Knockout"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="direction">
                                    <xs:annotation>
                                    <xs:documentation>This specifies whether the barrier direction is "Up" or "Down"; that is, that a barrier event occurs if the spot rate is at or above the trigger rate, or at or below the trigger rate during the period of observation of an american barrier, or at the times of observation of a discrete or european barrier. [LOV: Down;Up] [LOV: Down;Up]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="Down"/>

                                    <xs:enumeration value="Up"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="quotedCurrencyPair">
                                    <xs:annotation>
                                    <xs:documentation>Defines the two currencies for an FX trade and the quotation relationship between the two currencies.</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>

                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="currency1" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The first currency specified when a pair of currencies is to be evaluated. [CODELIST: DOMESTIC_PAYMENT_ORDER_CURRENCY_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="currency2" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The second currency specified when a pair of currencies is to be evaluated. [CODELIST: DOMESTIC_PAYMENT_ORDER_CURRENCY_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="quoteBasis">
                                    <xs:annotation>
                                    <xs:documentation>The method by which the exchange rate is quoted. [LOV: Currency1PerCurrency2;Currency2PerCurrency1] [LOV: Currency1PerCurrency2;Currency2PerCurrency1]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="Currency1PerCurrency2"/>

                                    <xs:enumeration value="Currency2PerCurrency1"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="triggerRate" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>The market rate is observed relative to the trigger rate, and if it is found to be on the predefined side of (above or below) the trigger rate, a trigger event is deemed to have occurred.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="1" name="confirmationFooter">
                    <xs:complexType>
                        <xs:sequence maxOccurs="1" minOccurs="1">
                            <xs:element maxOccurs="1" minOccurs="0" name="bankAccounts">
                                <xs:complexType>
                                    <xs:sequence maxOccurs="1" minOccurs="1">
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="referenceCurrencyAcount">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies particular bank. [CODELIST: BANK_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="currencyCode">
                                    <xs:annotation>
                                    <xs:documentation>Main currency folder within the multi-currency bank account used for a payment. [CODELIST: DOMESTIC_PAYMENT_ORDER_CURRENCY_CODES  ]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="3"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="settlementCurrencyAccount">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies particular bank. [CODELIST: BANK_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="currencyCode">
                                    <xs:annotation>
                                    <xs:documentation>Main currency folder within the multi-currency bank account used for a payment. [CODELIST: DOMESTIC_PAYMENT_ORDER_CURRENCY_CODES  ]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="3"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="clientAccounts">
                                <xs:complexType>
                                    <xs:sequence maxOccurs="1" minOccurs="1">
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="referenceCurrencyAcount">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies particular bank. [CODELIST: BANK_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="currencyCode">
                                    <xs:annotation>
                                    <xs:documentation>Main currency folder within the multi-currency bank account used for a payment. [CODELIST: DOMESTIC_PAYMENT_ORDER_CURRENCY_CODES  ]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="3"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="settlementCurrencyAccount">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies particular bank. [CODELIST: BANK_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="currencyCode">
                                    <xs:annotation>
                                    <xs:documentation>Main currency folder within the multi-currency bank account used for a payment. [CODELIST: DOMESTIC_PAYMENT_ORDER_CURRENCY_CODES  ]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="3"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="calculationAgent" type="xs:string"/>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="collateral" type="xs:string"/>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="contractSignitureDate" type="xs:date"/>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="note" type="xs:string"/>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="dealerName" type="xs:string"/>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="earlyTerminationDate" type="xs:date">
                                <xs:annotation>
                                    <xs:documentation>early termination date for closeout</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="finalSettlement">
                                <xs:complexType>
                                    <xs:sequence maxOccurs="1" minOccurs="1">
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="finalSettlementAmount">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="currency" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The currency in which an amount is denominated. [CODELIST: DOMESTIC_PAYMENT_ORDER_CURRENCY_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="amount">
                                    <xs:annotation>
                                    <xs:documentation>The positive monetary quantity in currency units.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="22"/>

                                    <xs:fractionDigits value="7"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="dueDate" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Final Settlement Amount Due Date</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="priceSource" type="xs:string"/>
                            <xs:element minOccurs="0" name="kidUrls">
                                <xs:annotation>
                                    <xs:documentation>List of URLs to KIDs (Key Information Document).</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="kidUrl">
                                    <xs:annotation>
                                    <xs:documentation>URL link to KID (Key Indication Document).</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="language" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Language of the KID to which the kidUrl refers to. [CODELIST: Language]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="url">
                                    <xs:annotation>
                                    <xs:documentation>URL to the KID</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
