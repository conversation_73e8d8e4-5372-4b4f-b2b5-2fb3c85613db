<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/services/entityService/document/Create_3_0"
    version="1.0.0"
    xmlns:opcreate_3_0="http://rb.cz/services/entityService/document/Create_3_0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="opCreate_3_0">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="documentClass" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Code of the document type (EMC codebook= document type, rb_m_document_classes -  ie: P0000645)</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="partyId">
                    <xs:annotation>
                        <xs:documentation>Party Id (Siebel identifier)</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:maxLength value="15"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element minOccurs="0" name="externalDocumentId">
                    <xs:annotation>
                        <xs:documentation>External identification of the document. Unique ID generated by other application then DMS (documentId in DMS). In case of no externalId exist the document is identified by internal DMS ID (dctmId)</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:maxLength value="32"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="author" nillable="false">
                    <xs:annotation>
                        <xs:documentation>Author of the document (free text)</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:maxLength value="255"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="objectOfCreation">
                    <xs:annotation>
                        <xs:documentation>Parameter that express what is created (Content must be always created, metadata and esdo are additional)</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="1" minOccurs="0" name="content">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="format" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>File type extension to identify the content of the document format (doc, pdf, xls, ...) [CODELIST: DocumentFormat]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:choice maxOccurs="1" minOccurs="1">
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="externalContent">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="uri" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>URI of stored document on shared storage</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="documentName">
                                    <xs:annotation>
                                    <xs:documentation>Name of the document</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="rendition" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Rendition (True = rendition, False = main content)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="renderingContent">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="processing">
                                    <xs:annotation>
                                    <xs:documentation>Flag indicating finalization of generated document. Providing info about processing action [LOV: conf;edit;store;local;nourl;printandstore]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="conf"/>

                                    <xs:enumeration value="edit"/>

                                    <xs:enumeration value="store"/>

                                    <xs:enumeration value="local"/>

                                    <xs:enumeration value="nourl"/>

                                    <xs:enumeration value="printandstore"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="signRequired" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>flag if the document has to be signed or not</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="technicalMetadata">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="userId">
                                    <xs:annotation>
                                    <xs:documentation>Unique identification of the user, who initiates the request.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="100"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="deliveries">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="delivery">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="deliveryChannel" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>delivery channel of the document [CODELIST: FavouredConfirmationDeliveryChannel]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="emails">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded" name="email">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    minOccurs="0"
                                    name="sender" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>sender of email (From)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1"
                                    name="receiver" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>E-mail address of the report recipient (To)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="0"
                                    name="carbonCopy" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>carbon copy to receiver (Cc)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="0"
                                    name="blindCarbonCopy" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>blind carbon copy to receiver (Bcc)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0"
                                    name="subject" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>subject of email</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0"
                                    name="text" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>text of email </xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="additionalText" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>additional text for email </xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0" name="attachments">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded" name="attachment">
                                    <xs:annotation>
                                    <xs:documentation>list of possible attachments for a reprt</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="type">
                                    <xs:annotation>
                                    <xs:documentation>any document can be attached based on the documentId or an url (url link to open the document in DMS) [LOV: documentId;url]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="documentId"/>

                                    <xs:enumeration value="url"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="value" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>value of documentId or url</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0"
                                    name="documentName" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>name of the attachment</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="attachmentId" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Identifier of attachment. Consumer can assign and use this identifier for purpose of dynamic selection on SCR side. </xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="addresses">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded" name="address">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    minOccurs="0" name="addressee">
                                    <xs:annotation>
                                    <xs:documentation>Mail addressee</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="additionalInfo">
                                    <xs:annotation>
                                    <xs:documentation>Additional address info</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="street">
                                    <xs:annotation>
                                    <xs:documentation>Street name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="200"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="number">
                                    <xs:annotation>
                                    <xs:documentation>Building number/street number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="city">
                                    <xs:annotation>
                                    <xs:documentation>City name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="postalCode">
                                    <xs:annotation>
                                    <xs:documentation>ZIP code</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0"
                                    name="country" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>[CODELIST: GENERAL_CNTR]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="faxes">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded" name="fax">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="faxNumber" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>fax of the report recipient</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="additionalInfo">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    minOccurs="0"
                                    name="sender" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>sender of email (From)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="0"
                                    name="receiver" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>E-mail address of the report recipient (To)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="0"
                                    name="carbonCopy" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>carbon copy to receiver (Cc)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="0"
                                    name="blindCarbonCopy" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>blind carbon copy to receiver (Bcc)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0"
                                    name="subject" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>subject of email</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0"
                                    name="text" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>text of email </xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="additionalText" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>additional text for email </xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0"
                                    name="reportLanguage" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>language of the report, default is czech [CODELIST: LanguageDMS]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0"
                                    name="fileName" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>name of the generated report</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element name="fileFormatProperties">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="fileType">
                                    <xs:annotation>
                                    <xs:documentation>Statement file type

abo (1 | 2) (XX | AV) ( c | n) 
abo (1 = ediční, 2 = vnitřní ) (XX = není AV, AV = je AV) 
(c = v Doplňující údaj je Counter Acc Name | n = v Doplňující údaj je Note ) 
abo1XXc ABO (ediční formát) bez AV pole s názvem protistrany (v poli Doplňující údaj) 
abo1XXn ABO (ediční formát) bez AV pole s poznámkou (v poli Doplňující údaj) 
abo2XXc ABO (vnitřní formát) bez AV pole s názvem protistrany (v poli Doplňující údaj) 
abo2XXn ABO (vnitřní formát) bez AV pole s poznámkou (v poli Doplňující údaj) 
abo1AVc ABO (ediční formát) s AV polem s názvem protistrany (v poli Doplňující údaj) 
abo1AVn ABO (ediční formát) s AV polem s poznámkou (v poli Doplňující údaj) 
abo2AVc ABO (vnitřní formát) s AV polem s názvem protistrany (v poli Doplňující údaj) 
abo2AVn ABO (vnitřní formát) s AV polem s poznámkou (v poli Doplňující údaj) 
docx - Microsoft Word docx [LOV: abo1XXc;abo1XXn;abo2XXc;abo2XXn;abo1AVc;abo1AVn;abo2AVc;abo2AVn;csv;docx;gemini1;gemini2;jpg;pdf;PDFA-1a;PDFA-1b;quest;xml]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="abo1XXc"/>

                                    <xs:enumeration value="abo1XXn"/>

                                    <xs:enumeration value="abo2XXc"/>

                                    <xs:enumeration value="abo2XXn"/>

                                    <xs:enumeration value="abo1AVc"/>

                                    <xs:enumeration value="abo1AVn"/>

                                    <xs:enumeration value="abo2AVc"/>

                                    <xs:enumeration value="abo2AVn"/>

                                    <xs:enumeration value="csv"/>

                                    <xs:enumeration value="docx"/>

                                    <xs:enumeration value="gemini1"/>

                                    <xs:enumeration value="gemini2"/>

                                    <xs:enumeration value="jpg"/>

                                    <xs:enumeration value="pdf"/>

                                    <xs:enumeration value="PDFA-1a"/>

                                    <xs:enumeration value="PDFA-1b"/>

                                    <xs:enumeration value="quest"/>

                                    <xs:enumeration value="xml"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0" name="addParams">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded" name="addParam">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="code">
                                    <xs:annotation>
                                    <xs:documentation>[LOV: dpi]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="dpi"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="value" type="xs:string"/>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0" name="attachments">
                                    <xs:annotation>
                                    <xs:documentation>Attachments which are embeded in generated document (not included in email)</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded" name="attachment">
                                    <xs:annotation>
                                    <xs:documentation>list of possible attachments for a reprt</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="type">
                                    <xs:annotation>
                                    <xs:documentation>any document can be attached based on the documentId or an url (url link to open the document in DMS) [LOV: documentId;url]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="documentId"/>

                                    <xs:enumeration value="url"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="value" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>value of documentId or url</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0"
                                    name="documentName" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>name of the attachment</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="attachmentId" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Identifier of attachment. Consumer can assign and use this identifier for purpose of dynamic selection on SCR side. </xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="place" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>City of the branch where the report is generated</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="80"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="reportDatetime"
                                    nillable="false" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Date and time on which this report is generated</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="preview">
                                    <xs:annotation>
                                    <xs:documentation>This complex is used in case when consumer wants to see preview. According to methodology its useable only with "local" processing.</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    name="watermark" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Free text which is embeded diagonally to generated PDF doc</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="protectAttributes">
                                    <xs:annotation>
                                    <xs:documentation>list of PDF protection attributes</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="allowAssembly" type="xs:boolean"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="allowCopy" type="xs:boolean"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="allowDegradedPrinting" type="xs:boolean"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="allowFillIn" type="xs:boolean"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="allowModifyAnnotations" type="xs:boolean"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="allowModifyContents" type="xs:boolean"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="allowPrinting" type="xs:boolean"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="allowScreenreaders" type="xs:boolean"/>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="asynchronous">
                                    <xs:annotation>
                                    <xs:documentation>This complex is used in case when consumer wants to generate document asynchronously and SCR after generating will send notification to consumer.</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="srcReferenceId">
                                    <xs:annotation>
                                    <xs:documentation>Source reference ID. This value is send by SCR in notification after generating document.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="32"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="fileXmlData">
                                    <xs:annotation>
                                    <xs:documentation>The document data fields in XML format</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:any processContents="skip"/>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="fileXmlDataUrl">
                                    <xs:annotation>
                                    <xs:documentation>This complex is used in case of send URL to get source XML saved in DMS(EMC)</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="fileXmlUrl" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Report data URL</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="urlType">
                                    <xs:annotation>
                                    <xs:documentation>[LOV: webDAV]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="webDAV"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="fileXmlName" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>This element is used in case of use WEBDAV, consumer must fill folder and document name</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:choice>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="metadata">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="documentDetail">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="callingApplication" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Application that calls this service</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="documentSmState" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Document state - code of the state (codebook of states - State Machine - rb_t_state_definition, describes workflows.)
rb_d_abstract.sm_state_id</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="32"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="documentSmStateDescription" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Reason text (rb_d_abstract.sm_reason_text).</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="pki">
                                    <xs:annotation>
                                    <xs:documentation>Private Key Infrastructure</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="certificateExpiration"
                                    nillable="false" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Expiration date of oldest certificate - in case of signed the document</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="ltv"
                                    nillable="false" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Document is in LTV mode (Long Term Validation)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="validState" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Validation state of document certificates</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="20"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="relatedMetadata">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="accountNumbers">
                                    <xs:complexType>

                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1"
                                    name="accountNumber" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Account number related to the document</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="60"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="agreementNumbers">
                                    <xs:complexType>

                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1"
                                    name="agreementNumber" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Agreement number related to the document</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="60"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="transactionNumber" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Transaction number related to the document</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="100"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="applicationId" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Application ID</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="60"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="loanId" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Loan ID</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="60"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="clientNote" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Client note</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="1200"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="creatingSystem" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>System which created or changed the document</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="esdoCurrentDetail">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="cardNumber" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Card number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="20"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="sumOfMoney"
                                    nillable="false" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Sum of money</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="comment" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>ESDO comment</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="4000"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="partyDetail">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    minOccurs="0" name="relatedMetadata">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="relatedPartyIds">
                                    <xs:complexType>

                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1"
                                    name="relatedPartyId" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Party Id (Siebel identifier)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="16"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="statementDetail">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="statementNumber" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Statement number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="100"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="startDate"
                                    nillable="false" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Start date of statement</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="endDate"
                                    nillable="false" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>End date of statement</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="maskedCardNumber" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Masked card number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="64"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="currency" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Currency of statement</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="3"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="startBalanceAmount"
                                    nillable="false" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Start balance amount</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="endBalanceAmount"
                                    nillable="false" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>End balance amount</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="sumDebit"
                                    nillable="false" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Sum of debit items</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="sumCredit"
                                    nillable="false" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Sum of credit items</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="channel" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Channel for statement delivery (email, pošta, ib ...)
</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="emailId" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>EmailID from TRS</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="addressId" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>AddressId from TRS</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="trsId" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>TrsId </xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="language" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Language from TRS</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="accountTypeCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>AccountTypeCode from TRS</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="ordinalNumber"
                                    nillable="false" type="xs:double">
                                    <xs:annotation>
                                    <xs:documentation>Ordinal number of statement</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="yearBs"
                                    nillable="false" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Year of statement</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="opCreate_3_0_res">
        <xs:complexType>
            <xs:sequence>
                <xs:element maxOccurs="1" minOccurs="0" name="document">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="1" minOccurs="0" name="documentId">
                                <xs:annotation>
                                    <xs:documentation>The unique identifier of the document assigned by the system Documentum (dctmId)</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="16"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="externalId">
                                <xs:annotation>
                                    <xs:documentation>External identification of the document. Unique ID generated by other application then DMS (documentId in DMS). In case of no externalId exist the document is identified by internal DMS ID (dctmId)</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="32"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="isFinalized" type="xs:boolean">
                                <xs:annotation>
                                    <xs:documentation>Flag that shows if the document is finalized</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="version" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Version of the document </xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="url">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:choice>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="scrGuiUrl" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>URL into GUI of Scriptura (assumed for processing flag: conf; edit)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="scrLocalUrl" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>URL for download of physical document (assumed for processing flag: local)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="dmsGuiUrl" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>URL into GUI of DMS (assumed for processing flag: store)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="scrNoUrl" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>no URL (assumed for processing flag: nourl)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="dmsContentUrl" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>URL for download of physical document from DMS (assumed for processing flag: printandstore)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:choice>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
