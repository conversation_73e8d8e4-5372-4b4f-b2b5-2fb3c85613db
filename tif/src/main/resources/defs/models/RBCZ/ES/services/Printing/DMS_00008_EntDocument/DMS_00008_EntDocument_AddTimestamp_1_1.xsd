<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/services/entityService/document/AddTimestamp_1_1"
    version="1.0.0"
    xmlns:opaddtimestamp_1_1="http://rb.cz/services/entityService/document/AddTimestamp_1_1" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="opAddTimestamp_1_1">
        <xs:complexType>
            <xs:sequence>
                <xs:element maxOccurs="1" minOccurs="0" name="documentId">
                    <xs:annotation>
                        <xs:documentation>The unique identifier of the document assigned by the system Documentum (dctmId)</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:maxLength value="16"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="1" name="format">
                    <xs:annotation>
                        <xs:documentation>File type extension to identify the content of the document format (doc, pdf, xls, ...)</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:maxLength value="400"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element minOccurs="0" name="uri" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>URI address of the document saved to DMS SFTP server</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element minOccurs="0" name="ltvAnalyzeflag" type="xs:boolean">
                    <xs:annotation>
                        <xs:documentation>Flag that determines if there is called A_PKI_LtvAnalyze in the orchestration. 
TRUE or NOT FILLED = LtvAnalyze is called, 
FALSE = LtvAnalyze is not called</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="opAddTimestamp_1_1_res">
        <xs:complexType>
            <xs:sequence>
                <xs:element maxOccurs="1" minOccurs="0"
                    name="globalExpiration" type="xs:dateTime">
                    <xs:annotation>
                        <xs:documentation>Expiry date of document, content all signatures and timestamps.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element minOccurs="0" name="uri" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>URI address of the document with timestamp saved to DMS SFTP server</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
