<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2013 sp1 (http://www.altova.com) by Raiffeisenbank a.s. (Raiffeisenbank a.s.) -->
<!--
Xml Schema generated by NetLT EAAddin.CDM.Designer plugin ver. 3.0.16 [29.7.2015 13:21' ]

Important!!!

Automatically generated from EA model, don't update manually!!!


Document revision: 1.0.0

-->
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/services/entityService/domesticPayment/CancelStandingPO_1_0"
    version="1.0.0"
    xmlns:opcancelstandingpo_1_0="http://rb.cz/services/entityService/domesticPayment/CancelStandingPO_1_0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="opCancelStandingPO_1_0">
        <xs:complexType>
            <xs:sequence>
                <xs:element maxOccurs="1" minOccurs="1" name="accountNumber">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="1" minOccurs="0" name="numberPart1">
                                <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number.</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="1" name="numberPart2">
                                <xs:annotation>
                                    <xs:documentation>Base part of the bank account number.</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="bankCode" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies particular bank. [CODELIST: BANK_CODES]</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="1"
                                name="currencyCode" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation> [CODELIST: DOMESTIC_PAYMENT_ORDER_CURRENCY_CODES  ]</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="1" name="paymentOrder">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="1" minOccurs="0" name="srcReferenceId">
                                <xs:annotation>
                                    <xs:documentation>Unique identification of the entity from consumer point of view.</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="1" name="trgReferenceId">
                                <xs:annotation>
                                    <xs:documentation>Unique identification of the entity from provider point of view.</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="1" name="authorizationSignatures">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded"
                                minOccurs="1" name="authorizationSignature">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="applicationId" type="xs:string"/>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="frontEndApplicationId" type="xs:string"/>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="channelCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Channel used for signing the payment order. [CODELIST: CHANNEL_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="signatureDate" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Date of authorization</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="opCancelStandingPO_1_0_res">
        <xs:complexType>
            <xs:sequence>
                <xs:element maxOccurs="1" minOccurs="1" name="paymentOrder">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="1" minOccurs="0" name="identification">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="srcReferenceId">
                                    <xs:annotation>
                                    <xs:documentation>Unique identification of the entity from consumer point of view.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="trgReferenceId">
                                    <xs:annotation>
                                    <xs:documentation>Unique identification of the entity from provider point of view.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="resultInfo">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="resultItem">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="code">
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="description">
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="2000"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
