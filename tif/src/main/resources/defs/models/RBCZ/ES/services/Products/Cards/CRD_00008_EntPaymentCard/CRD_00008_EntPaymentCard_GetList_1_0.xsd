<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2013 sp1 (http://www.altova.com) by Raiffeisenbank a.s. (Raiffeisenbank a.s.) -->
<xs:schema xmlns:getlist_1_0="http://rb.cz/services/entityService/paymentCard/GetList_1_0" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="http://rb.cz/services/entityService/paymentCard/GetList_1_0" elementFormDefault="qualified" attributeFormDefault="unqualified" version="1.0.1">
	<xs:element name="opGetList_1_0">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="searchingCriteria">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="balanceRefresh" type="xs:boolean">
								<xs:annotation>
									<xs:documentation>Y - this element manages refreshing a credit card balances and transactions/holds.</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="cardType">
								<xs:annotation>
									<xs:documentation>Card type - credit, debit [LOV: Credit;Debit]</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:string">
										<xs:enumeration value="Credit"/>
										<xs:enumeration value="Debit"/>
									</xs:restriction>
								</xs:simpleType>
							</xs:element>
							<xs:element name="searchingCriteriaItem">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="accountNumbers">
											<xs:complexType>
												<xs:sequence>
													<xs:element name="accountNumber" maxOccurs="unbounded">
														<xs:complexType>
															<xs:sequence>
																<xs:element name="numberPart1" minOccurs="0" maxOccurs="1">
																	<xs:annotation>
																		<xs:documentation>Prefix part of the bank account number</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="6"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="numberPart2">
																	<xs:annotation>
																		<xs:documentation>Base part of the bank account number</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="10"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="bankCode" type="xs:string">
																	<xs:annotation>
																		<xs:documentation>Part of the bank account number which identifies the bank [CODELIST: BANK_CODES]</xs:documentation>
																	</xs:annotation>
																</xs:element>
																<xs:element name="currencyCode" type="xs:string" minOccurs="0">
																	<xs:annotation>
																		<xs:documentation>Currency of the bank account [CODELIST: Currency]</xs:documentation>
																	</xs:annotation>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="opGetList_1_0_res">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="searchResults">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="searchResult" maxOccurs="unbounded">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="bankAccount">
											<xs:complexType>
												<xs:sequence>
													<xs:element name="partyId" minOccurs="0">
														<xs:annotation>
															<xs:documentation>Party Id - account owner</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:string">
																<xs:maxLength value="15"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
													<xs:element name="accountNumber">
														<xs:complexType>
															<xs:sequence>
																<xs:element name="numberPart1" minOccurs="0" maxOccurs="1">
																	<xs:annotation>
																		<xs:documentation>Prefix part of the bank account number</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="6"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="numberPart2">
																	<xs:annotation>
																		<xs:documentation>Base part of the bank account number</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="10"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="bankCode" type="xs:string" minOccurs="0">
																	<xs:annotation>
																		<xs:documentation>Part of the bank account number which identifies the bank [CODELIST: BANK_CODES]</xs:documentation>
																	</xs:annotation>
																</xs:element>
																<xs:element name="currencyCode" type="xs:string" minOccurs="0">
																	<xs:annotation>
																		<xs:documentation>Currency of the bank account [CODELIST: Currency]</xs:documentation>
																	</xs:annotation>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
													<xs:element name="creditAccountDetail" minOccurs="0">
														<xs:complexType>
															<xs:sequence>
																<xs:element name="balances" minOccurs="0">
																	<xs:complexType>
																		<xs:sequence>
																			<xs:element name="balance" maxOccurs="unbounded">
																				<xs:complexType>
																					<xs:sequence>
																						<xs:element name="balanceCode" type="xs:string">
																							<xs:annotation>
																								<xs:documentation>[CODELIST: BALANCE_CODES]</xs:documentation>
																							</xs:annotation>
																						</xs:element>
																						<xs:element name="amount" type="xs:decimal"/>
																						<xs:element name="currencyCode" type="xs:string">
																							<xs:annotation>
																								<xs:documentation>[CODELIST: Currency]</xs:documentation>
																							</xs:annotation>
																						</xs:element>
																						<xs:element name="refreshDate" type="xs:dateTime" minOccurs="0" maxOccurs="1"/>
																					</xs:sequence>
																				</xs:complexType>
																			</xs:element>
																		</xs:sequence>
																	</xs:complexType>
																</xs:element>
																<xs:element name="instalment">
																	<xs:complexType>
																		<xs:sequence>
																			<xs:element name="accountNumber">
																				<xs:complexType>
																					<xs:sequence>
																						<xs:element name="numberPart1" minOccurs="0" maxOccurs="1">
																							<xs:annotation>
																								<xs:documentation>Prefix part of the bank account number</xs:documentation>
																							</xs:annotation>
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:maxLength value="6"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="numberPart2">
																							<xs:annotation>
																								<xs:documentation>Base part of the bank account number</xs:documentation>
																							</xs:annotation>
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:maxLength value="10"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="bankCode" type="xs:string" minOccurs="0">
																							<xs:annotation>
																								<xs:documentation>Part of the bank account number which identifies the bank [CODELIST: BANK_CODES]</xs:documentation>
																							</xs:annotation>
																						</xs:element>
																						<xs:element name="currencyCode" type="xs:string" minOccurs="0">
																							<xs:annotation>
																								<xs:documentation>Currency of the bank account [CODELIST: Currency]</xs:documentation>
																							</xs:annotation>
																						</xs:element>
																					</xs:sequence>
																				</xs:complexType>
																			</xs:element>
																			<xs:element name="instalmentAmount" minOccurs="0">
																				<xs:complexType>
																					<xs:sequence>
																						<xs:element name="amount" type="xs:decimal">
																							<xs:annotation>
																								<xs:documentation>Amount of money</xs:documentation>
																							</xs:annotation>
																						</xs:element>
																						<xs:element name="currencyCode" type="xs:string">
																							<xs:annotation>
																								<xs:documentation>Currency code of the amount [CODELIST: Currency]</xs:documentation>
																							</xs:annotation>
																						</xs:element>
																					</xs:sequence>
																				</xs:complexType>
																			</xs:element>
																			<xs:element name="symbols">
																				<xs:complexType>
																					<xs:sequence>
																						<xs:element name="variable" minOccurs="0">
																							<xs:annotation>
																								<xs:documentation>Variable symbol</xs:documentation>
																							</xs:annotation>
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:maxLength value="10"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="constant" minOccurs="0">
																							<xs:annotation>
																								<xs:documentation>Constant symbol</xs:documentation>
																							</xs:annotation>
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:maxLength value="10"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="specific" minOccurs="0">
																							<xs:annotation>
																								<xs:documentation>Specific symbol</xs:documentation>
																							</xs:annotation>
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:maxLength value="10"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																					</xs:sequence>
																				</xs:complexType>
																			</xs:element>
																			<xs:element name="date" type="xs:date" minOccurs="0">
																				<xs:annotation>
																					<xs:documentation>Date of a payment of an instalment</xs:documentation>
																				</xs:annotation>
																			</xs:element>
																		</xs:sequence>
																	</xs:complexType>
																</xs:element>
																<xs:element name="statementInfo" minOccurs="0">
																	<xs:complexType>
																		<xs:sequence>
																			<xs:element name="lastStatementDate" type="xs:date" minOccurs="0"/>
																			<xs:element name="nextStatementDate" type="xs:date" minOccurs="0"/>
																		</xs:sequence>
																	</xs:complexType>
																</xs:element>
																<xs:element name="limits" minOccurs="1" maxOccurs="1">
																	<xs:complexType>
																		<xs:sequence>
																			<xs:element name="limit" maxOccurs="unbounded">
																				<xs:complexType>
																					<xs:sequence>
																						<xs:element name="limitType">
																							<xs:annotation>
																								<xs:documentation>[LOV: General;ATM;POS]</xs:documentation>
																							</xs:annotation>
																							<xs:simpleType>
																								<xs:restriction base="xs:string">
																									<xs:enumeration value="General"/>
																									<xs:enumeration value="ATM"/>
																									<xs:enumeration value="POS"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="maxAmount">
																							<xs:complexType>
																								<xs:sequence>
																									<xs:element name="amount" type="xs:decimal">
																										<xs:annotation>
																											<xs:documentation>Amount of money</xs:documentation>
																										</xs:annotation>
																									</xs:element>
																									<xs:element name="currencyCode" type="xs:string">
																										<xs:annotation>
																											<xs:documentation>Currency code of the amount [CODELIST: Currency]</xs:documentation>
																										</xs:annotation>
																									</xs:element>
																								</xs:sequence>
																							</xs:complexType>
																						</xs:element>
																						<xs:element name="period" type="xs:string">
																							<xs:annotation>
																								<xs:documentation>[CODELIST: FrequencyType]</xs:documentation>
																							</xs:annotation>
																						</xs:element>
																					</xs:sequence>
																				</xs:complexType>
																			</xs:element>
																		</xs:sequence>
																	</xs:complexType>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
										<xs:element name="cardDetails" minOccurs="0">
											<xs:complexType>
												<xs:sequence>
													<xs:element name="cardDetail" maxOccurs="unbounded">
														<xs:complexType>
															<xs:sequence>
																<xs:element name="identifier">
																	<xs:complexType>
																		<xs:sequence>
																			<xs:element name="identificationCode">
																				<xs:annotation>
																					<xs:documentation>Card identifier. A value of the identifier (type of the identifier is specified in the identificationType field)</xs:documentation>
																				</xs:annotation>
																				<xs:simpleType>
																					<xs:restriction base="xs:string">
																						<xs:maxLength value="30"/>
																					</xs:restriction>
																				</xs:simpleType>
																			</xs:element>
																			<xs:element name="identificationType" type="xs:string">
																				<xs:annotation>
																					<xs:documentation>[CODELIST: UNI_PIDT]</xs:documentation>
																				</xs:annotation>
																			</xs:element>
																		</xs:sequence>
																	</xs:complexType>
																</xs:element>
																<xs:element name="maskedNumber" type="xs:string">
																	<xs:annotation>
																		<xs:documentation>A masked payment card number</xs:documentation>
																	</xs:annotation>
																</xs:element>
																<xs:element name="primaryCard" type="xs:boolean" minOccurs="0"/>
																<xs:element name="embossedName1" minOccurs="0">
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="25"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="paymentCardHolder" minOccurs="0">
																	<xs:complexType>
																		<xs:sequence>
																			<xs:element name="partyId">
																				<xs:annotation>
																					<xs:documentation>Party Id (Siebel identifier)</xs:documentation>
																				</xs:annotation>
																				<xs:simpleType>
																					<xs:restriction base="xs:string">
																						<xs:maxLength value="15"/>
																					</xs:restriction>
																				</xs:simpleType>
																			</xs:element>
																		</xs:sequence>
																	</xs:complexType>
																</xs:element>
																<xs:element name="validity">
																	<xs:complexType>
																		<xs:sequence>
																			<xs:element name="validFrom" type="xs:date">
																				<xs:annotation>
																					<xs:documentation>Duration from date</xs:documentation>
																				</xs:annotation>
																			</xs:element>
																			<xs:element name="validTo" type="xs:date">
																				<xs:annotation>
																					<xs:documentation>Duration to date</xs:documentation>
																				</xs:annotation>
																			</xs:element>
																		</xs:sequence>
																	</xs:complexType>
																</xs:element>
																<xs:element name="setting">
																	<xs:complexType>
																		<xs:sequence>
																			<xs:element name="renewal" minOccurs="0" maxOccurs="1">
																				<xs:complexType>
																					<xs:sequence>
																						<xs:element name="automaticRenewal" type="xs:boolean">
																							<xs:annotation>
																								<xs:documentation>Flag, if a client wants to set an automatic renewal of a card.</xs:documentation>
																							</xs:annotation>
																						</xs:element>
																						<xs:element name="deliveryType" type="xs:string" minOccurs="0">
																							<xs:annotation>
																								<xs:documentation>Type of a delivery - a client address or a branch - A, B [CODELIST: DistributionChannel]</xs:documentation>
																							</xs:annotation>
																						</xs:element>
																						<xs:element name="branchCode" type="xs:string" minOccurs="0" maxOccurs="1">
																							<xs:annotation>
																								<xs:documentation>Identification of a branch [CODELIST: Branch]</xs:documentation>
																							</xs:annotation>
																						</xs:element>
																					</xs:sequence>
																				</xs:complexType>
																			</xs:element>
																			<xs:element name="insurances" minOccurs="0" maxOccurs="1">
																				<xs:complexType>
																					<xs:sequence>
																						<xs:element name="insurance" maxOccurs="unbounded">
																							<xs:complexType>
																								<xs:sequence>
																									<xs:element name="insurancePurposeCode" type="xs:string">
																										<xs:annotation>
																											<xs:documentation>[CODELIST: INSURANCE_PURPOSE_CODES]</xs:documentation>
																										</xs:annotation>
																									</xs:element>
																									<xs:element name="insuranceProductCode" type="xs:string">
																										<xs:annotation>
																											<xs:documentation>Insurance product code. [CODELIST: INSURANCE_PRODUCT_CODES]</xs:documentation>
																										</xs:annotation>
																									</xs:element>
																								</xs:sequence>
																							</xs:complexType>
																						</xs:element>
																					</xs:sequence>
																				</xs:complexType>
																			</xs:element>
																			<xs:element name="eCommerce" minOccurs="0" maxOccurs="1">
																				<xs:complexType>
																					<xs:sequence>
																						<xs:element name="activated" type="xs:boolean">
																							<xs:annotation>
																								<xs:documentation>The e-commerce feature is activated (true/false).</xs:documentation>
																							</xs:annotation>
																						</xs:element>
																						<xs:element name="setUpAvailable" type="xs:boolean">
																							<xs:annotation>
																								<xs:documentation>Client has E-commerce set up available (turn on/turn off).</xs:documentation>
																							</xs:annotation>
																						</xs:element>
																					</xs:sequence>
																				</xs:complexType>
																			</xs:element>
																			<xs:element name="limits" minOccurs="1" maxOccurs="1">
																				<xs:complexType>
																					<xs:sequence>
																						<xs:element name="limit" maxOccurs="unbounded">
																							<xs:complexType>
																								<xs:sequence>
																									<xs:element name="limitType">
																										<xs:annotation>
																											<xs:documentation>[LOV: General;ATM;POS]</xs:documentation>
																										</xs:annotation>
																										<xs:simpleType>
																											<xs:restriction base="xs:string">
																												<xs:enumeration value="General"/>
																												<xs:enumeration value="ATM"/>
																												<xs:enumeration value="POS"/>
																											</xs:restriction>
																										</xs:simpleType>
																									</xs:element>
																									<xs:element name="maxAmount">
																										<xs:complexType>
																											<xs:sequence>
																												<xs:element name="amount" type="xs:decimal">
																													<xs:annotation>
																														<xs:documentation>Amount of money</xs:documentation>
																													</xs:annotation>
																												</xs:element>
																												<xs:element name="currencyCode" type="xs:string">
																													<xs:annotation>
																														<xs:documentation>Currency code of the amount [CODELIST: Currency]</xs:documentation>
																													</xs:annotation>
																												</xs:element>
																											</xs:sequence>
																										</xs:complexType>
																									</xs:element>
																									<xs:element name="period" type="xs:string" minOccurs="0">
																										<xs:annotation>
																											<xs:documentation>[CODELIST: FrequencyType]</xs:documentation>
																										</xs:annotation>
																									</xs:element>
																								</xs:sequence>
																							</xs:complexType>
																						</xs:element>
																					</xs:sequence>
																				</xs:complexType>
																			</xs:element>
																		</xs:sequence>
																	</xs:complexType>
																</xs:element>
																<xs:element name="balances" minOccurs="0" maxOccurs="1">
																	<xs:complexType>
																		<xs:sequence>
																			<xs:element name="balance" minOccurs="1" maxOccurs="unbounded">
																				<xs:complexType>
																					<xs:sequence>
																						<xs:element name="balanceCode" type="xs:string">
																							<xs:annotation>
																								<xs:documentation>[CODELIST: BALANCE_CODES]</xs:documentation>
																							</xs:annotation>
																						</xs:element>
																						<xs:element name="amount" type="xs:decimal"/>
																						<xs:element name="currencyCode" type="xs:string">
																							<xs:annotation>
																								<xs:documentation>[CODELIST: Currency]</xs:documentation>
																							</xs:annotation>
																						</xs:element>
																						<xs:element name="refreshDate" type="xs:dateTime" minOccurs="0" maxOccurs="1"/>
																					</xs:sequence>
																				</xs:complexType>
																			</xs:element>
																		</xs:sequence>
																	</xs:complexType>
																</xs:element>
																<xs:element name="creditCard" minOccurs="0">
																	<xs:complexType>
																		<xs:sequence>
																			<xs:element name="cardProductType" type="xs:string">
																				<xs:annotation>
																					<xs:documentation>[CODELIST: CreditCardTypeCode]</xs:documentation>
																				</xs:annotation>
																			</xs:element>
																			<xs:element name="status" type="xs:string" minOccurs="1" maxOccurs="1">
																				<xs:annotation>
																					<xs:documentation>[CODELIST: CREDIT_CARD_STATUS_CODES]</xs:documentation>
																				</xs:annotation>
																			</xs:element>
																		</xs:sequence>
																	</xs:complexType>
																</xs:element>
																<xs:element name="debitCard" minOccurs="0">
																	<xs:complexType>
																		<xs:sequence>
																			<xs:element name="cardProductType" type="xs:string">
																				<xs:annotation>
																					<xs:documentation>[CODELIST: DebitCardTypeCode]</xs:documentation>
																				</xs:annotation>
																			</xs:element>
																			<xs:element name="status" type="xs:string">
																				<xs:annotation>
																					<xs:documentation>[CODELIST: DEBIT_CARD_STATUS_CODES]</xs:documentation>
																				</xs:annotation>
																			</xs:element>
																		</xs:sequence>
																	</xs:complexType>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
										<xs:element name="resultInfo" minOccurs="0" maxOccurs="1">
											<xs:complexType>
												<xs:sequence>
													<xs:element name="resultItem" minOccurs="1" maxOccurs="unbounded">
														<xs:complexType>
															<xs:sequence>
																<xs:element name="code" type="xs:string">
																	<xs:annotation>
																		<xs:documentation>A code of the error/warning [CODELIST: ERROR_CODES]</xs:documentation>
																	</xs:annotation>
																</xs:element>
																<xs:element name="description" type="xs:string">
																	<xs:annotation>
																		<xs:documentation>A description of the error/warning</xs:documentation>
																	</xs:annotation>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
</xs:schema>
