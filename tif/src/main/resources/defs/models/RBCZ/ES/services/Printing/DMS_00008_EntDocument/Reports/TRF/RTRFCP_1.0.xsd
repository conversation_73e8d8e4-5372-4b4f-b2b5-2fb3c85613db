<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/dms/dds/trf/cp/RTRFCP/1.0"
    xmlns="http://rb.cz/dms/dds/trf/cp/RTRFCP/1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="RTRFCP">
        <xs:complexType>
            <xs:sequence>
                <xs:element maxOccurs="1" minOccurs="1" name="client" nillable="false">
                    <xs:annotation>
                        <xs:documentation>Client party detail information</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence maxOccurs="1" minOccurs="1">
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="commercialRegistration" nillable="false">
                                <xs:complexType>
                                    <xs:sequence maxOccurs="1" minOccurs="1">
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="commercialName" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>commercial name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="contact" nillable="false">
                                <xs:complexType>
                                    <xs:sequence maxOccurs="1" minOccurs="1">
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="preferred" nillable="false">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="0"
                                    name="email" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>preferred email address</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="150"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="fax" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>preferred fax</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="fixLine" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>preferred fix line</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="25"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="mobile" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>preferred mobile number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="20"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="personalForm" nillable="false">
                                <xs:annotation>
                                    <xs:documentation>Personal form  information of the party</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence maxOccurs="1" minOccurs="1">
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="fullName" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Fullname, consumer isn't able to split firstname and surname.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="80"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="1"
                                name="address" nillable="false">
                                <xs:annotation>
                                    <xs:documentation>Client addresses</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence maxOccurs="1" minOccurs="1">
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="mailing" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Party mailing address</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="street" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Name of the street</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="200"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="number" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Street number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="part" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>part of the city</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="city" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Name of the city</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="200"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="postalCode" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Postal code</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="country" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>[CODELIST: GENERAL_CNTR]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="place" nillable="false">
                    <xs:annotation>
                        <xs:documentation>City of the branch where the report is generated</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:maxLength value="80"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="1"
                    name="reportDate" nillable="false" type="xs:date">
                    <xs:annotation>
                        <xs:documentation>Date on which this document was generated</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0"
                    name="reportLanguage" nillable="false">
                    <xs:annotation>
                        <xs:documentation>language of the report [LOV: English;German;Czech] [LOV: English;German;Czech]</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:enumeration value="English"/>
                            <xs:enumeration value="German"/>
                            <xs:enumeration value="Czech"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element minOccurs="1" name="deliveryChannel" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>delivery channel of the document [CODELIST: FavouredConfirmationDeliveryChannel]</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="1"
                    name="confirmation" nillable="false">
                    <xs:complexType>
                        <xs:sequence maxOccurs="1" minOccurs="1">
                            <xs:element maxOccurs="1" minOccurs="1"
                                name="confirmationId" nillable="false" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>ID of the confirmation - orderRequest; tradeRequest; trade</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="uti" nillable="false" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>unique trade identification</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="confirmationName" nillable="false" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>confirmation name</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="transactionType" nillable="false" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>order type</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="1"
                                name="dealDate" nillable="false" type="xs:date">
                                <xs:annotation>
                                    <xs:documentation>deal date</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="dealTime" nillable="false" type="xs:time">
                                <xs:annotation>
                                    <xs:documentation>deal time</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0"
                    name="creditorDebtor" nillable="false">
                    <xs:complexType>
                        <xs:sequence maxOccurs="1" minOccurs="1">
                            <xs:element maxOccurs="1" minOccurs="1"
                                name="creditorParty" nillable="false" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>creditor</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="1"
                                name="debtorParty" nillable="false" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>debtor</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="1"
                    name="tradeConfirmation" nillable="false">
                    <xs:complexType>
                        <xs:sequence maxOccurs="1" minOccurs="1">
                            <xs:element maxOccurs="1" minOccurs="1"
                                name="ISIN" nillable="false" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>ISIN</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="1"
                                name="description" nillable="false" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>security name</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="1"
                                name="market" nillable="false" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>market</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="1"
                                name="numberOfUnits" nillable="false" type="xs:decimal">
                                <xs:annotation>
                                    <xs:documentation>number of units of securities</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="1"
                                name="nominalValue" nillable="false">
                                <xs:complexType>
                                    <xs:sequence maxOccurs="1" minOccurs="1">
                                    <xs:element name="currency" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The currency in which an amount is denominated.
 [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="amount"
                                    nillable="false" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>The positive monetary quantity in currency units.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="MIC" nillable="false" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Market Identification Code</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="haricut" nillable="false" type="xs:decimal">
                                <xs:annotation>
                                    <xs:documentation>percentage that is substracted from the market value of an asset that is being use as collateral</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="marketValue" nillable="false">
                                <xs:complexType>
                                    <xs:sequence maxOccurs="1" minOccurs="1">
                                    <xs:element name="currency" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The currency in which an amount is denominated.
 [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="amount"
                                    nillable="false" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>The positive monetary quantity in currency units.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="1"
                                name="tradeData" nillable="false">
                                <xs:complexType>
                                    <xs:sequence maxOccurs="1" minOccurs="1">
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="spotLeg" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>A repo contract is modeled  as two purchase/repurchase transactions which are called legs. This is the spot leg, i.e. the transaction that will be executed on the settlement date of the contract. This part is also used in case of Purchase/Sale and Buy/Sell.</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="buyerSeller" nillable="false">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="buyerParty"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>buyer of the security</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="sellerParty"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>seller of the security</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="valueDate"
                                    nillable="false" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Settlement Date.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="settlementType"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>settlement type</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="unitPrice" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Price per unit.</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    minOccurs="0"
                                    name="currency" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The currency in which an amount is denominated.
 [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="amount"
                                    nillable="false" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>The positive monetary quantity in currency units.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="unitPricePercentage"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Unit price in percentage</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="totalAmount" nillable="false">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="initialAmount" nillable="false">
                                    <xs:complexType>

                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="currencyType" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Type of the currency of the amount (e.g. currency of the settlement account, agreed settlemen currency, currency in which the trade was made) [LOV: account;settlement;trade] [LOV: account;settlement;trade]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="account"/>

                                    <xs:enumeration value="settlement"/>

                                    <xs:enumeration value="trade"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="amount"
                                    nillable="false" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>The currency in which an amount is denominated.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0"
                                    name="currency" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The positive monetary quantity in currency units.
 [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="accruedInterest" nillable="false">
                                    <xs:complexType>

                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    minOccurs="0"
                                    name="currency" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The currency in which an amount is denominated.
 [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="amount"
                                    nillable="false" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>The positive monetary quantity in currency units.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="0"
                                    name="commisions" nillable="false">
                                    <xs:complexType>

                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="amount" nillable="false">
                                    <xs:complexType>

                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="currencyType" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Type of the currency of the amount (e.g. currency of the settlement account, agreed settlemen currency, currency in which the trade was made) [LOV: account;settlement;trade] [LOV: account;settlement;trade]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="account"/>

                                    <xs:enumeration value="settlement"/>

                                    <xs:enumeration value="trade"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="amount"
                                    nillable="false" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>The currency in which an amount is denominated.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0"
                                    name="currency" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The positive monetary quantity in currency units.
 [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="type" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Type of the commision (e.g. settlement commision, trade commision, total..) [LOV: SettlementCommision;TotalCommision;TradeCommision] [LOV: SettlementCommision;TotalCommision;TradeCommision]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="SettlementCommision"/>

                                    <xs:enumeration value="TotalCommision"/>

                                    <xs:enumeration value="TradeCommision"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="0"
                                    name="fees" nillable="false">
                                    <xs:complexType>

                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="amount" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Fee amount.</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>

                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="currencyType" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Type of the currency of the amount (e.g. currency of the settlement account, agreed settlemen currency, currency in which the trade was made) [LOV: account;settlement;trade] [LOV: account;settlement;trade]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="account"/>

                                    <xs:enumeration value="settlement"/>

                                    <xs:enumeration value="trade"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="amount"
                                    nillable="false" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>The currency in which an amount is denominated.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="1"
                                    name="currency" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The positive monetary quantity in currency units.
 [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="type" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Type of the fee (e.g. settlement fee, trade fee, total..) [LOV: SettlementFee;TotalFee;TradeFee] [LOV: SettlementFee;TotalFee;TradeFee]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="SettlementFee"/>

                                    <xs:enumeration value="TotalFee"/>

                                    <xs:enumeration value="TradeFee"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="feeRate"
                                    nillable="false" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Exchange rate of the fee</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1"
                                    name="totalAmount" nillable="false">
                                    <xs:complexType>

                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="currencyType" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Type of the currency of the amount (e.g. currency of the settlement account, agreed settlemen currency, currency in which the trade was made) [LOV: account;settlement;trade] [LOV: account;settlement;trade]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="account"/>

                                    <xs:enumeration value="settlement"/>

                                    <xs:enumeration value="trade"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="amount"
                                    nillable="false" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>The currency in which an amount is denominated.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="1"
                                    name="currency" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The positive monetary quantity in currency units.
 [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="exchangeRate"
                                    nillable="false" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Exchange rate - amount currency vs. reference currency</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="commisionsAndFees" nillable="false">
                                    <xs:complexType>

                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    minOccurs="0"
                                    name="currency" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The currency in which trade amount is debited/credited from client account
 [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="amount"
                                    nillable="false" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>The positive monetary quantity in currency units.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="repoRate"
                                    nillable="false" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Rate at which the central bank of a country lends money to commercial banks in the event of any shortfall of funds</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="marginCall"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>margin call</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="forwardLeg" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>The forward leg of the repo contract, i.e. the repurchase transaction. This part is also used in case of Sell/Buy.</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="buyerSeller" nillable="false">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="buyerParty"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>buyer of the security</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="sellerParty"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>seller of the security</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="maturityDate"
                                    nillable="false" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>maturity date</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="settlementType"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>settlement type</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="unitPrice" nillable="false">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    minOccurs="0"
                                    name="currency" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The currency in which an amount is denominated.
 [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="amount"
                                    nillable="false" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>The positive monetary quantity in currency units.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="unitPricePercentage"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Unit price in percentage</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="totalAmount" nillable="false">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="initialAmount" nillable="false">
                                    <xs:complexType>

                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="amount"
                                    nillable="false" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>The positive monetary quantity in currency units.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0"
                                    name="currency" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The currency in which an amount is denominated.
 [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="accruedInterest" nillable="false">
                                    <xs:complexType>

                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    minOccurs="0"
                                    name="currency" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The currency in which an amount is denominated.
 [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="amount"
                                    nillable="false" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>The positive monetary quantity in currency units.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="commisions" nillable="false">
                                    <xs:complexType>

                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="amount" nillable="false">
                                    <xs:complexType>

                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="amount"
                                    nillable="false" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>The positive monetary quantity in currency units.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0"
                                    name="currency" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The currency in which Commission is denominated.
 [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="fees" nillable="false">
                                    <xs:complexType>

                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="amount" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Fee amount.</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>

                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="amount"
                                    nillable="false" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>The positive monetary quantity in currency units.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0"
                                    name="currency" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The currency in which Fees is denominated.
 [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="totalAmount" nillable="false">
                                    <xs:complexType>

                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="amount"
                                    nillable="false" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>The positive monetary quantity in currency units.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="currency" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The currency in which an amount is denominated.
 [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="tradeCostsIllustration" nillable="false">
                                <xs:annotation>
                                    <xs:documentation>Trade costs illustration</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="fxCosts" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Conversion costs</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="amount"
                                    nillable="false" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Amount of conversion costs</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="currency"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The currency in which costs are denominated (ISO 3char code).</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="expectedSellingCosts" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Expected Selling Costs</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="amount"
                                    nillable="false" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Amount of expected selling costs</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="currency"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The currency in which costs are denominated (ISO 3char code).</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="expectedOneYearCosts" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Expected Custody Fee For 1 Year</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="amount"
                                    nillable="false" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Amount of expected 1 year costs</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="currency"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The currency in which costs are denominated (ISO 3char code).</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="1"
                    name="confirmationFooter" nillable="false">
                    <xs:complexType>
                        <xs:sequence maxOccurs="1" minOccurs="1">
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="cashAccount" nillable="false">
                                <xs:complexType>
                                    <xs:sequence maxOccurs="1" minOccurs="1">
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="name"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>cash account</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="numberPart1" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="numberPart2" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies particular bank. [CODELIST: BANK_CODES] [CODELIST: BANK_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="note" nillable="false" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>note</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="contractId" nillable="false" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>internal contract ID</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="contractName" nillable="false" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>contract name</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="contractType" nillable="false" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>contract type</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="agreementWithClient" nillable="false">
                                <xs:annotation>
                                    <xs:documentation>agreement with client [LOV: AM;OTHER;REPO] [LOV: AM;OTHER;REPO]</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:enumeration value="AM"/>
                                    <xs:enumeration value="OTHER"/>
                                    <xs:enumeration value="REPO"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="agreementDate" nillable="false" type="xs:date"/>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="counterPartyCustodian"
                                nillable="false" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>counterparty custodian</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="1"
                                name="dealerName" nillable="false" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>name of the dealer</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element minOccurs="0" name="kidUrls">
                                <xs:annotation>
                                    <xs:documentation>List of URLs to KIDs (Key Information Document).</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="kidUrl">
                                    <xs:annotation>
                                    <xs:documentation>URL link to KID (Key Indication Document).</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="language" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Language of the KID to which the kidUrl refers to. [CODELIST: Language]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="url">
                                    <xs:annotation>
                                    <xs:documentation>URL to the KID</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
