<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/services/entityService/document/Update_1_0"
    version="1.0.0"
    xmlns:opupdate_1_0="http://rb.cz/services/entityService/document/Update_1_0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="opUpdate_1_0">
        <xs:complexType>
            <xs:sequence>
                <xs:choice>
                    <xs:element maxOccurs="1" minOccurs="1" name="documentId">
                        <xs:annotation>
                            <xs:documentation>The unique identifier of the document assigned by the system Documentum </xs:documentation>
                        </xs:annotation>
                        <xs:simpleType>
                            <xs:restriction base="xs:string">
                                <xs:maxLength value="16"/>
                            </xs:restriction>
                        </xs:simpleType>
                    </xs:element>
                    <xs:element maxOccurs="1" minOccurs="1" name="externalId">
                        <xs:annotation>
                            <xs:documentation>External identification of the document. Unique ID generated by other application then DMS (documentId in DMS). In case of no externalId exist the document is identified by internal DMS ID (dctmId)</xs:documentation>
                        </xs:annotation>
                        <xs:simpleType>
                            <xs:restriction base="xs:string">
                                <xs:maxLength value="32"/>
                            </xs:restriction>
                        </xs:simpleType>
                    </xs:element>
                </xs:choice>
                <xs:element name="objectOfUpdate">
                    <xs:annotation>
                        <xs:documentation>Parameter that express what is updated
(content cannot be RESET)</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="content" type="xs:boolean">
                                <xs:annotation>
                                    <xs:documentation>content of document</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="metadata" type="xs:boolean">
                                <xs:annotation>
                                    <xs:documentation>metadata of document</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="esdo" type="xs:boolean">
                                <xs:annotation>
                                    <xs:documentation>esdo of the document</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element name="versioningType">
                    <xs:annotation>
                        <xs:documentation>Document is versioned - major/minor/no_version(ing) [LOV: MAJOR;MINOR;NO_VERSION]</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:enumeration value="MAJOR"/>
                            <xs:enumeration value="MINOR"/>
                            <xs:enumeration value="NO_VERSION"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="content">
                    <xs:annotation>
                        <xs:documentation>Content can be updated when the operationType = SET  (RESET Doesnt work with content)</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="format" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>File type extension to identify the content of the document format (doc, pdf, xls, ...) [CODELIST: DocumentFormat]</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="uri">
                                <xs:annotation>
                                    <xs:documentation>URI of stored document on shared storage</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="400"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="rendition" type="xs:boolean">
                                <xs:annotation>
                                    <xs:documentation>Rendition (True = rendition, False = main content)</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="metadata">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="1" minOccurs="0" name="documentDetail">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="callingApplicationSet">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="operationType">
                                    <xs:annotation>
                                    <xs:documentation>Type of operation  [LOV: SET;RESET]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="SET"/>

                                    <xs:enumeration value="RESET"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="callingApplication" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Application that calls this service</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="documentTypeSet">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="operationType">
                                    <xs:annotation>
                                    <xs:documentation>Type of operation  [LOV: SET;RESET]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="SET"/>

                                    <xs:enumeration value="RESET"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="documentType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>ReportCode (ie: P0000645)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="documentTypeCodeSet">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="operationType">
                                    <xs:annotation>
                                    <xs:documentation>Type of operation  [LOV: SET;RESET]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="SET"/>

                                    <xs:enumeration value="RESET"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="documentTypeCode" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Code of the document type</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="142"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="documentNameSet">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="operationType">
                                    <xs:annotation>
                                    <xs:documentation>Type of operation  [LOV: SET;RESET]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="SET"/>

                                    <xs:enumeration value="RESET"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="documentName">
                                    <xs:annotation>
                                    <xs:documentation>Name of document</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="documentSmStateSet">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="operationType">
                                    <xs:annotation>
                                    <xs:documentation>Type of operation  [LOV: SET;RESET]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="SET"/>

                                    <xs:enumeration value="RESET"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="documentSmState" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Document state - code of the state (codebook of states - State Machine - rb_t_state_definition, describes workflows.)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="32"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="documentSmStateDescriptionSet">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="operationType">
                                    <xs:annotation>
                                    <xs:documentation>Type of operation  [LOV: SET;RESET]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="SET"/>

                                    <xs:enumeration value="RESET"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="documentSmStateDescription" nillable="false">
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="32"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="deletedDocument"
                                    nillable="false" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Can be used to recover document (set to false to recover)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="pki">
                                    <xs:annotation>
                                    <xs:documentation>Private Key Infrastructure</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="certificateExpirationSet">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="operationType">
                                    <xs:annotation>
                                    <xs:documentation>Type of operation  [LOV: SET;RESET]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="SET"/>

                                    <xs:enumeration value="RESET"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="certificateExpiration"
                                    nillable="false" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Expiration date of oldest certificate - in case of signed the document</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="ltvSet">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="operationType">
                                    <xs:annotation>
                                    <xs:documentation>Type of operation  [LOV: SET;RESET]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="SET"/>

                                    <xs:enumeration value="RESET"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="ltv"
                                    nillable="false" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Document is in LTV mode (Long Term Validation)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="validStateSet">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="operationType">
                                    <xs:annotation>
                                    <xs:documentation>Type of operation  [LOV: SET;RESET]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="SET"/>

                                    <xs:enumeration value="RESET"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="validState" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Validation state of document certificates</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="20"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="relatedMetadata">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="accountNumbersSet">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="operationType">
                                    <xs:annotation>
                                    <xs:documentation>[LOV: SET;RESET;ADD;REMOVE]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="SET"/>

                                    <xs:enumeration value="RESET"/>

                                    <xs:enumeration value="ADD"/>

                                    <xs:enumeration value="REMOVE"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="0"
                                    name="accountNumber" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Account number related to the document</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="60"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="agreementNumbersSet">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="operationType">
                                    <xs:annotation>
                                    <xs:documentation>[LOV: SET;RESET;ADD;REMOVE]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="SET"/>

                                    <xs:enumeration value="RESET"/>

                                    <xs:enumeration value="ADD"/>

                                    <xs:enumeration value="REMOVE"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="0"
                                    name="agreementNumber" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Agreement number related to the document</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="60"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="transactionNumberSet">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="operationType">
                                    <xs:annotation>
                                    <xs:documentation>Type of operation  [LOV: SET;RESET]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="SET"/>

                                    <xs:enumeration value="RESET"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="transactionNumber" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Transaction number related to the document</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="100"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="applicationIdSet">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="operationType">
                                    <xs:annotation>
                                    <xs:documentation>Type of operation  [LOV: SET;RESET]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="SET"/>

                                    <xs:enumeration value="RESET"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="applicationId" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Application ID</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="60"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="loanIdSet">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="operationType">
                                    <xs:annotation>
                                    <xs:documentation>Type of operation  [LOV: SET;RESET]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="SET"/>

                                    <xs:enumeration value="RESET"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="loanId" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Loan ID</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="60"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="clientNoteSet">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="operationType">
                                    <xs:annotation>
                                    <xs:documentation>Type of operation  [LOV: SET;RESET]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="SET"/>

                                    <xs:enumeration value="RESET"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="clientNote" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Client note</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="1200"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="creatingSystemSet">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="operationType">
                                    <xs:annotation>
                                    <xs:documentation>Type of operation  [LOV: SET;RESET]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="SET"/>

                                    <xs:enumeration value="RESET"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="creatingSystem" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>System which created or changed the document</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="esdoCurrentDetail">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="cardNumberSet">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="operationType">
                                    <xs:annotation>
                                    <xs:documentation>Type of operation  [LOV: SET;RESET]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="SET"/>

                                    <xs:enumeration value="RESET"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="cardNumber" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Card number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="20"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="sumOfMoneySet">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="operationType">
                                    <xs:annotation>
                                    <xs:documentation>Type of operation  [LOV: SET;RESET]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="SET"/>

                                    <xs:enumeration value="RESET"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="sumOfMoney"
                                    nillable="false" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Sum of money</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="commentSet">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="operationType">
                                    <xs:annotation>
                                    <xs:documentation>Type of operation  [LOV: SET;RESET]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="SET"/>

                                    <xs:enumeration value="RESET"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="comment" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>ESDO comment</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="4000"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="partyDetail">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element minOccurs="0" name="relatedMetadata">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="relatedPartyIdsSet">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element name="operationType">
                                    <xs:annotation>
                                    <xs:documentation>[LOV: ADD;REMOVE;SET;RESET]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="ADD"/>

                                    <xs:enumeration value="REMOVE"/>

                                    <xs:enumeration value="SET"/>

                                    <xs:enumeration value="RESET"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="0"
                                    name="relatedPartyId" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Party Id (Siebel identifier)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="16"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="statementDetail">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="statementNumber">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="operationType">
                                    <xs:annotation>
                                    <xs:documentation>Type of operation  [LOV: SET;RESET]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="SET"/>

                                    <xs:enumeration value="RESET"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="statementNumber" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Statement number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="100"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="startDateSet">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="operationType">
                                    <xs:annotation>
                                    <xs:documentation>Type of operation  [LOV: SET;RESET]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="SET"/>

                                    <xs:enumeration value="RESET"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="startDate"
                                    nillable="false" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Start date of statement</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="endDateSet">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="operationType">
                                    <xs:annotation>
                                    <xs:documentation>Type of operation  [LOV: SET;RESET]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="SET"/>

                                    <xs:enumeration value="RESET"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="endDate"
                                    nillable="false" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>End date of statement</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="maskedCardNumberSet">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="operationType">
                                    <xs:annotation>
                                    <xs:documentation>Type of operation  [LOV: SET;RESET]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="SET"/>

                                    <xs:enumeration value="RESET"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="maskedCardNumber" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Masked card number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="64"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="currencySet">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="operationType">
                                    <xs:annotation>
                                    <xs:documentation>Type of operation  [LOV: SET;RESET]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="SET"/>

                                    <xs:enumeration value="RESET"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="currency" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Currency of statement</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="3"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="startBalanceAmountSet">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="operationType">
                                    <xs:annotation>
                                    <xs:documentation>Type of operation  [LOV: SET;RESET]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="SET"/>

                                    <xs:enumeration value="RESET"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="startBalanceAmount"
                                    nillable="false" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Start balance amount</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="endBalanceAmountSet">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="operationType">
                                    <xs:annotation>
                                    <xs:documentation>Type of operation  [LOV: SET;RESET]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="SET"/>

                                    <xs:enumeration value="RESET"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="endBalanceAmount"
                                    nillable="false" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>End balance amount</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="sumDebit">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="operationType">
                                    <xs:annotation>
                                    <xs:documentation>Type of operation  [LOV: SET;RESET]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="SET"/>

                                    <xs:enumeration value="RESET"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="sumDebit"
                                    nillable="false" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Sum of debit items</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="sumCreditSet">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="operationType">
                                    <xs:annotation>
                                    <xs:documentation>Type of operation  [LOV: SET;RESET]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="SET"/>

                                    <xs:enumeration value="RESET"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="sumCredit"
                                    nillable="false" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Sum of credit items</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="channelSet">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="operationType">
                                    <xs:annotation>
                                    <xs:documentation>Type of operation  [LOV: SET;RESET]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="SET"/>

                                    <xs:enumeration value="RESET"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="channel" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Channel for statement delivery (email, pošta, ib ...)
</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="emailIdSet">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="operationType">
                                    <xs:annotation>
                                    <xs:documentation>Type of operation  [LOV: SET;RESET]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="SET"/>

                                    <xs:enumeration value="RESET"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="emailId" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>EmailID from TRS</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="addressIdSet">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="operationType">
                                    <xs:annotation>
                                    <xs:documentation>Type of operation  [LOV: SET;RESET]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="SET"/>

                                    <xs:enumeration value="RESET"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="addressId" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>AddressId from TRS</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="trsIdSet">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="operationType">
                                    <xs:annotation>
                                    <xs:documentation>Type of operation  [LOV: SET;RESET]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="SET"/>

                                    <xs:enumeration value="RESET"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="trsId" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>TrsId </xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="languageSet">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="operationType">
                                    <xs:annotation>
                                    <xs:documentation>Type of operation  [LOV: SET;RESET]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="SET"/>

                                    <xs:enumeration value="RESET"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="language" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Language from TRS</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="accountTypeCodeSet">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="operationType">
                                    <xs:annotation>
                                    <xs:documentation>Type of operation  [LOV: SET;RESET]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="SET"/>

                                    <xs:enumeration value="RESET"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="accountTypeCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>AccountTypeCode from TRS</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="ordinalNumberSet">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="operationType">
                                    <xs:annotation>
                                    <xs:documentation>Type of operation  [LOV: SET;RESET]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="SET"/>

                                    <xs:enumeration value="RESET"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="ordinalNumber"
                                    nillable="false" type="xs:double">
                                    <xs:annotation>
                                    <xs:documentation>Ordinal number of statement</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="yearBs">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="operationType">
                                    <xs:annotation>
                                    <xs:documentation>Type of operation  [LOV: SET;RESET]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="SET"/>

                                    <xs:enumeration value="RESET"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="yearBs"
                                    nillable="false" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Year of statement</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="esdo">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="currentPlace" nillable="false">
                                <xs:annotation>
                                    <xs:documentation>ESDO - current location</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="267"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="currentPlaceType" nillable="false">
                                <xs:annotation>
                                    <xs:documentation>ESDO - current type of location (by codebook)</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="16"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="currentPlaceTypeId" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>ESDO - current typeID of location</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="esdoState" nillable="false">
                                <xs:annotation>
                                    <xs:documentation>ESDO state</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="16"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="isPrinted" type="xs:boolean">
                                <xs:annotation>
                                    <xs:documentation>Is document printed?</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="opUpdate_1_0_res">
        <xs:complexType>
            <xs:sequence>
                <xs:element maxOccurs="1" minOccurs="0" name="document">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="1" minOccurs="1" name="documentId">
                                <xs:annotation>
                                    <xs:documentation>The unique identifier of the document assigned by the system Documentum (dctmId)</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="16"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="externalId">
                                <xs:annotation>
                                    <xs:documentation>External identification of the document. Unique ID generated by other application then DMS (documentId in DMS). In case of no externalId exist the document is identified by internal DMS ID (dctmId)</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="32"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="version" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Version of the document</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="isFinalized" type="xs:boolean">
                                <xs:annotation>
                                    <xs:documentation>Flag that shows if the document is finalized</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="esdoState" nillable="false" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>EsdoState

</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="currentPlace" nillable="false" type="xs:string"/>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="currentPlaceType" nillable="false" type="xs:string"/>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="errorLists">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded"
                                minOccurs="1" name="errorList">
                                <xs:complexType>
                                    <xs:sequence maxOccurs="1" minOccurs="1">
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="errorCode"
                                    nillable="false" type="xs:string"/>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="errorDesc"
                                    nillable="false" type="xs:string"/>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="0" name="errorID"
                                    nillable="false" type="xs:string"/>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
