<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/dms/dds/BAI/K0000901/1.0"
    xmlns="http://rb.cz/dms/dds/BAI/K0000901/1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="K0000901">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="requestId" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Request identification</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="technicalMetadata">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element minOccurs="0" name="documentId">
                                <xs:annotation>
                                    <xs:documentation>The unique identifier of the document assigned by the system Documentum (dctmId)</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="16"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element minOccurs="0" name="externalDocumentId">
                                <xs:annotation>
                                    <xs:documentation>External identification of the document. Unique ID generated by other application then DMS (documentId in DMS). In case of no externalId exist the document is identified by internal DMS ID (dctmId)</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="16"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element minOccurs="0" name="delivery">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element minOccurs="1"
                                    name="deliveryChannel" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>delivery channel of the document [CODELIST: FavouredConfirmationDeliveryChannel]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:choice minOccurs="0">
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1"
                                    name="email" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>E-mail address of the report recipient</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    name="fax" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>fax of the report recipient</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1" name="addresses">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="address">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    minOccurs="0" name="addressee">
                                    <xs:annotation>
                                    <xs:documentation>Mail addressee</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="additionalInfo">
                                    <xs:annotation>
                                    <xs:documentation>Additional address info</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="street">
                                    <xs:annotation>
                                    <xs:documentation>Street name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="200"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="number">
                                    <xs:annotation>
                                    <xs:documentation>Building number/street number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="city">
                                    <xs:annotation>
                                    <xs:documentation>City name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="postalCode">
                                    <xs:annotation>
                                    <xs:documentation>ZIP code</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0"
                                    name="country" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>[CODELIST: GENERAL_CNTR]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:choice>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element minOccurs="0"
                                name="reportLanguage" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>language of the report, default is czech [CODELIST: LanguageDMS]</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element minOccurs="0" name="fileName" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>name of the generated report</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="fileFormatProperties">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="fileType">
                                    <xs:annotation>
                                    <xs:documentation>Statement file type

abo (1 | 2) (XX | AV) ( c | n) 
abo (1 = ediční, 2 = vnitřní ) (XX = není AV, AV = je AV) 
(c = v Doplňující údaj je Counter Acc Name | n = v Doplňující údaj je Note ) 
abo1XXc ABO (ediční formát) bez AV pole s názvem protistrany (v poli Doplňující údaj) 
abo1XXn ABO (ediční formát) bez AV pole s poznámkou (v poli Doplňující údaj) 
abo2XXc ABO (vnitřní formát) bez AV pole s názvem protistrany (v poli Doplňující údaj) 
abo2XXn ABO (vnitřní formát) bez AV pole s poznámkou (v poli Doplňující údaj) 
abo1AVc ABO (ediční formát) s AV polem s názvem protistrany (v poli Doplňující údaj) 
abo1AVn ABO (ediční formát) s AV polem s poznámkou (v poli Doplňující údaj) 
abo2AVc ABO (vnitřní formát) s AV polem s názvem protistrany (v poli Doplňující údaj) 
abo2AVn ABO (vnitřní formát) s AV polem s poznámkou (v poli Doplňující údaj)  [LOV: abo1XXc;abo1XXn;abo2XXc;abo2XXn;abo1AVc;abo1AVn;abo2AVc;abo2AVn;csv;gemini1;gemini2;jpg;pdf;PDFA-1a;PDFA-1b;xml]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:enumeration value="abo1XXc"/>
                                    <xs:enumeration value="abo1XXn"/>
                                    <xs:enumeration value="abo2XXc"/>
                                    <xs:enumeration value="abo2XXn"/>
                                    <xs:enumeration value="abo1AVc"/>
                                    <xs:enumeration value="abo1AVn"/>
                                    <xs:enumeration value="abo2AVc"/>
                                    <xs:enumeration value="abo2AVn"/>
                                    <xs:enumeration value="csv"/>
                                    <xs:enumeration value="gemini1"/>
                                    <xs:enumeration value="gemini2"/>
                                    <xs:enumeration value="jpg"/>
                                    <xs:enumeration value="pdf"/>
                                    <xs:enumeration value="PDFA-1a"/>
                                    <xs:enumeration value="PDFA-1b"/>
                                    <xs:enumeration value="xml"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="addParams">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="addParam">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="code">
                                    <xs:annotation>
                                    <xs:documentation>[LOV: dpi]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="dpi"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="value" type="xs:string"/>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element minOccurs="0" name="attachments">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="attachment">
                                    <xs:annotation>
                                    <xs:documentation>list of possible attachments for a reprt</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="type">
                                    <xs:annotation>
                                    <xs:documentation>any document can be attached based on the documentId or an url (url link to open the document in DMS) [LOV: documentId;url]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="documentId"/>

                                    <xs:enumeration value="url"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="value" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>value of documentId or url</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="documentName" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>name of the attachment</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="place" nillable="false">
                                <xs:annotation>
                                    <xs:documentation>City of the branch where the report is generated</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="80"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="timestamp">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="reportDate"
                                    nillable="false" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Date on which this report is generated</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="reportTime"
                                    nillable="false" type="xs:time">
                                    <xs:annotation>
                                    <xs:documentation>Time on which this report is generated</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="1" name="client" nillable="false">
                    <xs:annotation>
                        <xs:documentation>Client party detail information</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence maxOccurs="1" minOccurs="1">
                            <xs:element name="partyId">
                                <xs:annotation>
                                    <xs:documentation>Party Id (Siebel identifier)</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="1"
                                name="gender" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>[CODELIST: PRV_GENDER]</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="personName">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="titlesBefore">
                                    <xs:annotation>
                                    <xs:documentation>Titles in front of the name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="firstName">
                                    <xs:annotation>
                                    <xs:documentation>First name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="middleNames">
                                    <xs:annotation>
                                    <xs:documentation>Set of Middle names</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="lastName">
                                    <xs:annotation>
                                    <xs:documentation>Last name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="birthLastName">
                                    <xs:annotation>
                                    <xs:documentation>Birth last name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="titlesBehind">
                                    <xs:annotation>
                                    <xs:documentation>Titles behind the name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="address">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element minOccurs="0" name="addressee">
                                    <xs:annotation>
                                    <xs:documentation>Mail addressee</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="addressLine1">
                                    <xs:annotation>
                                    <xs:documentation>Street address, P.O. box, company name, c/o</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="addressLine2">
                                    <xs:annotation>
                                    <xs:documentation>Apartment, suite, unit, building, floor, etc.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="street">
                                    <xs:annotation>
                                    <xs:documentation>Street name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="200"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="number">
                                    <xs:annotation>
                                    <xs:documentation>Building number/street number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="city">
                                    <xs:annotation>
                                    <xs:documentation>City name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="country" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>[CODELIST: GENERAL_CNTR]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="postalCode">
                                    <xs:annotation>
                                    <xs:documentation>ZIP code</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="employee" nillable="false">
                    <xs:annotation>
                        <xs:documentation>worker signing/approving the document on behalf of RBCZ</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence maxOccurs="1" minOccurs="1">
                            <xs:element maxOccurs="1" minOccurs="1"
                                name="firstName" nillable="false">
                                <xs:annotation>
                                    <xs:documentation>First name</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="80"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="middleNames" nillable="false">
                                <xs:annotation>
                                    <xs:documentation>Middle names</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="80"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="1"
                                name="lastName" nillable="false">
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="100"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="titleBefore" nillable="false">
                                <xs:annotation>
                                    <xs:documentation>Title before the first name</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="titleBehind" nillable="false">
                                <xs:annotation>
                                    <xs:documentation>Title behind the surname</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="jobDescription" nillable="false">
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="75"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
