<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/services/party/GetRelations_1_0/1.0"
    version="1.0.0"
    xmlns:opgetrelations_1_0="http://rb.cz/services/party/GetRelations_1_0/1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="opGetRelations_1_0">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="partyId">
                    <xs:annotation>
                        <xs:documentation>Private party ID or Commercial Party ID for which relations are searched.</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:maxLength value="15"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="secondPartyId">
                    <xs:annotation>
                        <xs:documentation>Non mandatory second parameter to filter only those relations of PartyId that are related to PartyID2.</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:maxLength value="15"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element name="directionsFlag" type="xs:boolean">
                    <xs:annotation>
                        <xs:documentation>Indicates whether the service should return relations of type Relations Hierarchy for both directions.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="opGetRelations_1_0_res">
        <xs:complexType>
            <xs:sequence>
                <xs:element maxOccurs="1" minOccurs="0" name="relations">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded"
                                minOccurs="1" name="relation">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="partyId">
                                    <xs:annotation>
                                    <xs:documentation>Party Id (Siebel identifier)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="partyType" type="xs:string"/>
                                    <xs:element name="relatedPartyId">
                                    <xs:annotation>
                                    <xs:documentation>Related Party Id (Siebel identifier)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="relatedPartyType" type="xs:string"/>
                                    <xs:element name="relationType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Party Relation type [CODELIST: UNI_RELATIONSHIP]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="behalfAction" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Can act on behalf [CODELIST: GENERAL_ACTONBEHALF]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="relationValue" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Relation Value - usually percentage of the influence like ownership, share, etc.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="pgMembershipReason" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>P-G Membership reason - Filled from ISOR for GCC membership [CODELIST: GCC_MEMBERSHIP_REASON]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="gccGroupType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>GCC group type - Joined field GCC group type. Used only for GCC group membership [CODELIST: ACC_GCCCATEG]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="relationComment" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Comment</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="qualifiedRelationFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Calculated field indicating whether the relationship is of type "Qualified relationship" of "Relationship hierarchy" in Siebel. - Set to "Y" if the relationship is coming from the link between Account and Contact. Set to "N" if the relationship is coming from Party Relationship To BC.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="share" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Share represents a percentage ownership of a company</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
