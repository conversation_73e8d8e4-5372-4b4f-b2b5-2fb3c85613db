<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/dms/dds/partydata/PARTYDDS/1.1"
    xmlns="http://rb.cz/dms/dds/partydata/PARTYDDS/1.1" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="PARTYDDS">
        <xs:complexType>
            <xs:sequence>
                <xs:element maxOccurs="1" minOccurs="1" name="client" nillable="false">
                    <xs:complexType>
                        <xs:sequence maxOccurs="1" minOccurs="1">
                            <xs:element maxOccurs="1" minOccurs="1"
                                name="partyId" nillable="false">
                                <xs:annotation>
                                    <xs:documentation>Unique identification of the party</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:choice maxOccurs="1" minOccurs="1">
                                <xs:element maxOccurs="1" minOccurs="1"
                                    name="private" nillable="false">
                                    <xs:complexType>
                                    <xs:sequence maxOccurs="1" minOccurs="1">
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="firstName" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Jan</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="80"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="middleNames" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Xaver Josef Petr</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="80"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="lastName" nillable="false">
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="100"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="titleBefore" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Ing., RNDr.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="titleBehind" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>CSc., PhD.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="gender" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Muž/Žena [CODELIST: PRV_GENDER]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="citizenship" nillable="false">
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="birthInfo">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="birthCode" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>######/####</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="20"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="birthDate"
                                    nillable="false" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>30.01.1962</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="birthPlace" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>calculated from City of Birth and Country of birth</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="cityOfBirth" type="xs:string"/>
                                    <xs:element
                                    minOccurs="0"
                                    name="countryOfBirth" type="xs:string"/>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="productOwner" type="xs:boolean"/>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="futureProductOwner"
                                    nillable="false" type="xs:boolean"/>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="amlPEPFlg" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Flag, if person is politicaly exposed</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="amlPEPCheck">
                                    <xs:annotation>
                                    <xs:documentation>Confirmation of AML PEP flag. (SBL LOV RB_AML_PEP_CHECK contained values "Confirmed; Disclaimed"</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="consents">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="consent">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    name="consentPurpose" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Suffix of LIC code RB_CONSENTS_PURPOSE (because it consist of two = prefix and suffix which identifies
the concrete purpose) [CODELIST: ConsentPurpose]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="consentCategory" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Prefix of LIC code RB_CONSENTS_PURPOSE = MKTG|CARE|DATA (because it consist of two = prefix and suffix which identifies
the concrete purpose) [CODELIST: ConsentCategory]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="status" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Status of the consent (approved, not approved,...) [CODELIST: ConsentStatus]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="sourceSystem" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Code of the source system, where the consent has been collected 
(branch office, internet banking, smartphone application,...). Usefull if we need to find out when and
where consent has been completed. [CODELIST: Component]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="dateOfLastUpdate" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Contains date and time of the last update of a complex element consent</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="pepDetail">
                                    <xs:annotation>
                                    <xs:documentation>Politically exposed party/person</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:choice>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="yesPEP">
                                    <xs:annotation>
                                    <xs:documentation>Yes PEP</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="pepFunction" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>type of PEP function/occupation</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="pepCountry" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Country where PEP functions</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="from" type="xs:date"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="to" type="xs:date"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="stillFunction" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Flag if PEP function is still active</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="relatedPEP">
                                    <xs:annotation>
                                    <xs:documentation>in relantionship with PEP</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="relationshipType">
                                    <xs:annotation>
                                    <xs:documentation>type of relationship with PEP
1 - Jsem osobou blizkou
2 - Jsem spolecnikem nebo skutecnym majitelem stejne pravnicke osoby...
3 - Jsem skutecnym majitelem pravnicke osoby...</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string"/>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="firstName" nillable="false">
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="80"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="lastName" nillable="false">
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="100"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="pepFunction" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>type of PEP function/occupation</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="pepCountry" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Country where PEP functions</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="from" type="xs:date"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="to" type="xs:date"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="stillFunction" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Flag if PEP function is still active</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="noPEP" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Flag, if person is NOT PEP</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:choice>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element maxOccurs="1" minOccurs="1"
                                    name="commercial" nillable="false">
                                    <xs:complexType>
                                    <xs:sequence maxOccurs="1" minOccurs="1">
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="companyName" nillable="false">
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="taxId" nillable="false">
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="registrationNumber" nillable="false">
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="country" nillable="false">
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="legalForm" nillable="false">
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="dateOfEstablishment" type="xs:date"/>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="taxationResidency" nillable="false">
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="birthInfo">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="birthCode" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>######/####</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="20"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="birthDate"
                                    nillable="false" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>30.01.1962</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="birthPlace" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>calculated from City of Birth and Country of birth</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="cityOfBirth" type="xs:string"/>
                                    <xs:element
                                    minOccurs="0"
                                    name="countryOfBirth" type="xs:string"/>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="productOwner" type="xs:boolean"/>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="futureProductOwner"
                                    nillable="false" type="xs:boolean"/>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="consents">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="consent">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    name="consentPurpose" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Suffix of LIC code RB_CONSENTS_PURPOSE (because it consist of two = prefix and suffix which identifies
the concrete purpose) [CODELIST: ConsentPurpose]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="consentCategory" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Prefix of LIC code RB_CONSENTS_PURPOSE (because it consist of two = prefix and suffix which identifies
the concrete purpose) [CODELIST: ConsentCategory]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="status" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Status of the consent (approved, not approved,...) [CODELIST: ConsentStatus]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="sourceSystem" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Code of the source system, where the consent has been collected 
(branch office, internet banking, smartphone application,...). Usefull if we need to find out when and
where consent has been completed. [CODELIST: Component]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="ownership">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="owner">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="partyId">
                                    <xs:annotation>
                                    <xs:documentation>Unique identification of the party</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="registrationNumber">
                                    <xs:annotation>
                                    <xs:documentation>Number of registration</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="pseudoIC">
                                    <xs:annotation>
                                    <xs:documentation>Additional identifier of party.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="companyName">
                                    <xs:annotation>
                                    <xs:documentation>Name of company</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="country">
                                    <xs:annotation>
                                    <xs:documentation>Registration country</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="partyRelationType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Type of relation</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="percentOwned" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Percentage of ownership &gt;=0 AND &lt;= 100</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="partyRelationValue" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Value of the relation</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="residencyAddress">
                                    <xs:annotation>
                                    <xs:documentation>Addres of type RB_MAIL_TYPE="RESIDENCY"</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    name="type" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Mail Address type (Residency or Mail1-9)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="street" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A street name</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="number" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A building number/street number</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="part" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A part of the city</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="city" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A city name</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="postalCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A ZIP code</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="country" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Country of address</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="active" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>true/false - if address active flag in SBL filled</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="entityType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Type of entity (active/passive ...)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                            </xs:choice>
                            <xs:element maxOccurs="1" minOccurs="0" name="contacts">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="addresses">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="address">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="srcReferenceId">
                                    <xs:annotation>
                                    <xs:documentation>A unique ID of the address</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="addressType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Mail Address type (Residency or Mail1-9)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="preferred" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Preferred address Y/N</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="addressee" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A mail addressee</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="street" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A street name</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="number" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A building number/street number</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="part" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A part of the city</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="city" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A city name</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="postalCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A ZIP code</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="country" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>[CODELIST: GENERAL_CNTR]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="classification" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>[CODELIST: PRV_CONTCLASSIF]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="additionalInfo" type="xs:string"/>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="phones">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="phone">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="srcReferenceId">
                                    <xs:annotation>
                                    <xs:documentation>A unique ID of the contact</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="name">
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="contactType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Siebel technical field differentiating records in the same physical database table (LANDLINE_NUMBER, MOBILE_NUMBER, FAX_NUMBER, EMAIL_ADDRESS) [CODELIST: RB_COMM_MEDIUM_TYPE]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="preferred" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>A preferred contact</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="number">
                                    <xs:annotation>
                                    <xs:documentation>A contact number.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0" name="numberType">
                                    <xs:annotation>
                                    <xs:documentation>Contact number type (FAX1.., MOBILE1.., PHONE1..)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="classification" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A classification - home/work [CODELIST: PRV_CONTCLASSIF]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="comment">
                                    <xs:annotation>
                                    <xs:documentation>A contact comment</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="undeliverableFlag" type="xs:boolean"/>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="emails">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="email">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="srcReferenceId">
                                    <xs:annotation>
                                    <xs:documentation>A unique ID of the contact</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="name">
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="contactType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Siebel technical field differentiating records in the same physical database table (LANDLINE_NUMBER, MOBILE_NUMBER, FAX_NUMBER, EMAIL_ADDRESS) [CODELIST: RB_COMM_MEDIUM_TYPE]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="preferred" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>A preferred contact</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="address">
                                    <xs:annotation>
                                    <xs:documentation>A contact address (email, URL ..)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="100"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0" name="addressType">
                                    <xs:annotation>
                                    <xs:documentation>Email type (EMAIL1....)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="classification" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A classification - home/work [CODELIST: PRV_CONTCLASSIF]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="comment">
                                    <xs:annotation>
                                    <xs:documentation>A contact comment</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="undeliverableFlag" type="xs:boolean"/>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="urls">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="url">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="srcReferenceId">
                                    <xs:annotation>
                                    <xs:documentation>A unique ID of the contact</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="name">
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="preferred" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>A preferred contact</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="address">
                                    <xs:annotation>
                                    <xs:documentation>A contact address (email, URL ..)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="100"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="classification" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A classification - home/work [CODELIST: PRV_CONTCLASSIF]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="comment">
                                    <xs:annotation>
                                    <xs:documentation>A contact comment</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="categories" nillable="false">
                                <xs:complexType>
                                    <xs:sequence maxOccurs="1" minOccurs="0">
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1"
                                    name="category" nillable="false">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="type"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Type of category. Selectable from LOV. [CODELIST: UNI_PCATT]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="validity" nillable="false">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="validFrom"
                                    nillable="false" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Duration from date</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="validTo"
                                    nillable="false" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Duration to date</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="value" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>value of category - free text</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="100"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="categorization"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>value of category. Selectable from LOV [CODELIST: UNI_CTGZATION]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="comment" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>comment</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element minOccurs="0" name="cares">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="care">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="primaryCare" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>flag if the care is primary or not</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="careType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>[CODELIST: UNI_PCARET]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="firstName">
                                    <xs:annotation>
                                    <xs:documentation>Worker First name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="lastName">
                                    <xs:annotation>
                                    <xs:documentation>Worker Last name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="photoUrl">
                                    <xs:annotation>
                                    <xs:documentation>URL address to the employee´s photo file</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="2000"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="division">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="divisionName">
                                    <xs:annotation>
                                    <xs:documentation>Organization unit name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="100"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="divisionSOL">
                                    <xs:annotation>
                                    <xs:documentation>Organization unit SOL</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="parentDivisionId" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Parent Division Id - Siebel foreign key to record of Organization Unit entity which to which position belongs [CODELIST: Branch]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="position">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="positionId">
                                    <xs:annotation>
                                    <xs:documentation>Positon Id - Siebel ROW_ID</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="positionNumber">
                                    <xs:annotation>
                                    <xs:documentation>Position serial number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="positionType">
                                    <xs:annotation>
                                    <xs:documentation>Position type name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="parentPositionId">
                                    <xs:annotation>
                                    <xs:documentation>Parent Position Id - Siebel foreign key to Parent Position</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="userId">
                                    <xs:annotation>
                                    <xs:documentation>User ID of format "CZA…". Joined field from Worker.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="contacts">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="phones">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="phone">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="srcReferenceId">
                                    <xs:annotation>
                                    <xs:documentation>Unique identification of the entity in source system</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="contactName">
                                    <xs:annotation>
                                    <xs:documentation>User definable contact name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="90"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="contactType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Siebel technical field differentiating records in the same physical database table (LANDLINE_NUMBER, MOBILE_NUMBER, FAX_NUMBER, EMAIL_ADDRESS) [CODELIST: RB_COMM_MEDIUM_TYPE]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="number">
                                    <xs:annotation>
                                    <xs:documentation>A contact number.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberType">
                                    <xs:annotation>
                                    <xs:documentation>Contact number type (FAX1.., MOBILE1.., PHONE1..)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="address">
                                    <xs:annotation>
                                    <xs:documentation>A contact address (email, URL ..)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="100"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="addressType">
                                    <xs:annotation>
                                    <xs:documentation>Email address type (EMAIL1, EMAIL2,...)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="preferred" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>A preferred contact</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="undeliverableFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Undeliverable flag</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="classification" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A classification - home/work [CODELIST: PRV_CONTCLASSIF]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="comment">
                                    <xs:annotation>
                                    <xs:documentation>A contact comment</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="qualityScore" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>The Score defines a quality of the contact. Higher score means worse quality. The Score comes from Purity.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="qualityExplanationCode">
                                    <xs:annotation>
                                    <xs:documentation>Explanation codes defines a quality defects of the contact. Codes comes from Purity.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="contactOwner">
                                    <xs:annotation>
                                    <xs:documentation>PartyId of contact owner (use in case where the source of the contact is from related party, e.g. used for calculated contact for FOUR EYES CHECK) </xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="emails">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="email">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="srcReferenceId">
                                    <xs:annotation>
                                    <xs:documentation>Unique identification of the entity in source system</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="contactName">
                                    <xs:annotation>
                                    <xs:documentation>User definable contact name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="90"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="contactType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Siebel technical field differentiating records in the same physical database table (LANDLINE_NUMBER, MOBILE_NUMBER, FAX_NUMBER, EMAIL_ADDRESS) [CODELIST: RB_COMM_MEDIUM_TYPE]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="number">
                                    <xs:annotation>
                                    <xs:documentation>A contact number.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberType">
                                    <xs:annotation>
                                    <xs:documentation>Contact number type (FAX1.., MOBILE1.., PHONE1..)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="address">
                                    <xs:annotation>
                                    <xs:documentation>A contact address (email, URL ..)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="100"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="addressType">
                                    <xs:annotation>
                                    <xs:documentation>Email address type (EMAIL1, EMAIL2,...)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="preferred" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>A preferred contact</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="undeliverableFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Undeliverable flag</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="classification" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A classification - home/work [CODELIST: PRV_CONTCLASSIF]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="comment">
                                    <xs:annotation>
                                    <xs:documentation>A contact comment</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="qualityScore" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>The Score defines a quality of the contact. Higher score means worse quality. The Score comes from Purity.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="qualityExplanationCode">
                                    <xs:annotation>
                                    <xs:documentation>Explanation codes defines a quality defects of the contact. Codes comes from Purity.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="contactOwner">
                                    <xs:annotation>
                                    <xs:documentation>PartyId of contact owner (use in case where the source of the contact is from related party, e.g. used for calculated contact for FOUR EYES CHECK) </xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="displayInEBanking" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Displayed in Ebanking</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="comment">
                                    <xs:annotation>
                                    <xs:documentation>comment</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="documents">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="document">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="documentId" type="xs:string"/>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="documentType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>[CODELIST: UNI_PIDOCT]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="validity">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="validFrom" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Duration from date</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="validTo" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Duration to date</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="issuer" type="xs:string"/>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="issuerCountry" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>[CODELIST: GENERAL_CNTR]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="unacceptable" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Unacceptable flag</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="preferredDocument" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Preferred document</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="representatives" nillable="false">
                                <xs:complexType>
                                    <xs:sequence maxOccurs="1" minOccurs="0">
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1"
                                    name="representative" nillable="false">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    name="partyRelationType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>[CODELIST: UNI_RELATIONSHIP]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="partyId" nillable="false">
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="birthCode" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Person birth code</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="20"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="birthDate"
                                    nillable="false" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Person birth date</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="firstName" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>First name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="80"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="middleNames" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Middle names</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="80"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="lastName" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Surname</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="100"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="titleBefore" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Title before the first name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="titleBehind" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Title behind the surname</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="relationshipRole" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>[LOV: parent;child] [LOV: parent;child]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="parent"/>

                                    <xs:enumeration value="child"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="citizenship" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Citizenship</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="cityOfBirth">
                                    <xs:annotation>
                                    <xs:documentation>City of birth</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="countryOfBirth">
                                    <xs:annotation>
                                    <xs:documentation>Country of birth [CODELIST: GENERAL_CNTR]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="percentOwned">
                                    <xs:annotation>
                                    <xs:documentation>Percentage of ownership &gt;=0 AND &lt;= 100</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="15"/>

                                    <xs:fractionDigits value="7"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="taxResidency">
                                    <xs:annotation>
                                    <xs:documentation>Taxation residency [CODELIST: GENERAL_CNTR]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="taxIdNumber" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Taxation id number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="address" nillable="false">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="mailing" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Party mailing address</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>

                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="additionalAddressInfo" nillable="false">
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="street" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Name of the street</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="200"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="number" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Street number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="part" nillable="false">
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="city" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Name of the city</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="200"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="postalCode" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Postal code</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="country" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Name of the country</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="200"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="taxResidencies" nillable="false">
                                <xs:complexType>
                                    <xs:sequence maxOccurs="1" minOccurs="0">
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1"
                                    name="taxResidency" nillable="false">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    name="order" type="xs:integer"/>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="taxationResidency" nillable="false">
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="taxId" nillable="false">
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="meetingsReport">
                                <xs:complexType>
                                    <xs:sequence maxOccurs="1" minOccurs="0">
                                    <xs:element name="created" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Date of sending data.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="participantsCompany">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="0">
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="participantCompany">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="name" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Name of company.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="primaryFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Flag of primary company.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="participantsPerson">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="0">
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="participantPerson">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    name="firstName" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Name of participant person of corporate.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="lastName" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Last name of participant person of corporation.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="primaryFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Flag of primary participant person of company.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="bankParticipants">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="0">
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="bankParticipant">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    name="firstName" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Name of participant of bank.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="lastName" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Last name of participant of bank.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="primaryFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Flag of primary participant of bank.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="productGroups">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="0">
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="productGroup">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="productGroupName" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Name of group of products.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="products">
                                    <xs:complexType>

                                    <xs:sequence
                                    maxOccurs="1" minOccurs="0">

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="product">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="productCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Code of product.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="productName" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Name of product.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="opportunity" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Opportunity of product.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="volume" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Volume of product.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="currency" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Three words shortcut for currency according to iso4217 (CZK, EUR...)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="comment" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Comment for product group.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="summary" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Summary of meeting.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="activityDate" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Date and time activity.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="activityDescription" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Description of activity.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="createdBy" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Name of user.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="createdByLast" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Last name of user.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="employee" nillable="false">
                    <xs:complexType>
                        <xs:sequence maxOccurs="1" minOccurs="0">
                            <xs:element maxOccurs="1" minOccurs="1"
                                name="staffId" nillable="false">
                                <xs:annotation>
                                    <xs:documentation>Employee id of the person who has opened the investment ID</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="1"
                                name="firstName" nillable="false">
                                <xs:annotation>
                                    <xs:documentation>First name</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="80"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="1"
                                name="lastName" nillable="false">
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="100"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="jobDescription" nillable="false">
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="75"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="organizationUnit" nillable="false">
                                <xs:complexType>
                                    <xs:sequence maxOccurs="1" minOccurs="0">
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="name" nillable="false">
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="100"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="department">
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="residence" nillable="false">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="0">
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="street" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Name of the street</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="200"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="number" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Street number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="part" nillable="false">
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="city" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Name of the city</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="200"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="postalCode" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Postal code</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="country" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Name of the country</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="200"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="brand" nillable="false">
                    <xs:annotation>
                        <xs:documentation>[LOV: FWR;RB] [LOV: FWR;RB]</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:enumeration value="FWR"/>
                            <xs:enumeration value="RB"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element minOccurs="0" name="operationType">
                    <xs:annotation>
                        <xs:documentation>type of the operation - new/update [LOV: New;Update]</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:enumeration value="New"/>
                            <xs:enumeration value="Update"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="email" nillable="false">
                    <xs:annotation>
                        <xs:documentation>E-mail address of the report recipient</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:maxLength value="50"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="place" nillable="false">
                    <xs:annotation>
                        <xs:documentation>City of the branch where the report is generated</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:maxLength value="80"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0"
                    name="reportDate" nillable="false" type="xs:dateTime">
                    <xs:annotation>
                        <xs:documentation>Date on which this report is generated</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element minOccurs="0" name="deliveryChannel" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>channel for a delivery - EMAIL/FAX/POST/NONE [CODELIST: FavouredConfirmationDeliveryChannel]</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0"
                    name="reportLanguage" nillable="false" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>language of the report [CODELIST: LanguageDMS]</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0"
                    name="authorizationToken" nillable="false" type="xs:string"/>
                <xs:element maxOccurs="1" minOccurs="0"
                    name="externalDocumentId" nillable="false">
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:maxLength value="32"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element minOccurs="0" name="documentId" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>documentID generated by DMS</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0"
                    name="technicalProperties" nillable="false">
                    <xs:complexType>
                        <xs:choice maxOccurs="1" minOccurs="0">
                            <xs:element maxOccurs="1" minOccurs="1"
                                name="csv" nillable="false" type="xs:anyType"/>
                            <xs:element maxOccurs="1" minOccurs="1"
                                name="jpg" nillable="false">
                                <xs:complexType>
                                    <xs:sequence maxOccurs="1" minOccurs="1">
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="dpi" nillable="false">
                                    <xs:simpleType>
                                    <xs:restriction base="xs:integer">
                                    <xs:fractionDigits value="0"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:choice>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
