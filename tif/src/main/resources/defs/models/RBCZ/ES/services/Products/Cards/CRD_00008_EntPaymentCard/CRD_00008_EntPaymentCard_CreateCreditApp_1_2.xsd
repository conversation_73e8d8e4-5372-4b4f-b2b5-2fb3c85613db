<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2017 (http://www.altova.com) by <PERSON><PERSON> (Raiffeisenbank a.s.) -->
<xs:schema xmlns:opcreatecreditapp_1_2="http://rb.cz/services/entityService/paymentCard/CreateCreditApp_1_2" xmlns:xs="http://www.w3.org/2001/XMLSchema" targetNamespace="http://rb.cz/services/entityService/paymentCard/CreateCreditApp_1_2" elementFormDefault="qualified" attributeFormDefault="unqualified" version="1.0.0">
	<xs:element name="opCreateCreditApp_1_2">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="identification">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="srcReferenceId">
								<xs:annotation>
									<xs:documentation>Unique identification of the application from consumer point of view</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:string">
										<xs:maxLength value="30"/>
									</xs:restriction>
								</xs:simpleType>
							</xs:element>
							<xs:element name="trgReferenceId" minOccurs="0" maxOccurs="1">
								<xs:annotation>
									<xs:documentation>APS application Id (integration id) </xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:string">
										<xs:maxLength value="30"/>
									</xs:restriction>
								</xs:simpleType>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
				<xs:element name="applicationDate" type="xs:dateTime" minOccurs="0" maxOccurs="1">
					<xs:annotation>
						<xs:documentation>Date of application</xs:documentation>
					</xs:annotation>
				</xs:element>
				<xs:element name="sellerId" minOccurs="0" maxOccurs="1">
					<xs:annotation>
						<xs:documentation>ID of seller</xs:documentation>
					</xs:annotation>
					<xs:simpleType>
						<xs:restriction base="xs:string">
							<xs:maxLength value="10"/>
						</xs:restriction>
					</xs:simpleType>
				</xs:element>
				<xs:element name="personalData" minOccurs="0" maxOccurs="1">
					<xs:annotation>
						<xs:documentation>Personal data</xs:documentation>
					</xs:annotation>
					<xs:complexType>
						<xs:sequence>
							<xs:element name="party">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="partyId">
											<xs:annotation>
												<xs:documentation>Party Id (Siebel identifier)</xs:documentation>
											</xs:annotation>
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:maxLength value="15"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="isClient" type="xs:boolean"/>
										<xs:choice>
											<xs:element name="privateParty">
												<xs:complexType>
													<xs:sequence>
														<xs:element name="occupationType" type="xs:string" minOccurs="0">
															<xs:annotation>
																<xs:documentation>Type of an occupation [CODELIST: OccupationType]</xs:documentation>
															</xs:annotation>
														</xs:element>
														<xs:element name="maritalStatus" type="xs:string" minOccurs="1" maxOccurs="1">
															<xs:annotation>
																<xs:documentation>Marital status - married, single, widowed, divorced,...</xs:documentation>
															</xs:annotation>
														</xs:element>
													</xs:sequence>
												</xs:complexType>
											</xs:element>
										</xs:choice>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
							<xs:element name="IDPaper">
								<xs:annotation>
									<xs:documentation>An identifier of a personal document</xs:documentation>
								</xs:annotation>
								<xs:complexType>
									<xs:sequence>
										<xs:element name="documentType" type="xs:string">
											<xs:annotation>
												<xs:documentation>A type of an identity document [CODELIST: UNI_PIDOCT]</xs:documentation>
											</xs:annotation>
										</xs:element>
										<xs:element name="documentNo" type="xs:string">
											<xs:annotation>
												<xs:documentation>Identification document number</xs:documentation>
											</xs:annotation>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
							<xs:element name="residenceType" type="xs:string" minOccurs="0" maxOccurs="1">
								<xs:annotation>
									<xs:documentation>Type of residence</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="accomodationType" type="xs:string" maxOccurs="1">
								<xs:annotation>
									<xs:documentation>Type of accomodation</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="educationLevel" type="xs:string" maxOccurs="1">
								<xs:annotation>
									<xs:documentation>The highest level of an education</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="countOfChildren" type="xs:integer" maxOccurs="1">
								<xs:annotation>
									<xs:documentation>Count of children</xs:documentation>
								</xs:annotation>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
				<xs:element name="contacts" minOccurs="0">
					<xs:complexType>
						<xs:sequence>
							<xs:element name="contact" maxOccurs="unbounded">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="srcReferenceId">
											<xs:annotation>
												<xs:documentation>Siebel Id of a contact</xs:documentation>
											</xs:annotation>
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:maxLength value="15"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="contactType" type="xs:string">
											<xs:annotation>
												<xs:documentation>Siebel technical field differentiating records in the same physical database table (LANDLINE_NUMBER, MOBILE_NUMBER, FAX_NUMBER, EMAIL_ADDRESS) [CODELIST: RB_COMM_MEDIUM_TYPE]</xs:documentation>
											</xs:annotation>
										</xs:element>
										<xs:element name="addressType" type="xs:string">
											<xs:annotation>
												<xs:documentation>If this contact is for a residency address or for a contact address. [CODELIST: GENERAL_MAILTYPE]</xs:documentation>
											</xs:annotation>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
							<xs:element name="address">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="srcReferenceId">
											<xs:annotation>
												<xs:documentation>Siebel Id of an address</xs:documentation>
											</xs:annotation>
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:maxLength value="15"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="addressType" type="xs:string">
											<xs:annotation>
												<xs:documentation>If this address is a residency address or a contact address. [CODELIST: GENERAL_MAILTYPE]</xs:documentation>
											</xs:annotation>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
							<xs:element name="residencyDate">
								<xs:annotation>
									<xs:documentation>Since when a client stayes at the residency address.</xs:documentation>
								</xs:annotation>
								<xs:complexType>
									<xs:sequence>
										<xs:element name="from" type="xs:date">
											<xs:annotation>
												<xs:documentation>Residence from date</xs:documentation>
											</xs:annotation>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
				<xs:element name="employmentInfo" minOccurs="0" maxOccurs="1">
					<xs:annotation>
						<xs:documentation>Informations about a employment</xs:documentation>
					</xs:annotation>
					<xs:complexType>
						<xs:sequence>
							<xs:element name="businessField" type="xs:string" minOccurs="0" maxOccurs="1">
								<xs:annotation>
									<xs:documentation>Business field</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="position" type="xs:string" minOccurs="0" maxOccurs="1">
								<xs:annotation>
									<xs:documentation>Position</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="civilServant" type="xs:boolean" minOccurs="0" maxOccurs="1">
								<xs:annotation>
									<xs:documentation>Civil servant</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="employerIco" minOccurs="0" maxOccurs="1">
								<xs:annotation>
									<xs:documentation>Employer commercial registration number</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:string">
										<xs:maxLength value="8"/>
									</xs:restriction>
								</xs:simpleType>
							</xs:element>
							<xs:element name="employerName" minOccurs="0" maxOccurs="1">
								<xs:annotation>
									<xs:documentation>Name of employer</xs:documentation>
								</xs:annotation>
								<xs:simpleType>
									<xs:restriction base="xs:string">
										<xs:maxLength value="256"/>
									</xs:restriction>
								</xs:simpleType>
							</xs:element>
							<xs:element name="contacts" minOccurs="0">
								<xs:annotation>
									<xs:documentation>A list of contacts to work</xs:documentation>
								</xs:annotation>
								<xs:complexType>
									<xs:sequence>
										<xs:element name="contact" maxOccurs="unbounded">
											<xs:complexType>
												<xs:sequence>
													<xs:element name="contactType" type="xs:string" minOccurs="0" maxOccurs="1">
														<xs:annotation>
															<xs:documentation>Siebel technical field differentiating records in the same physical database table (LANDLINE_NUMBER, MOBILE_NUMBER, FAX_NUMBER, EMAIL_ADDRESS) [CODELIST: RB_COMM_MEDIUM_TYPE]</xs:documentation>
														</xs:annotation>
													</xs:element>
													<xs:element name="number" minOccurs="0" maxOccurs="1">
														<xs:annotation>
															<xs:documentation>A contact number.</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:string">
																<xs:maxLength value="15"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
							<xs:element name="dates" minOccurs="0">
								<xs:annotation>
									<xs:documentation>Dates when jobs have been started</xs:documentation>
								</xs:annotation>
								<xs:complexType>
									<xs:sequence>
										<xs:element name="currentJob" minOccurs="0">
											<xs:complexType>
												<xs:sequence>
													<xs:element name="from" type="xs:date">
														<xs:annotation>
															<xs:documentation>Current job from date</xs:documentation>
														</xs:annotation>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
										<xs:element name="lastJob" minOccurs="0">
											<xs:complexType>
												<xs:sequence>
													<xs:element name="from" type="xs:date">
														<xs:annotation>
															<xs:documentation>Last job from date</xs:documentation>
														</xs:annotation>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
							<xs:element name="drawingAccountInCR" type="xs:boolean" minOccurs="1" maxOccurs="1">
								<xs:annotation>
									<xs:documentation>Drawing account in CR</xs:documentation>
								</xs:annotation>
							</xs:element>
							<xs:element name="netIncome" type="xs:integer" minOccurs="1" maxOccurs="1">
								<xs:annotation>
									<xs:documentation>Net income</xs:documentation>
								</xs:annotation>
							</xs:element>
						</xs:sequence>
					</xs:complexType>
				</xs:element>
				<xs:element name="productDetails">
					<xs:complexType>
						<xs:choice>
							<xs:element name="cardApplication">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="cardDetails" minOccurs="1" maxOccurs="1">
											<xs:annotation>
												<xs:documentation>Informations about a product</xs:documentation>
											</xs:annotation>
											<xs:complexType>
												<xs:sequence>
													<xs:element name="productType" type="xs:string">
														<xs:annotation>
															<xs:documentation>Type of product [CODELIST: CreditCardTypeCode]</xs:documentation>
														</xs:annotation>
													</xs:element>
													<xs:element name="cardDesign" type="xs:string">
														<xs:annotation>
															<xs:documentation>Client selected design of CC [CODELIST: CardDesignType]</xs:documentation>
														</xs:annotation>
													</xs:element>
													<xs:element name="loanAmount" type="xs:integer" minOccurs="1" maxOccurs="1">
														<xs:annotation>
															<xs:documentation>Requierd loan amount (CZK)(L)
Requierd loan limit (CZK)(CC)
Total loan limit (CZK)(OD)</xs:documentation>
														</xs:annotation>
													</xs:element>
													<xs:element name="specialAction" minOccurs="0" maxOccurs="1">
														<xs:annotation>
															<xs:documentation>Special action</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:string">
																<xs:maxLength value="16"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
													<xs:element name="closingPeriod" type="xs:string">
														<xs:annotation>
															<xs:documentation>Closing cycle - when a statement is created. [CODELIST: CreditCardClosingCycle]</xs:documentation>
														</xs:annotation>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
										<xs:element name="financialDetails" minOccurs="1" maxOccurs="1">
											<xs:annotation>
												<xs:documentation>Financial data</xs:documentation>
											</xs:annotation>
											<xs:complexType>
												<xs:sequence>
													<xs:element name="creditCardLimits" type="xs:integer" minOccurs="1" maxOccurs="1">
														<xs:annotation>
															<xs:documentation>Credit card limits</xs:documentation>
														</xs:annotation>
													</xs:element>
													<xs:element name="otherInstalments" type="xs:integer" minOccurs="1" maxOccurs="1">
														<xs:annotation>
															<xs:documentation>Other instalments</xs:documentation>
														</xs:annotation>
													</xs:element>
													<xs:element name="otherLeasings" type="xs:integer" minOccurs="1" maxOccurs="1">
														<xs:annotation>
															<xs:documentation>Other leasings</xs:documentation>
														</xs:annotation>
													</xs:element>
													<xs:element name="overdraftLimits" type="xs:integer" minOccurs="1" maxOccurs="1">
														<xs:annotation>
															<xs:documentation>Overdraft limits</xs:documentation>
														</xs:annotation>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
										<xs:element name="additionalServices" minOccurs="0">
											<xs:annotation>
												<xs:documentation>Additional services</xs:documentation>
											</xs:annotation>
											<xs:complexType>
												<xs:sequence>
													<xs:element name="insurances" minOccurs="0">
														<xs:annotation>
															<xs:documentation>A list of insurances</xs:documentation>
														</xs:annotation>
														<xs:complexType>
															<xs:sequence>
																<xs:element name="insurance" type="xs:string" maxOccurs="unbounded">
																	<xs:annotation>
																		<xs:documentation>Insurance of a credit card [CODELIST: CreditCardInsurance]</xs:documentation>
																	</xs:annotation>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
													<xs:element name="creditInfo" minOccurs="0">
														<xs:annotation>
															<xs:documentation>Details about credit info service</xs:documentation>
														</xs:annotation>
														<xs:complexType>
															<xs:sequence>
																<xs:element name="creditInfoType" type="xs:string">
																	<xs:annotation>
																		<xs:documentation>A type of Kredit Info service [CODELIST: CreditInfoType]</xs:documentation>
																	</xs:annotation>
																</xs:element>
																<xs:element name="number" minOccurs="0">
																	<xs:annotation>
																		<xs:documentation>A phone number where an SMS is sent</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="15"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
										<xs:element name="deliveryInfo">
											<xs:annotation>
												<xs:documentation>Information where cards will be delivered</xs:documentation>
											</xs:annotation>
											<xs:complexType>
												<xs:sequence>
													<xs:element name="newCard">
														<xs:annotation>
															<xs:documentation>Where a new card will be sent</xs:documentation>
														</xs:annotation>
														<xs:complexType>
															<xs:sequence>
																<xs:element name="deliveryType" type="xs:string">
																	<xs:annotation>
																		<xs:documentation>Type of a delivery - a client address or a branch - A, B [CODELIST: DistributionChannel]</xs:documentation>
																	</xs:annotation>
																</xs:element>
																<xs:element name="branchCode" type="xs:string" minOccurs="0">
																	<xs:annotation>
																		<xs:documentation>Identification of a branch [CODELIST: Branch]</xs:documentation>
																	</xs:annotation>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
													<xs:element name="nextCard">
														<xs:annotation>
															<xs:documentation>Where a next card will be sent</xs:documentation>
														</xs:annotation>
														<xs:complexType>
															<xs:sequence>
																<xs:element name="deliveryType" type="xs:string">
																	<xs:annotation>
																		<xs:documentation>Type of a delivery - a client address or a branch - A, B [CODELIST: DistributionChannel]</xs:documentation>
																	</xs:annotation>
																</xs:element>
																<xs:element name="branchCode" type="xs:string" minOccurs="0">
																	<xs:annotation>
																		<xs:documentation>Identification of a branch [CODELIST: Branch]</xs:documentation>
																	</xs:annotation>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
										<xs:element name="repaymentInfo">
											<xs:complexType>
												<xs:sequence>
													<xs:element name="typeRepayment" type="xs:string">
														<xs:annotation>
															<xs:documentation>Type of repayment [CODELIST: TransactionType]</xs:documentation>
														</xs:annotation>
													</xs:element>
													<xs:element name="instalment" minOccurs="0">
														<xs:complexType>
															<xs:sequence>
																<xs:element name="accountNumber">
																	<xs:annotation>
																		<xs:documentation>A bank account number where a collection order is set</xs:documentation>
																	</xs:annotation>
																	<xs:complexType>
																		<xs:sequence>
																			<xs:element name="numberPart1" minOccurs="0" maxOccurs="1">
																				<xs:annotation>
																					<xs:documentation>Prefix part of the bank account number</xs:documentation>
																				</xs:annotation>
																				<xs:simpleType>
																					<xs:restriction base="xs:string">
																						<xs:maxLength value="6"/>
																					</xs:restriction>
																				</xs:simpleType>
																			</xs:element>
																			<xs:element name="numberPart2">
																				<xs:annotation>
																					<xs:documentation>Base part of the bank account number</xs:documentation>
																				</xs:annotation>
																				<xs:simpleType>
																					<xs:restriction base="xs:string">
																						<xs:maxLength value="10"/>
																					</xs:restriction>
																				</xs:simpleType>
																			</xs:element>
																			<xs:element name="bankCode" type="xs:string" minOccurs="0">
																				<xs:annotation>
																					<xs:documentation>Part of the bank account number which identifies the bank [CODELIST: BankNumericCode]</xs:documentation>
																				</xs:annotation>
																			</xs:element>
																		</xs:sequence>
																	</xs:complexType>
																</xs:element>
																<xs:element name="symbols" minOccurs="0">
																	<xs:complexType>
																		<xs:sequence>
																			<xs:element name="variable" minOccurs="0">
																				<xs:annotation>
																					<xs:documentation>Variable symbol</xs:documentation>
																				</xs:annotation>
																				<xs:simpleType>
																					<xs:restriction base="xs:string">
																						<xs:maxLength value="10"/>
																					</xs:restriction>
																				</xs:simpleType>
																			</xs:element>
																			<xs:element name="constant" minOccurs="0">
																				<xs:annotation>
																					<xs:documentation>Constant symbol</xs:documentation>
																				</xs:annotation>
																				<xs:simpleType>
																					<xs:restriction base="xs:string">
																						<xs:maxLength value="10"/>
																					</xs:restriction>
																				</xs:simpleType>
																			</xs:element>
																			<xs:element name="specific" minOccurs="0">
																				<xs:annotation>
																					<xs:documentation>Specific symbol</xs:documentation>
																				</xs:annotation>
																				<xs:simpleType>
																					<xs:restriction base="xs:string">
																						<xs:maxLength value="10"/>
																					</xs:restriction>
																				</xs:simpleType>
																			</xs:element>
																		</xs:sequence>
																	</xs:complexType>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
													<xs:element name="encashmentSize" type="xs:string" minOccurs="0" maxOccurs="1">
														<xs:annotation>
															<xs:documentation>encashmentSize</xs:documentation>
														</xs:annotation>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
										<xs:element name="statementInfo">
											<xs:complexType>
												<xs:sequence>
													<xs:element name="statementForm" type="xs:string">
														<xs:annotation>
															<xs:documentation>Paper, generated form IB, e-mail, none</xs:documentation>
														</xs:annotation>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
										<xs:element name="flags" minOccurs="0" maxOccurs="1">
											<xs:annotation>
												<xs:documentation>Data of WL application from EB - step DS3.</xs:documentation>
											</xs:annotation>
											<xs:complexType>
												<xs:sequence>
													<xs:element name="nrkiConsent" type="xs:boolean" maxOccurs="1">
														<xs:annotation>
															<xs:documentation>Agreement with NRKI</xs:documentation>
														</xs:annotation>
													</xs:element>
													<xs:element name="solusConsent" type="xs:boolean" maxOccurs="1">
														<xs:annotation>
															<xs:documentation>Agreement with SOLUS</xs:documentation>
														</xs:annotation>
													</xs:element>
													<xs:element name="preContractConsent" type="xs:boolean" maxOccurs="1">
														<xs:annotation>
															<xs:documentation>Agreement with precontract information</xs:documentation>
														</xs:annotation>
													</xs:element>
													<xs:element name="signedContract" type="xs:boolean" maxOccurs="1">
														<xs:annotation>
															<xs:documentation>Signed contract</xs:documentation>
														</xs:annotation>
													</xs:element>
													<xs:element name="preContractInfo" type="xs:boolean">
														<xs:annotation>
															<xs:documentation>Signed pre contract info</xs:documentation>
														</xs:annotation>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
										<xs:element name="otherData" minOccurs="1" maxOccurs="1">
											<xs:annotation>
												<xs:documentation>Other data</xs:documentation>
											</xs:annotation>
											<xs:complexType>
												<xs:sequence>
													<xs:element name="sellerChannel" type="xs:string" minOccurs="1" maxOccurs="1">
														<xs:annotation>
															<xs:documentation>Business channel</xs:documentation>
														</xs:annotation>
													</xs:element>
													<xs:element name="password" minOccurs="1" maxOccurs="1">
														<xs:annotation>
															<xs:documentation>Comunication password</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:string">
																<xs:maxLength value="32"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
										<xs:element name="offerDetails" minOccurs="1" maxOccurs="1">
											<xs:annotation>
												<xs:documentation>Information about an offer</xs:documentation>
											</xs:annotation>
											<xs:complexType>
												<xs:sequence>
													<xs:element name="offerId" minOccurs="0">
														<xs:annotation>
															<xs:documentation>CMT (Campaign management tool) individual offer ID</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:string">
																<xs:maxLength value="512"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
													<xs:element name="offerParams">
														<xs:annotation>
															<xs:documentation>A set of offer parameters</xs:documentation>
														</xs:annotation>
														<xs:complexType>
															<xs:sequence>
																<xs:element name="cardType">
																	<xs:annotation>
																		<xs:documentation>Attributes of credit card</xs:documentation>
																	</xs:annotation>
																	<xs:complexType>
																		<xs:sequence>
																			<xs:element name="cardProductType" type="xs:string">
																				<xs:annotation>
																					<xs:documentation>A product type means if a card is Maestro, Visa, Billa, etc. [CODELIST: CreditCardTypeCode]</xs:documentation>
																				</xs:annotation>
																			</xs:element>
																		</xs:sequence>
																	</xs:complexType>
																</xs:element>
																<xs:element name="rpsn">
																	<xs:annotation>
																		<xs:documentation>Roční procentní sazba nákladů. An equivalent for APR (Annual Percentage Rate) in Czech republic</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:decimal">
																			<xs:totalDigits value="8"/>
																			<xs:fractionDigits value="3"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="totalAmount">
																	<xs:annotation>
																		<xs:documentation>A sum of fees, instalments, interests which a client will pay together.</xs:documentation>
																	</xs:annotation>
																	<xs:complexType>
																		<xs:sequence>
																			<xs:element name="amount">
																				<xs:annotation>
																					<xs:documentation>Amount</xs:documentation>
																				</xs:annotation>
																				<xs:simpleType>
																					<xs:restriction base="xs:decimal">
																						<xs:totalDigits value="18"/>
																						<xs:fractionDigits value="5"/>
																					</xs:restriction>
																				</xs:simpleType>
																			</xs:element>
																			<xs:element name="currencyCode" type="xs:string" minOccurs="0">
																				<xs:annotation>
																					<xs:documentation>Currency of amount [CODELIST: Currency]</xs:documentation>
																				</xs:annotation>
																			</xs:element>
																		</xs:sequence>
																	</xs:complexType>
																</xs:element>
																<xs:element name="totalLimit">
																	<xs:annotation>
																		<xs:documentation>A total limit of a loan</xs:documentation>
																	</xs:annotation>
																	<xs:complexType>
																		<xs:sequence>
																			<xs:element name="amount">
																				<xs:annotation>
																					<xs:documentation>Amount</xs:documentation>
																				</xs:annotation>
																				<xs:simpleType>
																					<xs:restriction base="xs:decimal">
																						<xs:totalDigits value="18"/>
																						<xs:fractionDigits value="5"/>
																					</xs:restriction>
																				</xs:simpleType>
																			</xs:element>
																			<xs:element name="currencyCode" type="xs:string" minOccurs="0">
																				<xs:annotation>
																					<xs:documentation>Currency of amount [CODELIST: Currency]</xs:documentation>
																				</xs:annotation>
																			</xs:element>
																		</xs:sequence>
																	</xs:complexType>
																</xs:element>
																<xs:element name="interestRate">
																	<xs:annotation>
																		<xs:documentation>An interest rate - in %</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:decimal">
																			<xs:totalDigits value="10"/>
																			<xs:fractionDigits value="4"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="feeList">
																	<xs:annotation>
																		<xs:documentation>A list of fees</xs:documentation>
																	</xs:annotation>
																	<xs:complexType>
																		<xs:sequence>
																			<xs:element name="fee" maxOccurs="unbounded">
																				<xs:complexType>
																					<xs:sequence>
																						<xs:element name="type" type="xs:string">
																							<xs:annotation>
																								<xs:documentation>A type of a fee [CODELIST: LoanFee]</xs:documentation>
																							</xs:annotation>
																						</xs:element>
																						<xs:element name="amount">
																							<xs:annotation>
																								<xs:documentation>An amount of a fee</xs:documentation>
																							</xs:annotation>
																							<xs:simpleType>
																								<xs:restriction base="xs:decimal">
																									<xs:totalDigits value="18"/>
																									<xs:fractionDigits value="5"/>
																								</xs:restriction>
																							</xs:simpleType>
																						</xs:element>
																						<xs:element name="currencyCode" type="xs:string" minOccurs="0">
																							<xs:annotation>
																								<xs:documentation>A currency of a fee [CODELIST: Currency]</xs:documentation>
																							</xs:annotation>
																						</xs:element>
																					</xs:sequence>
																				</xs:complexType>
																			</xs:element>
																		</xs:sequence>
																	</xs:complexType>
																</xs:element>
																<xs:element name="sumOfFees">
																	<xs:annotation>
																		<xs:documentation>A sum of fees which a client will pay</xs:documentation>
																	</xs:annotation>
																	<xs:complexType>
																		<xs:sequence>
																			<xs:element name="amount">
																				<xs:annotation>
																					<xs:documentation>Amount</xs:documentation>
																				</xs:annotation>
																				<xs:simpleType>
																					<xs:restriction base="xs:decimal">
																						<xs:totalDigits value="18"/>
																						<xs:fractionDigits value="5"/>
																					</xs:restriction>
																				</xs:simpleType>
																			</xs:element>
																			<xs:element name="currencyCode" type="xs:string" minOccurs="0">
																				<xs:annotation>
																					<xs:documentation>Currency of amount [CODELIST: Currency]</xs:documentation>
																				</xs:annotation>
																			</xs:element>
																		</xs:sequence>
																	</xs:complexType>
																</xs:element>
																<xs:element name="regularInstalmentAmount">
																	<xs:annotation>
																		<xs:documentation>9% instalment (9% from the total limit)</xs:documentation>
																	</xs:annotation>
																	<xs:complexType>
																		<xs:sequence>
																			<xs:element name="amount">
																				<xs:annotation>
																					<xs:documentation>Amount</xs:documentation>
																				</xs:annotation>
																				<xs:simpleType>
																					<xs:restriction base="xs:decimal">
																						<xs:totalDigits value="18"/>
																						<xs:fractionDigits value="5"/>
																					</xs:restriction>
																				</xs:simpleType>
																			</xs:element>
																			<xs:element name="currencyCode" type="xs:string" minOccurs="0">
																				<xs:annotation>
																					<xs:documentation>Currency of amount [CODELIST: Currency]</xs:documentation>
																				</xs:annotation>
																			</xs:element>
																		</xs:sequence>
																	</xs:complexType>
																</xs:element>
																<xs:element name="instalmentCount" type="xs:integer">
																	<xs:annotation>
																		<xs:documentation>A count of instalments per year</xs:documentation>
																	</xs:annotation>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
							<xs:element name="fundTransfer">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="cardIdentifier" maxOccurs="1">
											<xs:complexType>
												<xs:sequence>
													<xs:element name="identificationCode">
														<xs:annotation>
															<xs:documentation>A value of the identifier (type of the identifier is specified in the identificationType field)</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:string">
																<xs:maxLength value="64"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
													<xs:element name="identificationType">
														<xs:annotation>
															<xs:documentation>Type of the code - core identifier or card hash [LOV: cardHash]</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:string">
																<xs:enumeration value="cardHash"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
										<xs:element name="mprodCode" type="xs:string">
											<xs:annotation>
												<xs:documentation>MPROD code from product catalogue</xs:documentation>
											</xs:annotation>
										</xs:element>
										<xs:element name="salesPlan" type="xs:integer">
											<xs:annotation>
												<xs:documentation>Identifier of a sales plan</xs:documentation>
											</xs:annotation>
										</xs:element>
										<xs:element name="internalSalesPlan" type="xs:integer">
											<xs:annotation>
												<xs:documentation>Internal identifier of a sales plan</xs:documentation>
											</xs:annotation>
										</xs:element>
										<xs:element name="offerId">
											<xs:annotation>
												<xs:documentation>Unique identification of an offer</xs:documentation>
											</xs:annotation>
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:maxLength value="512"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="opportunityId">
											<xs:annotation>
												<xs:documentation>Unique identification of an opportunity</xs:documentation>
											</xs:annotation>
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:maxLength value="15"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="maxLimitAmount">
											<xs:annotation>
												<xs:documentation>CRM max limit amount</xs:documentation>
											</xs:annotation>
											<xs:simpleType>
												<xs:restriction base="xs:decimal">
													<xs:totalDigits value="18"/>
													<xs:fractionDigits value="5"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="amountToTransfer">
											<xs:simpleType>
												<xs:restriction base="xs:decimal">
													<xs:totalDigits value="18"/>
													<xs:fractionDigits value="5"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="currencyCode" type="xs:string">
											<xs:annotation>
												<xs:documentation>Currency of amount [CODELIST: Currency]</xs:documentation>
											</xs:annotation>
										</xs:element>
										<xs:element name="beneficier">
											<xs:complexType>
												<xs:sequence>
													<xs:element name="accountNumber">
														<xs:complexType>
															<xs:sequence>
																<xs:element name="numberPart1" minOccurs="0" maxOccurs="1">
																	<xs:annotation>
																		<xs:documentation>Prefix part of the bank account number</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="6"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="numberPart2">
																	<xs:annotation>
																		<xs:documentation>Base part of the bank account number</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="10"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="bankCode" type="xs:string">
																	<xs:annotation>
																		<xs:documentation>Part of the bank account number which identifies the bank [CODELIST: BANK_CODES]</xs:documentation>
																	</xs:annotation>
																</xs:element>
																<xs:element name="currencyCode" type="xs:string">
																	<xs:annotation>
																		<xs:documentation>Currency of the bank account [CODELIST: Currency]</xs:documentation>
																	</xs:annotation>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
													<xs:element name="variableSymbol" minOccurs="0">
														<xs:annotation>
															<xs:documentation>Variable symbol</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:string">
																<xs:maxLength value="10"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
													<xs:element name="message" minOccurs="0">
														<xs:annotation>
															<xs:documentation>Delivered information (Specifies details of the individual transactions which are to be transmitted to the beneficiary customer - typically Field :20 of MT101 Swift)</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:string">
																<xs:maxLength value="140"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
							<xs:element name="instalmentProgram">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="salesPlan" type="xs:integer">
											<xs:annotation>
												<xs:documentation>Identifier of a sales plan</xs:documentation>
											</xs:annotation>
										</xs:element>
										<xs:element name="internalSalesPlan" type="xs:integer">
											<xs:annotation>
												<xs:documentation>Internal identifier of a sales plan</xs:documentation>
											</xs:annotation>
										</xs:element>
										<xs:element name="primaryId">
											<xs:annotation>
												<xs:documentation>A transaction identifier from RPC</xs:documentation>
											</xs:annotation>
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:maxLength value="18"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="offered">
											<xs:complexType>
												<xs:sequence>
													<xs:element name="totalAmount">
														<xs:annotation>
															<xs:documentation>A sum of fees, instalments, interests which a client will pay together.</xs:documentation>
														</xs:annotation>
														<xs:complexType>
															<xs:sequence>
																<xs:element name="amount">
																	<xs:annotation>
																		<xs:documentation>Amount</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:decimal">
																			<xs:totalDigits value="18"/>
																			<xs:fractionDigits value="5"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="currencyCode" type="xs:string">
																	<xs:annotation>
																		<xs:documentation>Currency of amount [CODELIST: Currency]</xs:documentation>
																	</xs:annotation>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
													<xs:element name="firstInstalment">
														<xs:complexType>
															<xs:sequence>
																<xs:element name="instalmentAmount">
																	<xs:annotation>
																		<xs:documentation>First instalment amount</xs:documentation>
																	</xs:annotation>
																	<xs:complexType>
																		<xs:sequence>
																			<xs:element name="amount">
																				<xs:annotation>
																					<xs:documentation>Amount</xs:documentation>
																				</xs:annotation>
																				<xs:simpleType>
																					<xs:restriction base="xs:decimal">
																						<xs:totalDigits value="18"/>
																						<xs:fractionDigits value="5"/>
																					</xs:restriction>
																				</xs:simpleType>
																			</xs:element>
																			<xs:element name="currencyCode" type="xs:string">
																				<xs:annotation>
																					<xs:documentation>Currency of amount [CODELIST: Currency]</xs:documentation>
																				</xs:annotation>
																			</xs:element>
																		</xs:sequence>
																	</xs:complexType>
																</xs:element>
																<xs:element name="date" type="xs:date">
																	<xs:annotation>
																		<xs:documentation>First instalment date</xs:documentation>
																	</xs:annotation>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
													<xs:element name="regularInstalmentAmount">
														<xs:annotation>
															<xs:documentation>Regular instalment amount</xs:documentation>
														</xs:annotation>
														<xs:complexType>
															<xs:sequence>
																<xs:element name="amount">
																	<xs:annotation>
																		<xs:documentation>Amount</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:decimal">
																			<xs:totalDigits value="18"/>
																			<xs:fractionDigits value="5"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="currencyCode" type="xs:string">
																	<xs:annotation>
																		<xs:documentation>Currency of amount [CODELIST: Currency]</xs:documentation>
																	</xs:annotation>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
													<xs:element name="lastInstalmentAmount">
														<xs:annotation>
															<xs:documentation>Last instalment amount</xs:documentation>
														</xs:annotation>
														<xs:complexType>
															<xs:sequence>
																<xs:element name="amount" maxOccurs="1">
																	<xs:annotation>
																		<xs:documentation>Amount</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:decimal">
																			<xs:totalDigits value="18"/>
																			<xs:fractionDigits value="5"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="currencyCode" type="xs:string">
																	<xs:annotation>
																		<xs:documentation>Currency of amount [CODELIST: Currency]</xs:documentation>
																	</xs:annotation>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
													<xs:element name="instalmentCount" type="xs:integer">
														<xs:annotation>
															<xs:documentation>A count of instalments</xs:documentation>
														</xs:annotation>
													</xs:element>
													<xs:element name="interestRate">
														<xs:annotation>
															<xs:documentation>Interest rate</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:decimal">
																<xs:totalDigits value="10"/>
																<xs:fractionDigits value="4"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
													<xs:element name="rpsn">
														<xs:annotation>
															<xs:documentation>Roční procentní sazba nákladů. An equivalent for APR (Annual Percentage Rate) in Czech republic</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:decimal">
																<xs:totalDigits value="8"/>
																<xs:fractionDigits value="3"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
							<xs:element name="loanOnPhone">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="cardIdentifier" maxOccurs="1">
											<xs:complexType>
												<xs:sequence>
													<xs:element name="identificationCode">
														<xs:annotation>
															<xs:documentation>A value of the identifier (type of the identifier is specified in the identificationType field)</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:string">
																<xs:maxLength value="64"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
													<xs:element name="identificationType">
														<xs:annotation>
															<xs:documentation>Type of the code - core identifier or card hash [LOV: cardHash]</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:string">
																<xs:enumeration value="cardHash"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
										<xs:element name="mprodCode" type="xs:string">
											<xs:annotation>
												<xs:documentation>MPROD code from product catalogue</xs:documentation>
											</xs:annotation>
										</xs:element>
										<xs:element name="salesPlan" type="xs:integer">
											<xs:annotation>
												<xs:documentation>Identifier of a sales plan</xs:documentation>
											</xs:annotation>
										</xs:element>
										<xs:element name="internalSalesPlan" type="xs:integer">
											<xs:annotation>
												<xs:documentation>Internal identifier of a sales plan</xs:documentation>
											</xs:annotation>
										</xs:element>
										<xs:element name="offerId">
											<xs:annotation>
												<xs:documentation>Unique identification of an offer</xs:documentation>
											</xs:annotation>
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:maxLength value="512"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="opportunityId">
											<xs:annotation>
												<xs:documentation>Unique identification of an opportunity</xs:documentation>
											</xs:annotation>
											<xs:simpleType>
												<xs:restriction base="xs:string">
													<xs:maxLength value="15"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="maxLimitAmount">
											<xs:annotation>
												<xs:documentation>CRM max limit amount</xs:documentation>
											</xs:annotation>
											<xs:simpleType>
												<xs:restriction base="xs:decimal">
													<xs:totalDigits value="18"/>
													<xs:fractionDigits value="5"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="amountToTransfer">
											<xs:simpleType>
												<xs:restriction base="xs:decimal">
													<xs:totalDigits value="18"/>
													<xs:fractionDigits value="5"/>
												</xs:restriction>
											</xs:simpleType>
										</xs:element>
										<xs:element name="currencyCode" type="xs:string">
											<xs:annotation>
												<xs:documentation>Currency of amount [CODELIST: Currency]</xs:documentation>
											</xs:annotation>
										</xs:element>
										<xs:element name="beneficier">
											<xs:complexType>
												<xs:sequence>
													<xs:element name="accountNumber">
														<xs:complexType>
															<xs:sequence>
																<xs:element name="numberPart1" minOccurs="0" maxOccurs="1">
																	<xs:annotation>
																		<xs:documentation>Prefix part of the bank account number</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="6"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="numberPart2">
																	<xs:annotation>
																		<xs:documentation>Base part of the bank account number</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:string">
																			<xs:maxLength value="10"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="bankCode" type="xs:string">
																	<xs:annotation>
																		<xs:documentation>Part of the bank account number which identifies the bank [CODELIST: BANK_CODES]</xs:documentation>
																	</xs:annotation>
																</xs:element>
																<xs:element name="currencyCode" type="xs:string">
																	<xs:annotation>
																		<xs:documentation>Currency of the bank account [CODELIST: Currency]</xs:documentation>
																	</xs:annotation>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
													<xs:element name="variableSymbol" minOccurs="0">
														<xs:annotation>
															<xs:documentation>Variable symbol</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:string">
																<xs:maxLength value="10"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
													<xs:element name="message" minOccurs="0">
														<xs:annotation>
															<xs:documentation>Delivered information (Specifies details of the individual transactions which are to be transmitted to the beneficiary customer - typically Field :20 of MT101 Swift)</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:string">
																<xs:maxLength value="140"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
										<xs:element name="offered">
											<xs:complexType>
												<xs:sequence>
													<xs:element name="totalAmount">
														<xs:annotation>
															<xs:documentation>A sum of fees, instalments, interests which a client will pay together.</xs:documentation>
														</xs:annotation>
														<xs:complexType>
															<xs:sequence>
																<xs:element name="amount">
																	<xs:annotation>
																		<xs:documentation>Amount</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:decimal">
																			<xs:totalDigits value="18"/>
																			<xs:fractionDigits value="5"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="currencyCode" type="xs:string">
																	<xs:annotation>
																		<xs:documentation>Currency of amount [CODELIST: Currency]</xs:documentation>
																	</xs:annotation>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
													<xs:element name="firstInstalment">
														<xs:complexType>
															<xs:sequence>
																<xs:element name="instalmentAmount">
																	<xs:annotation>
																		<xs:documentation>First instalment amount</xs:documentation>
																	</xs:annotation>
																	<xs:complexType>
																		<xs:sequence>
																			<xs:element name="amount">
																				<xs:annotation>
																					<xs:documentation>Amount</xs:documentation>
																				</xs:annotation>
																				<xs:simpleType>
																					<xs:restriction base="xs:decimal">
																						<xs:totalDigits value="18"/>
																						<xs:fractionDigits value="5"/>
																					</xs:restriction>
																				</xs:simpleType>
																			</xs:element>
																			<xs:element name="currencyCode" type="xs:string">
																				<xs:annotation>
																					<xs:documentation>Currency of amount [CODELIST: Currency]</xs:documentation>
																				</xs:annotation>
																			</xs:element>
																		</xs:sequence>
																	</xs:complexType>
																</xs:element>
																<xs:element name="date" type="xs:date">
																	<xs:annotation>
																		<xs:documentation>First instalment date</xs:documentation>
																	</xs:annotation>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
													<xs:element name="regularInstalmentAmount">
														<xs:annotation>
															<xs:documentation>Regular instalment amount</xs:documentation>
														</xs:annotation>
														<xs:complexType>
															<xs:sequence>
																<xs:element name="amount">
																	<xs:annotation>
																		<xs:documentation>Amount</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:decimal">
																			<xs:totalDigits value="18"/>
																			<xs:fractionDigits value="5"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="currencyCode" type="xs:string">
																	<xs:annotation>
																		<xs:documentation>Currency of amount [CODELIST: Currency]</xs:documentation>
																	</xs:annotation>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
													<xs:element name="lastInstalmentAmount">
														<xs:annotation>
															<xs:documentation>Last instalment amount</xs:documentation>
														</xs:annotation>
														<xs:complexType>
															<xs:sequence>
																<xs:element name="amount" maxOccurs="1">
																	<xs:annotation>
																		<xs:documentation>Amount</xs:documentation>
																	</xs:annotation>
																	<xs:simpleType>
																		<xs:restriction base="xs:decimal">
																			<xs:totalDigits value="18"/>
																			<xs:fractionDigits value="5"/>
																		</xs:restriction>
																	</xs:simpleType>
																</xs:element>
																<xs:element name="currencyCode" type="xs:string">
																	<xs:annotation>
																		<xs:documentation>Currency of amount [CODELIST: Currency]</xs:documentation>
																	</xs:annotation>
																</xs:element>
															</xs:sequence>
														</xs:complexType>
													</xs:element>
													<xs:element name="instalmentCount" type="xs:integer">
														<xs:annotation>
															<xs:documentation>A count of instalments</xs:documentation>
														</xs:annotation>
													</xs:element>
													<xs:element name="interestRate">
														<xs:annotation>
															<xs:documentation>Interest rate</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:decimal">
																<xs:totalDigits value="10"/>
																<xs:fractionDigits value="4"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
													<xs:element name="rpsn">
														<xs:annotation>
															<xs:documentation>Roční procentní sazba nákladů. An equivalent for APR (Annual Percentage Rate) in Czech republic</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:decimal">
																<xs:totalDigits value="8"/>
																<xs:fractionDigits value="3"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
						</xs:choice>
					</xs:complexType>
				</xs:element>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
	<xs:element name="opCreateCreditApp_1_2_res">
		<xs:complexType>
			<xs:sequence>
				<xs:element name="productDetails" minOccurs="0">
					<xs:complexType>
						<xs:choice>
							<xs:element name="cardApplication">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="identification">
											<xs:complexType>
												<xs:sequence>
													<xs:element name="srcReferenceId">
														<xs:annotation>
															<xs:documentation>Unique identification of the application from consumer point of view</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:string">
																<xs:maxLength value="30"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
													<xs:element name="trgReferenceId" type="xs:string" minOccurs="0">
														<xs:annotation>
															<xs:documentation>A primary key in a database of an application in a bank system.</xs:documentation>
														</xs:annotation>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
										<xs:element name="agreementNumber" type="xs:string" minOccurs="0">
											<xs:annotation>
												<xs:documentation>A number of an agreement. A client can see this number in a document header (Smlouva cislo xxx)</xs:documentation>
											</xs:annotation>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
							<xs:element name="fundTransfer">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="identification">
											<xs:complexType>
												<xs:sequence>
													<xs:element name="srcReferenceId">
														<xs:annotation>
															<xs:documentation>Unique identification of the entity from consumer point of view</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:string">
																<xs:maxLength value="30"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
													<xs:element name="trgReferenceId">
														<xs:annotation>
															<xs:documentation>Unique identification of an application from provider point of view</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:string">
																<xs:maxLength value="30"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
							<xs:element name="instalmentProgram">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="identification">
											<xs:complexType>
												<xs:sequence>
													<xs:element name="srcReferenceId">
														<xs:annotation>
															<xs:documentation>Unique identification of the entity from consumer point of view</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:string">
																<xs:maxLength value="30"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
													<xs:element name="trgReferenceId">
														<xs:annotation>
															<xs:documentation>Unique identification of an application from provider point of view</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:string">
																<xs:maxLength value="30"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
							<xs:element name="loanOnPhone">
								<xs:complexType>
									<xs:sequence>
										<xs:element name="identification">
											<xs:complexType>
												<xs:sequence>
													<xs:element name="srcReferenceId">
														<xs:annotation>
															<xs:documentation>Unique identification of the entity from consumer point of view</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:string">
																<xs:maxLength value="30"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
													<xs:element name="trgReferenceId">
														<xs:annotation>
															<xs:documentation>Unique identification of an application from provider point of view</xs:documentation>
														</xs:annotation>
														<xs:simpleType>
															<xs:restriction base="xs:string">
																<xs:maxLength value="30"/>
															</xs:restriction>
														</xs:simpleType>
													</xs:element>
												</xs:sequence>
											</xs:complexType>
										</xs:element>
									</xs:sequence>
								</xs:complexType>
							</xs:element>
						</xs:choice>
					</xs:complexType>
				</xs:element>
			</xs:sequence>
		</xs:complexType>
	</xs:element>
</xs:schema>
