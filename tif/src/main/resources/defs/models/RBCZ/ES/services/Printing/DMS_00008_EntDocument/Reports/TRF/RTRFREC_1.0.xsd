<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/dms/dds/trf/rec/RTRFREC/1.0"
    xmlns="http://rb.cz/dms/dds/trf/rec/RTRFREC/1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="rtrfrec">
        <xs:complexType>
            <xs:sequence>
                <xs:element maxOccurs="1" minOccurs="0" name="client" nillable="false">
                    <xs:annotation>
                        <xs:documentation>Client party detail information</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence maxOccurs="1" minOccurs="1">
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="commercialRegistration" nillable="false">
                                <xs:complexType>
                                    <xs:sequence maxOccurs="1" minOccurs="1">
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="commercialName" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>commercial name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="contact" nillable="false">
                                <xs:complexType>
                                    <xs:sequence maxOccurs="1" minOccurs="1">
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="preferred" nillable="false">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="0"
                                    name="email" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>preferred email address</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="150"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="fax" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>preferred fax</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="fixLine" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>preferred fix line</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="25"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="mobile" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>preferred mobile number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="20"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="personalForm" nillable="false">
                                <xs:annotation>
                                    <xs:documentation>Personal form  information of the party</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence maxOccurs="1" minOccurs="1">
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="firstName" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>First name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="80"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="middleNames" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Middle names</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="80"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="surName" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Surname</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="100"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="titleBefore" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Title before the first name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="titleBehind" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Title behind the surname</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="address" nillable="false">
                                <xs:annotation>
                                    <xs:documentation>Client addresses</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence maxOccurs="1" minOccurs="1">
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="mailing" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Party mailing address</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="street" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Name of the street</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="200"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="number" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Street number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="part" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>part of the city</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="city" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Name of the city</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="200"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="postalCode" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Postal code</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="country" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Name of the country</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="200"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="contract" nillable="false">
                    <xs:complexType>
                        <xs:sequence maxOccurs="1" minOccurs="1">
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="address" nillable="false">
                                <xs:complexType>
                                    <xs:sequence maxOccurs="1" minOccurs="1">
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="mailing" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Party mailing address</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="street" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Name of the street</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="200"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="number" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Street number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="city" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Name of the city</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="200"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="postalCode" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Postal code</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="country" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Name of the country</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="200"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="contact" nillable="false">
                                <xs:complexType>
                                    <xs:sequence maxOccurs="1" minOccurs="1">
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="preferred" nillable="false">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="0"
                                    name="email" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>preferred email address</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="150"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="fax" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>preferred fax</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="fixLine" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>preferred fix line</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="25"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="mobile" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>preferred mobile number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="20"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="personalForm" nillable="false">
                                <xs:complexType>
                                    <xs:sequence maxOccurs="1" minOccurs="1">
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="firstName" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>First name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="80"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="surName" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Surname</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="100"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="titleBefore" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Title before the first name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="titleBehind" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Title behind the surname</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="personNumber" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>employee identification number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="place" nillable="false">
                    <xs:annotation>
                        <xs:documentation>City of the branch where the report is generated</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:maxLength value="80"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="1"
                    name="reportDate" nillable="false" type="xs:date">
                    <xs:annotation>
                        <xs:documentation>Date on which this report is generated</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0"
                    name="deliveryChannel" nillable="false" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>preferred delivery channel [CODELIST: FavouredConfirmationDeliveryChannel]</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="1"
                    name="valuationDate" nillable="false" type="xs:date">
                    <xs:annotation>
                        <xs:documentation>business day used to determine Valuation Rate</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0"
                    name="transactionLimits" nillable="false">
                    <xs:complexType>
                        <xs:sequence maxOccurs="1" minOccurs="1">
                            <xs:element maxOccurs="unbounded"
                                minOccurs="1" name="transactionLimit" nillable="false">
                                <xs:complexType>
                                    <xs:sequence maxOccurs="1" minOccurs="1">
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="name"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>name of the transaction limit, primary value "FXD"</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="approvedLimitAmount" nillable="false">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="currency"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The currency in which an amount is denominated. [CODELIST: DOMESTIC_PAYMENT_ORDER_CURRENCY_CODES  ]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="amount" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>The positive monetary quantity in currency units.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="22"/>

                                    <xs:fractionDigits value="7"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="collateralAmount" nillable="false">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="currency"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The currency in which an amount is denominated. [CODELIST: DOMESTIC_PAYMENT_ORDER_CURRENCY_CODES  ]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="amount" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>The positive monetary quantity in currency units.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="22"/>

                                    <xs:fractionDigits value="7"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="utilization" nillable="false">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="rate" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>rate of the utilization</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:fractionDigits value="7"/>

                                    <xs:totalDigits value="22"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="utilizationAmount" nillable="false">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="currency"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The currency in which an amount is denominated. [CODELIST: DOMESTIC_PAYMENT_ORDER_CURRENCY_CODES  ]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="amount" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>The positive monetary quantity in currency units.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="22"/>

                                    <xs:fractionDigits value="7"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="availableLimit" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>OTC transaction available limit</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:decimal">
                                    <xs:totalDigits value="22"/>
                                    <xs:fractionDigits value="7"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="addOn" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>add on</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:decimal">
                                    <xs:totalDigits value="22"/>
                                    <xs:fractionDigits value="7"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="maximalTenorDate"
                                    nillable="false" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Maximal Tenor Date</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="availabilityDate"
                                    nillable="false" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Availability Date</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="valuationAmount" nillable="false">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="currency"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The currency in which an amount is denominated. [CODELIST: DOMESTIC_PAYMENT_ORDER_CURRENCY_CODES  ]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="amount" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>The positive monetary quantity in currency units.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="22"/>

                                    <xs:fractionDigits value="7"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0"
                    name="foreignExchanges" nillable="false">
                    <xs:complexType>
                        <xs:sequence maxOccurs="1" minOccurs="1">
                            <xs:element maxOccurs="unbounded"
                                minOccurs="1" name="foreignExchange" nillable="false">
                                <xs:complexType>
                                    <xs:sequence maxOccurs="1" minOccurs="1">
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="confirmationId"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>number of the confirmation</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="dealDate"
                                    nillable="false" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>deal date of the transaction</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="uti"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Universal trade ID (EMIR deal identification)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="valuationAmount" nillable="false">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="currency"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The currency in which an amount is denominated. [CODELIST: DOMESTIC_PAYMENT_ORDER_CURRENCY_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="amount" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>The positive monetary quantity in currency units.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:fractionDigits value="7"/>

                                    <xs:totalDigits value="22"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="tradeType" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Trade Type [LOV: FW;SW] [LOV: FW;SW]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:enumeration value="FW"/>
                                    <xs:enumeration value="SW"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="valuationRate" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Valuation Rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:decimal">
                                    <xs:fractionDigits value="7"/>
                                    <xs:totalDigits value="22"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="maturityDate"
                                    nillable="false" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>The end date of the calculation period. This date should already be adjusted for any applicable business day convention. w3c format</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="fixedRate" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Fixed Rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:decimal">
                                    <xs:totalDigits value="22"/>
                                    <xs:fractionDigits value="7"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="referenceCurrencyAmount" nillable="false">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="currency"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The currency in which an amount is denominated. [CODELIST: DOMESTIC_PAYMENT_ORDER_CURRENCY_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="amount" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>The positive monetary quantity in currency units.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="22"/>

                                    <xs:fractionDigits value="7"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="changeAmount">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="changeAmount" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Change amount in %</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="22"/>

                                    <xs:fractionDigits value="7"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="originalAmount" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Initial P&amp;L Value</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="22"/>

                                    <xs:fractionDigits value="7"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="currentAmount" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Current P&amp;L Value</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="22"/>

                                    <xs:fractionDigits value="7"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="options" nillable="false">
                    <xs:complexType>
                        <xs:sequence maxOccurs="1" minOccurs="1">
                            <xs:element maxOccurs="unbounded"
                                minOccurs="1" name="option" nillable="false">
                                <xs:complexType>
                                    <xs:sequence maxOccurs="1" minOccurs="1">
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="confirmationId"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>number of the confirmation</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="dealDate"
                                    nillable="false" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>deal date of the transaction</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="uti"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Universal trade ID (EMIR deal identification)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="valuationAmount" nillable="false">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="currency"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The currency in which an amount is denominated. [CODELIST: DOMESTIC_PAYMENT_ORDER_CURRENCY_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="amount" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>The positive monetary quantity in currency units.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="22"/>

                                    <xs:fractionDigits value="7"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="expirationDate"
                                    nillable="false" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>date of expiration</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="referenceCurrencyAmount" nillable="false">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="currency"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The currency in which an amount is denominated. [CODELIST: DOMESTIC_PAYMENT_ORDER_CURRENCY_CODES  ]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="amount" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>The positive monetary quantity in currency units.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:fractionDigits value="7"/>

                                    <xs:totalDigits value="22"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="settlementDate"
                                    nillable="false" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>date of  the settlement</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="underlyingAsset"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Underlying Asset</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="tradeType" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Defines wether the trade is buy or sell [LOV: B;S] [LOV: B;S]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:enumeration value="B"/>
                                    <xs:enumeration value="S"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="strikePrice" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Strike Price</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:decimal">
                                    <xs:totalDigits value="22"/>
                                    <xs:fractionDigits value="7"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="changeAmount">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="changeAmount" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Change amount in %</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="22"/>

                                    <xs:fractionDigits value="7"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="originalAmount" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Initial P&amp;L Value</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="22"/>

                                    <xs:fractionDigits value="7"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="currentAmount" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Current P&amp;L Value</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="22"/>

                                    <xs:fractionDigits value="7"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="swaps" nillable="false">
                    <xs:complexType>
                        <xs:sequence maxOccurs="1" minOccurs="1">
                            <xs:element maxOccurs="unbounded"
                                minOccurs="1" name="swap" nillable="false">
                                <xs:complexType>
                                    <xs:sequence maxOccurs="1" minOccurs="1">
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="confirmationId"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>number of the confirmation</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="dealDate"
                                    nillable="false" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>deal date of the transaction</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="uti"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Universal trade ID (EMIR deal identification)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="valuationAmount" nillable="false">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="currency"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The currency in which an amount is denominated. [CODELIST: DOMESTIC_PAYMENT_ORDER_CURRENCY_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="amount" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>The positive monetary quantity in currency units.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="22"/>

                                    <xs:fractionDigits value="7"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="startDate"
                                    nillable="false" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>The start date of the calculation period. w3c format</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="maturityDate"
                                    nillable="false" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>The end date of the calculation period. This date should already be adjusted for any applicable business day convention. w3c format</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="notionalAmount" nillable="false">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="currency"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The currency in which an amount is denominated. [CODELIST: DOMESTIC_PAYMENT_ORDER_CURRENCY_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="amount" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>The positive monetary quantity in currency units.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:fractionDigits value="7"/>

                                    <xs:totalDigits value="22"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="currentNotionalAmount" nillable="false">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="currency"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The currency in which an amount is denominated. [CODELIST: DOMESTIC_PAYMENT_ORDER_CURRENCY_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="amount" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>The positive monetary quantity in currency units.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:fractionDigits value="7"/>

                                    <xs:totalDigits value="22"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="interestRate" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Interest Rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:decimal">
                                    <xs:totalDigits value="22"/>
                                    <xs:fractionDigits value="7"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="changeAmount">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="changeAmount" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Change amount in %</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="22"/>

                                    <xs:fractionDigits value="7"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="originalAmount" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Initial P&amp;L Value</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="22"/>

                                    <xs:fractionDigits value="7"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="currentAmount" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Current P&amp;L Value</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="22"/>

                                    <xs:fractionDigits value="7"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0"
                    name="interestOptions" nillable="false">
                    <xs:complexType>
                        <xs:sequence maxOccurs="1" minOccurs="1">
                            <xs:element maxOccurs="unbounded"
                                minOccurs="1" name="interestOption" nillable="false">
                                <xs:complexType>
                                    <xs:sequence maxOccurs="1" minOccurs="1">
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="confirmationId"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>number of the confirmation</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="dealDate"
                                    nillable="false" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>deal date of the transaction</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="uti"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Universal trade ID (EMIR deal identification)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="valuationAmount" nillable="false">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="currency"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The currency in which an amount is denominated. [CODELIST: DOMESTIC_PAYMENT_ORDER_CURRENCY_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="amount" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>The positive monetary quantity in currency units.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:fractionDigits value="7"/>

                                    <xs:totalDigits value="22"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="maturityDate"
                                    nillable="false" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>The end date of the calculation period. This date should already be adjusted for any applicable business day convention. w3c format</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="optionType" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Defines wether the option is cap or floor [LOV: C;F] [LOV: C;F]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:enumeration value="C"/>
                                    <xs:enumeration value="F"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="notionlaAmount" nillable="false">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="currency"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The currency in which an amount is denominated. [CODELIST: DOMESTIC_PAYMENT_ORDER_CURRENCY_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="amount" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>The positive monetary quantity in currency units.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:fractionDigits value="7"/>

                                    <xs:totalDigits value="22"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="currentNotionalAmount" nillable="false">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="currency"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The currency in which an amount is denominated. [CODELIST: DOMESTIC_PAYMENT_ORDER_CURRENCY_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="amount" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>The positive monetary quantity in currency units.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:fractionDigits value="7"/>

                                    <xs:totalDigits value="22"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="interestRate" nillable="false">
                                    <xs:simpleType>
                                    <xs:restriction base="xs:decimal">
                                    <xs:totalDigits value="22"/>
                                    <xs:fractionDigits value="7"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="tradeType" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Trade Type [LOV: B;S] [LOV: B;S]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:enumeration value="B"/>
                                    <xs:enumeration value="S"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="changeAmount">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="changeAmount" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Change amount in %</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="22"/>

                                    <xs:fractionDigits value="7"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="originalAmount" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Initial P&amp;L Value</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="22"/>

                                    <xs:fractionDigits value="7"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="currentAmount" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Current P&amp;L Value</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="22"/>

                                    <xs:fractionDigits value="7"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0"
                    name="optionStrategies" nillable="false">
                    <xs:complexType>
                        <xs:sequence maxOccurs="1" minOccurs="1">
                            <xs:element maxOccurs="unbounded"
                                minOccurs="1" name="optionStrategy" nillable="false">
                                <xs:complexType>
                                    <xs:sequence maxOccurs="1" minOccurs="1">
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="confirmationId"
                                    nillable="false" type="xs:string"/>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="dealDate"
                                    nillable="false" type="xs:date"/>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="settlementDate"
                                    nillable="false" type="xs:date"/>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="uti"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Universal trade ID (EMIR deal identification)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="underlyingAsset"
                                    nillable="false" type="xs:string"/>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="referenceCurrencyAmount" nillable="false">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="currency"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The currency in which an amount is denominated. [CODELIST: DOMESTIC_PAYMENT_ORDER_CURRENCY_CODES  ]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="amount" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>The positive monetary quantity in currency units.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:fractionDigits value="7"/>

                                    <xs:totalDigits value="22"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="tradeType" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Trade Type [LOV: B;S] [LOV: B;S]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:enumeration value="B"/>
                                    <xs:enumeration value="S"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="changeAmount">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="changeAmount" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Change amount in %</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="22"/>

                                    <xs:fractionDigits value="7"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="originalAmount" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Initial P&amp;L Value</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="22"/>

                                    <xs:fractionDigits value="7"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="currentAmount" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Current P&amp;L Value</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="22"/>

                                    <xs:fractionDigits value="7"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
