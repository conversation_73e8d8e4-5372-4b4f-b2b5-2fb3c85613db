<?xml version="1.0" encoding="UTF-8"?>
<tif xmlns="http://ge.com/interfaces/connection_definition"
     xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
     xsi:schemaLocation="http://ge.com/interfaces/connection_definition BindingDefinition.xsd">
   <binding>
      <name>A_SBL_GetQuestionnaire</name>
      <version>100</version>
      <environment>TEST</environment>
      <api>
         <application>SBL</application>
         <name>A_SBL_GetQuestionnaire</name>
         <version>100</version>
      </api>
      <roundtrip_timeout>20</roundtrip_timeout>
      <class>INQUIRE</class>
      <request>
         <priority>4</priority>
         <timeout>30</timeout>
         <queue>ESB.SBL.IN</queue>
         <persistence>false</persistence>
      </request>
      <response>
         <priority>4</priority>
         <timeout>30</timeout>
         <queue>ESB.CUS.ENTPARTY.GETDETAIL2.2.RS.Q</queue>
         <persistence>false</persistence>
      </response>
   </binding>
</tif>
