<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/services/application/GetDetail_1_1"
    version="1.0.0"
    xmlns:opgetdetail_1_1="http://rb.cz/services/application/GetDetail_1_1" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="opGetDetail_1_1">
        <xs:complexType>
            <xs:sequence>
                <xs:element maxOccurs="1" minOccurs="1" name="applicationId">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:choice maxOccurs="1" minOccurs="1">
                                <xs:element name="primaryApplicationId" type="xs:long">
                                    <xs:annotation>
                                    <xs:documentation>Unique identification of the application in the back-end system which processes the requested application.</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element
                                    name="secondaryApplicationId" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>ID of the application generated in the common back-end (SBL)</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                            </xs:choice>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="1" name="channel"
                    nillable="false" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Channel through which the application was created - currently only ONB is supported</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="opGetDetail_1_1_res">
        <xs:complexType>
            <xs:sequence>
                <xs:element maxOccurs="1" minOccurs="1" name="applications">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="2" minOccurs="0" name="application">
                                <xs:annotation>
                                    <xs:documentation>If a client (FOP) together with a business account (MAIN application) also applied for a private account (ADDITIONAL application), then the detail of both of these applications is returned.</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="status">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="applicationStatus" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Status of the application - primary used for SBL [CODELIST: ApplicationStatus]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="applicationSubStatus" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Detailed status of actual state of application process [CODELIST: ApplicationStatus]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="rejectionReason">
                                    <xs:annotation>
                                    <xs:documentation>ID of rejection reason when REJECTED status and sub status is specified. Values taken from codelist ApplRejectionReason</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:long">

                                    <xs:fractionDigits value="0"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="relation" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Relation of application - parent child relationship may exist - MAIN, ADDITIONAL [LOV: MAIN;ADDITIONAL]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:enumeration value="MAIN"/>
                                    <xs:enumeration value="ADDITIONAL"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="relatedApplicationId" type="xs:long">
                                    <xs:annotation>
                                    <xs:documentation>Related MAIN application ID in ONB</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="requestorPartyId"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Party ID of entity which created this application, related to applicant private or commercial party</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="applicationType"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Type of application - according to the backend system application's internal codebook APPLICATIONTYPECODE</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="offerCode"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Offer code defining which configuration should be used for related products</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="sourceChannelCode"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Channel code defining source of the appliation [CODELIST: Channel] [CODELIST: CHANNEL_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="applicationId"
                                    nillable="false" type="xs:long">
                                    <xs:annotation>
                                    <xs:documentation>ID of application in srouce system ONB</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="secondaryApplicationId"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>ID of application generated in the common back-end (SBL)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="opportunityId"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>ID of opportunity in Siebel</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="contract">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="commissionIdPartner" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>identifier of the contractor (asset) - identification of 3rd party agency</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="commissionPartnerName" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>name of the contractor (asset) - name of 3rd party agency</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="commissionIdUser" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Identification of 3rd party user working for agency</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="contractId" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>document ID of contract signed by potential client</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="contractSignatureDate" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>actual date of document signature by client (signature date binded to specific contractId)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="userId"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Unique identification of the user, who reviewed contract</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="dates">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="createDate" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Datetime of the creation of the application</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="approvalDate" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>date of the approval of the application (by the bank)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="rejectionDate" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Datetime of rejection of application</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="lastUpdateDate" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Datetime of the last update of the application</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="applicants">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="applicant">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="applicantId"
                                    nillable="false" type="xs:long">
                                    <xs:annotation>
                                    <xs:documentation>ID of applicant in source system ONB</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="partyType" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Type of applicant - FO, FOP or PO [LOV: FO;FOP;PO]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="FO"/>

                                    <xs:enumeration value="FOP"/>

                                    <xs:enumeration value="PO"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="clientUserName">
                                    <xs:annotation>
                                    <xs:documentation>Client username (login) which is used for authentication in Authentication server </xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="256"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="privateParty">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="partyId" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>ID of client/prospect in Siebel</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="firstName"
                                    nillable="false" type="xs:string"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="lastName"
                                    nillable="false" type="xs:string"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="gender"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Male or female - semi-genders are not supported by the backend [CODELIST: PRV_GENDER]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="birthInfo">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="birthCode"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Czech birth code</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="countryOfBirth" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Country of birth [CODELIST: GENERAL_CNTR]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="cityOfBirth"
                                    nillable="false" type="xs:string"/>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="mobilePhone"
                                    nillable="false" type="xs:string"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="email"
                                    nillable="false" type="xs:string"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="addresses">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="address">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="addressType" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>[LOV: PERMANENT;CONTACT]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="PERMANENT"/>

                                    <xs:enumeration value="CONTACT"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="street" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Street name</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="number" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Possible formats with '/' or without it</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="city" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>City name</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="postalCode"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>ZIP code</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="country" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>[CODELIST: GENERAL_CNTR]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="identityCards">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="identityCard">
                                    <xs:annotation>
                                    <xs:documentation>doklady (op, ŘIDIIČÁK...</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="cardType"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>type of identification card (OP, řidický průkaz, cestovní pas)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="cardNumber"
                                    nillable="false" type="xs:string"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="issueDate"
                                    nillable="false" type="xs:date"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="validTo"
                                    nillable="false" type="xs:date"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="issuedBy"
                                    nillable="false" type="xs:string"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="trgReferenceId" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Unique identification of the entity from provider point of view</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="questionnaire">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="0" name="question">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="name"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Name of the question - from the backend system's internal codebook [CODELIST: COMPLIANCEQUESTIONNAIRE], column QUESTIONCODE</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="answers">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="answer">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="name" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Name of the answer - from the backend system's internal codebook [CODELIST: COMPLIANCEQUESTIONANSWERS], column OPTIONNUM</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="type" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Data type of the answer - from the backend system's internal codebook [CODELIST: COMPLIANCEQUESTIONANSWERS], column OPTIONNUM [LOV: STRING;INTEGER;LONG;DECIMAL;DOUBLE;BOOLEAN;DATE;TIME;DATETIME;TIMESTAMP;BINARY]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="STRING"/>

                                    <xs:enumeration value="INTEGER"/>

                                    <xs:enumeration value="LONG"/>

                                    <xs:enumeration value="DECIMAL"/>

                                    <xs:enumeration value="DOUBLE"/>

                                    <xs:enumeration value="BOOLEAN"/>

                                    <xs:enumeration value="DATE"/>

                                    <xs:enumeration value="TIME"/>

                                    <xs:enumeration value="DATETIME"/>

                                    <xs:enumeration value="TIMESTAMP"/>

                                    <xs:enumeration value="BINARY"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="value" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Value of the answer</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="eligibility">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="plannedTransactionType"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Planned transaction type [CODELIST: PLANNEDTRANSACTIONS], column PLANNED_TRNS_CODE</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="sourceOfFunds"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Source of funds [CODELIST: SOURCEOFFUNDS], column FUNDSOURCE_CODE</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="esa95"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Esa 95 value [CODELIST: ESASNA], column ESA95_CODE</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="care">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="firstName"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>First name of the banker</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="lastName"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Last name of the banker</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="position"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Position of the banker</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="branch"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Branch to which the care is placed</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="clientCategory">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="categoryType"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Type of the client category</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="categorization"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Client categorization</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="commercialParty">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="partyId" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>ID of client/prospect in Siebel</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="registrationNumber" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>ICO of commercial party</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="taxId" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>DIC of commercial party</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="companyName" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>company name of commercial party</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="registeredCompanyName" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>certificate ID of party</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="registeredCountry" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>registered country of party</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="dateOfEstablishment" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>date of establishment of party</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="taxOffice" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>tax office of party</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="tradesLicensingOffice" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>trade licensing office of party</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="registrationCourt" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>registration court of party</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="certificateIDInRegisters" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>certificate ID of party</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="legalForm" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>legal form of party</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="legalStatus" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>legal status of party</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="esa95" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>esa95 of party</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="oenaceDetail">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="srcNaceMain" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>source NACE main code</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="srcNace1" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>source NACE code 1</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="srcNace2" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>source NACE code 2</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="srcNace3" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>source NACE code 3</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="srcNace4" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>source NACE code 4</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="srcNace5" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>source NACE code 5</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="addresses">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="address">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="addressType" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>[LOV: PERMANENT;CONTACT]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="PERMANENT"/>

                                    <xs:enumeration value="CONTACT"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="street" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Street name</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="number" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Possible formats with '/' or without it</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="city" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>City name</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="postalCode"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>ZIP code</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="country" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>[CODELIST: GENERAL_CNTR]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="questionnaire">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="0" name="question">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="name"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Name of the question - from the backend system's internal codebook [CODELIST: COMPLIANCEQUESTIONNAIRE], column QUESTIONCODE</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="answers">
                                    <xs:complexType>

                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="answer">
                                    <xs:complexType>

                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="name" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Name of the answer - from the backend system's internal codebook [CODELIST: COMPLIANCEQUESTIONANSWERS], column OPTIONNUM</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="type" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Data type of the answer - from the backend system's internal codebook [CODELIST: COMPLIANCEQUESTIONANSWERS], column OPTIONNUM [LOV: STRING;INTEGER;LONG;DECIMAL;DOUBLE;BOOLEAN;DATE;TIME;DATETIME;TIMESTAMP;BINARY]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="STRING"/>

                                    <xs:enumeration value="INTEGER"/>

                                    <xs:enumeration value="LONG"/>

                                    <xs:enumeration value="DECIMAL"/>

                                    <xs:enumeration value="DOUBLE"/>

                                    <xs:enumeration value="BOOLEAN"/>

                                    <xs:enumeration value="DATE"/>

                                    <xs:enumeration value="TIME"/>

                                    <xs:enumeration value="DATETIME"/>

                                    <xs:enumeration value="TIMESTAMP"/>

                                    <xs:enumeration value="BINARY"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="value" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Value of the answer</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="eligibility">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="plannedTransactionType"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Planned transaction type [CODELIST: PLANNEDTRANSACTIONS], column PLANNED_TRNS_CODE</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="sourceOfFunds" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Source of funds [CODELIST: SOURCEOFFUNDS], column FUNDSOURCE_CODE</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="esa95"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Esa 95 value [CODELIST: ESASNA], column ESA95_CODE</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="care">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="firstName"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>First name of the banker</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="lastName"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Last name of the banker</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="position"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Position of the banker</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="branch"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Branch to which the care is placed</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="clientCategory">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="categoryType"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Type of the client category</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="categorization"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Client categorization</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="pennyPayment">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="bankAccount">
                                    <xs:annotation>
                                    <xs:documentation>účet, který uvedl, ze kterého příjdou peníze</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="accountNumber">
                                    <xs:annotation>
                                    <xs:documentation>if fulfilled then return addresses for credit card delivery connected with this account</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="numberPart2" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="currencyFolders">
                                    <xs:annotation>
                                    <xs:documentation>Set of currency folders</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="0" name="currencyFolder">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of the bank account [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="mainCurrency" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>flag if this is main currency code of the bank account</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="transactionDetail">
                                    <xs:complexType>

                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="debitAccount">
                                    <xs:annotation>
                                    <xs:documentation>if fulfilled then return addresses for credit card delivery connected with this account</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>

                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="accountNumber">
                                    <xs:annotation>
                                    <xs:documentation>if fulfilled then return addresses for credit card delivery connected with this account</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="numberPart2" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="currencyFolders">
                                    <xs:annotation>
                                    <xs:documentation>Set of currency folders</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="0" name="currencyFolder">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of the bank account [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="mainCurrency" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>flag if this is main currency code of the bank account</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="creditAccount">
                                    <xs:annotation>
                                    <xs:documentation>účet, ze kterého přišla platba</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="accountNumber">
                                    <xs:annotation>
                                    <xs:documentation>if fulfilled then return addresses for credit card delivery connected with this account</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="numberPart2" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="currencyFolders">
                                    <xs:annotation>
                                    <xs:documentation>Set of currency folders</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="0" name="currencyFolder">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of the bank account [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="mainCurrency" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>flag if this is main currency code of the bank account</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="amount"
                                    nillable="false" type="xs:decimal"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="currencyCode"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of the bank account [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="debitName" type="xs:string"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="debitInfo" type="xs:string"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="message" type="xs:string"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="transactionDate"
                                    nillable="false" type="xs:dateTime"/>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="consents">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="biometricDataConsent"
                                    nillable="false" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Consent with biometric data usage</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="marketingConsent"
                                    nillable="false" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Master marketing consent as represented in Siebel, where this consent consist of several consents</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="scanning">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="scanningSamples">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="sample">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="sampleId"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>ID of sample in ZenID application</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="sampleType"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Sample type as defined in codelist TODO</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="side">
                                    <xs:annotation>
                                    <xs:documentation>For certain sample types it tells us which side of sample it is - front or back side [LOV: FRONT;BACK]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="FRONT"/>

                                    <xs:enumeration value="BACK"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="status" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>ok or not, if some score wasn't sufficient for certain field = OCR process couln't extract some field with sufficient precision [LOV: OK;NOK]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="OK"/>

                                    <xs:enumeration value="NOK"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="docType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Type of scanned document - based on MCH Codelist [ZidDocumentRole]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="minedDocMetadata">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="docIdentificationNumber" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Document number</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="validTo" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Expiration date of  the document (only if the property applies to this document type)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="issueDate" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Issue date of document (only if the property applies to this document type)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="issuedBy" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Who issued the document (only if the property applies to this document type)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="investigationResults">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="investigationResult">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="investigationId"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>ID of investigation result obtained from ZenId</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="relatedSampleIds">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1"
                                    name="sampleId"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>ID of sample in ZenID</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="result" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Reslt of investigation and aggregation fucntion - uncertain results forces user to upload another sample video [LOV: OK;OK_UNCERTAIN;NOK_UNCERTAIN;NOK]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="OK"/>

                                    <xs:enumeration value="OK_UNCERTAIN"/>

                                    <xs:enumeration value="NOK_UNCERTAIN"/>

                                    <xs:enumeration value="NOK"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="dateCreated"
                                    nillable="false" type="xs:dateTime"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="aggregationFunctionVersion" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Version of aggregation function used to evaluate result from investigation validations</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:long">

                                    <xs:fractionDigits value="0"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="validationResults">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="validationResult">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="validationType"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Validation type of ZenID, defined in codelist TODO</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="result"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Result of validation represented as a number from ZenID</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="minedApplicantMetadata">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="firstName" type="xs:string"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="lastName" type="xs:string"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="birthCode" type="xs:string"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="cityOfBirth" type="xs:string"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="address">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="addressType">
                                    <xs:annotation>
                                    <xs:documentation>In this context always PERMANENT [LOV: PERMANENT;CONTACT]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="PERMANENT"/>

                                    <xs:enumeration value="CONTACT"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="addressee" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Addressee bound to address</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="street" type="xs:string"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="streetNum" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Possible formats with / or without it</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="city" type="xs:string"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="postalCode" type="xs:string"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="country">
                                    <xs:annotation>
                                    <xs:documentation>[CODELIST: GENERAL_CNTR]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="applicationFlags">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="0" name="applicationFlag">
                                    <xs:annotation>
                                    <xs:documentation>Represents several metadata about aplication, some special actions of user etc.</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="applicationFlagType"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Flag type as defined in the backend system's codelist [TODO]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="applicationFlagValue"
                                    nillable="false" type="xs:string"/>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="additionalData">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="transferPaymentAndCancel" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Should the old account be canceled and payments transfered to new one</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="productPackages">
                                    <xs:complexType>
                                    <xs:choice>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="currentAccountApplication">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="relatedApplicants">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="applicantId"
                                    nillable="false" type="xs:long">
                                    <xs:annotation>
                                    <xs:documentation>ID of related applicant for the product package</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="role" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Role of applicant on application - value from backend system's internal codebook [ApplicantRole - TBD]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="productCode" type="xs:string"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="priceProgramCode"
                                    nillable="false" type="xs:string"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="interestProductCode"
                                    nillable="false" type="xs:string"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="agreementNumber"
                                    nillable="false" type="xs:string"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="accountNumber">
                                    <xs:annotation>
                                    <xs:documentation>if fulfilled then return addresses for credit card delivery connected with this account</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="numberPart2" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="currencyFolders">
                                    <xs:annotation>
                                    <xs:documentation>Set of currency folders</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="0" name="currencyFolder">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of the bank account [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="mainCurrency" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>flag if this is main currency code of the bank account</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="paymentCardApplication">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="embossedName1"
                                    nillable="false" type="xs:string"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="embossedName2" type="xs:string"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="cardProductType"
                                    nillable="false" type="xs:string"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="validDuringLifeOnly"
                                    nillable="false" type="xs:boolean"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="eCommerceActivated"
                                    nillable="false" type="xs:boolean"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="expressDelivery"
                                    nillable="false" type="xs:boolean"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="deliveryType"
                                    nillable="false" type="xs:string"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="pinDeliveryType"
                                    nillable="false" type="xs:string"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="limits">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="limit">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="limitType"
                                    nillable="false" type="xs:string"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="amount"
                                    nillable="false" type="xs:decimal"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="currencyCode"
                                    nillable="false" type="xs:string"/>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="savingAccountApplication">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="productCode"
                                    nillable="false" type="xs:string"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="priceProgramCode"
                                    nillable="false" type="xs:string"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="interestProductCode"
                                    nillable="false" type="xs:string"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="businessProductCode"
                                    nillable="false" type="xs:string"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="accountNumber">
                                    <xs:annotation>
                                    <xs:documentation>if fullfilled then return addresses for credit card delivery connected with this account</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="numberPart2" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="currencyFolders">
                                    <xs:annotation>
                                    <xs:documentation>Set of currency folders</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="0" name="currencyFolder">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of the bank account [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="mainCurrency"
                                    nillable="false" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>flag if this is main currency code of the bank account</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="mobilityApplication">
                                    <xs:annotation>
                                    <xs:documentation>Mobility application</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="signatureDate" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Date of mobility application signature</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="documentId" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>DMS ID of application document</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="status" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Status of application (filled by BPM); Enum (SENT, CANCELLED)	</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="statusDate" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Date of status (filled by BPM)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element name="originalProvider">
                                    <xs:annotation>
                                    <xs:documentation>Active client account in foreign bank which will be closed and mandatory operations for needed to be processed by foreign bank</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="numberPart2" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="bankCode"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="currencyCode"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of the bank account [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="idenetificationNumber" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>IČO - company identification number of original bank account provider</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="providerActivities" nillable="false">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="flags">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="blockIncomingPayments"
                                    nillable="false" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Request to stop incoming payments on old account</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="transferCreditAccountBalance"
                                    nillable="false" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Request to transfer ballnace from old account to new account</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="handoverStandingOrders"
                                    nillable="false" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Request to provide list of standing orders to new provider (RBCZ)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="handoverIncomingPayments"
                                    nillable="false" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Request to provide list of regular incoming payments to new provider (RBCZ)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="handoverMobility"
                                    nillable="false" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Request to provide account cancellation order to new provider</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="standOrders" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Request to stop standinng orders on old account [LOV: STOPALL;STOPSELECTED]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="STOPALL"/>

                                    <xs:enumeration value="STOPSELECTED"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="sendOrdersAndPayments" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Request to send list of standing orders and payments to applicant [LOV: POST;EMAIL]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="POST"/>

                                    <xs:enumeration value="EMAIL"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="dueDate"
                                    nillable="false" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Due date to process activites by current provider</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="futureProvider">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="providerActivities">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="futureStandingOrders"
                                    nillable="false" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Request to start standing orders and collections by new provider</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="synergy" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Request to provide synergy [LOV: ACCOUNTCHANGE;INFORMATION;NOSYNERGY]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="ACCOUNTCHANGE"/>

                                    <xs:enumeration value="INFORMATION"/>

                                    <xs:enumeration value="NOSYNERGY"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="startDate"
                                    nillable="false" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Due date to process activites by new provider</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:choice>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="gdpr">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="gdprStatus"
                                    nillable="false" type="xs:string"/>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="gdprStatusDate"
                                    nillable="false" type="xs:date"/>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
