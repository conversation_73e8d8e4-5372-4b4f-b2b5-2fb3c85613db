<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/services/cashAccount/GetTransactionDetail_1_2/1.0"
    version="1.0.0"
    xmlns:opgettransactiondetail_1_2="http://rb.cz/services/cashAccount/GetTransactionDetail_1_2/1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="opGetTransactionDetail_1_2">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="transactionId">
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:maxLength value="30"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:choice>
                    <xs:element name="accountNumber">
                        <xs:annotation>
                            <xs:documentation>Account number</xs:documentation>
                        </xs:annotation>
                        <xs:complexType>
                            <xs:sequence>
                                <xs:element maxOccurs="1" minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                                <xs:element name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                                <xs:element name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of the bank account</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                            </xs:sequence>
                        </xs:complexType>
                    </xs:element>
                    <xs:element name="termDepositNumber">
                        <xs:annotation>
                            <xs:documentation>Term deposit number</xs:documentation>
                        </xs:annotation>
                        <xs:complexType>
                            <xs:sequence>
                                <xs:element minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                                <xs:element name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                                <xs:element name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of the bank account</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element minOccurs="0" name="depositNumber">
                                    <xs:annotation>
                                    <xs:documentation>Number of deposit</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                            </xs:sequence>
                        </xs:complexType>
                    </xs:element>
                </xs:choice>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="opGetTransactionDetail_1_2_res">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="transactionDetail">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="transactionId">
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="transactionNumber">
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="typeCode" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>[CODELIST: TRANSACTION_TYPE_CODES]</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="transactionAmount">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="amount" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Amount of money</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency code of the amount</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="transactionDate" type="xs:dateTime"/>
                            <xs:element name="accountingDate" type="xs:dateTime"/>
                            <xs:element minOccurs="0" name="originatorMessage">
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="240"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element minOccurs="0" name="note">
                                <xs:annotation>
                                    <xs:documentation>A custom note provided by client to mark transaction with note.</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="254"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element minOccurs="0" name="shopId">
                                <xs:annotation>
                                    <xs:documentation>Unique id of the shop.</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="23"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element minOccurs="0" name="counterParty">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element minOccurs="0" name="accountName">
                                    <xs:annotation>
                                    <xs:documentation>Bank account name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="140"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="accountNumber">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="iban" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>IBAN code of the bank account</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="message">
                                    <xs:annotation>
                                    <xs:documentation>Delivered information</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="140"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="internalAccount" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Internal account</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element minOccurs="0"
                                name="paymentCardNumber" type="xs:string"/>
                            <xs:element minOccurs="0" name="requestedAmount">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="amount" type="xs:decimal"/>
                                    <xs:element name="currencyCode" type="xs:string"/>
                                    <xs:element name="exchangeRate" type="xs:decimal"/>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="direction">
                                <xs:annotation>
                                    <xs:documentation>[LOV: All;Credit;Debit]</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:enumeration value="All"/>
                                    <xs:enumeration value="Credit"/>
                                    <xs:enumeration value="Debit"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element minOccurs="0" name="symbols">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element minOccurs="0" name="variable">
                                    <xs:annotation>
                                    <xs:documentation>Variable symbol</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="constant">
                                    <xs:annotation>
                                    <xs:documentation>Constant symbol</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="specific">
                                    <xs:annotation>
                                    <xs:documentation>Specific symbol</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element minOccurs="0" name="fees">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element minOccurs="0" name="changeFee">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="amount" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Amount of money</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency code of the amount</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="messageFee">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="amount" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Amount of money</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency code of the amount</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="transactionFee">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="amount" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Amount of money</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency code of the amount</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="feeDetails">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="feeDetail">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    minOccurs="0"
                                    name="ordinalNumber" type="xs:string"/>

                                    <xs:element
                                    name="amount" type="xs:decimal"/>

                                    <xs:element
                                    name="currencyCode" type="xs:string"/>

                                    <xs:element
                                    minOccurs="0"
                                    name="realizationDate" type="xs:dateTime"/>

                                    <xs:element
                                    minOccurs="0" name="initialAmount">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    minOccurs="0"
                                    name="amount" type="xs:decimal"/>

                                    <xs:element
                                    minOccurs="0"
                                    name="currencyCode" type="xs:string"/>

                                    <xs:element
                                    minOccurs="0"
                                    name="exchangeRate" type="xs:decimal"/>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0"
                                    name="shortDescription" type="xs:string"/>

                                    <xs:element
                                    minOccurs="0"
                                    name="description" type="xs:string"/>

                                    <xs:element
                                    minOccurs="0"
                                    name="actionTypeCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>[CODELIST: ACTION_TYPE_CODE]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0"
                                    name="bundelItemCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>[CODELIST: BundelItemCode]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="flags">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="allowRepeatTransaction" type="xs:boolean"/>
                                    <xs:element
                                    name="allowRefundTransaction" type="xs:boolean"/>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="originatorReferenceId">
                                <xs:annotation>
                                    <xs:documentation>Originator reference ID - End to End ID; The Originator’s reference of the transaction</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="35"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="transactionCategory" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>[CODELIST: TransactionCategory]</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="movementType" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>[CODELIST: MovementType]</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="actionTypeCode" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>[CODELIST: ACTION_TYPE_CODE]</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="orderIdentification">
                                <xs:annotation>
                                    <xs:documentation>Purchase order identification</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="trgReferenceId">
                                    <xs:annotation>
                                    <xs:documentation>Unique identification of the entity from provider point of view</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element minOccurs="0" name="resultInfo">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded"
                                minOccurs="1" name="resultItem">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="code" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A code of the error/warning [CODELIST: ERROR_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="description">
                                    <xs:annotation>
                                    <xs:documentation>A description of the error/warning</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="2000"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
