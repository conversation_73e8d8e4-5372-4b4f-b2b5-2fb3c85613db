<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2017 (http://www.altova.com) by <PERSON> (Raiffeisenbank a.s.) -->
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/services/entityService/cardProcessing/GetCCAAccountBalance_1_0"
    version="1.0.0"
    xmlns:opgetccaaccountbalance_1_0="http://rb.cz/services/entityService/cardProcessing/GetCCAAccountBalance_1_0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="opGetCCAAccountBalance_1_0">
        <xs:complexType>
            <xs:sequence>
                <xs:element maxOccurs="1" minOccurs="1" name="account">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="accountNumber">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="product" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Product code</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="branch" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Branch</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element name="valDate" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Requested period (in format YYYYMM)</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="opGetCCAAccountBalance_1_0_res">
        <xs:complexType>
            <xs:sequence>
                <xs:element maxOccurs="1" minOccurs="0" name="errCode" type="xs:integer">
                    <xs:annotation>
                        <xs:documentation>Error code
</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="account">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="accountNumber">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="product" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Product code</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="branch" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Branch</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="balance">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="1" minOccurs="0" name="otbInfo">
                                <xs:annotation>
                                    <xs:documentation>amount available for client in this moment</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="infoNode">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="currency" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency code</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="dc" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>D - debet, C - credit</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="amount" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Amount
</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="crlmInfo">
                                <xs:annotation>
                                    <xs:documentation>credit limit for client (fix value for credit account)</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="infoNode">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="currency" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency code</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="dc" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>D - debet, C - credit</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="amount" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Amount
</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="lpInfo">
                                <xs:annotation>
                                    <xs:documentation>payments in local currency from beginning of period till now</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="infoNode">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="currency" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency code</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="dc" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>D - debet, C - credit</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="amount" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Amount
</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="rpInfo">
                                <xs:annotation>
                                    <xs:documentation>payments in reference currency from beginning of period till now</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="infoNode">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="currency" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency code</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="dc" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>D - debet, C - credit</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="amount" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Amount
</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="bLocal">
                                <xs:annotation>
                                    <xs:documentation>balance for whole not actual period in local currency</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="infoNode">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="currency" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency code</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="dc" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>D - debet, C - credit</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="amount" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Amount
</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="bRef">
                                <xs:annotation>
                                    <xs:documentation>balance for whole not actual period in reference currency</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="infoNode">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="currency" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency code</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="dc" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>D - debet, C - credit</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="amount" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Amount
</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="pLocal">
                                <xs:annotation>
                                    <xs:documentation>sum of payments in local currency for whole nonactual period</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="infoNode">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="currency" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency code</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="dc" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>D - debet, C - credit</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="amount" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Amount
</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="pRef">
                                <xs:annotation>
                                    <xs:documentation>sum of payments in reference currency for whole nonactual period</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="infoNode">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="currency" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency code</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="dc" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>D - debet, C - credit</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="amount" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Amount
</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="mpLocal">
                                <xs:annotation>
                                    <xs:documentation>minimal payment counted for whole nonactual period. It depends on
payments and balance. In local currency</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="infoNode">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="currency" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency code</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="dc" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>D - debet, C - credit</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="amount" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Amount
</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="mpRef">
                                <xs:annotation>
                                    <xs:documentation>minimal payment counted for whole nonactual period. It depends on
payments and balance. In reference currency</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="infoNode">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="currency" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency code</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="dc" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>D - debet, C - credit</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="amount" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Amount
</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="cards">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded"
                                minOccurs="1" name="card">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="main" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Main ('true') or additional ('false')</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="cardHashNumber" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Card hash number</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="expirationDate" type="xs:string"/>
                                    <xs:element name="state" type="xs:integer"/>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="otb_card">
                                    <xs:annotation>
                                    <xs:documentation>Available balance for this credit card. Element with card available. This element is optional.</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="info_node">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    name="dc" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>D - debet, C - credit</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="amount" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Amount
</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="1" name="period">
                    <xs:annotation>
                        <xs:documentation>Number of periods.</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="count" type="xs:integer">
                                <xs:annotation>
                                    <xs:documentation>Number of periods</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="unbounded"
                                minOccurs="1" name="pInfo">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="valDate" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Period (format YYYYMM)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="startDate" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Start date
Value 999 means unknown start of the first period
</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="endDate" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Closing date
</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
