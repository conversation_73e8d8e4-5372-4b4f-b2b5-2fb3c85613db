<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/services/remoteCashAccount/GetBalanceHistory_1_1/1.0"
    version="1.0.0"
    xmlns:opgetbalancehistory_1_1="http://rb.cz/services/remoteCashAccount/GetBalanceHistory_1_1/1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="opGetBalanceHistory_1_1">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="1" name="psu">
                    <xs:annotation>
                        <xs:documentation>Payment Service User</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="IpAddress" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Identifier of a customer's IP address from which he/she is connected to the TPP infrastructure. It might be in the format of IPv4 o IPv6 address. ASPSP shall indicate which values are acceptable</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element minOccurs="0" name="deviceOS" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Customer's device and/or operating system identification from which he/she is connected to the TPP infrastructure</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element minOccurs="0" name="userAgent" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Customer's web browser of other client device identification from which he/she is connected to the TPP infrastructure. Agent header field of the http request between PSU and TPP.)</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element minOccurs="0" name="session" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>A customer session id</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element minOccurs="0" name="id" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Unique customer identification</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element name="userId" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>External ID of the user/client in remote bank (hash)</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element minOccurs="0" name="providerId" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Provider ID (hash) of external provider</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element minOccurs="0" name="subscriptionId" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Subscription ID</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="searchingCriteria">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded"
                                minOccurs="1" name="accountId" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Account ID</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element name="sourceType">
                    <xs:annotation>
                        <xs:documentation>The way how the response data will be obtained:
ONLINE - slow but fresh data are provided because remote bank/provider is called synchronously. If there was a problem with calling remote bank, then an error is raised and it is returned under errors structure for given account (it is associated with subscriptionId). The data and errors are stored in cache.
OFFLINE – fast, data are from cache (data are refreshed 4 times a day by job or by calling the service in ONLINE or OFFLINE_WITH_REFRESH). Errors are provided if previous call (ONLINE of OFFLINE_WITH_REFRESH) to the remote bank failed with error.
OFFLINE_WITH_REFRESH - same as OFFLINE but in addition a remote bank is called asynchronously to get fresh data that are then stored to the cache (Galileo). In case there is an error to get data from the remote bank during the refresh, the error is stored into cache. 
 [LOV: ONLINE;OFFLINE;OFFLINE_WITH_REFRESH]</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:enumeration value="ONLINE"/>
                            <xs:enumeration value="OFFLINE"/>
                            <xs:enumeration value="OFFLINE_WITH_REFRESH"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="opGetBalanceHistory_1_1_res">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="balances">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded" name="balance">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element minOccurs="1" name="balanceAmount">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="amount">
                                    <xs:annotation>
                                    <xs:documentation>Amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of amount [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="balanceType">
                                    <xs:annotation>
                                    <xs:documentation>The following balance types are defined:
 - "openingBooked":
    Book balance of the account at the beginning of the account reporting period.
    It always equals the closing book balance from the previous report.
  - "closingBooked": 
    Balance of the account at the end of the pre-agreed account reporting period. 
    It is the sum of the opening booked balance at the beginning of the period and all entries booked 
    to the account during the pre-agreed account reporting period.
    For card-accounts, this is composed of
      - invoiced, but not yet paid entries
  - "interimAvailable":
    Available balance calculated in the course of the account servicers business day, 
    at the time specified, and subject to further changes during the business day. 
    The interim balance is calculated on the basis of booked credit and debit items during the calculation 
    time/period specified.
    For card-accounts, this is composed of
      - invoiced, but not yet paid entries, 
      - not yet invoiced but already booked entries
  - "expected":
    Balance composed of booked entries and pending items known at the time of calculation, 
    which projects the end of day balance if everything is booked on the account and no other entry is posted
    For card accounts, this is composed of 
      - invoiced, but not yet paid entries, 
      - not yet invoiced but already booked entries and
      - pending items (not yet booked)
  - "authorised":
    The expected balance together with the value of a pre-approved credit line the ASPSP makes permanently available to the user.
    For card-accounts:
    "money to spend with the value of a pre-approved credit limit on the card account"
  - "forwardAvailable":
    Forward available balance of money that is at the disposal of the account owner on the date specified.
  - "nonInvoiced": 
    Only for card accounts, to be checked yet.
  - "other":
    Some type of balance which is not recognized [LOV: openingBooked;closingBooked;interimAvailable;expected;authorised;forwardAvailable;nonInvoiced;other]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:enumeration value="openingBooked"/>
                                    <xs:enumeration value="closingBooked"/>
                                    <xs:enumeration value="interimAvailable"/>
                                    <xs:enumeration value="expected"/>
                                    <xs:enumeration value="authorised"/>
                                    <xs:enumeration value="forwardAvailable"/>
                                    <xs:enumeration value="nonInvoiced"/>
                                    <xs:enumeration value="other"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="referenceDate" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Reference date of the balance lastCommittedTransaction. Date of the balance. It is obtained from the other bank.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element minOccurs="0" name="lastUpdateDate" type="xs:dateTime">
                    <xs:annotation>
                        <xs:documentation>Date of last actualization of the record in aggregator (GAG)</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
