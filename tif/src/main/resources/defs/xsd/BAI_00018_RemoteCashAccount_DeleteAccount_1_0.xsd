<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/services/remoteCashAccount/DeleteAccount_1_0/1.0"
    version="1.0.0"
    xmlns:opdeleteaccount_1_0="http://rb.cz/services/remoteCashAccount/DeleteAccount_1_0/1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="opDeleteAccount_1_0">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="userId" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>External ID of the user/client in remote bank (hash)</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="subscriptionId" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Subscription ID</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element minOccurs="0" name="accountId" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>ResourceId from list of account</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element minOccurs="0" name="permanentDelete" type="xs:boolean">
                    <xs:annotation>
                        <xs:documentation>Flag wheter the data will be removed permanetly or archived only.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="opDeleteAccount_1_0_res">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
</xs:schema>
