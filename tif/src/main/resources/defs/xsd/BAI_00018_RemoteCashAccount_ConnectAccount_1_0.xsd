<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/services/remoteCashAccount/ConnectAccount_1_0/1.0"
    version="1.0.0"
    xmlns:opconnectaccount_1_0="http://rb.cz/services/remoteCashAccount/ConnectAccount_1_0/1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="opConnectAccount_1_0">
        <xs:complexType>
            <xs:sequence>
                <xs:choice>
                    <xs:element minOccurs="1" name="partyId">
                        <xs:annotation>
                            <xs:documentation>Party Id (Siebel identifier)</xs:documentation>
                        </xs:annotation>
                        <xs:simpleType>
                            <xs:restriction base="xs:string">
                                <xs:maxLength value="30"/>
                            </xs:restriction>
                        </xs:simpleType>
                    </xs:element>
                    <xs:sequence>
                        <xs:element minOccurs="1" name="userId" type="xs:string">
                            <xs:annotation>
                                <xs:documentation>External ID of the user/client in remote bank (hash)</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element minOccurs="0" name="subscriptionId" type="xs:string">
                            <xs:annotation>
                                <xs:documentation>Subscription ID</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:choice>
                <xs:element minOccurs="1" name="providerId" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Provider ID (hash) of external provider</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="1" name="service">
                    <xs:annotation>
                        <xs:documentation>Service to which the client subscribes. E.g. AISP only read access. PISP only payment orders, CISP only cards. [LOV: AISP;PISP;CISP]</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:enumeration value="AISP"/>
                            <xs:enumeration value="PISP"/>
                            <xs:enumeration value="CISP"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element minOccurs="1" name="redirectUrl" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Redirect URL to deliver authorization code after the process of authorization is finished in the remote provider/bank.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="opConnectAccount_1_0_res">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="1" name="userId" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>External ID of the user/client in remote bank (hash)</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element minOccurs="1" name="subscriptionId" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Subscription ID</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element minOccurs="1" name="authUrl" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>URL of the authorization endpoint - where user must be redirected to finish authorization process in the remote account provider.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
