<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/services/cashAccount/GetCashWithdrawalNoticeList_1_0/1.0"
    version="1.0.0"
    xmlns:opgetcashwithdrawalnoticelist_1_0="http://rb.cz/services/cashAccount/GetCashWithdrawalNoticeList_1_0/1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="opGetCashWithdrawalNoticeList_1_0">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="paging">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element minOccurs="0" name="startIndex" type="xs:integer">
                                <xs:annotation>
                                    <xs:documentation>Index of the first item within the returned collection. Default value is 1</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="maxResults" type="xs:integer">
                                <xs:annotation>
                                    <xs:documentation>Maximal number of returned collection items</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="1" name="searchingCriteria">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="accountNumber">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="bankCode">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="7"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="currencyCode">
                                    <xs:annotation>
                                    <xs:documentation>Currency of the bank account</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="3"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element minOccurs="0" name="intervalCreate">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element minOccurs="0"
                                    name="dateFrom" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Create date from</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="dateTo" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Create date to</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="opGetCashWithdrawalNoticeList_1_0_res">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="paging">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="startIndex" type="xs:integer">
                                <xs:annotation>
                                    <xs:documentation>Index of the first item within the returned collection. Default value is 1</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="endIndex" type="xs:integer">
                                <xs:annotation>
                                    <xs:documentation>Index of the last item within the returned collection</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="endOfRecords" type="xs:boolean">
                                <xs:annotation>
                                    <xs:documentation>Flag indicating end of the records</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element minOccurs="0" name="cashWithdrawalNotices">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded" name="cashWithdrawalNotice">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="identification">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="trgReferenceId">
                                    <xs:annotation>
                                    <xs:documentation>Unique identification of the entity from provider point of view</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="accountNumber">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="bankCode">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="7"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="currencyCode">
                                    <xs:annotation>
                                    <xs:documentation>Currency of the bank account</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="3"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="createDate" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Date of withdrawal request creation</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="withdrawalDate" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Date of physical withdrawal</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="branch" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>[CODELIST: Branch]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="withdrawalAmount">
                                    <xs:annotation>
                                    <xs:documentation>Amount of withdrawal</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="amount" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Amount of money</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="currencyCode">
                                    <xs:annotation>
                                    <xs:documentation>Currency code of the amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="3"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="note">
                                    <xs:annotation>
                                    <xs:documentation>Withdrawal note</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="254"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="status" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>[CODELIST: CashWithdrawalNoticeStatus]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="nominals">
                                    <xs:annotation>
                                    <xs:documentation>Currency nominals requested for withdrawal</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="nominal">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    name="currencyNominal" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>[CODELIST: CurrencyNominal]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="count" type="xs:integer"/>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element minOccurs="0" name="resultInfo">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded"
                                minOccurs="1" name="resultItem">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="code" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A code of the error/warning [CODELIST: ERROR_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="description">
                                    <xs:annotation>
                                    <xs:documentation>A description of the error/warning</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="2000"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
