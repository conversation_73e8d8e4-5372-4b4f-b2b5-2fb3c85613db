<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/services/bpm/GetTaskList_1_0/1.0"
    version="1.0.0"
    xmlns:opgettasklist_1_0="http://rb.cz/services/bpm/GetTaskList_1_0/1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="opGetTaskList_1_0">
        <xs:complexType>
            <xs:sequence>
                <xs:element maxOccurs="1" minOccurs="0" name="model" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>The name of the
 process model. Restricts the results to instances of the specified process model.
	</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="processId" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>The ID of the process instance.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="taskStates">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded"
                                minOccurs="1" name="state">
                                <xs:annotation>
                                    <xs:documentation>The acronym of a state of the process application [LOV: claimed;ready;completed;terminated;suspended]</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:enumeration value="claimed"/>
                                    <xs:enumeration value="ready"/>
                                    <xs:enumeration value="completed"/>
                                    <xs:enumeration value="terminated"/>
                                    <xs:enumeration value="suspended"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0"
                    name="returnOffset" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>In a list of entries the offset specifies the position of the first process instance to return from the query result set. </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0"
                    name="returnSize" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Maximum number of process instances to return. </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="optionalParts">
                    <xs:annotation>
                        <xs:documentation>A comma-separated list of optional parts to be returned in the response object. Valid values are: 'data’, 'actions’.</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded"
                                minOccurs="1" name="optionalPart" nillable="false">
                                <xs:annotation>
                                    <xs:documentation>Optional part to be returned in the response object. Valid values are: 'data', 'actions', 'team_details', 'container_data'. 
Values  'team_details', 'container_data' use only for TaskUser. [LOV: data;actions;team_details;container_data]</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:enumeration value="data"/>
                                    <xs:enumeration value="actions"/>
                                    <xs:enumeration value="team_details"/>
                                    <xs:enumeration value="container_data"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="taskSort">
                    <xs:annotation>
                        <xs:documentation>A comma-separated list of sort criteria. The order of the items determines the sorting sequence. The list entries must have the following format: property:sort_direction. Valid values are: 'creation_time:asc’, 'creation_time:desc’, 'completion_time:asc’, 'completion_time:desc’, 'due_date:asc’, 'due_date:desc’. </xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded"
                                minOccurs="1" name="sortBy" nillable="false">
                                <xs:annotation>
                                    <xs:documentation>[LOV: creation_time:asc;creation_time:desc;completion_time:asc;completion_time:desc;due_date:asc;due_date:desc]</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:enumeration value="creation_time:asc"/>
                                    <xs:enumeration value="creation_time:desc"/>
                                    <xs:enumeration value="completion_time:asc"/>
                                    <xs:enumeration value="completion_time:desc"/>
                                    <xs:enumeration value="due_date:asc"/>
                                    <xs:enumeration value="due_date:desc"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="opGetTaskList_1_0_res">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="tasks">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded"
                                minOccurs="0" name="task">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="taskId" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>User task instance ID.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="taskName" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The name of the user task instance.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="displayName" type="xs:string"/>
                                    <xs:element name="model" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The name of the
 process model. Restricts the results to instances of the specified process model.
	</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="processId" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The ID of the process instance.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="processName" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The name of the user task instance.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="creationTime" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>The activation time.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="completionTime" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The completion time of the task instance. </xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="dueDate" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>The due date of the task instance.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="startTime" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The completion time of the task instance. </xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="atRiskTime" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The at-risk time of the task instance. </xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="owner" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The owner of the user task. </xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="priority" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The priority of the task instance. The lower the value, the higher the priority.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="container" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The acronym of the process application that contains the process model.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="containerName" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The name of the
 process application that contains the user task instance.
</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="version" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The acronym of the process application snapshot that contains the process model. If a version is not specified, the instance is started from the default snapshot in a Process Server environment and from the latest snapshot in a Process Center environment.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="versionName" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The name of the process application snapshot that contains the user task instance.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="branchName" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The name of the branch of the process application that contains the user task instance.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="versionId" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The ID of the process application snapshot that contains the user task instance.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="state">
                                    <xs:annotation>
                                    <xs:documentation>The acronym of a state of the process application [LOV: claimed;ready;completed;terminated;suspended]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:enumeration value="claimed"/>
                                    <xs:enumeration value="ready"/>
                                    <xs:enumeration value="completed"/>
                                    <xs:enumeration value="terminated"/>
                                    <xs:enumeration value="suspended"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="assignments">
                                    <xs:annotation>
                                    <xs:documentation>User task instance role assignments.</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="potentialOwners">
                                    <xs:annotation>
                                    <xs:documentation>Potential owner assignments for the user task instance.</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    minOccurs="0" name="team">
                                    <xs:annotation>
                                    <xs:documentation>Team instance.</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    name="modelId" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The ID of the team model.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="teamId" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The member of the team or manager team instance. </xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="teamName" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The name of the team.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="members">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="0"
                                    name="member" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The member of the manager team instance. </xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="managerTeam">
                                    <xs:annotation>
                                    <xs:documentation>Manager Team instance</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="teamId" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The member of the team or manager team instance. </xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="teamName" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The name of the team.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="members">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="0"
                                    name="member" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The member of the manager team instance. </xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="actions">
                                    <xs:annotation>
                                    <xs:documentation>Actions available to the current user for the user task instance.</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="0"
                                    name="action" nillable="false">
                                    <xs:annotation>
                                    <xs:documentation>Action available to be
						performed on the user task instance.
					 [LOV: assign;update_due_date;update_priority;claim;cancel_claim;complete;view;set_data]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="assign"/>

                                    <xs:enumeration value="update_due_date"/>

                                    <xs:enumeration value="update_priority"/>

                                    <xs:enumeration value="claim"/>

                                    <xs:enumeration value="cancel_claim"/>

                                    <xs:enumeration value="complete"/>

                                    <xs:enumeration value="view"/>

                                    <xs:enumeration value="set_data"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="input">
                                    <xs:annotation>
                                    <xs:documentation>Variables of the user task.
						</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="0" name="dataObject">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="name"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The name of the data
						input object, data output object, or variable.
					</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element name="data">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="0" name="field">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    name="name" type="xs:string"/>

                                    <xs:element name="type">
                                    <xs:annotation>
                                    <xs:documentation>[LOV: string;integer;float;boolean]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="string"/>

                                    <xs:enumeration value="integer"/>

                                    <xs:enumeration value="float"/>

                                    <xs:enumeration value="boolean"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="value" type="xs:string"/>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="output">
                                    <xs:annotation>
                                    <xs:documentation>Variables of the user task.
						</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="0" name="dataObject">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="name"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The name of the data
						input object, data output object, or variable.
					</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element name="data">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="0" name="field">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    name="name" type="xs:string"/>

                                    <xs:element name="type">
                                    <xs:annotation>
                                    <xs:documentation>[LOV: string;integer;float;boolean]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="string"/>

                                    <xs:enumeration value="integer"/>

                                    <xs:enumeration value="float"/>

                                    <xs:enumeration value="boolean"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="value" type="xs:string"/>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="internal">
                                    <xs:annotation>
                                    <xs:documentation>Variables of the user task.
						</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="0" name="dataObject">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="name"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The name of the data
						input object, data output object, or variable.
					</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element name="data">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="0" name="field">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    name="name" type="xs:string"/>

                                    <xs:element name="type">
                                    <xs:annotation>
                                    <xs:documentation>[LOV: string;integer;float;boolean]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="string"/>

                                    <xs:enumeration value="integer"/>

                                    <xs:enumeration value="float"/>

                                    <xs:enumeration value="boolean"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="value" type="xs:string"/>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0"
                    name="previousUrl" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>A URL that returns the previous page of process instances. </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="nextUrl" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>A URL that returns the next page of process instances.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
