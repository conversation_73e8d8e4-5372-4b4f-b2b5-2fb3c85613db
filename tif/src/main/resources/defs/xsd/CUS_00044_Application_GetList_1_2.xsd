<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/services/application/GetList_1_2/1.2"
    version="1.0.0"
    xmlns:opgetlist_1_2="http://rb.cz/services/application/GetList_1_2/1.2" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="opGetList_1_2">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="searchingCriteria">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="partyId">
                                <xs:annotation>
                                    <xs:documentation>Party Id (Siebel identifier)</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="dateFrom" type="xs:dateTime">
                                <xs:annotation>
                                    <xs:documentation>Date of creation of application (YYYY-MM-DD)</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="opGetList_1_2_res">
        <xs:complexType>
            <xs:sequence>
                <xs:element maxOccurs="1" minOccurs="0" name="applications">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded"
                                minOccurs="1" name="application">
                                <xs:annotation>
                                    <xs:documentation>zakladni info pro seznam zadosti ze sieblu</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="entityId" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Siebel Application ID</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="srcRefId" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Business Id of application from primary system</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="applicationChannel" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>application source channel [CODELIST: Component]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="submitChannel" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>channel where the application was submitted [CODELIST: OPTY_CHANNEL_TYPE]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="applicationStatus" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Global status of an application [CODELIST: ApplicationStatus]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="applicationId">
                                    <xs:annotation>
                                    <xs:documentation>Technical Id - Unique identification of an application from primary system</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="terms">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="createdDate" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>date when the application was created in the target system</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="expiryDate" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>date when the application expired</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="approvalDate" type="xs:dateTime"/>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="statusDescription" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>application status - free text </xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="productDetail">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="mprodCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>MPROD code from product catalogue</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="productCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Siebel product code</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="productCategory" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>IDCATEGORY from product catalogue</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="productName" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>X_SHORT_NAME from product catalogue</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="productType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Product codes codebook for MultiChannel [CODELIST: ProductCodes]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="limits">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>currency code of the application [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="requestedAmount">
                                    <xs:annotation>
                                    <xs:documentation>requested amount (by client) to be approved</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="totalLimitAmount">
                                    <xs:annotation>
                                    <xs:documentation>total limit being approved (by bank)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
