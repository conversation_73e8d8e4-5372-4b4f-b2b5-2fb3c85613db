<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/services/entityService/cardProcessing/UpdateBIRestriction_1_0"
    version="1.0.0"
    xmlns:opupdatebirestriction_1_0="http://rb.cz/services/entityService/cardProcessing/UpdateBIRestriction_1_0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="opUpdateBIRestriction_1_0">
        <xs:complexType>
            <xs:sequence>
                <xs:element maxOccurs="1" minOccurs="1" name="pan" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>A payment card number</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="mti">
                    <xs:annotation>
                        <xs:documentation>MTI</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:integer">
                            <xs:minInclusive value="0"/>
                            <xs:maxInclusive value="9999"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element name="proc_code">
                    <xs:annotation>
                        <xs:documentation>Process code</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:integer">
                            <xs:minInclusive value="0"/>
                            <xs:maxInclusive value="9999"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element name="action">
                    <xs:annotation>
                        <xs:documentation>Type of action:
A - add (activate rule)
D - delete (deactivate rule) [LOV: A;D]</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:enumeration value="A"/>
                            <xs:enumeration value="D"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="opUpdateBIRestriction_1_0_res">
        <xs:complexType>
            <xs:sequence>
                <xs:element maxOccurs="1" minOccurs="0" name="pan" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>A payment card number</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="mti">
                    <xs:annotation>
                        <xs:documentation>MTI</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:integer">
                            <xs:minInclusive value="0"/>
                            <xs:maxInclusive value="9999"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="proc_code">
                    <xs:annotation>
                        <xs:documentation>Process code</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:integer">
                            <xs:minInclusive value="0"/>
                            <xs:maxInclusive value="9999"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="status" type="xs:integer">
                    <xs:annotation>
                        <xs:documentation>Card status</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
