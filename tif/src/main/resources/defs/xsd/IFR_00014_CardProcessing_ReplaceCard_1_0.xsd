<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/services/entityService/cardProcessing/ReplaceCard_1_0"
    version="1.0.0"
    xmlns:opreplacecard_1_0="http://rb.cz/services/entityService/cardProcessing/ReplaceCard_1_0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="opReplaceCard_1_0">
        <xs:complexType>
            <xs:sequence>
                <xs:element maxOccurs="1" minOccurs="1" name="pan" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>A payment card number</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="1" name="embossedName">
                    <xs:annotation>
                        <xs:documentation>Card holder embossed name</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:maxLength value="27"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element name="generatePlasticFlag" type="xs:boolean">
                    <xs:annotation>
                        <xs:documentation>For generate plastic from target system.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="1"
                    name="urgentCardFlag" type="xs:boolean">
                    <xs:annotation>
                        <xs:documentation>Flag if card is urgent</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="cardType">
                    <xs:annotation>
                        <xs:documentation>Type of card</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:integer">
                            <xs:minInclusive value="0"/>
                            <xs:maxInclusive value="99"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element name="serviceAmortizeFlag" type="xs:boolean">
                    <xs:annotation>
                        <xs:documentation>If amortize service are available.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="renovationFlag" type="xs:boolean"/>
                <xs:element maxOccurs="1" minOccurs="0" name="pin">
                    <xs:annotation>
                        <xs:documentation>Card pin</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:minLength value="0"/>
                            <xs:maxLength value="16"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="opReplaceCard_1_0_res">
        <xs:complexType>
            <xs:sequence>
                <xs:element maxOccurs="1" minOccurs="0" name="card">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="1" minOccurs="1"
                                name="pan" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>A payment card number</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="1"
                                name="csn" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Czech norm</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
