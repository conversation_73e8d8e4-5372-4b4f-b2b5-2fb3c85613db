<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/services/entityService/cardProcessing/UpdateOptionalDocuments_1_0"
    version="1.0.0"
    xmlns:opupdateoptionaldocuments_1_0="http://rb.cz/services/entityService/cardProcessing/UpdateOptionalDocuments_1_0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="opUpdateOptionalDocuments_1_0">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="accountkey">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="issuer">
                                <xs:annotation>
                                    <xs:documentation>Issuer of card</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:integer">
                                    <xs:minInclusive value="0"/>
                                    <xs:maxInclusive value="9999"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="branch">
                                <xs:annotation>
                                    <xs:documentation>Branch</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:integer">
                                    <xs:minInclusive value="0"/>
                                    <xs:maxInclusive value="99999"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="product">
                                <xs:annotation>
                                    <xs:documentation>Product code</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:integer">
                                    <xs:minInclusive value="0"/>
                                    <xs:maxInclusive value="99"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="1" name="accountNumber">
                                <xs:annotation>
                                    <xs:documentation>Card account number (CBS account)</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:integer">
                                    <xs:minInclusive value="0"/>
                                    <xs:maxInclusive value="*********"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element name="document">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="type">
                                <xs:annotation>
                                    <xs:documentation>Document type</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:integer">
                                    <xs:maxInclusive value="99"/>
                                    <xs:minInclusive value="0"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="doc">
                                <xs:annotation>
                                    <xs:documentation>Particular document</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:minLength value="1"/>
                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="operation">
                                <xs:annotation>
                                    <xs:documentation>Operation with document [LOV: U;A;D]</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:enumeration value="U"/>
                                    <xs:enumeration value="A"/>
                                    <xs:enumeration value="D"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="validity">
                                <xs:annotation>
                                    <xs:documentation>Validity of document</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:integer">
                                    <xs:minInclusive value="19000101"/>
                                    <xs:maxInclusive value="29991231"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element name="optionalDocument">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="type">
                                <xs:annotation>
                                    <xs:documentation>Document type</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:integer">
                                    <xs:maxInclusive value="99"/>
                                    <xs:minInclusive value="0"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="docNew">
                                <xs:annotation>
                                    <xs:documentation>Particular document</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:minLength value="1"/>
                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="docOld">
                                <xs:annotation>
                                    <xs:documentation>Particular document</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:minLength value="1"/>
                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="opUpdateOptionalDocuments_1_0_res">
        <xs:complexType>
            <xs:sequence>
                <xs:element maxOccurs="1" minOccurs="0" name="accountkey">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="issuer">
                                <xs:annotation>
                                    <xs:documentation>Issuer of card</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:integer">
                                    <xs:minInclusive value="0"/>
                                    <xs:maxInclusive value="9999"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="branch">
                                <xs:annotation>
                                    <xs:documentation>Branch</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:integer">
                                    <xs:minInclusive value="0"/>
                                    <xs:maxInclusive value="99999"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="product">
                                <xs:annotation>
                                    <xs:documentation>Product code</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:integer">
                                    <xs:minInclusive value="0"/>
                                    <xs:maxInclusive value="99"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="1" name="accountNumber">
                                <xs:annotation>
                                    <xs:documentation>Card account number (CBS account)</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:integer">
                                    <xs:minInclusive value="0"/>
                                    <xs:maxInclusive value="*********"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="document">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="type">
                                <xs:annotation>
                                    <xs:documentation>Document type</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:integer">
                                    <xs:maxInclusive value="99"/>
                                    <xs:minInclusive value="0"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="doc">
                                <xs:annotation>
                                    <xs:documentation>Particular document</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:minLength value="1"/>
                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="validity">
                                <xs:annotation>
                                    <xs:documentation>Validity of document</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:integer">
                                    <xs:minInclusive value="19000101"/>
                                    <xs:maxInclusive value="29991231"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="optionalDocument">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="type">
                                <xs:annotation>
                                    <xs:documentation>Document type</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:integer">
                                    <xs:maxInclusive value="99"/>
                                    <xs:minInclusive value="0"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="doc">
                                <xs:annotation>
                                    <xs:documentation>Particular document</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:minLength value="1"/>
                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
