<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/services/loan/GetOfferParam_1_5/1.0"
    version="1.0.0"
    xmlns:opgetofferparam_1_5="http://rb.cz/services/loan/GetOfferParam_1_5/1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="opGetOfferParam_1_5">
        <xs:complexType>
            <xs:sequence>
                <xs:choice>
                    <xs:element name="getOffer">
                        <xs:complexType>
                            <xs:sequence>
                                <xs:element minOccurs="0" name="applicationId">
                                    <xs:annotation>
                                    <xs:documentation>Unique identification of an application</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                                <xs:element name="offerId">
                                    <xs:annotation>
                                    <xs:documentation>Unique identification of an offer. O (zero) for default loan.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="512"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                                <xs:element name="partyId">
                                    <xs:annotation>
                                    <xs:documentation>Loan owner party id (Siebel identifier)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                                <xs:element minOccurs="0"
                                    name="campaignType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Campaign type.</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element minOccurs="0"
                                    name="occupationType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Loan owner occupation type. [CODELIST: OccupationType]</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element minOccurs="0" name="requestedAmount">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="amount">
                                    <xs:annotation>
                                    <xs:documentation>Loan amount requested by user.  If NULL then maximal offered amount is used.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:decimal">
                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of amount [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element minOccurs="0" name="requestedInstalment">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element minOccurs="1"
                                    name="instalmentCount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Requested instalment count. If NULL then greatest available number is used.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="1"
                                    name="frequencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Time unit for requestedInstalmentCount. [CODELIST: FrequencyType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element maxOccurs="1" minOccurs="0" name="bonusCampaign">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="bonusCampaign" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>flag if it is a bonus campaign (True for bonus campaign)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="bonusInterestRate">
                                    <xs:annotation>
                                    <xs:documentation>Interest rate of bonus campaign</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:decimal">
                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element maxOccurs="1" minOccurs="0" name="interestRateDiscount">
                                    <xs:annotation>
                                    <xs:documentation>Lowering of standard interest rate (percentage point decrease)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:decimal">
                                    <xs:totalDigits value="18"/>
                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                                <xs:element maxOccurs="1" minOccurs="0"
                                    name="mortgage" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>If client has already mortgage</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element minOccurs="0"
                                    name="loanRepayment" type="xs:decimal"/>
                                <xs:element minOccurs="0"
                                    name="overdraftLimit" type="xs:decimal"/>
                                <xs:element minOccurs="0"
                                    name="livingCosts" type="xs:decimal"/>
                                <xs:element minOccurs="0" name="dataDS1">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="employmentInfo">
                                    <xs:annotation>
                                    <xs:documentation>Informations about a employment</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="maritalStatus" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Marital status - married, single, widowed, divorced,...</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="accomodationType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Type of accomodation</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="educationLevel" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>The highest level of an education</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="position" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Position</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="businessField" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Business field</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="employerName">
                                    <xs:annotation>
                                    <xs:documentation>Name of employer</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="256"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="employerIco">
                                    <xs:annotation>
                                    <xs:documentation>Employer commercial registration number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="8"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="dates">
                                    <xs:annotation>
                                    <xs:documentation>Dates when jobs have been started</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    minOccurs="0" name="currentJob">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    name="from" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Current job from date</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="civilServant" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Civil servant</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="contacts">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    minOccurs="0" name="residencyDate">
                                    <xs:annotation>
                                    <xs:documentation>Since when a client stayes at the residency address.</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    name="from" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Residence from date</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="residenceType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Druh pobytu u cizincu ciselnik LST028</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element minOccurs="0" name="otherData">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element minOccurs="0"
                                    name="sellerChannel" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Channel – LST072_0008  (SPB),–	LST072_0005 (GIB), last used channel.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="sellerId">
                                    <xs:annotation>
                                    <xs:documentation>ID of seller</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="solusConsent" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Agreement with SOLUS</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="nrkiConsent" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Agreement with NRKI</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="registerAgreement" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>register Agreement</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="preContractConsent" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Agreement with precontract information</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="telcoScoreAgreement" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Telco score agreement.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="telcoScoreContent" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Telco score content.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                            </xs:sequence>
                        </xs:complexType>
                    </xs:element>
                    <xs:element name="getOfferMIC">
                        <xs:complexType>
                            <xs:sequence>
                                <xs:element name="partyId">
                                    <xs:annotation>
                                    <xs:documentation>Loan owner party id (Siebel identifier)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                                <xs:element name="productType">
                                    <xs:annotation>
                                    <xs:documentation>Enumeration of requested product type. PK for overdraft and PRP for repayment loan. [LOV: PK;PRP]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:enumeration value="PK"/>
                                    <xs:enumeration value="PRP"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                                <xs:element minOccurs="0" name="requestedAmount">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="amount">
                                    <xs:annotation>
                                    <xs:documentation>Loan amount requested by user.  If NULL then maximal offered amount is used.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:decimal">
                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of amount [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element minOccurs="0" name="requestedInstalment">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element minOccurs="1"
                                    name="instalmentCount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Requested instalment count. If NULL then greatest available number is used.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                            </xs:sequence>
                        </xs:complexType>
                    </xs:element>
                    <xs:element name="preEvaluate">
                        <xs:complexType>
                            <xs:sequence>
                                <xs:element minOccurs="0" name="applicationId">
                                    <xs:annotation>
                                    <xs:documentation>Unique identification of an application</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                                <xs:element name="offerId">
                                    <xs:annotation>
                                    <xs:documentation>Unique identification of an offer</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="512"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                                <xs:element name="partyId">
                                    <xs:annotation>
                                    <xs:documentation>Loan owner party id (Siebel identifier)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                                <xs:element minOccurs="0"
                                    name="campaignType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Campaign type.</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element minOccurs="1" name="requestedAmount">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="amount">
                                    <xs:annotation>
                                    <xs:documentation>Loan amount requested by user.  If NULL then maximal offered amount is used.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:decimal">
                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of amount [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element minOccurs="0" name="requestedInstalment">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element minOccurs="1"
                                    name="instalmentCount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Requested instalment count. If NULL then greatest available number is used.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="1"
                                    name="frequencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Time unit for requestedInstalmentCount. [CODELIST: FrequencyType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element maxOccurs="1" minOccurs="0" name="bonusCampaign">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="bonusCampaign" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>flag if it is a bonus campaign (True for bonus campaign)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element maxOccurs="1" minOccurs="0" name="interestRateDiscount">
                                    <xs:annotation>
                                    <xs:documentation>Lowering of standard interest rate (percentage point decrease)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:decimal">
                                    <xs:fractionDigits value="5"/>
                                    <xs:totalDigits value="18"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                                <xs:element maxOccurs="1" minOccurs="1"
                                    name="mortgage" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>If client has already mortgage</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element name="netIncome" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Total net income declared by customer.</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element minOccurs="1"
                                    name="loanRepayment" type="xs:decimal"/>
                                <xs:element minOccurs="1"
                                    name="overdraftLimit" type="xs:decimal"/>
                                <xs:element minOccurs="1"
                                    name="livingCosts" type="xs:decimal"/>
                                <xs:element minOccurs="0"
                                    name="occupationType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Loan owner occupation type. [CODELIST: OccupationType]</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element minOccurs="0" name="proofOfIncome">
                                    <xs:annotation>
                                    <xs:documentation>Proof of income data</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="type"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Type of income proof - (CODEBOOK-LST183)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="incomeProof">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    minOccurs="0" name="netIncome1">
                                    <xs:annotation>
                                    <xs:documentation>Net income of 1. month</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="15"/>

                                    <xs:fractionDigits value="2"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="netIncome2">
                                    <xs:annotation>
                                    <xs:documentation>Net income of 2. month</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="15"/>

                                    <xs:fractionDigits value="2"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="netIncome3">
                                    <xs:annotation>
                                    <xs:documentation>Net income of 3. month</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:fractionDigits value="2"/>

                                    <xs:totalDigits value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="taxReturn">
                                    <xs:annotation>
                                    <xs:documentation>Tax return data</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="sumIncomeEmpl" nillable="true">
                                    <xs:annotation>
                                    <xs:documentation>Úhrn příjmů od všech zaměstnavatelů (řádek 31)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:integer">

                                    <xs:totalDigits value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="sumBTax"
                                    nillable="true" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Úhrn dílčích základů daně (řádek 41a)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="tax"
                                    nillable="true" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Daň po uplatnění slev (řádek 74)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="taxPeriod"
                                    nillable="true" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Zdanovaci obdobi</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element minOccurs="0"
                                    name="sellerChannel" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Channel – LST072_0008  (SPB),–	LST072_0005 (GIB), last used channel.</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                            </xs:sequence>
                        </xs:complexType>
                    </xs:element>
                    <xs:element name="preEvaluateMIC">
                        <xs:complexType>
                            <xs:sequence>
                                <xs:element name="sellerChannel">
                                    <xs:annotation>
                                    <xs:documentation>(SPB|GIB) last used channel.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="3"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                                <xs:element name="userId">
                                    <xs:annotation>
                                    <xs:documentation>Siebel party ID or personal number (CZA) of a user which innitiated call of this service.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                                <xs:element name="userIdType">
                                    <xs:annotation>
                                    <xs:documentation>[LOV: PersonalNumber;PartyId]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:enumeration value="PersonalNumber"/>
                                    <xs:enumeration value="PartyId"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                                <xs:element minOccurs="1" name="applicationId">
                                    <xs:annotation>
                                    <xs:documentation>Unique identification of an application</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                                <xs:element name="productType">
                                    <xs:annotation>
                                    <xs:documentation>Enumeration of requested product type. PK for overdraft and PRP for repayment loan. [LOV: PK;PRP]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:enumeration value="PK"/>
                                    <xs:enumeration value="PRP"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                                <xs:element maxOccurs="1" minOccurs="1" name="applicationParameters">
                                    <xs:annotation>
                                    <xs:documentation>Technical - Application parameters containter</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="naceCode">
                                    <xs:annotation>
                                    <xs:documentation>European statistical classification of economic activities</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="peerGroup" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Group of individuals or entities that share similar characteristics (Profession Group)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="employeesNo" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>The number of employees</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="phone" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Phone</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="email" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Email</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element maxOccurs="1" minOccurs="0" name="listOfCompanyOwners">
                                    <xs:annotation>
                                    <xs:documentation>Technical - Company owners container</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="owner">
                                    <xs:annotation>
                                    <xs:documentation>Technical - Company owner item</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    minOccurs="0"
                                    name="applicantPartyId" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Company managing director PartyId.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="businessName" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Name of a company agent in Siebel</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="birthDate" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Birth date of a company agent</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="familyState" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Person's relationship in law with another</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="educationType" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Type of achieved education</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="businessShare" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Percentage ownership in the company</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element maxOccurs="1" minOccurs="0" name="listOfCblLoans">
                                    <xs:annotation>
                                    <xs:documentation>Technical - Loans container</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="0" name="loan">
                                    <xs:annotation>
                                    <xs:documentation>Technical - Loan item</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="id" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Unique loan id.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="collateralType" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Code for Loan collateral type and  collateral percentage</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="collateralPercentage" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Collateral percentage</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="loanRepaid" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Loan repaid flag.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element maxOccurs="1" minOccurs="0" name="listOfManualLoans">
                                    <xs:annotation>
                                    <xs:documentation>Technical - Manual Loans container</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="0" name="loan">
                                    <xs:annotation>
                                    <xs:documentation>Technical - Loan item</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="creditor">
                                    <xs:annotation>
                                    <xs:documentation>Bank code (5500) or 0000 for financial institutions or Todo  non RB bak or ID of Lease company (27 - Raiffeisen-Leasing, s.r.o., 38  - Jiná)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="loanType" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Type of loan (1 - Kontokorent, 3 - Revolvingový úvěr, 4 - Spotřebitelský úvěr, 5 - Investiční úvěr, 6 - Hypotéční úvěr, 7 - Kreditní karta, 8 - Úvěr ze stavebního spoření, 99 - Jiný)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="originalAmount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>The initial size of a loan or limit for credit card</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="installmentAmount" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation> A fixed payment amount made by a borrower to a lender at a specified date each calendar month.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="contractDate" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Loan Contract date</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="loanDuration" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Duration of a loan in months</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="currentAmount" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>The amount of the principal of a loan that a borrower has not repaid.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="collateralType" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Code for Loan collateral type and  collateral percentage</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="collateralPercentage" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Collateral percentage</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                            </xs:sequence>
                        </xs:complexType>
                    </xs:element>
                    <xs:element name="getInsurance">
                        <xs:complexType>
                            <xs:sequence>
                                <xs:element minOccurs="0" name="applicationId">
                                    <xs:annotation>
                                    <xs:documentation>Unique identification of an application</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                                <xs:element name="offerId">
                                    <xs:annotation>
                                    <xs:documentation>Unique identification of an offer</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="512"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                                <xs:element name="partyId">
                                    <xs:annotation>
                                    <xs:documentation>Loan owner party id (Siebel identifier)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                                <xs:element minOccurs="0"
                                    name="campaignType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Campaign type.</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element minOccurs="1"
                                    name="occupationType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Loan owner occupation type. [CODELIST: OccupationType]</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element minOccurs="1" name="requestedAmount">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="amount">
                                    <xs:annotation>
                                    <xs:documentation>Loan amount requested by user.  If NULL then maximal offered amount is used.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:decimal">
                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of amount [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element minOccurs="1"
                                    name="instalmentCount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Requested instalment count. If NULL then greatest available number is used.</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element name="instalmentAmount" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Requested instalment amount.</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element maxOccurs="1" minOccurs="0" name="instalmentAccount">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of the bank account [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element maxOccurs="1" minOccurs="0"
                                    name="mortgage" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>If client has already mortgage</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element minOccurs="0"
                                    name="netIncome" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Total net income declared by customer.</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element minOccurs="0"
                                    name="loanRepayment" type="xs:decimal"/>
                                <xs:element minOccurs="0"
                                    name="overdraftLimit" type="xs:decimal"/>
                                <xs:element minOccurs="0"
                                    name="livingCosts" type="xs:decimal"/>
                                <xs:element minOccurs="0"
                                    name="variantOrder" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Order (priority) of variations.</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element minOccurs="0" name="proofOfIncome">
                                    <xs:annotation>
                                    <xs:documentation>Proof of income data</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="type"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Type of income proof - (CODEBOOK-LST183)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="incomeProof">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    minOccurs="0" name="netIncome1">
                                    <xs:annotation>
                                    <xs:documentation>Net income of 1. month</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="15"/>

                                    <xs:fractionDigits value="2"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="netIncome2">
                                    <xs:annotation>
                                    <xs:documentation>Net income of 2. month</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="15"/>

                                    <xs:fractionDigits value="2"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="netIncome3">
                                    <xs:annotation>
                                    <xs:documentation>Net income of 3. month</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:fractionDigits value="2"/>

                                    <xs:totalDigits value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="taxReturn">
                                    <xs:annotation>
                                    <xs:documentation>Tax return data</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="sumIncomeEmpl" nillable="true">
                                    <xs:annotation>
                                    <xs:documentation>Úhrn příjmů od všech zaměstnavatelů (řádek 31)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:integer">

                                    <xs:totalDigits value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="sumBTax"
                                    nillable="true" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Úhrn dílčích základů daně (řádek 41a)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="tax"
                                    nillable="true" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Daň po uplatnění slev (řádek 74)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="taxPeriod"
                                    nillable="true" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Zdanovaci obdobi</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                            </xs:sequence>
                        </xs:complexType>
                    </xs:element>
                    <xs:element name="initConsolidation">
                        <xs:complexType>
                            <xs:sequence>
                                <xs:element minOccurs="0" name="applicationId">
                                    <xs:annotation>
                                    <xs:documentation>Unique identification of an application</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                                <xs:element minOccurs="0" name="otherData">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element minOccurs="0"
                                    name="sellerChannel" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Channel – LST072_0008  (SPB),–	LST072_0005 (GIB), last used channel.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="sellerId">
                                    <xs:annotation>
                                    <xs:documentation>ID of seller</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="solusConsent" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Agreement with SOLUS</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="nrkiConsent" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Agreement with NRKI</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="registerAgreement" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>register Agreement</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="preContractConsent" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Agreement with precontract information</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="telcoScoreAgreement" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Telco score agreement.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="telcoScoreContent" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Telco score content.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                            </xs:sequence>
                        </xs:complexType>
                    </xs:element>
                    <xs:element name="tuneOffer">
                        <xs:complexType>
                            <xs:sequence>
                                <xs:element minOccurs="1" name="applicationId">
                                    <xs:annotation>
                                    <xs:documentation>Unique identification of an application</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                                <xs:element minOccurs="0"
                                    name="sellerChannel" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Channel – LST072_0008  (SPB),–	LST072_0005 (GIB), last used channel.</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element name="variantOrder" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Order (priority) of variations.</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element name="changeKey">
                                    <xs:annotation>
                                    <xs:documentation>Change enumeration Limit or Tenor. [LOV: limit;tenor]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:enumeration value="limit"/>
                                    <xs:enumeration value="tenor"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                                <xs:element name="changeValue" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Value of changed key</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                            </xs:sequence>
                        </xs:complexType>
                    </xs:element>
                    <xs:element name="tuneOfferMIC">
                        <xs:complexType>
                            <xs:sequence>
                                <xs:element name="sellerChannel">
                                    <xs:annotation>
                                    <xs:documentation>(SPB|GIB) last used channel.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="3"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                                <xs:element name="userId">
                                    <xs:annotation>
                                    <xs:documentation>Siebel party ID or personal number (CZA) of a user which innitiated call of this service.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                                <xs:element name="userIdType">
                                    <xs:annotation>
                                    <xs:documentation>[LOV: PersonalNumber;PartyId]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:enumeration value="PersonalNumber"/>
                                    <xs:enumeration value="PartyId"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                                <xs:element minOccurs="1" name="applicationId">
                                    <xs:annotation>
                                    <xs:documentation>Unique identification of an application</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                                <xs:element maxOccurs="1" minOccurs="1"
                                    name="variantId" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>The variant for particular loan offer. </xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element minOccurs="0"
                                    name="purposeFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Information if the response is for the variant of purpose or non purpose loan request.</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element maxOccurs="1" minOccurs="0"
                                    name="loanAmount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Max loan amount based on default loan parameters of particular client. </xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element maxOccurs="1" minOccurs="0"
                                    name="installmentCount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Default length of maturity in months for displaing the default offer. </xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element maxOccurs="1" minOccurs="0"
                                    name="fixPeriod" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>The length of interest rate fixation in years.</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                            </xs:sequence>
                        </xs:complexType>
                    </xs:element>
                    <xs:element name="getLoanList">
                        <xs:complexType>
                            <xs:sequence>
                                <xs:element name="sellerChannel">
                                    <xs:annotation>
                                    <xs:documentation>(SPB|GIB) last used channel.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="3"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                                <xs:element name="userId">
                                    <xs:annotation>
                                    <xs:documentation>Siebel party ID or personal number (CZA) of a user which innitiated call of this service.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                                <xs:element name="userIdType">
                                    <xs:annotation>
                                    <xs:documentation>[LOV: PersonalNumber;PartyId]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:enumeration value="PersonalNumber"/>
                                    <xs:enumeration value="PartyId"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                                <xs:element minOccurs="1" name="applicationId">
                                    <xs:annotation>
                                    <xs:documentation>Unique identification of an application</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                            </xs:sequence>
                        </xs:complexType>
                    </xs:element>
                    <xs:element name="createFinancialStatement">
                        <xs:complexType>
                            <xs:sequence>
                                <xs:element name="sellerChannel">
                                    <xs:annotation>
                                    <xs:documentation>(SPB|GIB) last used channel.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="3"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                                <xs:element name="userId">
                                    <xs:annotation>
                                    <xs:documentation>Siebel party ID or personal number (CZA) of a user which innitiated call of this service.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                                <xs:element name="userIdType">
                                    <xs:annotation>
                                    <xs:documentation>[LOV: PersonalNumber;PartyId]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:enumeration value="PersonalNumber"/>
                                    <xs:enumeration value="PartyId"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                                <xs:element minOccurs="1" name="applicationId">
                                    <xs:annotation>
                                    <xs:documentation>Unique identification of an application</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                                <xs:element minOccurs="0" name="registrationNo">
                                    <xs:annotation>
                                    <xs:documentation>Business loan application ID.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="8"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                                <xs:element name="companyPartyId">
                                    <xs:annotation>
                                    <xs:documentation>Company Party ID (PO or FOP)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                                <xs:element name="statementHeader">
                                    <xs:annotation>
                                    <xs:documentation>Header of financial statement.</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element minOccurs="0"
                                    name="documentId" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Financial Statement DMS document ID.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="endDate" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>End of the taxable period.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="1"
                                    name="submissionDate" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Date of submission financial statement to Tax office.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="methodOfDelivery" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Method of delivery to tax office. (1-online, 2-personally)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="taxPeriodMonths" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Number of months of tax period.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="taxOffice">
                                    <xs:annotation>
                                    <xs:documentation>Tax office name.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="80"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element minOccurs="0" name="statementItems">
                                    <xs:annotation>
                                    <xs:documentation>Collection of financial statement.</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="statementCode" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Financial statement template code.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="255" name="item">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="code" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Financial statement item code.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="value" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Financial statement item value.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                            </xs:sequence>
                        </xs:complexType>
                    </xs:element>
                </xs:choice>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="opGetOfferParam_1_5_res">
        <xs:complexType>
            <xs:sequence>
                <xs:choice minOccurs="0">
                    <xs:element name="offerParamSet">
                        <xs:complexType>
                            <xs:sequence>
                                <xs:element minOccurs="0" name="preApplicationId">
                                    <xs:annotation>
                                    <xs:documentation>Unique identification of an pre-application.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                                <xs:element minOccurs="0" name="applicationId">
                                    <xs:annotation>
                                    <xs:documentation>Unique identification of an pre-application.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                                <xs:element name="productCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Offered loan product code [CODELIST: ProductCodes]</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Offered loan currency code [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:choice>
                                    <xs:element name="loanPI">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="limits">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="clientMinAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - minimal principal amount for particular loan product type and client</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="clientMaxAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - maximal principal amount for particular loan product type and client </xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="offerMaxAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - maximal principal amount for particular offer</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="instalmentMinCount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Minimal instalment count</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="instalmentMaxCount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Maximal instalment count</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="allowedCombinations">
                                    <xs:annotation>
                                    <xs:documentation>Allowed combinations of (repayment period; maximal principal amount)</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="allowedCombination">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    name="instalmentCount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Instalment count</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element name="maxAmount">
                                    <xs:annotation>
                                    <xs:documentation>Maximal allowed amount for given instalment count</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="frequencyType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Time unit for instalment [CODELIST: FrequencyType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="drawingAccounts">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="accountNumber">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of the bank account [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="instalmentAccounts">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="accountNumber">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of the bank account [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="requested">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="instalment">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="amount">
                                    <xs:annotation>
                                    <xs:documentation>Instalment amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="interestRate">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="totalRate">
                                    <xs:annotation>
                                    <xs:documentation>Total interest rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="period" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Interest period [CODELIST: FrequencyType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0" name="float">
                                    <xs:annotation>
                                    <xs:documentation>Filled only for float type of interest rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="baseRate">
                                    <xs:annotation>
                                    <xs:documentation>Base interest rate (PRIBOR, LIBOR, ...)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="baseRateType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Type of base rate (PRIBOR-1M, PRIBOR-3M, ...)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element name="rate">
                                    <xs:annotation>
                                    <xs:documentation>Added rate. TotalRate = baseRate + rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="insurance">
                                    <xs:annotation>
                                    <xs:documentation>Offered insurance products</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="mandatory" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Flag - TRUE if insurance is mandatory, otherwise FALSE</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="defaultProduct" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Default insurance product code [CODELIST: LoanInsuranceCodes]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="availableProducts">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded" name="insuranceProduct">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    name="productCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Type of insurance for loan [CODELIST: LoanInsuranceCodes]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="feePeriod" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Insurance fee charge period [CODELIST: FrequencyType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element name="feeAmount">
                                    <xs:annotation>
                                    <xs:documentation>Insurance fee amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="bonusCampaign">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="payableTotalAmount">
                                    <xs:annotation>
                                    <xs:documentation>Total amount to be paid (for standard rate)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="bonusPayableTotalAmount">
                                    <xs:annotation>
                                    <xs:documentation>Total amount to be paid (for bonus rate)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="riskDecisions">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="0">
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="mortgageInquire" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Information if show inquire to client</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="minimalMonthlyIncome" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Minimal monthly income</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="showMinMonthlyIncome" type="xs:string"/>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="regionalCosts" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Regional Costs</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="registryLoanRepayment" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Information of registry (BRKI), which sum of monthly instalment amounts the client pay currently.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="registryOverDraftLimit" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Information of registry (BRKI), which sum of limits revolving (overdrafts) the client has currently.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="registryData" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Flag if BRKI data has been collected.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="topUpPI">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="limits">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="clientMinAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - minimal principal amount for particular loan product type and client</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="clientMaxAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - maximal principal amount for particular loan product type and client </xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="offerMaxAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - maximal principal amount for particular offer</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="instalmentMinCount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Minimal instalment count</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="instalmentMaxCount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Maximal instalment count</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="allowedCombinations">
                                    <xs:annotation>
                                    <xs:documentation>Allowed combinations of (repayment period; maximal principal amount)</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="allowedCombination">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    name="instalmentCount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Instalment count</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element name="maxAmount">
                                    <xs:annotation>
                                    <xs:documentation>Maximal allowed amount for given instalment count</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="frequencyType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Time unit for instalment [CODELIST: FrequencyType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="drawingAccounts">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="accountNumber">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of the bank account [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="instalmentAccounts">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="accountNumber">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of the bank account [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="requested">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="instalment">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="amount">
                                    <xs:annotation>
                                    <xs:documentation>Instalment amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="interestRate">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="totalRate">
                                    <xs:annotation>
                                    <xs:documentation>Total interest rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="period" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Interest period [CODELIST: FrequencyType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0" name="float">
                                    <xs:annotation>
                                    <xs:documentation>Filled only for float type of interest rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="baseRate">
                                    <xs:annotation>
                                    <xs:documentation>Base interest rate (PRIBOR, LIBOR, ...)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="baseRateType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Type of base rate (PRIBOR-1M, PRIBOR-3M, ...)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element name="rate">
                                    <xs:annotation>
                                    <xs:documentation>Added rate. TotalRate = baseRate + rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="insurance">
                                    <xs:annotation>
                                    <xs:documentation>Offered insurance products</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="mandatory" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Flag - TRUE if insurance is mandatory, otherwise FALSE</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="defaultProduct" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Default insurance product code [CODELIST: LoanInsuranceCodes]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="availableProducts">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded" name="insuranceProduct">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    name="productCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Type of insurance for loan [CODELIST: LoanInsuranceCodes]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="feePeriod" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Insurance fee charge period [CODELIST: FrequencyType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element name="feeAmount">
                                    <xs:annotation>
                                    <xs:documentation>Insurance fee amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="current">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="loanReceivable">
                                    <xs:annotation>
                                    <xs:documentation>Current loan receivable</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="floatInterestRate" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Flag - TRUE if interest rate is float, oherwise FALSE</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="bonusCampaign">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="payableTotalAmount">
                                    <xs:annotation>
                                    <xs:documentation>Total amount to be paid (for standard rate)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="bonusPayableTotalAmount">
                                    <xs:annotation>
                                    <xs:documentation>Total amount to be paid (for bonus rate)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="riskDecisions">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="0">
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="mortgageInquire" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Information if show inquire to client</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="minimalMonthlyIncome" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Minimal monthly income</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="showMinMonthlyIncome" type="xs:string"/>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="regionalCosts" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Regional Costs</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="registryLoanRepayment" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Information of registry (BRKI), which sum of monthly instalment amounts the client pay currently.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="registryOverDraftLimit" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Information of registry (BRKI), which sum of limits revolving (overdrafts) the client has currently.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="registryData" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Flag if BRKI data has been collected.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="overdraftPI">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="limits">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="clientMinAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - minimal principal amount for particular loan product type and client</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="clientMaxAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - maximal principal amount for particular loan product type and client </xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="offerMaxAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - maximal principal amount for particular offer</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="currentAccounts">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="currentAccount">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="accountNumber">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of the bank account [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element name="concessionaryInterestLimit">
                                    <xs:annotation>
                                    <xs:documentation>Limit for concessionary (zero) interest</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="requested">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="interestRate">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="totalRate">
                                    <xs:annotation>
                                    <xs:documentation>Total interest rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="period" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Interest period [CODELIST: FrequencyType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0" name="float">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="baseRate">
                                    <xs:annotation>
                                    <xs:documentation>Base interest rate (PRIBOR, LIBOR, ...)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="baseRateType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Type of base rate (PRIBOR-1M, PRIBOR-3M, ...)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element name="rate">
                                    <xs:annotation>
                                    <xs:documentation>Added rate. TotalRate = baseRate + rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="riskDecisions">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="0">
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="mortgageInquire" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Information if show inquire to client</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="minimalMonthlyIncome" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Minimal monthly income</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="showMinMonthlyIncome" type="xs:string"/>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="regionalCosts" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Regional Costs</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="registryLoanRepayment" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Information of registry (BRKI), which sum of monthly instalment amounts the client pay currently.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="registryOverDraftLimit" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Information of registry (BRKI), which sum of limits revolving (overdrafts) the client has currently.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="registryData" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Flag if BRKI data has been collected.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="overdraftTopUpPI">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="limits">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="clientMinAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - minimal principal amount for particular loan product type and client</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="clientMaxAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - maximal principal amount for particular loan product type and client </xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="offerMaxAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - maximal principal amount for particular offer</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="currentAccounts">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="currentAccount">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="accountNumber">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of the bank account [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element name="concessionaryInterestLimit">
                                    <xs:annotation>
                                    <xs:documentation>Limit for concessionary (zero) interest</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="requested">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="interestRate">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="totalRate">
                                    <xs:annotation>
                                    <xs:documentation>Total interest rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="period" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Interest period [CODELIST: FrequencyType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0" name="float">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="baseRate">
                                    <xs:annotation>
                                    <xs:documentation>Base interest rate (PRIBOR, LIBOR, ...)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="baseRateType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Type of base rate (PRIBOR-1M, PRIBOR-3M, ...)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element name="rate">
                                    <xs:annotation>
                                    <xs:documentation>Added rate. TotalRate = baseRate + rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="current">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="loanLimitAmount">
                                    <xs:annotation>
                                    <xs:documentation>Current ovedraft amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="floatInterestRate" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Flag - TRUE if interest rate is float, oherwise FALSE</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="riskDecisions">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="0">
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="mortgageInquire" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Information if show inquire to client</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="minimalMonthlyIncome" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Minimal monthly income</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="showMinMonthlyIncome" type="xs:string"/>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="regionalCosts" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Regional Costs</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="registryLoanRepayment" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Information of registry (BRKI), which sum of monthly instalment amounts the client pay currently.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="registryOverDraftLimit" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Information of registry (BRKI), which sum of limits revolving (overdrafts) the client has currently.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="registryData" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Flag if BRKI data has been collected.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="interestFreePI">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="limits">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="clientMinAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - minimal principal amount for particular loan product type and client</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="clientMaxAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - maximal principal amount for particular loan product type and client </xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="offerMaxAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - maximal principal amount for particular offer</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="currentAccounts">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="currentAccount">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="accountNumber">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of the bank account [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="requested">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="interestRate">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="totalRate">
                                    <xs:annotation>
                                    <xs:documentation>Total interest rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="period" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Interest period [CODELIST: FrequencyType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0" name="float">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="baseRate">
                                    <xs:annotation>
                                    <xs:documentation>Base interest rate (PRIBOR, LIBOR, ...)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="baseRateType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Type of base rate (PRIBOR-1M, PRIBOR-3M, ...)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element name="rate">
                                    <xs:annotation>
                                    <xs:documentation>Added rate. TotalRate = baseRate + rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="loanMIC">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="limits">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="clientMinAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - minimal principal amount for particular loan product type and client</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="clientMaxAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - maximal principal amount for particular loan product type and client </xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="offerMaxAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - maximal principal amount for particular offer</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="zoneDelimiterAmount">
                                    <xs:annotation>
                                    <xs:documentation>Currently MIC only. Treshold value between zone I and zone II. The calculation is based on current credit exposure of client. If requested limit &lt;= Delimiter then zone I otherwise zone II.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="instalmentMinCount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Minimal instalment count</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="instalmentMaxCount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Maximal instalment count</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="allowedCombinations">
                                    <xs:annotation>
                                    <xs:documentation>Allowed combinations of (repayment period; maximal principal amount)</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="allowedCombination">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    name="instalmentCount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Instalment count</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element name="maxAmount">
                                    <xs:annotation>
                                    <xs:documentation>Maximal allowed amount for given instalment count</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="frequencyType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Time unit for instalment [CODELIST: FrequencyType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="drawingAccounts">
                                    <xs:annotation>
                                    <xs:documentation>List of accounts which can be used for drawing</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="accountNumber">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of the bank account [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="instalmentAccounts">
                                    <xs:annotation>
                                    <xs:documentation>List of accounts which can be used for instalments</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="accountNumber">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of the bank account [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="requested">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="instalment">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="amount">
                                    <xs:annotation>
                                    <xs:documentation>Instalment amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="interestRate">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="totalRate">
                                    <xs:annotation>
                                    <xs:documentation>Total interest rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="period" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Interest period [CODELIST: FrequencyType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0" name="float">
                                    <xs:annotation>
                                    <xs:documentation>Filled only for float type of interest rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="baseRate">
                                    <xs:annotation>
                                    <xs:documentation>Base interest rate (PRIBOR, LIBOR, ...)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="baseRateType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Type of base rate (PRIBOR-1M, PRIBOR-3M, ...)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element name="rate">
                                    <xs:annotation>
                                    <xs:documentation>Added rate. TotalRate = baseRate + rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="insurance">
                                    <xs:annotation>
                                    <xs:documentation>Offered insurance products</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="mandatory" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Flag - TRUE if insurance is mandatory, otherwise FALSE</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="defaultProduct" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Default insurance product code [CODELIST: LoanInsuranceCodes]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="availableProducts">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="insuranceProduct">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    name="productCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Type of insurance for loan [CODELIST: LoanInsuranceCodes]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="feePeriod" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Insurance fee charge period [CODELIST: FrequencyType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element name="feeAmount">
                                    <xs:annotation>
                                    <xs:documentation>Insurance fee amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="topUpMIC">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="limits">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="clientMinAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - minimal principal amount for particular loan product type and client</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="clientMaxAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - maximal principal amount for particular loan product type and client </xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="offerMaxAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - maximal principal amount for particular offer</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="zoneDelimiterAmount">
                                    <xs:annotation>
                                    <xs:documentation>Currently MIC only. Treshold value between zone I and zone II. The calculation is based on current credit exposure of client. If requested limit &lt;= Delimiter then zone I otherwise zone II.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="instalmentMinCount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Minimal instalment count</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="instalmentMaxCount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Maximal instalment count</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="allowedCombinations">
                                    <xs:annotation>
                                    <xs:documentation>Allowed combinations of (repayment period; maximal principal amount)</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="allowedCombination">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    name="instalmentCount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Instalment count</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element name="maxAmount">
                                    <xs:annotation>
                                    <xs:documentation>Maximal allowed amount for given instalment count</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="frequencyType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Time unit for instalment [CODELIST: FrequencyType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="drawingAccounts">
                                    <xs:annotation>
                                    <xs:documentation>List of accounts which can be used for drawing</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="accountNumber">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of the bank account [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="instalmentAccounts">
                                    <xs:annotation>
                                    <xs:documentation>List of accounts which can be used for instalments</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="accountNumber">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of the bank account [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="requested">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="instalment">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="amount">
                                    <xs:annotation>
                                    <xs:documentation>Instalment amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="interestRate">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="totalRate">
                                    <xs:annotation>
                                    <xs:documentation>Total interest rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="period" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Interest period [CODELIST: FrequencyType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0" name="float">
                                    <xs:annotation>
                                    <xs:documentation>Filled only for float type of interest rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="baseRate">
                                    <xs:annotation>
                                    <xs:documentation>Base interest rate (PRIBOR, LIBOR, ...)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="baseRateType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Type of base rate (PRIBOR-1M, PRIBOR-3M, ...)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element name="rate">
                                    <xs:annotation>
                                    <xs:documentation>Added rate. TotalRate = baseRate + rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="current">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="loanReceivable">
                                    <xs:annotation>
                                    <xs:documentation>Current loan receivable</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="floatInterestRate" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Flag - TRUE if interest rate is float, oherwise FALSE</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="insurance">
                                    <xs:annotation>
                                    <xs:documentation>Offered insurance products</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="mandatory" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Flag - TRUE if insurance is mandatory, otherwise FALSE</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="defaultProduct" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Default insurance product code [CODELIST: LoanInsuranceCodes]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="availableProducts">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="insuranceProduct">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    name="productCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Type of insurance for loan [CODELIST: LoanInsuranceCodes]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="feePeriod" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Insurance fee charge period [CODELIST: FrequencyType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element name="feeAmount">
                                    <xs:annotation>
                                    <xs:documentation>Insurance fee amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="overdraftMIC">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="limits">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="clientMinAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - minimal principal amount for particular loan product type and client</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="clientMaxAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - maximal principal amount for particular loan product type and client </xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="offerMaxAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - maximal principal amount for particular offer</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="zoneDelimiterAmount">
                                    <xs:annotation>
                                    <xs:documentation>Currently MIC only. Treshold value between zone I and zone II. The calculation is based on current credit exposure of client. If requested limit &lt;= Delimiter then zone I otherwise zone II.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="currentAccounts">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="currentAccount">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="accountNumber">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of the bank account [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="requested">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="interestRate">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="totalRate">
                                    <xs:annotation>
                                    <xs:documentation>Total interest rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="period" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Interest period [CODELIST: FrequencyType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0" name="float">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="baseRate">
                                    <xs:annotation>
                                    <xs:documentation>Base interest rate (PRIBOR, LIBOR, ...)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="baseRateType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Type of base rate (PRIBOR-1M, PRIBOR-3M, ...)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element name="rate">
                                    <xs:annotation>
                                    <xs:documentation>Added rate. TotalRate = baseRate + rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="overdraftIncMIC">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="limits">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="clientMinAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - minimal principal amount for particular loan product type and client</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="clientMaxAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - maximal principal amount for particular loan product type and client </xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="offerMaxAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - maximal principal amount for particular offer</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="zoneDelimiterAmount">
                                    <xs:annotation>
                                    <xs:documentation>Currently MIC only. Treshold value between zone I and zone II. The calculation is based on current credit exposure of client. If requested limit &lt;= Delimiter then zone I otherwise zone II.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="currentAccounts">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="currentAccount">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="accountNumber">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of the bank account [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="requested">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="interestRate">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="totalRate">
                                    <xs:annotation>
                                    <xs:documentation>Total interest rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="period" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Interest period [CODELIST: FrequencyType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0" name="float">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="baseRate">
                                    <xs:annotation>
                                    <xs:documentation>Base interest rate (PRIBOR, LIBOR, ...)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="baseRateType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Type of base rate (PRIBOR-1M, PRIBOR-3M, ...)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element name="rate">
                                    <xs:annotation>
                                    <xs:documentation>Added rate. TotalRate = baseRate + rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="current">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="loanLimitAmount">
                                    <xs:annotation>
                                    <xs:documentation>Current ovedraft amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="floatInterestRate" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Flag - TRUE if interest rate is float, oherwise FALSE</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="collateralBillOfExchange" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>true when there is a collateral (bill of exchange) for the loan that should be increased</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="topUpWL">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="requested">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="amount">
                                    <xs:annotation>
                                    <xs:documentation>Loan amount requested by user</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="newAmount" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>New cash amount.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="minAmount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Minimal amount of new loan.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="instalment">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    name="count" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Instalment count</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element name="amount">
                                    <xs:annotation>
                                    <xs:documentation>Instalment amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="interestRate">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="totalRate">
                                    <xs:annotation>
                                    <xs:documentation>Total interest rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="current">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="toppedUpLoans">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="topUpBonusAmount" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Bonus amount to pay by top-uped by loan</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="bonusCampaign">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="bonusInterestRate">
                                    <xs:annotation>
                                    <xs:documentation>Interest rate of bonus campaign</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="bonusAmount">
                                    <xs:annotation>
                                    <xs:documentation>Bonus amount in local currency (CZK)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="riskDecisions">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="0">
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="mortgageInquire" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Information if show inquire to client</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="minimalMonthlyIncome" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Minimal monthly income</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="showMinMonthlyIncome" type="xs:string"/>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="regionalCosts" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Regional Costs</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="registryLoanRepayment" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Information of registry (BRKI), which sum of monthly instalment amounts the client pay currently.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="registryOverDraftLimit" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Information of registry (BRKI), which sum of limits revolving (overdrafts) the client has currently.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="registryData" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Flag if BRKI data has been collected.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="loanOther">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="requested">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="amount">
                                    <xs:annotation>
                                    <xs:documentation>Loan amount requested by user</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="instalment">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    minOccurs="0"
                                    name="count" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Instalment count</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element name="amount">
                                    <xs:annotation>
                                    <xs:documentation>Instalment amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="interestRate">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="totalRate">
                                    <xs:annotation>
                                    <xs:documentation>Total interest rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="limits">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="clientMinAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - minimal principal amount for particular loan product type and client</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="clientMaxAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - maximal principal amount for particular loan product type and client </xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="instalmentMinCount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Minimal instalment count</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="instalmentMaxCount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Maximal instalment count</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="bonusCampaign">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="bonusInterestRate">
                                    <xs:annotation>
                                    <xs:documentation>Interest rate of bonus campaign</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="bonusAmount">
                                    <xs:annotation>
                                    <xs:documentation>Bonus amount in local currency (CZK)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="riskDecisions">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="0">
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="mortgageInquire" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Information if show inquire to client</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="minimalMonthlyIncome" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Minimal monthly income</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="showMinMonthlyIncome" type="xs:string"/>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="regionalCosts" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Regional Costs</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="registryLoanRepayment" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Information of registry (BRKI), which sum of monthly instalment amounts the client pay currently.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="registryOverDraftLimit" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Information of registry (BRKI), which sum of limits revolving (overdrafts) the client has currently.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="registryData" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Flag if BRKI data has been collected.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="incomeProveRequired" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>FICO result. Income prove of income required.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="proofOfIncome" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Proof of income as income proof.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="taxReturn" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Tax return as income proof.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="transactionIncome" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Transaction income as income proof.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                </xs:choice>
                                <xs:element minOccurs="0" name="contractFees">
                                    <xs:annotation>
                                    <xs:documentation>A list of fees</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="fee">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="type" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>This codebook contais values about fees which belong to a loan [CODELIST: LoanFee]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="amount">
                                    <xs:annotation>
                                    <xs:documentation>An amount of a fee</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="period" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>If filled then the fee is charged periodically for given period, otherwise the fee is charged for unspecified event. [CODELIST: FrequencyType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                            </xs:sequence>
                        </xs:complexType>
                    </xs:element>
                    <xs:element name="offerParamSetMIC">
                        <xs:complexType>
                            <xs:sequence>
                                <xs:element maxOccurs="unbounded" name="offerParam">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="productType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Offer param product type</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="loanSecuredFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Loan secured flag.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="requested">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="amount">
                                    <xs:annotation>
                                    <xs:documentation>Loan amount requested by user</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="instalment">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    minOccurs="0"
                                    name="count" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Instalment count</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0" name="amount">
                                    <xs:annotation>
                                    <xs:documentation>Instalment amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="interestRate">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    minOccurs="0" name="totalRate">
                                    <xs:annotation>
                                    <xs:documentation>Total interest rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="limits">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="clientMinAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - minimal principal amount for particular loan product type and client</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:fractionDigits value="5"/>

                                    <xs:totalDigits value="18"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="clientMaxAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - maximal principal amount for particular loan product type and client </xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="instalmentMinCount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Minimal instalment count</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="instalmentMaxCount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Maximal instalment count</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="margin">
                                    <xs:annotation>
                                    <xs:documentation>Margin interest rate.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:decimal">
                                    <xs:totalDigits value="8"/>

                                    <xs:fractionDigits value="2"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="defaultOffer" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Default offer set.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                            </xs:sequence>
                        </xs:complexType>
                    </xs:element>
                    <xs:element name="preEvaluateResult">
                        <xs:complexType>
                            <xs:sequence>
                                <xs:element name="productCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Offered loan product code [CODELIST: ProductCodes]</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element name="result" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Code of preevaluation status.</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element minOccurs="0"
                                    name="consolidationOffer" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Consolidation offer.</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element minOccurs="0" name="variants">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="variant">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="variantOrder" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Order (priority) of variations.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="offerAmount">
                                    <xs:annotation>
                                    <xs:documentation>Offered limit</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency code for offered limit. [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="instalmentCount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Total instalment count</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="recommended" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Flag for recommended variation by bank.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="limits">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="clientMinAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - minimal principal amount for particular loan product type and client</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:fractionDigits value="5"/>

                                    <xs:totalDigits value="18"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="clientMaxAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - maximal principal amount for particular loan product type and client </xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="instalmentMinCount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Minimal instalment count</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="instalmentMaxCount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Maximal instalment count</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="subOffer" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Sub offer flag.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                            </xs:sequence>
                        </xs:complexType>
                    </xs:element>
                    <xs:element name="preEvaluateResultFull">
                        <xs:complexType>
                            <xs:sequence>
                                <xs:element name="productCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Offered loan product code [CODELIST: ProductCodes]</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element name="result" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Code of preevaluation status.</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element minOccurs="0"
                                    name="consolidationOffer" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Consolidation offer.</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element minOccurs="0" name="variants">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="variant">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="variantOrder" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Order (priority) of variations.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="offerAmount">
                                    <xs:annotation>
                                    <xs:documentation>Offered limit</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="newAmount" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>New cash amount.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency code for offered limit. [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="instalmentCount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Total instalment count</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="instalmentAmount">
                                    <xs:annotation>
                                    <xs:documentation>Instalment amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="interestRate" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Interest rate.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="recommended" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Flag for recommended variation by bank.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="limits">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="clientMinAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - minimal principal amount for particular loan product type and client</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:fractionDigits value="5"/>

                                    <xs:totalDigits value="18"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="clientMaxAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - maximal principal amount for particular loan product type and client </xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="instalmentMinCount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Minimal instalment count</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="instalmentMaxCount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Maximal instalment count</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="bonusCampaign">
                                    <xs:annotation>
                                    <xs:documentation>Bonus campaign information.</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="bonusInterestRate">
                                    <xs:annotation>
                                    <xs:documentation>Interest rate of bonus campaign</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="bonusAmount">
                                    <xs:annotation>
                                    <xs:documentation>Bonus amount in local currency (CZK)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0"
                                    name="savedBonusAmount" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Saved (accumulated) bonus amount.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="subOffer" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Sub offer flag.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                            </xs:sequence>
                        </xs:complexType>
                    </xs:element>
                    <xs:element name="preEvaluateResultMIC">
                        <xs:complexType>
                            <xs:sequence>
                                <xs:element maxOccurs="1" minOccurs="1"
                                    name="applicationId" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Technical id of a loan application.</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element maxOccurs="1" minOccurs="0" name="variants">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="variant">
                                    <xs:annotation>
                                    <xs:documentation>The variant for particular loan offer. </xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="variantId" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>The variant for particular loan offer. </xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="loanType">
                                    <xs:annotation>
                                    <xs:documentation>Product type abbreviation.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="defaultVariant" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>According to predefined matrix in Micash, there is a product which should be displayed to the customer as an Initial variant i.e. this field = Y</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="purposeFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Information if the response is for the variant of purpose or non purpose loan request.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="collateralType" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>The type of collateral allowed. </xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="loanAmount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Max loan amount based on default loan parameters of particular client. </xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="installmentCount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Default length of maturity in months for displaing the default offer. </xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="annuityAmount" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Calculated monthly repayment based on defaulted values. </xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="singleOfferFixFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Based on rules defined in Micash there are predefined products where is offer of different interest fixation period allowed. </xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="interestRate" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>In case of PRK it is the final interest rate.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="margin" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>In case of PK it is the value of margin. </xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="minLoanAmount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Min. amount which can be choosen by client in case of offer fine tuning in GIB.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="maxLoanAmount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Man. amount which can be choosen by client in case of offer fine tuning in GIB.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="minInstallmentCount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Min. number of installments in months to be choosen by client in case of offer fine tunint in GIB.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="maxInstallmentCount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Max. number of installments in months to be choosen by client in case of offer fine tunint in GIB.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="fixPeriods">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="fixPeriod">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="fixPeriod" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>The length of interest rate fixation in years.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0"
                                    name="defaultFixPeriodFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>If this rate should be used for displaying the default offer to the customer. Default value is "N".</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                            </xs:sequence>
                        </xs:complexType>
                    </xs:element>
                    <xs:element minOccurs="0" name="insuranceList">
                        <xs:annotation>
                            <xs:documentation>Avaiable insurance product list.</xs:documentation>
                        </xs:annotation>
                        <xs:complexType>
                            <xs:sequence>
                                <xs:element maxOccurs="unbounded" name="insurance">
                                    <xs:annotation>
                                    <xs:documentation>Insurance product details.</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="productCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Insurance product code [CODELIST: LoanInsuranceCodes]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A currency of a fee amount [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="1"
                                    name="period" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Instalments of insurance are usually monthly. [CODELIST: FrequencyType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="feeAmount">
                                    <xs:annotation>
                                    <xs:documentation>Insurance fee amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:decimal">
                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="power" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Power of insurance (for order in list)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="isOffered" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Sign if insurance offer can be presented to customer.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element minOccurs="0"
                                    name="defaultInsurance" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Default insurance [CODELIST: LoanInsuranceCodes]</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                            </xs:sequence>
                        </xs:complexType>
                    </xs:element>
                    <xs:element minOccurs="0" name="insuranceListFull">
                        <xs:annotation>
                            <xs:documentation>Avaiable insurance product list.</xs:documentation>
                        </xs:annotation>
                        <xs:complexType>
                            <xs:sequence>
                                <xs:element maxOccurs="unbounded" name="insurance">
                                    <xs:annotation>
                                    <xs:documentation>Insurance product details.</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="productCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Insurance product code [CODELIST: LoanInsuranceCodes]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A currency of a fee amount [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="1"
                                    name="period" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Instalments of insurance are usually monthly. [CODELIST: FrequencyType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="feeAmount">
                                    <xs:annotation>
                                    <xs:documentation>Insurance fee amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:decimal">
                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="power" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Power of insurance (for order in list)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="isOffered" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Sign if insurance offer can be presented to customer.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element minOccurs="0"
                                    name="defaultInsurance" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Default insurance [CODELIST: LoanInsuranceCodes]</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element minOccurs="0"
                                    name="variantOrder" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Order (priority) of variations.</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                            </xs:sequence>
                        </xs:complexType>
                    </xs:element>
                    <xs:element name="consolidationResult">
                        <xs:complexType>
                            <xs:sequence>
                                <xs:element minOccurs="1" name="applicationId">
                                    <xs:annotation>
                                    <xs:documentation>Unique identification of an application</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                            </xs:sequence>
                        </xs:complexType>
                    </xs:element>
                    <xs:element name="tuneOfferResult">
                        <xs:complexType>
                            <xs:sequence>
                                <xs:element minOccurs="1" name="applicationId">
                                    <xs:annotation>
                                    <xs:documentation>Unique identification of an application</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                                <xs:element maxOccurs="1" minOccurs="0" name="offer">
                                    <xs:complexType>
                                    <xs:sequence maxOccurs="1" minOccurs="1">
                                    <xs:element name="requested">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="amount">
                                    <xs:annotation>
                                    <xs:documentation>Loan amount requested by user</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:fractionDigits value="5"/>

                                    <xs:totalDigits value="18"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="instalment">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    minOccurs="0"
                                    name="count" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Instalment count</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element name="amount">
                                    <xs:annotation>
                                    <xs:documentation>Instalment amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="interestRate">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="totalRate">
                                    <xs:annotation>
                                    <xs:documentation>Total interest rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="bonusCampaign">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="bonusInterestRate">
                                    <xs:annotation>
                                    <xs:documentation>Interest rate of bonus campaign</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="bonusAmount">
                                    <xs:annotation>
                                    <xs:documentation>Bonus amount in local currency (CZK)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="tuneResult"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Tune offer result</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                            </xs:sequence>
                        </xs:complexType>
                    </xs:element>
                    <xs:element name="tuneOfferResultMIC">
                        <xs:complexType>
                            <xs:sequence>
                                <xs:element maxOccurs="1" minOccurs="1"
                                    name="applicationId" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Technical id of a loan application.</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element maxOccurs="1" minOccurs="1" name="variant">
                                    <xs:annotation>
                                    <xs:documentation>The variant for particular loan offer. </xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="variantId" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>The variant for particular loan offer. </xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="loanType">
                                    <xs:annotation>
                                    <xs:documentation>Product type abbreviation.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="defaultVariant" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>According to predefined matrix in Micash, there is a product which should be displayed to the customer as an Initial variant i.e. this field = Y</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="purposeFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Information if the response is for the variant of purpose or non purpose loan request.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="collateralType" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>The type of collateral allowed. </xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="loanAmount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Max loan amount based on default loan parameters of particular client. </xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="installmentCount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Default length of maturity in months for displaing the default offer. </xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="annuityAmount" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Calculated monthly repayment based on defaulted values. </xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="singleOfferFixFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Based on rules defined in Micash there are predefined products where is offer of different interest fixation period allowed. </xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="interestRate" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>In case of PRK it is the final interest rate.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="margin" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>In case of PK it is the value of margin. </xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="minLoanAmount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Min. amount which can be choosen by client in case of offer fine tuning in GIB.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="maxLoanAmount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Man. amount which can be choosen by client in case of offer fine tuning in GIB.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="minInstallmentCount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Min. number of installments in months to be choosen by client in case of offer fine tunint in GIB.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="maxInstallmentCount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Max. number of installments in months to be choosen by client in case of offer fine tunint in GIB.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="fixPeriods">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="fixPeriod">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="fixPeriod" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>The length of interest rate fixation in years.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0"
                                    name="defaultFixPeriodFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>If this rate should be used for displaying the default offer to the customer. Default value is "N".</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                            </xs:sequence>
                        </xs:complexType>
                    </xs:element>
                    <xs:element name="loanList">
                        <xs:annotation>
                            <xs:documentation>Loan list.</xs:documentation>
                        </xs:annotation>
                        <xs:complexType>
                            <xs:sequence>
                                <xs:element maxOccurs="unbounded"
                                    minOccurs="0" name="loan">
                                    <xs:annotation>
                                    <xs:documentation>Loan detail.</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="id" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Unique loan id.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="creditor" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Bank or financial company.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="type" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Code of loan type.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="amount" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Loan amount.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="currentAmount" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Remaining principal amount or actual loan amount.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="instalment" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Loan instalment amount.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency code. [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="contractDate" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>The date the loan contract was signed.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="maturityDate" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>The date the loan contract expires.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                            </xs:sequence>
                        </xs:complexType>
                    </xs:element>
                </xs:choice>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
