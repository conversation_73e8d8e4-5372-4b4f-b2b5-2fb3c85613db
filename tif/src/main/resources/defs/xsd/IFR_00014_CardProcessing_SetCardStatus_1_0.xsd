<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/services/entityService/cardProcessing/SetCardStatus_1_0"
    version="1.0.0"
    xmlns:opsetcardstatus_1_0="http://rb.cz/services/entityService/cardProcessing/SetCardStatus_1_0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="opSetCardStatus_1_0">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="pan" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>A payment card number</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="cardSeqNumber">
                    <xs:annotation>
                        <xs:documentation>Card sequence number; 
0 - process actual card
9999 - process for all cards with the same card number </xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:integer">
                            <xs:minInclusive value="0"/>
                            <xs:maxInclusive value="9999"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element name="status" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>[CODELIST: PaymentCardStatus]</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="observation">
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:minLength value="0"/>
                            <xs:maxLength value="200"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="opSetCardStatus_1_0_res">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="cardList">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded" name="card">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="pan" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A payment card number</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="cardSeqNumber">
                                    <xs:annotation>
                                    <xs:documentation>Card sequence number; 
0 - process actual card
9999 - process for all cards with the same card number </xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:integer">
                                    <xs:minInclusive value="0"/>
                                    <xs:maxInclusive value="9999"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="status" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>[CODELIST: PaymentCardStatus]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
