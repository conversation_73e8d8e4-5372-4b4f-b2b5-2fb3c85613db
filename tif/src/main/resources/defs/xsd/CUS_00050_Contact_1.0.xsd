<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/services/contact/1.0" version="1.0.5"
    xmlns:cus_00050_contact="http://rb.cz/services/contact/1.0"
    xmlns:opupload_1_0="http://rb.cz/services/contact/Upload_1_0/1.0"
    xmlns:opupload_1_1="http://rb.cz/services/contact/Upload_1_1/1.0"
    xmlns:technical_es_response_xsd="http://rb.cz/dictionary/technical/" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:import namespace="http://rb.cz/dictionary/technical/" schemaLocation="./technical/es_response.xsd"/>
    <xs:import namespace="http://rb.cz/services/contact/Upload_1_0/1.0" schemaLocation="CUS_00050_Contact_Upload_1_0.xsd"/>
    <xs:import namespace="http://rb.cz/services/contact/Upload_1_1/1.0" schemaLocation="CUS_00050_Contact_Upload_1_1.xsd"/>
    <xs:element name="contactRequest">
        <xs:complexType>
            <xs:sequence>
                <xs:element maxOccurs="1" minOccurs="0"
                    name="channelCode" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Identification of the channel which has been request sent. [CODELIST: CHANNEL_CODES]</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="cultureCode" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Language code that is configured in the application that launched the service call [CODELIST: Language]</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="userId" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Unique identification of the user, who initiates the request.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="userType">
                    <xs:annotation>
                        <xs:documentation>Additional info for identification internal user (cza...) or external user (technical accounts, 3rd parties, clients...) [LOV: INT;EXT]</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:enumeration value="INT"/>
                            <xs:enumeration value="EXT"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="1" name="entityService">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="1" minOccurs="1" name="entity">
                                <xs:annotation>
                                    <xs:documentation>[LOV: Contact]</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:enumeration value="Contact"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="1" name="operation">
                                <xs:annotation>
                                    <xs:documentation>[LOV: opUpload_1_0;opUpload_1_1]</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:enumeration value="opUpload_1_0"/>
                                    <xs:enumeration value="opUpload_1_1"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:choice>
                                <xs:element ref="opupload_1_0:opUpload_1_0"/>
                                <xs:element ref="opupload_1_1:opUpload_1_1"/>
                            </xs:choice>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="contactResponse">
        <xs:complexType>
            <xs:sequence>
                <xs:element ref="technical_es_response_xsd:activities"/>
                <xs:element maxOccurs="1" minOccurs="0" name="response">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:choice>
                                <xs:element ref="opupload_1_0:opUpload_1_0_res"/>
                                <xs:element ref="opupload_1_1:opUpload_1_1_res"/>
                            </xs:choice>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
