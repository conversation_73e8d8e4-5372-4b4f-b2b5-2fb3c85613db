<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/services/cashAccount/GetList_1_3/1.0"
    version="1.0.0"
    xmlns:opgetlist_1_3="http://rb.cz/services/cashAccount/GetList_1_3/1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="opGetList_1_3">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="partyId">
                    <xs:annotation>
                        <xs:documentation>Party Id (Siebel identifier)</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:maxLength value="15"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element minOccurs="0" name="requestedDetail">
                    <xs:annotation>
                        <xs:documentation>Flags indicating whether to include additional information</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element minOccurs="0" name="balances" type="xs:boolean">
                                <xs:annotation>
                                    <xs:documentation>Include balances</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="overdrafts" type="xs:boolean">
                                <xs:annotation>
                                    <xs:documentation>Include overdraft</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element minOccurs="0"
                                name="savingOptions" type="xs:boolean">
                                <xs:annotation>
                                    <xs:documentation>Include saving account options</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="insurance" type="xs:boolean">
                                <xs:annotation>
                                    <xs:documentation>Include insurance</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element minOccurs="0" name="managedByMultiChannel" type="xs:boolean">
                    <xs:annotation>
                        <xs:documentation>Return only accounts managed by MCH</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element minOccurs="0" name="configProfile">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded" name="item">
                                <xs:annotation>
                                    <xs:documentation>Account config profile item</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:choice>
                                    <xs:element name="cpAccountType">
                                    <xs:annotation>
                                    <xs:documentation>type of the account (single or multi currency) [LOV: SINGLE_CF;MULTI_CF]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:enumeration value="SINGLE_CF"/>
                                    <xs:enumeration value="MULTI_CF"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:choice>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="accountStatusList">
                    <xs:annotation>
                        <xs:documentation>Filter for account status. Service returns only accounts in any of listed statuses. 
If not filled it returns accounts only in status 0 - active (default behavior). 
Current restrictions: 
1.) Only 4 - non active and 0 - active statuses are supported;
2.) 0 - active must be always contained (but it's not checked, the service just always returns active accounts).</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded"
                                minOccurs="1" name="accountStatus" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>[CODELIST: BankAccountStatus]</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="opGetList_1_3_res">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="bankAccounts">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded"
                                minOccurs="1" name="bankAccount">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="accountNumber">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="iban" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>IBAN code of the bank account</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="swift" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>SWIFT code of the bank account</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>(Main) currency of the bank account [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="name">
                                    <xs:annotation>
                                    <xs:documentation>Account name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="20"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="partyId">
                                    <xs:annotation>
                                    <xs:documentation>Party Id (Siebel identifier)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="productCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Product codes codebook for MultiChannel [CODELIST: ProductCodes]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="priceProgramCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Account active price program. [CODELIST: PriceProgramCodes]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="interestProductCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Product codes codebook for MultiChannel [CODELIST: ProductCodes]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="accountType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Type of the account (CURRENT, SAVINGS, OTHER..) [CODELIST: AccountType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="productClassification" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Product classification [CODELIST: ProductClassification]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="accountStatus" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Status of the bank account. [CODELIST: BankAccountStatus]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="overdraft">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="loanId" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>loan id of overdraft</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="productCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Product codes codebook  [CODELIST: ProductCodes]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="status" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>ovedraft status [CODELIST: LoanStatus]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="limit">
                                    <xs:annotation>
                                    <xs:documentation>Overdraft Limit</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="savingAccountOptions">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="optionCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>ACTIVE option code (typ aktivní výpovědní lhůty) [CODELIST: SavingsAccountOption]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="currencyFolders">
                                    <xs:annotation>
                                    <xs:documentation>Set of currency folders.</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="currencyFolder">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency code of the currency folder. [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0"
                                    name="currencyFolderStatus" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Status of the currency folder. [CODELIST: CurrencyFolderStatus]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0" name="balances">
                                    <xs:annotation>
                                    <xs:documentation>Set of balances of the account.</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded" name="balance">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    name="balanceCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>[CODELIST: BALANCE_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element name="amount">
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="configProfile">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="item">
                                    <xs:annotation>
                                    <xs:documentation>Account config profile item</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:choice>

                                    <xs:element name="cpAccountType">
                                    <xs:annotation>
                                    <xs:documentation>type of the account (single or multi currency) [LOV: SINGLE_CF;MULTI_CF]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="SINGLE_CF"/>

                                    <xs:enumeration value="MULTI_CF"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:choice>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="insurance">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="trgReferenceId">
                                    <xs:annotation>
                                    <xs:documentation>Target system identification of insurance</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="code" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Type of insurance [CODELIST: AccountInsuranceTypeCode]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="status" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>State of insurance [CODELIST: AccountInsuranceStatus]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element minOccurs="0" name="resultInfo">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded" name="resultItem">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="code" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A code of the error/warning [CODELIST: ERROR_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="description" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A description of the error/warning</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
