<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/services/remoteCashAccount/GetTransactionList_1_0/1.0"
    version="1.0.0"
    xmlns:opgettransactionlist_1_0="http://rb.cz/services/remoteCashAccount/GetTransactionList_1_0/1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="opGetTransactionList_1_0">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="1" name="psu">
                    <xs:annotation>
                        <xs:documentation>Payment Service User</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="IpAddress" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Identifier of a customer's IP address from which he/she is connected to the TPP infrastructure. It might be in the format of IPv4 o IPv6 address. ASPSP shall indicate which values are acceptable</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element minOccurs="0" name="deviceOS" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Customer's device and/or operating system identification from which he/she is connected to the TPP infrastructure</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element minOccurs="0" name="userAgent" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Customer's web browser of other client device identification from which he/she is connected to the TPP infrastructure. Agent header field of the http request between PSU and TPP.)</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element minOccurs="0" name="session" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>A customer session id</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element minOccurs="0" name="id" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Unique customer identification</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element minOccurs="1" name="userId" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>External ID of the user/client in remote bank (hash)</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element minOccurs="1" name="accountId" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>ResourceId from list of account</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element minOccurs="0" name="subscriptionId" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>subscriptionId</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element minOccurs="0" name="paging">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="startIndex" type="xs:integer">
                                <xs:annotation>
                                    <xs:documentation>Index of the first item within the returned collection. Default value is 1</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0"
                                name="maxResults" type="xs:integer">
                                <xs:annotation>
                                    <xs:documentation>Maximal number of returned collection items</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element minOccurs="0" name="sourceType">
                    <xs:annotation>
                        <xs:documentation>The way how the response data NWB wants to obtained [LOV: ONLINE;OFFLINE;OFFLINE_WITH_REFRESH]</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:enumeration value="ONLINE"/>
                            <xs:enumeration value="OFFLINE"/>
                            <xs:enumeration value="OFFLINE_WITH_REFRESH"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element minOccurs="0" name="schgCrit">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element minOccurs="0" name="intrvl">
                                <xs:annotation>
                                    <xs:documentation>Interval date</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence maxOccurs="1" minOccurs="0">
                                    <xs:element minOccurs="0"
                                    name="vldFr" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Valid from - duration from date</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="vldTo" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Valid to - duration to date</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element minOccurs="0" name="bookgSts">
                                <xs:annotation>
                                    <xs:documentation>Possible statuses of transactions requested from the history. [LOV: BOOKED;PENDING;ALL]</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:enumeration value="BOOKED"/>
                                    <xs:enumeration value="PENDING"/>
                                    <xs:enumeration value="ALL"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="opGetTransactionList_1_0_res">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="paging">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="endOfRecords" type="xs:boolean">
                                <xs:annotation>
                                    <xs:documentation>Flag indicating end of the records</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element minOccurs="0" name="txs">
                    <xs:annotation>
                        <xs:documentation>Transactions</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded" name="tx">
                                <xs:annotation>
                                    <xs:documentation>Transaction</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element minOccurs="0" name="txId">
                                    <xs:annotation>
                                    <xs:documentation>Transaction ID</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="txAmt">
                                    <xs:annotation>
                                    <xs:documentation>Transaction amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="amt" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>amount of money</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="ccy" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>currency of the amount - ISO code</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="reqdAmt">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="xchgRate" type="xs:decimal">
                                    <xs:annotation>
                                    <xs:documentation>Exchange rate</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="txDt" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Transaction date; value date; date which is use to count interest; e.g. date when money were withdrawn from ATM</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="acctgDt" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Accounting date; Date when transaction ocurred; e.g. date when withdrawn money from ATM were processed on the back account (date of transaction in the transaction history)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="orgtrRefId">
                                    <xs:annotation>
                                    <xs:documentation>End to End ID; The Originator’s reference of the Credit Transfer Transaction</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="35"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="rmtInf">
                                    <xs:annotation>
                                    <xs:documentation>Remittance information. Typically field :70 from Swift or field remittanceInformationUnstructured from SEPA.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="140"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="ctrPty">
                                    <xs:annotation>
                                    <xs:documentation>Counterparty</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    minOccurs="0" name="acctNm">
                                    <xs:annotation>
                                    <xs:documentation>bank account name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="140"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="acctNb">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    minOccurs="0"
                                    name="iban" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>IBAN code of the bank account</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="txCtgy" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Transaction category [CODELIST: TransactionCategory]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="mvmntTp" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Movement type [CODELIST: MovementType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
