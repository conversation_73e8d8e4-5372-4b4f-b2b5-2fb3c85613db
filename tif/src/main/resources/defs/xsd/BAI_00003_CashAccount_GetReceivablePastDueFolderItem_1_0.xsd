<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/services/cashAccount/GetReceivablePastDueFolderItem_1_0/1.0"
    version="1.0.0"
    xmlns:opgetreceivablepastduefolderitem_1_0="http://rb.cz/services/cashAccount/GetReceivablePastDueFolderItem_1_0/1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="opGetReceivablePastDueFolderItem_1_0">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="identification">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="trgReferenceId">
                                <xs:annotation>
                                    <xs:documentation>Unique identification of the entity from provider point of view</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:choice>
                    <xs:element name="accountNumber">
                        <xs:complexType>
                            <xs:sequence>
                                <xs:element maxOccurs="1" minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                                <xs:element name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                                <xs:element name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of the bank account [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                            </xs:sequence>
                        </xs:complexType>
                    </xs:element>
                    <xs:element name="loanId">
                        <xs:complexType>
                            <xs:sequence>
                                <xs:element name="entityId" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Id of related entity</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element name="idType">
                                    <xs:annotation>
                                    <xs:documentation>Type of entityId [LOV: GLOBAL;TRS;MID]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:enumeration value="GLOBAL"/>
                                    <xs:enumeration value="TRS"/>
                                    <xs:enumeration value="MID"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                            </xs:sequence>
                        </xs:complexType>
                    </xs:element>
                </xs:choice>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="opGetReceivablePastDueFolderItem_1_0_res">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="receivablePastDueDetail">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="transactionNumber">
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="typeCode" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Transaction type [CODELIST: TRANSACTION_TYPE_CODES]</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element minOccurs="0" name="amount" type="xs:decimal"/>
                            <xs:element name="currencyCode" type="xs:string"/>
                            <xs:element name="transactionDate" type="xs:dateTime"/>
                            <xs:element name="accountingDate" type="xs:dateTime"/>
                            <xs:element minOccurs="0" name="originatorMessage">
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="240"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element minOccurs="0" name="counterParty">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element minOccurs="0" name="accountName">
                                    <xs:annotation>
                                    <xs:documentation>Bank account name</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="140"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="accountNumber">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="message">
                                    <xs:annotation>
                                    <xs:documentation>Delivered information</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="140"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="internalAccount" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Internal account</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element minOccurs="0"
                                name="paymentCardNumber" type="xs:string"/>
                            <xs:element minOccurs="0" name="requestedAmount">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="amount" type="xs:decimal"/>
                                    <xs:element name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency code of the amount</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="exchangeRate" type="xs:decimal"/>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="direction">
                                <xs:annotation>
                                    <xs:documentation>[LOV: All;Credit;Debit]</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:enumeration value="All"/>
                                    <xs:enumeration value="Credit"/>
                                    <xs:enumeration value="Debit"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element minOccurs="0" name="symbols">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element minOccurs="0" name="variable">
                                    <xs:annotation>
                                    <xs:documentation>Variable symbol</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="constant">
                                    <xs:annotation>
                                    <xs:documentation>Constant symbol</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="specific">
                                    <xs:annotation>
                                    <xs:documentation>Specific symbol</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element minOccurs="0" name="fees">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element minOccurs="0" name="changeFee">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="amount">
                                    <xs:annotation>
                                    <xs:documentation>Amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of amount [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="messageFee">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="amount">
                                    <xs:annotation>
                                    <xs:documentation>Amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of amount [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="transactionFee">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="amount">
                                    <xs:annotation>
                                    <xs:documentation>Amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of amount [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="flags">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="allowRepeatTransaction" type="xs:boolean"/>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="allowRefundTransaction" type="xs:boolean"/>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="transactionCategory" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>[CODELIST: TransactionCategory]</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="movementType" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>[CODELIST: MovementType]</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="actionTypeCode" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>A type of the client´s action (eg. transfer, payment, standing payment, ..). [CODELIST: ACTION_TYPE_CODE]</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element minOccurs="0" name="resultInfo">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded"
                                minOccurs="1" name="resultItem">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="code" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A code of the error/warning [CODELIST: ERROR_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="description">
                                    <xs:annotation>
                                    <xs:documentation>A description of the error/warning</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="2000"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
