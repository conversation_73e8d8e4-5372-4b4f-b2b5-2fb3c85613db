<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/services/loan/CancelApp_1_0"
    version="1.0.0"
    xmlns:opcancelapp_1_0="http://rb.cz/services/loan/CancelApp_1_0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="opCancelApp_1_0">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="sellerChannel">
                    <xs:annotation>
                        <xs:documentation>(SPB|GIB) last used channel.</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:maxLength value="255"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element name="userId">
                    <xs:annotation>
                        <xs:documentation>Siebel party ID or personal number (CZA) of a user which innitiated call of this service.</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:maxLength value="15"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element name="userIdType">
                    <xs:annotation>
                        <xs:documentation>[LOV: PersonalNumber;PartyId]</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:enumeration value="PersonalNumber"/>
                            <xs:enumeration value="PartyId"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element minOccurs="1" name="applicationId">
                    <xs:annotation>
                        <xs:documentation>Unique identification of an application</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:maxLength value="30"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="opCancelApp_1_0_res">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
</xs:schema>
