<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/services/consumerLoan/GetOffers_1_0/1.0"
    version="1.0.0"
    xmlns:bai_00029_consumerloan-getoffers_1_0="http://rb.cz/services/consumerLoan/GetOffers_1_0/1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="req">
        <xs:complexType>
            <xs:sequence>
                <xs:element maxOccurs="1" minOccurs="0"
                    name="channelCode" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Identification of the channel which has been request sent.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="1"
                    name="cultureCode" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Language code that is configured in the application that launched the service call [CODELIST: Language]</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="userId" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Unique identification of the user, who initiates the request.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="userType">
                    <xs:annotation>
                        <xs:documentation>Additional info for identification internal user (cza...) or external user (technical accounts, 3rd parties, clients...) [LOV: INT;EXT]</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:enumeration value="INT"/>
                            <xs:enumeration value="EXT"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element name="query">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="partyId">
                                <xs:annotation>
                                    <xs:documentation>Party Id of debtor (owner of current account) (numbers only)</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:minLength value="1"/>
                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="productCode" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Product codes codebook for MultiChannel [CODELIST: ProductCodes]</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="1" name="currencyCode">
                                <xs:annotation>
                                    <xs:documentation>Currency of the bank account [CODELIST: Currency]</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:minLength value="1"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="loanFrom" type="xs:date">
                                <xs:annotation>
                                    <xs:documentation>Application date (žádost)</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="1"
                                name="requestedLimit" type="xs:integer">
                                <xs:annotation>
                                    <xs:documentation>Requested loan limit</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="1" name="negativeInterest">
                                <xs:annotation>
                                    <xs:documentation>Interest rate (pa in % , 2 decimals)</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:decimal">
                                    <xs:fractionDigits value="2"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="1"
                                name="instalmentCount" type="xs:integer">
                                <xs:annotation>
                                    <xs:documentation>Number of instalments (payments)</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="repaymentSchedule" type="xs:boolean">
                                <xs:annotation>
                                    <xs:documentation>true = send me complete repayment schedule</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="res">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="0" name="activities">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded"
                                minOccurs="0" name="activityResult">
                                <xs:complexType>
                                    <xs:sequence maxOccurs="1" minOccurs="1">
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="activityId"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Unique identification of activity within an enterprise service</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="resultCode"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Result code of application service call. Is empty for non-call activities.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="errorCode"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Error code of application service call. Is empty for non-call activities</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="errorDescription"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Error description of application service call. Is empty for non-call activities</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1" name="data">
                                    <xs:annotation>
                                    <xs:documentation>Set of xpath-value pairs</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="0" name="activityData">
                                    <xs:complexType>
                                    <xs:sequence
                                    maxOccurs="1" minOccurs="1">

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="xpath"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Selection of data that should be in activity result. Xpath points to activity outcome. Only Xpath pointing to scalar values are allowed</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="value"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Value of data</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element name="offer">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="1" minOccurs="1"
                                name="instalmentAmount" type="xs:double">
                                <xs:annotation>
                                    <xs:documentation>Monthly payment in loan currency - no insurance and fees</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="1" name="annualCostRate">
                                <xs:annotation>
                                    <xs:documentation>RPSN</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:decimal">
                                    <xs:fractionDigits value="2"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="1" name="payableTotalAmount">
                                <xs:annotation>
                                    <xs:documentation>Total amout payable by debtor</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:decimal">
                                    <xs:fractionDigits value="2"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="1" name="dayInterest">
                                <xs:annotation>
                                    <xs:documentation>Daily interest</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:decimal">
                                    <xs:fractionDigits value="2"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="listOfRepayments">
                                <xs:annotation>
                                    <xs:documentation>Repayment collection</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="repayment">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="date" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>repayment (due) date </xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="amount">
                                    <xs:annotation>
                                    <xs:documentation>repayment amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:fractionDigits value="2"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="currencyCode">
                                    <xs:annotation>
                                    <xs:documentation>Currency of the repayment [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:minLength value="1"/>

                                    <xs:maxLength value="3"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="debtType">
                                    <xs:annotation>
                                    <xs:documentation>code of type of instalment (principal, interest, fee etc )</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:minLength value="1"/>

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
