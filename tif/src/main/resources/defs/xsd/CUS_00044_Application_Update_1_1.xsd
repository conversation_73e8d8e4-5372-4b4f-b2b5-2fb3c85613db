<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/services/application/Update_1_1"
    version="1.0.0"
    xmlns:opupdate_1_1="http://rb.cz/services/application/Update_1_1" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="opUpdate_1_1">
        <xs:complexType>
            <xs:sequence>
                <xs:element maxOccurs="1" minOccurs="1" name="application">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="mainApplicationId">
                                <xs:annotation>
                                    <xs:documentation>ID of the MAIN application which we want to update OR if we want to update an additional application, then enter the ID of its related MAIN application.</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:choice>
                                    <xs:element
                                    name="primaryApplicationId" type="xs:long">
                                    <xs:annotation>
                                    <xs:documentation>ID of application in ONB which has relation=MAIN</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="secondaryApplicationId">
                                    <xs:annotation>
                                    <xs:documentation>ID of application in Siebel which has relation=MAIN</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:choice>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element name="updatedApplicationId">
                                <xs:annotation>
                                    <xs:documentation>ID of the application which we want to update. We can update either a MAIN application or an ADDITIONAL application. If we want to update an ADDITIONAL application, then secondaryApplicationId MUST be entered.</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:choice>
                                    <xs:element
                                    name="primaryApplicationId" type="xs:long">
                                    <xs:annotation>
                                    <xs:documentation>ID in ONB of the application which we want to update. Can be used ONLY IF we want to update a MAIN application. (In order to update an ADDITIONAL application, then secondaryApplicationId MUST be used.)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="secondaryApplicationId">
                                    <xs:annotation>
                                    <xs:documentation>Siebel ID of the application which we want to update. CAN be used to update a MAIN or an ADDITIONAL application.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="30"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:choice>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="1" name="basicInfo">
                                <xs:annotation>
                                    <xs:documentation>SBL
</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="1"
                                    name="applicationType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Type of application - based on codelist ApplicationTypeCode</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="status">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="applicationStatus" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Status of the application - primary used for SBL [CODELIST: ApplicationStatus]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0"
                                    name="applicationSubStatus" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Detailed status of actual state of application process [CODELIST: ApplicationStatus]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="rejectionReason">
                                    <xs:annotation>
                                    <xs:documentation>ID of rejection reason when REJECTED status and sub status is specified. Values taken from codelist ApplRejectionReason in MCH codeBook</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:long">
                                    <xs:fractionDigits value="0"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                            <xs:element minOccurs="0" name="detail">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="clientUserName">
                                    <xs:annotation>
                                    <xs:documentation>Client username (login) which is used for authentication in Authentication server </xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="256"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="checklist">
                                    <xs:annotation>
                                    <xs:documentation>Check list of manual verification of application in bank</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="check">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="type" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Type of check (Enumeration described in A_ONB_ApplicationUpdate AS)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="result" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Result of check</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="note"
                                    nillable="false" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Optional note for the check</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="pennyPayment">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="transactionDetail">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="debitAccount">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="accountNumber">
                                    <xs:annotation>
                                    <xs:documentation>if fulfilled then return addresses for credit card delivery connected with this account</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank [CODELIST: BANK_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="currencyFolders">
                                    <xs:annotation>
                                    <xs:documentation>Set of currency folders</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="0" name="currencyFolder">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of the bank account [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="mainCurrency" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>flag if this is main currency code of the bank account</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="creditAccount">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="accountNumber">
                                    <xs:annotation>
                                    <xs:documentation>if fulfilled then return addresses for credit card delivery connected with this account</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank [CODELIST: BANK_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="currencyFolders">
                                    <xs:annotation>
                                    <xs:documentation>Set of currency folders</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="0" name="currencyFolder">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of the bank account [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="mainCurrency" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>flag if this is main currency code of the bank account</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="amount" type="xs:decimal"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of the bank account [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="debitName" type="xs:string"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="debitInfo" type="xs:string"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="message" type="xs:string"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="transactionDate"
                                    nillable="false" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Date of received transaction from defined account
</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="savingAccount">
                                    <xs:annotation>
                                    <xs:documentation>Represents saving account of the user</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="accountNumber">
                                    <xs:annotation>
                                    <xs:documentation>if fulfilled then return addresses for credit card delivery connected with this account</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank [CODELIST: BANK_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1" name="currencyFolders">
                                    <xs:annotation>
                                    <xs:documentation>Set of currency folders</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="currencyFolder">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of the bank account [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="mainCurrency" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>flag if this is main currency code of the bank account</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="paymentCard">
                                    <xs:annotation>
                                    <xs:documentation>Represents payment card of the user</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="idPaymentCard" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>ID of the payment card in the primary system</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="mobilityApplication">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="status" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Status of application; enumeration based on IBP's internal codelist; ONB is indifferent to the received values</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="statusDate" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Date of status</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="registryValidation">
                                    <xs:annotation>
                                    <xs:documentation>Information on the verification of an FOP's data in the Register of Companies - relevant only to FOP (individual entrepreneur)</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="validationDate" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>When the verification was performed</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="1"
                                    name="userId" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Who performed the verification</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="opUpdate_1_1_res">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
</xs:schema>
