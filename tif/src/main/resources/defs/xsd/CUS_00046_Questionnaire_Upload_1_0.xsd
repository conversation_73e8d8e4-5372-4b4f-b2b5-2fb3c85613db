<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/services/questionnaire/Upload_1_0/1.0"
    version="1.0.0"
    xmlns:opupload_1_0="http://rb.cz/services/questionnaire/Upload_1_0/1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="opUpload_1_0">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="questionnaire">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="questionnaireName">
                                <xs:annotation>
                                    <xs:documentation>Identification of unique questionnaire.</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="partyId">
                                <xs:annotation>
                                    <xs:documentation>Party Id (Siebel identifier)</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="userId">
                                <xs:annotation>
                                    <xs:documentation>Unique identification of the user, who initiates the request.
maxLength is derived from SBL logical data model, which is master for questionnaires.</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element minOccurs="0" name="mprodCode">
                                <xs:annotation>
                                    <xs:documentation>MPROD code from product catalogue
maxLength is derived from SBL logical data model, which is master for questionnaires.</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element minOccurs="0" name="productInstanceId">
                                <xs:annotation>
                                    <xs:documentation>Id of a product instance to which the completed questionnaire is related to (if exists)
maxLength is derived from SBL logical data model, which is master for questionnaires.</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="100"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="createdOnSource" type="xs:dateTime">
                                <xs:annotation>
                                    <xs:documentation>Date and time of questionnaire submiting by an author or certification or completion</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="questions">
                                <xs:annotation>
                                    <xs:documentation>Collection of questions. Here has to be at least one question. </xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="question">
                                    <xs:annotation>
                                    <xs:documentation>One until unbounded questions necessary. </xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="questionName">
                                    <xs:annotation>
                                    <xs:documentation>Identification of unique question.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="questionCategory">
                                    <xs:annotation>
                                    <xs:documentation>Identification of unique category of according questions.
maxLength is derived from SBL logical data model, which is master for questionnaires.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="comment">
                                    <xs:annotation>
                                    <xs:documentation>Free text comment to the question</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="200"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="answers">
                                    <xs:annotation>
                                    <xs:documentation>Collection of answers. Answers are unnecessaries.</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="0" name="answer">
                                    <xs:annotation>
                                    <xs:documentation>Answers are unnecessaries, but can be filled to unbounded.</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="answerName">
                                    <xs:annotation>
                                    <xs:documentation>Identification of unique answer.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="answerType">
                                    <xs:annotation>
                                    <xs:documentation>Data type of answer [LOV: STRING;INTEGER;LONG;DECIMAL;DOUBLE;BOOLEAN;DATE;TIME;DATETIME;TIMESTAMP;BINARY]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="STRING"/>

                                    <xs:enumeration value="INTEGER"/>

                                    <xs:enumeration value="LONG"/>

                                    <xs:enumeration value="DECIMAL"/>

                                    <xs:enumeration value="DOUBLE"/>

                                    <xs:enumeration value="BOOLEAN"/>

                                    <xs:enumeration value="DATE"/>

                                    <xs:enumeration value="TIME"/>

                                    <xs:enumeration value="DATETIME"/>

                                    <xs:enumeration value="TIMESTAMP"/>

                                    <xs:enumeration value="BINARY"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="answerValue">
                                    <xs:annotation>
                                    <xs:documentation>Free text value</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="255"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="comment">
                                    <xs:annotation>
                                    <xs:documentation>Free text comment to the answer</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="200"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="opUpload_1_0_res">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="questionnaireId">
                    <xs:annotation>
                        <xs:documentation>Technical identification of unique questionnaire.</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:maxLength value="15"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
