<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/services/loan/GetOfferParam_1_2/1.0"
    version="1.0.0"
    xmlns:opgetofferparam_1_2="http://rb.cz/services/loan/GetOfferParam_1_2/1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="opGetOfferParam_1_2">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="offerId">
                    <xs:annotation>
                        <xs:documentation>Unique identification of an offer</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:maxLength value="512"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element name="partyId">
                    <xs:annotation>
                        <xs:documentation>Loan owner party id (Siebel identifier)</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:maxLength value="15"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element minOccurs="0" name="occupationType" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Loan owner occupation type. [CODELIST: OccupationType]</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element minOccurs="0" name="requestedAmount">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="amount">
                                <xs:annotation>
                                    <xs:documentation>Loan amount requested by user.  If NULL then maximal offered amount is used.</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:decimal">
                                    <xs:totalDigits value="18"/>
                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="currencyCode" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Currency of amount [CODELIST: Currency]</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element minOccurs="0" name="requestedInstalment">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element minOccurs="1"
                                name="instalmentCount" type="xs:integer">
                                <xs:annotation>
                                    <xs:documentation>Requested instalment count. If NULL then greatest available number is used.</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element minOccurs="1"
                                name="frequencyCode" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Time unit for requestedInstalmentCount. [CODELIST: FrequencyType]</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="bonusCampaign">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="1" minOccurs="1"
                                name="bonusCampaign" type="xs:boolean">
                                <xs:annotation>
                                    <xs:documentation>flag if it is a bonus campaign (True for bonus campaign)</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element maxOccurs="1" minOccurs="0" name="bonusInterestRate">
                                <xs:annotation>
                                    <xs:documentation>Interest rate of bonus campaign</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:decimal">
                                    <xs:totalDigits value="18"/>
                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="interestRateDiscount">
                    <xs:annotation>
                        <xs:documentation>Lowering of standard interest rate (percentage point decrease)</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:decimal">
                            <xs:fractionDigits value="5"/>
                            <xs:totalDigits value="18"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="opGetOfferParam_1_2_res">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="offerParamSet">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="productCode" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Offered loan product code [CODELIST: ProductCodes]</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="currencyCode" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Offered loan currency code [CODELIST: Currency]</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:choice>
                                <xs:element name="loanPI">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="limits">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="clientMinAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - minimal principal amount for particular loan product type and client</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="clientMaxAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - maximal principal amount for particular loan product type and client </xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="offerMaxAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - maximal principal amount for particular offer</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="instalmentMinCount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Minimal instalment count</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="instalmentMaxCount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Maximal instalment count</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="allowedCombinations">
                                    <xs:annotation>
                                    <xs:documentation>Allowed combinations of (repayment period; maximal principal amount)</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="allowedCombination">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    name="instalmentCount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Instalment count</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element name="maxAmount">
                                    <xs:annotation>
                                    <xs:documentation>Maximal allowed amount for given instalment count</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="frequencyType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Time unit for instalment [CODELIST: FrequencyType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="drawingAccounts">
                                    <xs:annotation>
                                    <xs:documentation>List of accounts which can be used for drawing</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="accountNumber">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of the bank account [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="instalmentAccounts">
                                    <xs:annotation>
                                    <xs:documentation>List of accounts which can be used for instalments</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="accountNumber">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of the bank account [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="requested">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="instalment">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="amount">
                                    <xs:annotation>
                                    <xs:documentation>Instalment amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="interestRate">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="totalRate">
                                    <xs:annotation>
                                    <xs:documentation>Total interest rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="period" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Interest period [CODELIST: FrequencyType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0" name="float">
                                    <xs:annotation>
                                    <xs:documentation>Filled only for float type of interest rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="baseRate">
                                    <xs:annotation>
                                    <xs:documentation>Base interest rate (PRIBOR, LIBOR, ...)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="baseRateType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Type of base rate (PRIBOR-1M, PRIBOR-3M, ...)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element name="rate">
                                    <xs:annotation>
                                    <xs:documentation>Added rate. TotalRate = baseRate + rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="insurance">
                                    <xs:annotation>
                                    <xs:documentation>Offered insurance products, sorted according to general business rule for loan insurance offer</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    minOccurs="1"
                                    name="mandatory" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Flag - TRUE if insurance is mandatory, otherwise FALSE</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="defaultProduct" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Default insurance product code [CODELIST: LoanInsuranceCodes]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="1" name="availableProducts">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded" name="insuranceProduct">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    name="productCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Type of insurance for loan [CODELIST: LoanInsuranceCodes]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="feePeriod" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Insurance fee charge period [CODELIST: FrequencyType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element name="feeAmount">
                                    <xs:annotation>
                                    <xs:documentation>Insurance fee amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="bonusCampaign">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="payableTotalAmount">
                                    <xs:annotation>
                                    <xs:documentation>Total amount to be paid (for standard rate)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="bonusPayableTotalAmount">
                                    <xs:annotation>
                                    <xs:documentation>Total amount to be paid (for bonus rate)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element name="topUpPI">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="limits">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="clientMinAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - minimal principal amount for particular loan product type and client</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="clientMaxAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - maximal principal amount for particular loan product type and client </xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="offerMaxAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - maximal principal amount for particular offer</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="instalmentMinCount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Minimal instalment count</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="instalmentMaxCount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Maximal instalment count</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="allowedCombinations">
                                    <xs:annotation>
                                    <xs:documentation>Allowed combinations of (repayment period; maximal principal amount)</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="allowedCombination">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    name="instalmentCount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Instalment count</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element name="maxAmount">
                                    <xs:annotation>
                                    <xs:documentation>Maximal allowed amount for given instalment count</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="frequencyType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Time unit for instalment [CODELIST: FrequencyType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="drawingAccounts">
                                    <xs:annotation>
                                    <xs:documentation>List of accounts which can be used for drawing</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="accountNumber">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of the bank account [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="instalmentAccounts">
                                    <xs:annotation>
                                    <xs:documentation>List of accounts which can be used for instalments</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="accountNumber">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of the bank account [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="requested">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="instalment">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="amount">
                                    <xs:annotation>
                                    <xs:documentation>Instalment amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="interestRate">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="totalRate">
                                    <xs:annotation>
                                    <xs:documentation>Total interest rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="period" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Interest period [CODELIST: FrequencyType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0" name="float">
                                    <xs:annotation>
                                    <xs:documentation>Filled only for float type of interest rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="baseRate">
                                    <xs:annotation>
                                    <xs:documentation>Base interest rate (PRIBOR, LIBOR, ...)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="baseRateType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Type of base rate (PRIBOR-1M, PRIBOR-3M, ...)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element name="rate">
                                    <xs:annotation>
                                    <xs:documentation>Added rate. TotalRate = baseRate + rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="insurance">
                                    <xs:annotation>
                                    <xs:documentation>Offered insurance products, sorted according to general business rule for loan insurance offer</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="mandatory" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Flag - TRUE if insurance is mandatory, otherwise FALSE</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="defaultProduct" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Default insurance product code [CODELIST: LoanInsuranceCodes]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="availableProducts">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="unbounded" name="insuranceProduct">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    name="productCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Type of insurance for loan [CODELIST: LoanInsuranceCodes]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="feePeriod" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Insurance fee charge period [CODELIST: FrequencyType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element name="feeAmount">
                                    <xs:annotation>
                                    <xs:documentation>Insurance fee amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="current">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="loanReceivable">
                                    <xs:annotation>
                                    <xs:documentation>Current loan receivable</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="floatInterestRate" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Flag - TRUE if interest rate is float, oherwise FALSE</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="bonusCampaign">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="payableTotalAmount">
                                    <xs:annotation>
                                    <xs:documentation>Total amount to be paid (for standard rate)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="bonusPayableTotalAmount">
                                    <xs:annotation>
                                    <xs:documentation>Total amount to be paid (for bonus rate)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element name="overdraftPI">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="limits">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="clientMinAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - minimal principal amount for particular loan product type and client</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="clientMaxAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - maximal principal amount for particular loan product type and client </xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="offerMaxAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - maximal principal amount for particular offer</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="currentAccounts">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="currentAccount">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="accountNumber">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of the bank account [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element name="concessionaryInterestLimit">
                                    <xs:annotation>
                                    <xs:documentation>Limit for concessionary (zero) interest</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="requested">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="interestRate">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="totalRate">
                                    <xs:annotation>
                                    <xs:documentation>Total interest rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="period" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Interest period [CODELIST: FrequencyType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0" name="float">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="baseRate">
                                    <xs:annotation>
                                    <xs:documentation>Base interest rate (PRIBOR, LIBOR, ...)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="baseRateType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Type of base rate (PRIBOR-1M, PRIBOR-3M, ...)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element name="rate">
                                    <xs:annotation>
                                    <xs:documentation>Added rate. TotalRate = baseRate + rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element name="overdraftTopUpPI">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="limits">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="clientMinAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - minimal principal amount for particular loan product type and client</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="clientMaxAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - maximal principal amount for particular loan product type and client </xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="offerMaxAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - maximal principal amount for particular offer</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="currentAccounts">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="currentAccount">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="accountNumber">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of the bank account [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>

                                    <xs:element name="concessionaryInterestLimit">
                                    <xs:annotation>
                                    <xs:documentation>Limit for concessionary (zero) interest</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="requested">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="interestRate">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="totalRate">
                                    <xs:annotation>
                                    <xs:documentation>Total interest rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="period" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Interest period [CODELIST: FrequencyType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0" name="float">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="baseRate">
                                    <xs:annotation>
                                    <xs:documentation>Base interest rate (PRIBOR, LIBOR, ...)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="baseRateType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Type of base rate (PRIBOR-1M, PRIBOR-3M, ...)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element name="rate">
                                    <xs:annotation>
                                    <xs:documentation>Added rate. TotalRate = baseRate + rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="current">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="loanLimitAmount">
                                    <xs:annotation>
                                    <xs:documentation>Current ovedraft amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="floatInterestRate" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Flag - TRUE if interest rate is float, oherwise FALSE</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element name="interestFreePI">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="limits">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="clientMinAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - minimal principal amount for particular loan product type and client</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="clientMaxAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - maximal principal amount for particular loan product type and client </xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="offerMaxAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - maximal principal amount for particular offer</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="currentAccounts">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="currentAccount">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="accountNumber">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of the bank account [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="requested">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="interestRate">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="totalRate">
                                    <xs:annotation>
                                    <xs:documentation>Total interest rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="period" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Interest period [CODELIST: FrequencyType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0" name="float">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="baseRate">
                                    <xs:annotation>
                                    <xs:documentation>Base interest rate (PRIBOR, LIBOR, ...)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="baseRateType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Type of base rate (PRIBOR-1M, PRIBOR-3M, ...)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element name="rate">
                                    <xs:annotation>
                                    <xs:documentation>Added rate. TotalRate = baseRate + rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element name="loanMIC">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="limits">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="clientMinAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - minimal principal amount for particular loan product type and client</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="clientMaxAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - maximal principal amount for particular loan product type and client </xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="offerMaxAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - maximal principal amount for particular offer</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="zoneDelimiterAmount">
                                    <xs:annotation>
                                    <xs:documentation>Currently MIC only. Treshold value between zone I and zone II. The calculation is based on current credit exposure of client. If requested limit &lt;= Delimiter then zone I otherwise zone II.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="instalmentMinCount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Minimal instalment count</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="instalmentMaxCount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Maximal instalment count</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="allowedCombinations">
                                    <xs:annotation>
                                    <xs:documentation>Allowed combinations of (repayment period; maximal principal amount)</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="allowedCombination">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    name="instalmentCount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Instalment count</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element name="maxAmount">
                                    <xs:annotation>
                                    <xs:documentation>Maximal allowed amount for given instalment count</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="frequencyType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Time unit for instalment [CODELIST: FrequencyType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="drawingAccounts">
                                    <xs:annotation>
                                    <xs:documentation>List of accounts which can be used for drawing</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="accountNumber">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of the bank account [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="instalmentAccounts">
                                    <xs:annotation>
                                    <xs:documentation>List of accounts which can be used for instalments</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="accountNumber">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of the bank account [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="requested">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="instalment">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="amount">
                                    <xs:annotation>
                                    <xs:documentation>Instalment amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="interestRate">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="totalRate">
                                    <xs:annotation>
                                    <xs:documentation>Total interest rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="period" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Interest period [CODELIST: FrequencyType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0" name="float">
                                    <xs:annotation>
                                    <xs:documentation>Filled only for float type of interest rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="baseRate">
                                    <xs:annotation>
                                    <xs:documentation>Base interest rate (PRIBOR, LIBOR, ...)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="baseRateType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Type of base rate (PRIBOR-1M, PRIBOR-3M, ...)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element name="rate">
                                    <xs:annotation>
                                    <xs:documentation>Added rate. TotalRate = baseRate + rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element name="topUpMIC">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="limits">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="clientMinAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - minimal principal amount for particular loan product type and client</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="clientMaxAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - maximal principal amount for particular loan product type and client </xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="offerMaxAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - maximal principal amount for particular offer</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="zoneDelimiterAmount">
                                    <xs:annotation>
                                    <xs:documentation>Currently MIC only. Treshold value between zone I and zone II. The calculation is based on current credit exposure of client. If requested limit &lt;= Delimiter then zone I otherwise zone II.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="instalmentMinCount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Minimal instalment count</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="instalmentMaxCount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Maximal instalment count</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="allowedCombinations">
                                    <xs:annotation>
                                    <xs:documentation>Allowed combinations of (repayment period; maximal principal amount)</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="allowedCombination">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    name="instalmentCount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Instalment count</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element name="maxAmount">
                                    <xs:annotation>
                                    <xs:documentation>Maximal allowed amount for given instalment count</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="frequencyType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Time unit for instalment [CODELIST: FrequencyType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="drawingAccounts">
                                    <xs:annotation>
                                    <xs:documentation>List of accounts which can be used for drawing</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="accountNumber">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of the bank account [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="instalmentAccounts">
                                    <xs:annotation>
                                    <xs:documentation>List of accounts which can be used for instalments</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="accountNumber">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of the bank account [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="requested">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="instalment">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="amount">
                                    <xs:annotation>
                                    <xs:documentation>Instalment amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="interestRate">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="totalRate">
                                    <xs:annotation>
                                    <xs:documentation>Total interest rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="period" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Interest period [CODELIST: FrequencyType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0" name="float">
                                    <xs:annotation>
                                    <xs:documentation>Filled only for float type of interest rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="baseRate">
                                    <xs:annotation>
                                    <xs:documentation>Base interest rate (PRIBOR, LIBOR, ...)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="baseRateType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Type of base rate (PRIBOR-1M, PRIBOR-3M, ...)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element name="rate">
                                    <xs:annotation>
                                    <xs:documentation>Added rate. TotalRate = baseRate + rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="current">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="loanReceivable">
                                    <xs:annotation>
                                    <xs:documentation>Current loan receivable</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="floatInterestRate" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Flag - TRUE if interest rate is float, oherwise FALSE</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element name="overdraftMIC">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="limits">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="clientMinAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - minimal principal amount for particular loan product type and client</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="clientMaxAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - maximal principal amount for particular loan product type and client </xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="offerMaxAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - maximal principal amount for particular offer</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="zoneDelimiterAmount">
                                    <xs:annotation>
                                    <xs:documentation>Currently MIC only. Treshold value between zone I and zone II. The calculation is based on current credit exposure of client. If requested limit &lt;= Delimiter then zone I otherwise zone II.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="currentAccounts">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="currentAccount">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="accountNumber">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of the bank account [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="requested">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="interestRate">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="totalRate">
                                    <xs:annotation>
                                    <xs:documentation>Total interest rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="period" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Interest period [CODELIST: FrequencyType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0" name="float">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="baseRate">
                                    <xs:annotation>
                                    <xs:documentation>Base interest rate (PRIBOR, LIBOR, ...)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="baseRateType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Type of base rate (PRIBOR-1M, PRIBOR-3M, ...)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element name="rate">
                                    <xs:annotation>
                                    <xs:documentation>Added rate. TotalRate = baseRate + rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element name="overdraftIncMIC">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="limits">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="clientMinAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - minimal principal amount for particular loan product type and client</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="clientMaxAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - maximal principal amount for particular loan product type and client </xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="offerMaxAmount">
                                    <xs:annotation>
                                    <xs:documentation>Limit - maximal principal amount for particular offer</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="zoneDelimiterAmount">
                                    <xs:annotation>
                                    <xs:documentation>Currently MIC only. Treshold value between zone I and zone II. The calculation is based on current credit exposure of client. If requested limit &lt;= Delimiter then zone I otherwise zone II.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="currentAccounts">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="currentAccount">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="accountNumber">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of the bank account [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="requested">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="interestRate">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="totalRate">
                                    <xs:annotation>
                                    <xs:documentation>Total interest rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="period" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Interest period [CODELIST: FrequencyType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0" name="float">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="baseRate">
                                    <xs:annotation>
                                    <xs:documentation>Base interest rate (PRIBOR, LIBOR, ...)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="baseRateType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Type of base rate (PRIBOR-1M, PRIBOR-3M, ...)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element name="rate">
                                    <xs:annotation>
                                    <xs:documentation>Added rate. TotalRate = baseRate + rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="current">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="loanLimitAmount">
                                    <xs:annotation>
                                    <xs:documentation>Current ovedraft amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="floatInterestRate" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Flag - TRUE if interest rate is float, oherwise FALSE</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="collateralBillOfExchange" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>true when there is a collateral (bill of exchange) for the loan that should be increased</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                            </xs:choice>
                            <xs:element minOccurs="0" name="contractFees">
                                <xs:annotation>
                                    <xs:documentation>A list of fees</xs:documentation>
                                </xs:annotation>
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="fee">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="type" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>This codebook contais values about fees which belong to a loan [CODELIST: LoanFee]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="amount">
                                    <xs:annotation>
                                    <xs:documentation>An amount of a fee</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="period" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>If filled then the fee is charged periodically for given period, otherwise the fee is charged for unspecified event. [CODELIST: FrequencyType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element minOccurs="0" name="resultInfo">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded" name="resultItem">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="code" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A code of the error/warning [CODELIST: ERROR_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="description" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A description of the error/warning</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
