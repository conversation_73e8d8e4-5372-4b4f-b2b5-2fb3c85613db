<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/services/loan/CreatePreApp_1_0/1.0"
    version="1.0.0"
    xmlns:opcreatepreapp_1_0="http://rb.cz/services/loan/CreatePreApp_1_0/1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="opCreatePreApp_1_0">
        <xs:complexType>
            <xs:sequence>
                <xs:choice>
                    <xs:element name="preApplication">
                        <xs:complexType>
                            <xs:sequence>
                                <xs:element name="offerId">
                                    <xs:annotation>
                                    <xs:documentation>Unique identification of an offer.
0 for unknown</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="512"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                                <xs:element name="partyId">
                                    <xs:annotation>
                                    <xs:documentation>Party Id (Siebel identifier)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                                <xs:element minOccurs="0"
                                    name="campaignType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Campaign type</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element minOccurs="1" name="requestedAmount">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="amount">
                                    <xs:annotation>
                                    <xs:documentation>Loan amount requested by user.  If NULL then maximal offered amount is used.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:decimal">
                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of amount [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element minOccurs="0" name="requestedInstalment">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element minOccurs="1"
                                    name="instalmentCount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Requested instalment count. If NULL then greatest available number is used.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="1"
                                    name="frequencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Time unit for requestedInstalmentCount. [CODELIST: FrequencyType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element minOccurs="0"
                                    name="telcoScoreConsent" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Clients consent with TELCO scoring.</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element maxOccurs="1" minOccurs="0" name="opportunityId">
                                    <xs:annotation>
                                    <xs:documentation>Unique identification of an opportunity</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                                <xs:element minOccurs="0"
                                    name="productType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Product type CODEBOOK:LST120</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element minOccurs="0"
                                    name="sellerChannel" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Channel – LST072_0008  (SPB),–	LST072_0005 (GIB), last used channel.</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element minOccurs="0"
                                    name="consolidation" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Application for loan consolidation.</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                            </xs:sequence>
                        </xs:complexType>
                    </xs:element>
                    <xs:element name="preApplicationMIC">
                        <xs:complexType>
                            <xs:sequence>
                                <xs:element minOccurs="0"
                                    name="campaignType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Campaign type</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element name="sellerChannel">
                                    <xs:annotation>
                                    <xs:documentation>(SPB|GIB) last used channel.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="3"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                                <xs:element name="userId">
                                    <xs:annotation>
                                    <xs:documentation>Siebel party ID or personal number (CZA) of a user which innitiated call of this service.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                                <xs:element name="userIdType">
                                    <xs:annotation>
                                    <xs:documentation>[LOV: PersonalNumber;PartyId]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:enumeration value="PersonalNumber"/>
                                    <xs:enumeration value="PartyId"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                                <xs:element name="companyPartyId" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Company Party ID (PO or FOP)</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element name="companyName" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Company name (PO or FOP)</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element minOccurs="0"
                                    name="applicantPartyId" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Company managing director PartyId.</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element name="productType">
                                    <xs:annotation>
                                    <xs:documentation>Enumeration of requested product type. PK for overdraft and PRP for repayment loan. [LOV: PK;PRP]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:enumeration value="PK"/>
                                    <xs:enumeration value="PRP"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                                <xs:element minOccurs="1" name="requestedAmount">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="amount">
                                    <xs:annotation>
                                    <xs:documentation>Loan amount requested by user.  If NULL then maximal offered amount is used.</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:decimal">
                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of amount [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element minOccurs="0" name="requestedInstalment">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element minOccurs="1"
                                    name="instalmentCount" type="xs:integer">
                                    <xs:annotation>
                                    <xs:documentation>Requested instalment count. If NULL then greatest available number is used.</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="1"
                                    name="frequencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Time unit for requestedInstalmentCount. [CODELIST: FrequencyType]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                </xs:element>
                                <xs:element name="loanSecuredFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Loan secured flag.</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element name="verificationConsent" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Client verification consent.</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element maxOccurs="1" minOccurs="1" name="opportunityId">
                                    <xs:annotation>
                                    <xs:documentation>Unique identification of an opportunity</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                                <xs:element minOccurs="0"
                                    name="branchCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Bank branch code. [CODELIST: Branch]</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element name="privateManagerId" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Private manager client number (CZA).</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element minOccurs="1"
                                    name="privateManagerEmail" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Private manager e-mail address.</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                                <xs:element minOccurs="1"
                                    name="privateManagerName" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Private manager full name.</xs:documentation>
                                    </xs:annotation>
                                </xs:element>
                            </xs:sequence>
                        </xs:complexType>
                    </xs:element>
                </xs:choice>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="opCreatePreApp_1_0_res">
        <xs:complexType>
            <xs:sequence>
                <xs:element minOccurs="1" name="preApplicationId">
                    <xs:annotation>
                        <xs:documentation>Unique identification of an application</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:maxLength value="30"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element minOccurs="0" name="registrationNo" type="xs:string">
                    <xs:annotation>
                        <xs:documentation>Business loan application ID.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element minOccurs="0" name="stmtDate" type="xs:date">
                    <xs:annotation>
                        <xs:documentation>Financial statement date.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element minOccurs="0" name="stmtDeliveryDate" type="xs:date">
                    <xs:annotation>
                        <xs:documentation>Date of submission financial statement to Tax office.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
