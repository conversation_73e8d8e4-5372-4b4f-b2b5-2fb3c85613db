<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/services/loan/ProcessConsolidation_1_0/1.0"
    version="1.0.0"
    xmlns:opprocessconsolidation_1_0="http://rb.cz/services/loan/ProcessConsolidation_1_0/1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="opProcessConsolidation_1_0">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="accountNumber">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="1" minOccurs="0" name="numberPart1">
                                <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="numberPart2">
                                <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                </xs:annotation>
                                <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                </xs:simpleType>
                            </xs:element>
                            <xs:element name="bankCode" type="xs:string">
                                <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:choice>
                    <xs:element name="loanDocument">
                        <xs:complexType>
                            <xs:sequence>
                                <xs:element name="docNumber">
                                    <xs:annotation>
                                    <xs:documentation>Document number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="40"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                            </xs:sequence>
                        </xs:complexType>
                    </xs:element>
                    <xs:element name="loanId">
                        <xs:complexType>
                            <xs:sequence>
                                <xs:element name="loanId">
                                    <xs:annotation>
                                    <xs:documentation>ID of an active loan from TRS</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="50"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                                <xs:element name="loanIdType">
                                    <xs:annotation>
                                    <xs:documentation>Type of a loan identifier (TRS = Common Loan ID composed by TRS,  TRSOP = Business Case ID)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                </xs:element>
                            </xs:sequence>
                        </xs:complexType>
                    </xs:element>
                </xs:choice>
                <xs:element name="repaymentAmount" type="xs:decimal">
                    <xs:annotation>
                        <xs:documentation>Amount of konsolidation.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="opProcessConsolidation_1_0_res">
        <xs:complexType>
            <xs:sequence/>
        </xs:complexType>
    </xs:element>
</xs:schema>
