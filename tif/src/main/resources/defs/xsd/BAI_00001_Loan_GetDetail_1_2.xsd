<?xml version="1.0" encoding="UTF-8"?>
<xs:schema attributeFormDefault="unqualified"
    elementFormDefault="qualified"
    targetNamespace="http://rb.cz/services/loan/GetDetail_1_2/1.0"
    version="1.0.0"
    xmlns:opgetdetail_1_2="http://rb.cz/services/loan/GetDetail_1_2/1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="opGetDetail_1_2">
        <xs:complexType>
            <xs:sequence>
                <xs:element maxOccurs="1" minOccurs="0" name="partyId">
                    <xs:annotation>
                        <xs:documentation>Party Id (Siebel identifier)</xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:restriction base="xs:string">
                            <xs:maxLength value="15"/>
                        </xs:restriction>
                    </xs:simpleType>
                </xs:element>
                <xs:element name="loans">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded"
                                minOccurs="1" name="loanId">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="entityId" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Loan ID</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="idType">
                                    <xs:annotation>
                                    <xs:documentation>entity ID type/context [LOV: GLOBAL;TRS;MID]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:enumeration value="GLOBAL"/>
                                    <xs:enumeration value="TRS"/>
                                    <xs:enumeration value="MID"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element name="requestedDetail">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="fullDetailFlag" type="xs:boolean">
                                <xs:annotation>
                                    <xs:documentation>Greater detail flag (true/false)</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
    <xs:element name="opGetDetail_1_2_res">
        <xs:complexType>
            <xs:sequence>
                <xs:element maxOccurs="1" minOccurs="0" name="loans">
                    <xs:annotation>
                        <xs:documentation>Collection of loans</xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element maxOccurs="unbounded" name="loan">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="entityId" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Id of related entity</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element minOccurs="0" name="idParts">
                                    <xs:annotation>
                                    <xs:documentation>Particular ids</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    minOccurs="1"
                                    name="part1" type="xs:string"/>
                                    <xs:element
                                    minOccurs="1"
                                    name="part2" type="xs:string"/>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="part3" type="xs:string"/>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="globalLoanId">
                                    <xs:annotation>
                                    <xs:documentation>Global loan ID from Midas</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:maxLength value="11"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="idType">
                                    <xs:annotation>
                                    <xs:documentation>Type of entityId [LOV: TRS]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>
                                    <xs:restriction base="xs:string">
                                    <xs:enumeration value="TRS"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="productReferences">
                                    <xs:annotation>
                                    <xs:documentation>References to other products</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="productReference">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="type">
                                    <xs:annotation>
                                    <xs:documentation>Type of product [LOV: INSURANCE]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="INSURANCE"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element name="refEntity">
                                    <xs:annotation>
                                    <xs:documentation>Identification of referenced product</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:choice>

                                    <xs:element name="insuranceRef">
                                    <xs:complexType>
                                    <xs:choice>

                                    <xs:element name="loanInsurance">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    name="code" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Type of insurance for loan [CODELIST: LoanInsuranceCodes]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    name="status" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>[CODELIST: LoanInsuranceStatus]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:choice>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:choice>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="type" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Type of the loan - product type [CODELIST: ProductCodes]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="status" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>State of the loan [CODELIST: LoanStatus]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="loanCurrency" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Main currency of the loan [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0" name="partyIds">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded" name="partyId">
                                    <xs:annotation>
                                    <xs:documentation>Party Id (Siebel identifier)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="15"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="contractNumber" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Contract number of the loan</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="overdueFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Overdue flag of the loan (true/false)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element maxOccurs="1"
                                    minOccurs="0"
                                    name="offerInsuranceFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Flag - Y when insurance should be offered for particular loan</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="pastDueDetailFlag" type="xs:boolean">
                                    <xs:annotation>
                                    <xs:documentation>Flag - Is available past due detail ?</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element name="balances">
                                    <xs:annotation>
                                    <xs:documentation>Balances of the loan</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="unbounded"
                                    minOccurs="1" name="balance">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element name="amount">
                                    <xs:annotation>
                                    <xs:documentation>Amount of the loan balance</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of the loan balance [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element name="type">
                                    <xs:annotation>
                                    <xs:documentation>Type of the loan balance [LOV: APPROVED;ACTUAL;AVAILABLE;DRAWN;OVERDUE]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="APPROVED"/>

                                    <xs:enumeration value="ACTUAL"/>

                                    <xs:enumeration value="AVAILABLE"/>

                                    <xs:enumeration value="DRAWN"/>

                                    <xs:enumeration value="OVERDUE"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="terms">
                                    <xs:annotation>
                                    <xs:documentation>Significant dates of the loan</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    minOccurs="0"
                                    name="signatureDate" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Date of signature of the contract</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="availabilityDate" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Availability day for drawdown</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="maturityDate" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Maturity date</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="nextInstalmentDate" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Date of the next instalment</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="drawdownDate" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Drawdown date</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="foundingDate" type="xs:dateTime">
                                    <xs:annotation>
                                    <xs:documentation>Date of the loan founding</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:choice maxOccurs="1" minOccurs="0">
                                    <xs:element name="instalmentLoanDetail">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="interest">
                                    <xs:annotation>
                                    <xs:documentation>Interest rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="totalRate">
                                    <xs:annotation>
                                    <xs:documentation>Total interest rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="period">
                                    <xs:annotation>
                                    <xs:documentation>Interest period [LOV: p.a.;p.s.;p.q.;p.m.;p.d.]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="p.a."/>

                                    <xs:enumeration value="p.s."/>

                                    <xs:enumeration value="p.q."/>

                                    <xs:enumeration value="p.m."/>

                                    <xs:enumeration value="p.d."/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0"
                                    name="validTo" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Valid to date</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0" name="float">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="baseRate">
                                    <xs:annotation>
                                    <xs:documentation>Base interest rate (PRIBOR, LIBOR, ...)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="baseRateType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Type of base rate (PRIBOR-1M, PRIBOR-3M, ...)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="rate">
                                    <xs:annotation>
                                    <xs:documentation>Added rate. TotalRate = baseRate + rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="repaymentAccount">
                                    <xs:annotation>
                                    <xs:documentation>Account number for repayment</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="iban" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>IBAN code of the bank account</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="swift" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>SWIFT code of the bank account</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of the bank account</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="regularInstalmentAmount">
                                    <xs:annotation>
                                    <xs:documentation>Regular instalment amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="amount">
                                    <xs:annotation>
                                    <xs:documentation>Amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of amount [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="nextInstalmentAmount">
                                    <xs:annotation>
                                    <xs:documentation>Next instalment amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="amount">
                                    <xs:annotation>
                                    <xs:documentation>Amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of amount [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="overdraftDetail">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="currentAccount">
                                    <xs:annotation>
                                    <xs:documentation>Base current account</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberPart1">
                                    <xs:annotation>
                                    <xs:documentation>Prefix part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="6"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0" name="numberPart2">
                                    <xs:annotation>
                                    <xs:documentation>Base part of the bank account number</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:maxLength value="10"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="bankCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Part of the bank account number which identifies the bank</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="iban" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>IBAN code of the bank account</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="swift" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>SWIFT code of the bank account</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of the bank account [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="interest">
                                    <xs:annotation>
                                    <xs:documentation>Overdraft interest</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    minOccurs="0" name="totalRate">
                                    <xs:annotation>
                                    <xs:documentation>Total interest rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0" name="period">
                                    <xs:annotation>
                                    <xs:documentation>Interest period [LOV: p.a.;p.s.;p.q.;p.m.;p.d.]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="p.a."/>

                                    <xs:enumeration value="p.s."/>

                                    <xs:enumeration value="p.q."/>

                                    <xs:enumeration value="p.m."/>

                                    <xs:enumeration value="p.d."/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0"
                                    name="validTo" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Valid to date</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0" name="float">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    minOccurs="0" name="baseRate">
                                    <xs:annotation>
                                    <xs:documentation>Base interest rate (PRIBOR, LIBOR, ...)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0"
                                    name="baseRateType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Type of base rate (PRIBOR-1M, PRIBOR-3M, ...)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0" name="rate">
                                    <xs:annotation>
                                    <xs:documentation>Added rate. TotalRate = baseRate + rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="interestFree">
                                    <xs:annotation>
                                    <xs:documentation>Interest - free of interest</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    minOccurs="0" name="totalRate">
                                    <xs:annotation>
                                    <xs:documentation>Total interest rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0" name="period">
                                    <xs:annotation>
                                    <xs:documentation>Interest period [LOV: p.a.;p.s.;p.q.;p.m.;p.d.]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="p.a."/>

                                    <xs:enumeration value="p.s."/>

                                    <xs:enumeration value="p.q."/>

                                    <xs:enumeration value="p.m."/>

                                    <xs:enumeration value="p.d."/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0"
                                    name="validTo" type="xs:date">
                                    <xs:annotation>
                                    <xs:documentation>Valid to date</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0" name="float">
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    minOccurs="0" name="baseRate">
                                    <xs:annotation>
                                    <xs:documentation>Base interest rate (PRIBOR, LIBOR, ...)</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0"
                                    name="baseRateType" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Type of base rate (PRIBOR-1M, PRIBOR-3M, ...)</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0" name="rate">
                                    <xs:annotation>
                                    <xs:documentation>Added rate. TotalRate = baseRate + rate</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="10"/>

                                    <xs:fractionDigits value="4"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="interestFreeAmount">
                                    <xs:annotation>
                                    <xs:documentation>Amount - free of interest</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    minOccurs="0" name="amount">
                                    <xs:annotation>
                                    <xs:documentation>Amount</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:decimal">

                                    <xs:totalDigits value="18"/>

                                    <xs:fractionDigits value="5"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>

                                    <xs:element
                                    minOccurs="0"
                                    name="currencyCode" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Currency of amount [CODELIST: Currency]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:choice>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element maxOccurs="1" minOccurs="0" name="failedItems">
                    <xs:complexType>
                        <xs:sequence>
                            <xs:element name="failedItem">
                                <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="entity">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element
                                    name="entityId" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Id of related entity</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0" name="idParts">
                                    <xs:annotation>
                                    <xs:documentation>Particular ids</xs:documentation>
                                    </xs:annotation>
                                    <xs:complexType>
                                    <xs:sequence>

                                    <xs:element
                                    minOccurs="1"
                                    name="part1" type="xs:string"/>

                                    <xs:element
                                    minOccurs="1"
                                    name="part2" type="xs:string"/>

                                    <xs:element
                                    maxOccurs="1"
                                    minOccurs="0"
                                    name="part3" type="xs:string"/>

                                    <xs:element
                                    minOccurs="0"
                                    name="part4" type="xs:string"/>

                                    <xs:element
                                    minOccurs="0"
                                    name="part5" type="xs:string"/>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element
                                    minOccurs="0"
                                    name="srcRefId" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>Consumer's ID, source reference ID</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    <xs:element name="result">
                                    <xs:complexType>
                                    <xs:sequence>
                                    <xs:element name="resultCode">
                                    <xs:annotation>
                                    <xs:documentation>0=OK, 4=warning, 8=error [LOV: 0;4;8]</xs:documentation>
                                    </xs:annotation>
                                    <xs:simpleType>

                                    <xs:restriction base="xs:string">

                                    <xs:enumeration value="0"/>

                                    <xs:enumeration value="4"/>

                                    <xs:enumeration value="8"/>
                                    </xs:restriction>
                                    </xs:simpleType>
                                    </xs:element>
                                    <xs:element
                                    name="code" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A code of the error/warning [CODELIST: ERROR_CODES]</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    <xs:element
                                    name="description" type="xs:string">
                                    <xs:annotation>
                                    <xs:documentation>A description of the error/warning</xs:documentation>
                                    </xs:annotation>
                                    </xs:element>
                                    </xs:sequence>
                                    </xs:complexType>
                                    </xs:element>
                                    </xs:sequence>
                                </xs:complexType>
                            </xs:element>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
            </xs:sequence>
        </xs:complexType>
    </xs:element>
</xs:schema>
