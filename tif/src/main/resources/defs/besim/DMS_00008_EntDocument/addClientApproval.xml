<?xml version="1.0" encoding="UTF-8"?>
<java version="1.8.0_102" class="java.beans.XMLDecoder">
    <object class="com.ge.dapiclient.moduls.besim.rules.Rule">
        <void property="actionName">
            <string>REPLY</string>
        </void>
        <void property="actionParameter">
            <string>
                &lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot; standalone=&quot;no&quot;?&gt;
                    &lt;message version=&quot;3&quot;&gt;
                    &lt;head&gt;
                    &lt;FCE_ID&gt;DMS_EntDocument-AddClientApproval_1_1&lt;/FCE_ID&gt;
                    &lt;FCE_VER&gt;100&lt;/FCE_VER&gt;
                    &lt;USERID_L2&gt;TifTester&lt;/USERID_L2&gt;
                    &lt;USERID_L3&gt;TIF&lt;/USERID_L3&gt;
                    &lt;RQ_SYS_ID&gt;V00X6039.rb.cz&lt;/RQ_SYS_ID&gt;
                    &lt;RQ_APP_ID&gt;ONB_TEST&lt;/RQ_APP_ID&gt;
                    &lt;RE_SYS_ID&gt;ip11esbwmb1id02&lt;/RE_SYS_ID&gt;
                    &lt;RE_APP_ID&gt;WMB_TEST&lt;/RE_APP_ID&gt;
                    &lt;FE_SYS_ID&gt;V00X6039.rb.cz&lt;/FE_SYS_ID&gt;
                    &lt;FE_APP_ID&gt;ONF_TEST&lt;/FE_APP_ID&gt;
                    &lt;RQ_DATE&gt;2020-10-16&lt;/RQ_DATE&gt;
                    &lt;RQ_TIME&gt;13:40:58,276&lt;/RQ_TIME&gt;
                    &lt;RE_DATE&gt;2022-10-18&lt;/RE_DATE&gt;
                    &lt;RE_TIME&gt;02:05:57,321&lt;/RE_TIME&gt;
                    &lt;RES_CODE&gt;0&lt;/RES_CODE&gt;
                    &lt;ERR_DESC&gt;DMS_00008_AddClientApproval_1_1_:_A_PKI_00004_SignDocument_101_TC.1.1&lt;/ERR_DESC&gt;
                    &lt;ROUNDTRIP_TIMEOUT&gt;60&lt;/ROUNDTRIP_TIMEOUT&gt;
                    &lt;ERR_CODE/&gt;
                    &lt;OPERATION_ID&gt;ADM001535629258272528140&lt;/OPERATION_ID&gt;
                    &lt;/head&gt;
                    &lt;body&gt;
                    &lt;NS1:entDocumentResponse xmlns:NS1=&quot;http://rb.cz/services/entityService/document/1.0&quot;&gt;
                    &lt;NS2:activities xmlns:NS2=&quot;http://rb.cz/dictionary/technical/&quot;/&gt;
                    &lt;NS1:response&gt;
                    &lt;NS3:opAddClientApproval_1_1_res xmlns:NS3=&quot;http://rb.cz/services/entityService/document/AddClientApproval_1_1&quot;/&gt;
                    &lt;/NS1:response&gt;
                    &lt;/NS1:entDocumentResponse&gt;
                    &lt;/body&gt;
                    &lt;/message&gt;
            </string>
        </void>
        <void property="besimId">
            <string>1</string>
        </void>
        <void property="delay">
            <long>1</long>
        </void>
        <void property="fceId">
            <string>DMS_EntDocument-AddClientApproval_1_1</string>
        </void>
        <void property="fceVer">
            <int>100</int>
        </void>
        <void property="id">
            <string>src/main/resources/defs/besim/MAR_EntOffer-GetDetail_2_0/A_1.xml</string>
        </void>
        <void property="loggingLevel">
            <string>DEFAULT</string>
        </void>
        <void property="name">
            <string>DMS_EntDocument-AddClientApproval_1_1</string>
        </void>
        <void property="order">
            <long>498</long>
        </void>
        <void property="ruleActive">
            <int>1</int>
        </void>
    </object>
</java>
