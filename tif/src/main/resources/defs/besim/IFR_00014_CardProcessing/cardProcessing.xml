<?xml version="1.0" encoding="UTF-8"?>
<java version="1.8.0_102" class="java.beans.XMLDecoder">
    <object class="com.ge.dapiclient.moduls.besim.rules.Rule">
        <void property="actionName">
            <string>REPLY</string>
        </void>
        <void property="actionParameter">
            <string>&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot; standalone=&quot;no&quot;?&gt;
                &lt;message version=&quot;3&quot;&gt;
                &lt;head&gt;
                &lt;FCE_ID&gt;IFR_CardProcessing-SetParticularLimit_1_0&lt;/FCE_ID&gt;
                &lt;FCE_VER&gt;100&lt;/FCE_VER&gt;
                &lt;USERID_L2&gt;CZA30874&lt;/USERID_L2&gt;
                &lt;USERID_L3&gt;TIF&lt;/USERID_L3&gt;
                &lt;RQ_SYS_ID&gt;V00X6178.rb.cz&lt;/RQ_SYS_ID&gt;
                &lt;RQ_APP_ID&gt;AMS_TEST&lt;/RQ_APP_ID&gt;
                &lt;RE_SYS_ID&gt;ip11esbwmb1id02&lt;/RE_SYS_ID&gt;
                &lt;RE_APP_ID&gt;WMB_TEST&lt;/RE_APP_ID&gt;
                &lt;FE_SYS_ID&gt;V00X6178.rb.cz&lt;/FE_SYS_ID&gt;
                &lt;FE_APP_ID&gt;AMS_TEST&lt;/FE_APP_ID&gt;
                &lt;RQ_DATE&gt;2019-04-15&lt;/RQ_DATE&gt;
                &lt;RQ_TIME&gt;15:34:43,053&lt;/RQ_TIME&gt;
                &lt;RE_DATE&gt;2022-03-31&lt;/RE_DATE&gt;
                &lt;RE_TIME&gt;23:52:13,304&lt;/RE_TIME&gt;
                &lt;RES_CODE&gt;0&lt;/RES_CODE&gt;
                &lt;ERR_DESC&gt;IFR_00016_StartService_1_0_:_A_IBP_00009_PostServiceStart_100_TC.1.1_AMS&lt;/ERR_DESC&gt;
                &lt;ROUNDTRIP_TIMEOUT&gt;60&lt;/ROUNDTRIP_TIMEOUT&gt;
                &lt;ERR_CODE/&gt;
                &lt;OPERATION_ID&gt;ADM001555335283053921214&lt;/OPERATION_ID&gt;
                &lt;/head&gt;
                &lt;body&gt;
                &lt;NS1:bpmResponse xmlns:NS1=&quot;http://rb.cz/services/bpm/1.0&quot; xmlns:esNS2=&quot;http://rb.cz/services/bpm/StartService_1_0/1.0&quot;&gt;
                &lt;NS2:activities xmlns:NS2=&quot;http://rb.cz/dictionary/technical/&quot;/&gt;
                &lt;NS1:response&gt;
                &lt;esNS2:opStartService_1_0_res&gt;
                &lt;esNS2:status&gt;BYj&lt;/esNS2:status&gt;
                &lt;esNS2:data&gt;
                &lt;esNS2:serviceStatus&gt;ano&lt;/esNS2:serviceStatus&gt;
                &lt;esNS2:key&gt;P9g&lt;/esNS2:key&gt;
                &lt;esNS2:step&gt;YtH&lt;/esNS2:step&gt;
                &lt;esNS2:reset&gt;N&lt;/esNS2:reset&gt;
                &lt;esNS2:coach&gt;1p5&lt;/esNS2:coach&gt;
                &lt;esNS2:coachEvals&gt;
                &lt;esNS2:field&gt;
                &lt;esNS2:name&gt;6IM&lt;/esNS2:name&gt;
                &lt;esNS2:type&gt;float&lt;/esNS2:type&gt;
                &lt;esNS2:value/&gt;
                &lt;/esNS2:field&gt;
                &lt;/esNS2:coachEvals&gt;
                &lt;esNS2:actions&gt;
                &lt;esNS2:action&gt;YHz&lt;/esNS2:action&gt;
                &lt;/esNS2:actions&gt;
                &lt;esNS2:actionsMap&gt;
                &lt;esNS2:field&gt;
                &lt;esNS2:name&gt;xlY&lt;/esNS2:name&gt;
                &lt;esNS2:type&gt;float&lt;/esNS2:type&gt;
                &lt;esNS2:value/&gt;
                &lt;/esNS2:field&gt;
                &lt;/esNS2:actionsMap&gt;
                &lt;/esNS2:data&gt;
                &lt;/esNS2:opStartService_1_0_res&gt;
                &lt;/NS1:response&gt;
                &lt;/NS1:bpmResponse&gt;
                &lt;/body&gt;
                &lt;/message&gt;
            </string>
        </void>
        <void property="besimId">
            <string>1</string>
        </void>
        <void property="delay">
            <long>1</long>
        </void>
        <void property="fceId">
            <string>IFR_CardProcessing-SetParticularLimit_1_0</string>
        </void>
        <void property="fceVer">
            <int>100</int>
        </void>
        <void property="id">
            <string>src/main/resources/defs/besim/MAR_EntOffer-Create_1_0/A_1.xml</string>
        </void>
        <void property="loggingLevel">
            <string>DEFAULT</string>
        </void>
        <void property="name">
            <string>IFR_CardProcessing-SetParticularLimit_1_0</string>
        </void>
        <void property="order">
            <long>498</long>
        </void>
        <void property="ruleActive">
            <int>1</int>
        </void>
    </object>
</java>

