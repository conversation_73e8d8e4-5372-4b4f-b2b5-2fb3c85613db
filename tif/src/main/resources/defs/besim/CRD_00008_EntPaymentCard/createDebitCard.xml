<?xml version="1.0" encoding="UTF-8"?>
<java version="1.8.0_102" class="java.beans.XMLDecoder">
    <object class="com.ge.dapiclient.moduls.besim.rules.Rule">
        <void property="actionName">
            <string>REPLY</string>
        </void>
        <void property="actionParameter">
            <string>&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?&gt;
                &lt;message version=&quot;3&quot;&gt;
                    &lt;head&gt;
                        &lt;FCE_ID&gt;CRD_EntPaymentCard-CreateDebitCard_1_0&lt;/FCE_ID&gt;
                        &lt;FCE_VER&gt;100&lt;/FCE_VER&gt;
                        &lt;USERID_L2&gt;cza16297&lt;/USERID_L2&gt;
                        &lt;USERID_L3&gt;l3&lt;/USERID_L3&gt;
                        &lt;RQ_SYS_ID&gt;ICHC_T01_1&lt;/RQ_SYS_ID&gt;
                        &lt;RQ_APP_ID&gt;ICH_TEST&lt;/RQ_APP_ID&gt;
                        &lt;FE_SYS_ID&gt;ICHC_T01_1&lt;/FE_SYS_ID&gt;
                        &lt;FE_APP_ID&gt;UFE&lt;/FE_APP_ID&gt;
                        &lt;RE_SYS_ID&gt;esbwmb1at1&lt;/RE_SYS_ID&gt;
                        &lt;RE_APP_ID&gt;WMB_TEST&lt;/RE_APP_ID&gt;
                        &lt;RQ_DATE&gt;2022-02-02&lt;/RQ_DATE&gt;
                        &lt;RQ_TIME&gt;10:43:15,873&lt;/RQ_TIME&gt;
                        &lt;RE_DATE&gt;2022-02-02&lt;/RE_DATE&gt;
                        &lt;RE_TIME&gt;10:43:21,686&lt;/RE_TIME&gt;
                        &lt;RES_CODE&gt;0&lt;/RES_CODE&gt;
                        &lt;ERR_CODE/&gt;
                        &lt;ERR_DESC/&gt;
                        &lt;OPERATION_ID&gt;ICH001643794995871675668:[NA/CreateAccountShellComponent/.secured.v1.application/64981ec03cdf4974b7c8466392e41ffd/2022-02-02-10;43;10-671_bfde-f4406e8a68c5]&lt;/OPERATION_ID&gt;
                        &lt;RQ_UUID/&gt;
                        &lt;ROUNDTRIP_TIMEOUT&gt;10&lt;/ROUNDTRIP_TIMEOUT&gt;
                    &lt;/head&gt;
                    &lt;body&gt;
                        &lt;NS1:entPaymentCardResponse xmlns:NS1=&quot;http://rb.cz/services/paymentCard/1.0&quot;&gt;
                            &lt;NS2:activities xmlns:NS2=&quot;http://rb.cz/dictionary/technical/&quot;&gt;&lt;/NS2:activities&gt;
                            &lt;NS1:response&gt;
                                &lt;NS3:opCreateDebitCard_1_0_res xmlns:NS3=&quot;http://rb.cz/services/entityService/paymentCard/CreateDebitCard_1_0&quot;&gt;
                                    &lt;NS3:idPaymentCard&gt;6824435&lt;/NS3:idPaymentCard&gt;
                                &lt;/NS3:opCreateDebitCard_1_0_res&gt;
                            &lt;/NS1:response&gt;
                        &lt;/NS1:entPaymentCardResponse&gt;
                    &lt;/body&gt;
                &lt;/message&gt;</string>
        </void>
        <void property="besimId">
            <string>1</string>
        </void>
        <void property="delay">
            <long>1</long>
        </void>
        <void property="fceId">
            <string>CRD_EntPaymentCard-CreateDebitCard_1_0</string>
        </void>
        <void property="fceVer">
            <int>100</int>
        </void>
        <void property="id">
            <string>src/main/resources/defs/besim/MAR_EntOffer-GetDetail_2_0/A_1.xml</string>
        </void>
        <void property="loggingLevel">
            <string>DEFAULT</string>
        </void>
        <void property="name">
            <string>CRD_EntPaymentCard-CreateDebitCard_1_0</string>
        </void>
        <void property="order">
            <long>498</long>
        </void>
        <void property="ruleActive">
            <int>1</int>
        </void>
    </object>
</java>
