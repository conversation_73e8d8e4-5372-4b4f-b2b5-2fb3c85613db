<?xml version="1.0" encoding="UTF-8"?>
<java version="1.8.0_102" class="java.beans.XMLDecoder">
 <object class="com.ge.dapiclient.moduls.besim.rules.Rule">
  <void property="actionName">
   <string>REPLY</string>
  </void>
  <void property="actionParameter">
     <string>&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot; standalone=&quot;no&quot;?&gt;
&lt;message version=&quot;3&quot;&gt;
&lt;head&gt;
&lt;FCE_ID&gt;PAY_EntDomesticPayment-CreateSinglePO_2_1&lt;/FCE_ID&gt;
&lt;FCE_VER&gt;100&lt;/FCE_VER&gt;
&lt;USERID_L2&gt;TifTester&lt;/USERID_L2&gt;
&lt;USERID_L3&gt;TIF&lt;/USERID_L3&gt;
&lt;RQ_SYS_ID&gt;v00x7560.rb.cz&lt;/RQ_SYS_ID&gt;
&lt;RQ_APP_ID&gt;MCH_TEST&lt;/RQ_APP_ID&gt;
&lt;RE_SYS_ID&gt;ip11esbwmb1id02&lt;/RE_SYS_ID&gt;
&lt;RE_APP_ID&gt;WMB_TEST&lt;/RE_APP_ID&gt;
&lt;FE_SYS_ID&gt;v00x7560.rb.cz&lt;/FE_SYS_ID&gt;
&lt;FE_APP_ID&gt;GIB_TEST&lt;/FE_APP_ID&gt;
&lt;RQ_DATE&gt;2021-09-15&lt;/RQ_DATE&gt;
&lt;RQ_TIME&gt;11:47:28,985&lt;/RQ_TIME&gt;
&lt;RE_DATE&gt;2021-09-15&lt;/RE_DATE&gt;
&lt;RE_TIME&gt;11:47:30,538&lt;/RE_TIME&gt;
&lt;RES_CODE&gt;0&lt;/RES_CODE&gt;
&lt;ERR_CODE/&gt;
&lt;ERR_DESC&gt;PAY_00037_CreateSinglePO_2_1_:_A_TRS_00162_IPInitPaymentOrder_100_TC.1.1&lt;/ERR_DESC&gt;
&lt;ROUNDTRIP_TIMEOUT&gt;60&lt;/ROUNDTRIP_TIMEOUT&gt;
&lt;OPERATION_ID&gt;GIB001631699248973006961&lt;/OPERATION_ID&gt;
&lt;RQ_UUID/&gt;
&lt;/head&gt;
&lt;body&gt;
&lt;NS1:entDomesticPaymentResponse xmlns:NS1=&quot;http://rb.cz/services/domesticPayment/1.0&quot;&gt;
&lt;NS2:activities xmlns:NS2=&quot;http://rb.cz/dictionary/technical/&quot;/&gt;
&lt;NS1:response&gt;
&lt;NS3:opCreateSinglePO_2_1_res xmlns:NS3=&quot;http://rb.cz/services/entDomesticPayment/CreateSinglePO_2_1&quot;&gt;
&lt;NS3:paymentOrder&gt;
&lt;NS3:identification&gt;
&lt;NS3:srcReferenceId&gt;tg00oLczVEu57aBFUfXZwuEgT2VYWlvvUUl&lt;/NS3:srcReferenceId&gt;
&lt;NS3:trgReferenceId&gt;4&lt;/NS3:trgReferenceId&gt;
&lt;/NS3:identification&gt;
&lt;NS3:realizationInfo&gt;
&lt;NS3:status&gt;WAITING&lt;/NS3:status&gt;
&lt;/NS3:realizationInfo&gt;
&lt;/NS3:paymentOrder&gt;
&lt;/NS3:opCreateSinglePO_2_1_res&gt;
&lt;/NS1:response&gt;
&lt;/NS1:entDomesticPaymentResponse&gt;
&lt;/body&gt;
&lt;/message&gt;
</string>
  </void>
  <void property="besimId">
   <string>1</string>
  </void>
  <void property="delay">
   <long>1</long>
  </void>
  <void property="fceId">
   <string>PAY_EntDomesticPayment-CreateSinglePO_2_1</string>
  </void>
  <void property="fceVer">
   <int>100</int>
  </void>
  <void property="id">
   <string>src/main/resources/defs/besim/MAR_EntOffer-GetDetail_2_0/A_1.xml</string>
  </void>
  <void property="loggingLevel">
   <string>DEFAULT</string>
  </void>
  <void property="name">
   <string>PAY_EntDomesticPayment-CreateSinglePO_2_1</string>
  </void>
  <void property="order">
   <long>498</long>
  </void>
  <void property="ruleActive">
   <int>1</int>
  </void>
 </object>
</java>

