<?xml version="1.0" encoding="UTF-8"?>
<tif xmlns="http://trask.cz/tif/definitions/api">
   <api>
      <application>WMB</application>
      <name>CRD_EntPaymentCard-GenerateWalletEnrollReq_1_0</name>
      <version>100</version>
      <operation_type>active</operation_type>
      <documentation>Generates request for payment card enrollment into wallet (for Apple pay, Digi pay or other similar wallet types).</documentation>
      <encryption_enabled>true</encryption_enabled>
      <encryption_api_group>CARD</encryption_api_group>
      <message type="XML" version="3">
         <input_parameters>
            <parameter>
               <name>Request</name>
               <type name="XSD"
                     elementName="entPaymentCardRequest"
                     ns="http://rb.cz/services/paymentCard/1.0"
                     location="./models/RBCZ/ES/services/Products/Cards/CRD_00008_EntPaymentCard/CRD_00008_EntPaymentCard_1.0.xsd"/>
               <required>true</required>
            </parameter>
         </input_parameters>
         <output_parameters>
            <parameter>
               <name>Response</name>
               <type name="XSD"
                     elementName="entPaymentCardResponse"
                     ns="http://rb.cz/services/paymentCard/1.0"
                     location="./models/RBCZ/ES/services/Products/Cards/CRD_00008_EntPaymentCard/CRD_00008_EntPaymentCard_1.0.xsd"/>
               <required>true</required>
               <elementExtraInfoList>
                  <elementExtraInfo>
                     <elementPath>entPaymentCardResponse/response/opGenerateWalletEnrollReq_1_0_res/card/enrollInput/ApplePay/pan</elementPath>
                     <encryption>
                        <enabled>true</enabled>
                        <group>CARD</group>
                     </encryption>
                  </elementExtraInfo>
               </elementExtraInfoList>
            </parameter>
         </output_parameters>
      </message>
   </api>
</tif>
