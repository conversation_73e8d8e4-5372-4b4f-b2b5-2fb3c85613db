<?xml version="1.0" encoding="UTF-8"?>
<tif xmlns="http://ge.com/interfaces/connection_definition"
     xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
     xsi:schemaLocation="http://ge.com/interfaces/connection_definition BindingDefinition.xsd">
   <binding>
      <name>BAI_RemoteCashAccount-GetProviderList_1_0</name>
      <version>100</version>
      <environment>TEST</environment>
      <api>
         <application>WMB</application>
         <name>BAI_RemoteCashAccount-GetProviderList_1_0</name>
         <version>100</version>
      </api>
      <roundtrip_timeout>30</roundtrip_timeout>
      <class>INQUIRE</class>
      <request>
         <priority>4</priority>
         <timeout>30</timeout>
         <queue>PWB.WMB.IN</queue>
         <persistence>false</persistence>
      </request>
      <response>
         <priority>4</priority>
         <timeout>30</timeout>
         <queue>PWB.REPLYQ</queue>
         <persistence>false</persistence>
      </response>
   </binding>
</tif>
