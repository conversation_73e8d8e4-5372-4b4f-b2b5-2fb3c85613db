<?xml version="1.0" encoding="UTF-8"?>
<tif xmlns="http://ge.com/interfaces/connection_definition" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://ge.com/interfaces/connection_definition BindingDefinition.xsd">
	<binding>
		<name>DMS_EntDocument-CreateExternal_1_1</name>
		<version>100</version>
		<documentation>1.00.00</documentation>
		<environment>TEST</environment>
		<api>
			<application>WMB</application>
			<name>DMS_EntDocument-CreateExternal_1_1</name>
			<version>100</version>
		</api>
		<roundtrip_timeout>30</roundtrip_timeout>
		<class>INPUT_WITH_REPLY</class>
		<request>
			<priority>4</priority>
			<timeout>30</timeout>
			<queue>MSL.WMB.IN</queue>
		</request>
		<response>
			<priority>4</priority>
			<timeout>-1</timeout>
			<queue>MSL.REPLYQ</queue>
		</response>
	</binding>
</tif>
