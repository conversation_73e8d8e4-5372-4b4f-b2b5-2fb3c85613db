<?xml version="1.0" encoding="UTF-8"?>
<tif xmlns="http://trask.cz/tif/definitions/api">
   <api>
      <application>WMB</application>
      <name>BAI_Loan-GetOfferParam_1_5</name>
      <version>100</version>
      <operation_type>passive</operation_type>
      <documentation>This operation returns a loan offer parameters. Actually is it applicable to these loan products maintained by TRS (its internal code is in brackets): Consumer Loan and top up (SU), Personal Overdraft and top up (PDU), Interest Free Reserve (PDINST), Investment Loan and top up (IU), Overdraft Facility and top up (KKU).</documentation>
      <message type="XML" version="3">
         <input_parameters>
            <parameter>
               <name>Request</name>
               <type name="XSD"
                     elementName="loanRequest"
                     ns="http://rb.cz/services/loan/1.0"
                     location="./xsd/BAI_00001_Loan_1.0.xsd"/>
               <required>true</required>
            </parameter>
         </input_parameters>
         <output_parameters>
            <parameter>
               <name>Response</name>
               <type name="XSD"
                     elementName="loanResponse"
                     ns="http://rb.cz/services/loan/1.0"
                     location="./xsd/BAI_00001_Loan_1.0.xsd"/>
               <required>true</required>
            </parameter>
         </output_parameters>
      </message>
   </api>
</tif>
