package cz.rb.tif.dms_00008_ent_document;

import cz.equa.camapp.model.document.EmailDTO;
import cz.equa.camapp.service.ServiceException;
import cz.equa.camapp.utils.Constants;
import cz.rb.services.entityservice.document._1.EntDocumentRequest;
import cz.rb.services.entityservice.document._1.EntDocumentResponse;
import cz.rb.services.entityservice.document.create_4_0.OpCreate40;
import cz.rb.services.entityservice.document.create_4_0.OpCreate40Res;
import cz.rb.tif.TifServiceDescription;
import org.springframework.stereotype.Component;
import org.w3c.dom.Element;

import java.time.ZonedDateTime;

@Component
public class Dms00008EntDocumentCreate extends Dms00008EntDocument {

    private static final TifServiceDescription CREATE = new TifServiceDescription("opCreate_4_0", "DMS_EntDocument-Create_4_0");

    public String create(OpCreate40 createRequest) throws ServiceException {
        EntDocumentRequest request = createEntDocumentRequest(CREATE);
        request.getEntityService().setOpCreate40(createRequest);
        EntDocumentResponse serviceResponse = tifClient.call(CREATE.getServiceName(), 100, request);

        if ((serviceResponse == null) || (serviceResponse.getResponse() == null)) {
            throw new ServiceException("Service response or response.document is null :" + serviceResponse);
        }

        OpCreate40Res response = serviceResponse.getResponse().getOpCreate40Res();
        if ((response == null) || (response.getDocument() == null)) {
            throw new ServiceException("OpCreate response or response.document is null :" + response);
        }

        return String.valueOf(response.getDocument().getEcmId());
    }

    public String create(String partyId, String busApplId, String fullAccountNumberWithBankCode, DocumentType documentType, String documentName, Object document, EmailDTO email) throws ServiceException {
        EntDocumentRequest request = createEntDocumentRequest(CREATE);
        OpCreate40 createRequest = new OpCreate40();
        request.getEntityService().setOpCreate40(createRequest);

        createRequest.setPartyId(partyId);
        createRequest.setDocumentClass(documentType.name());
        createRequest.setDocumentName(documentName);
        createRequest.setObjectOfCreation(createObjectOfCreation(busApplId, fullAccountNumberWithBankCode, documentType, marshallXml(document), email));

        EntDocumentResponse serviceResponse = tifClient.call(CREATE.getServiceName(), 100, request);

        if ((serviceResponse == null) || (serviceResponse.getResponse() == null)) {
            throw new ServiceException("Service response or response.document is null :" + serviceResponse);
        }

        OpCreate40Res response = serviceResponse.getResponse().getOpCreate40Res();

        if ((response == null) || (response.getDocument() == null)) {
            throw new ServiceException("OpCreate response or response.document is null :" + response);
        }

        return response.getDocument().getDocumentId();
    }

    private OpCreate40.ObjectOfCreation createObjectOfCreation(String busApplId, String fullAccountNumberWithBankCode, DocumentType documentType, Element xmlDocumentData, EmailDTO email) {
        OpCreate40.ObjectOfCreation objectOfCreation = new OpCreate40.ObjectOfCreation();
        objectOfCreation.setContent(createContent(documentType, xmlDocumentData, email));
        if (email == null) {
            objectOfCreation.setMetadata(createMetadata(busApplId, documentType, fullAccountNumberWithBankCode));
        }
        return objectOfCreation;
    }

    private OpCreate40.ObjectOfCreation.Content createContent(DocumentType documentType, Element xmlDocumentData, EmailDTO email) {
        OpCreate40.ObjectOfCreation.Content content = new OpCreate40.ObjectOfCreation.Content();
        content.setFormat(documentType.getContentFormat());
        content.setRenderingContent(createRenderingContent(documentType, xmlDocumentData, email));
        return content;
    }

    private OpCreate40.ObjectOfCreation.Content.RenderingContent createRenderingContent(DocumentType documentType, Element xmlDocumentData, EmailDTO email) {
        OpCreate40.ObjectOfCreation.Content.RenderingContent renderingContent = new OpCreate40.ObjectOfCreation.Content.RenderingContent();
        renderingContent.setProcessing(documentType.getProcessing());
        renderingContent.setTechnicalMetadata(createTechnicalMetadata(documentType, email));
        if (xmlDocumentData != null) {
            renderingContent.setFileXmlData(createDocumentData(xmlDocumentData));
        }
        return renderingContent;
    }

    private OpCreate40.ObjectOfCreation.Content.RenderingContent.TechnicalMetadata createTechnicalMetadata(DocumentType documentType, EmailDTO email) {
        OpCreate40.ObjectOfCreation.Content.RenderingContent.TechnicalMetadata technicalMetadata = new OpCreate40.ObjectOfCreation.Content.RenderingContent.TechnicalMetadata();
        technicalMetadata.setDeliveries(createDeliveries(documentType, email));
        technicalMetadata.setFileFormatProperties(crateFileFormatProperties(documentType));
        technicalMetadata.setReportDatetime(ZonedDateTime.now());
        return technicalMetadata;
    }

    private OpCreate40.ObjectOfCreation.Content.RenderingContent.TechnicalMetadata.FileFormatProperties crateFileFormatProperties(DocumentType documentType) {
        OpCreate40.ObjectOfCreation.Content.RenderingContent.TechnicalMetadata.FileFormatProperties fileFormatProperties =
                new OpCreate40.ObjectOfCreation.Content.RenderingContent.TechnicalMetadata.FileFormatProperties();
        fileFormatProperties.setFileType(documentType.getFormat());
        return fileFormatProperties;
    }

    private OpCreate40.ObjectOfCreation.Content.RenderingContent.TechnicalMetadata.Deliveries createDeliveries(DocumentType documentType, EmailDTO email) {
        OpCreate40.ObjectOfCreation.Content.RenderingContent.TechnicalMetadata.Deliveries deliveries =
                new OpCreate40.ObjectOfCreation.Content.RenderingContent.TechnicalMetadata.Deliveries();
        OpCreate40.ObjectOfCreation.Content.RenderingContent.TechnicalMetadata.Deliveries.Delivery delivery =
                new OpCreate40.ObjectOfCreation.Content.RenderingContent.TechnicalMetadata.Deliveries.Delivery();
        delivery.setDeliveryChannel(documentType.getDeliveryChannel());
        if (email != null) {
            delivery.setEmails(new OpCreate40.ObjectOfCreation.Content.RenderingContent.TechnicalMetadata.Deliveries.Delivery.Emails());
            OpCreate40.ObjectOfCreation.Content.RenderingContent.TechnicalMetadata.Deliveries.Delivery.Emails.Email emailToSend =
                    new OpCreate40.ObjectOfCreation.Content.RenderingContent.TechnicalMetadata.Deliveries.Delivery.Emails.Email();
            emailToSend.getReceiver().add(email.getReceiver());

            if (email.getAttachmentValue() != null) {
                OpCreate40.ObjectOfCreation.Content.RenderingContent.TechnicalMetadata.Deliveries.Delivery.Emails.Email.Attachments attachments =
                        new OpCreate40.ObjectOfCreation.Content.RenderingContent.TechnicalMetadata.Deliveries.Delivery.Emails.Email.Attachments();

                OpCreate40.ObjectOfCreation.Content.RenderingContent.TechnicalMetadata.Deliveries.Delivery.Emails.Email.Attachments.Attachment attachment =
                        new OpCreate40.ObjectOfCreation.Content.RenderingContent.TechnicalMetadata.Deliveries.Delivery.Emails.Email.Attachments.Attachment();

                attachment.setType(email.getAttachmentType());
                attachment.setValue(email.getAttachmentValue());
                attachment.setDocumentName(email.getAttachmentName());

                attachments.getAttachment().add(attachment);
                emailToSend.setAttachments(attachments);
            }
            delivery.getEmails().getEmail().add(emailToSend);
        }
        deliveries.getDelivery().add(delivery);
        return deliveries;
    }

    private OpCreate40.ObjectOfCreation.Content.RenderingContent.FileXmlData createDocumentData(Element xmlDocumentData) {
        OpCreate40.ObjectOfCreation.Content.RenderingContent.FileXmlData fileXmlData = new OpCreate40.ObjectOfCreation.Content.RenderingContent.FileXmlData();
        fileXmlData.setAny(xmlDocumentData);
        return fileXmlData;
    }

    private OpCreate40.ObjectOfCreation.Metadata createMetadata(String busApplId, DocumentType documentType, String fullAccountNumberWithBankCode) {
        OpCreate40.ObjectOfCreation.Metadata metadata = new OpCreate40.ObjectOfCreation.Metadata();
        metadata.setDocumentDetail(createDocumentDetail(busApplId, documentType, fullAccountNumberWithBankCode));
        return metadata;
    }

    private OpCreate40.ObjectOfCreation.Metadata.DocumentDetail createDocumentDetail(String busApplId, DocumentType documentType, String fullAccountNumberWithBankCode) {
        OpCreate40.ObjectOfCreation.Metadata.DocumentDetail documentDetail = new OpCreate40.ObjectOfCreation.Metadata.DocumentDetail();
        if (documentType.getDocumentSmState() != null) {
            documentDetail.setDocumentSmState(documentType.getDocumentSmState());
        }
        documentDetail.setRelatedMetadata(createRelatedMetadata(busApplId, fullAccountNumberWithBankCode));
        return documentDetail;
    }

    private OpCreate40.ObjectOfCreation.Metadata.DocumentDetail.RelatedMetadata createRelatedMetadata(String busApplId, String fullAccountNumberWithBankCode) {
        OpCreate40.ObjectOfCreation.Metadata.DocumentDetail.RelatedMetadata relatedMetadata =
                new OpCreate40.ObjectOfCreation.Metadata.DocumentDetail.RelatedMetadata();
        relatedMetadata.setApplicationId(Constants.getAdbBusApplId(busApplId));
        relatedMetadata.setAgreementNumbers(new OpCreate40.ObjectOfCreation.Metadata.DocumentDetail.RelatedMetadata.AgreementNumbers());
        relatedMetadata.getAgreementNumbers().getAgreementNumber().add(busApplId);
        if (fullAccountNumberWithBankCode != null) {
            relatedMetadata.setAccountNumbers(new OpCreate40.ObjectOfCreation.Metadata.DocumentDetail.RelatedMetadata.AccountNumbers());
            relatedMetadata.getAccountNumbers().getAccountNumber().add(fullAccountNumberWithBankCode);
        }
        return relatedMetadata;
    }
}
