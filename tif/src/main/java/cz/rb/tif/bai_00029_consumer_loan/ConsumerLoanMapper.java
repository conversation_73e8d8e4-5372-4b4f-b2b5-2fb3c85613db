package cz.rb.tif.bai_00029_consumer_loan;

import cz.equa.camapp.lovs.LovFactory;
import cz.equa.camapp.model.OfferDTO;
import cz.rb.services.consumerloan.getoffers_1_0._1.Res;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

@Mapper(uses = {LovFactory.class})
public interface ConsumerLoanMapper { //NOSONAR
    ConsumerLoanMapper INSTANCE = Mappers.getMapper(ConsumerLoanMapper.class);

    @Mappings({@Mapping(source = "entity.listOfRepayments.repayment", target = "repayments")})
    OfferDTO tifToDto(Res.Offer entity);

    @InheritInverseConfiguration
    Res.Offer dtoToTif(OfferDTO entity);
}
