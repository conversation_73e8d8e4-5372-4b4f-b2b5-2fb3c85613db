package cz.rb.tif.ifr_00016_start_service.model;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;

import java.util.List;

public class SSTServiceBuilder {

    private final ObjectMapper mapper = new ObjectMapper();
    private ObjectNode params;
    private ArrayNode dataListArray;
    private ArrayNode productsListArray;
    private final StartServiceDTO startService;

    public SSTServiceBuilder(String instanceId, String templateCode) {
        startService = new StartServiceDTO();
        startService.setInstanceId(instanceId);
        startService.setAction("start");
        initJsonParams(templateCode, "CMN");
    }

    public SSTServiceBuilder(String instanceId, String templateCode, String originatingSystemId) {
        startService = new StartServiceDTO();
        startService.setInstanceId(instanceId);
        startService.setAction("start");
        initJsonParams(templateCode, originatingSystemId);
    }

    public SSTServiceBuilder addParam(String paramName, String paramValue) {
        dataListArray.add(createObjectNodeForParameter(mapper, paramName, paramValue));
        return this;
    }

    public SSTServiceBuilder addProduct(ProductParam product) {
        if (productsListArray == null) {
            ObjectNode request = (ObjectNode) params.get("request");
            productsListArray = request.putArray("products");
        }

        productsListArray.add(createObjectNodeForProduct(mapper, product));
        return this;
    }

    public SSTServiceBuilder setTemplateCodeList(List<String> templateCodeList) {
        ObjectNode request = (ObjectNode) params.get("request");
        ArrayNode templateCodeListArray = request.putArray("templateCodeList");
        templateCodeList.forEach(templateCodeListArray::add);

        return this;
    }

    public StartServiceDTO build() throws JsonProcessingException {
        startService.setParams(mapper.writerWithDefaultPrettyPrinter().writeValueAsString(params));
        return startService;
    }

    private void initJsonParams(String templateCode, String originatingSystemId) {
        params = mapper.createObjectNode();
        ObjectNode request = mapper.createObjectNode();
        params.set("request", request);

        ObjectNode originator = mapper.createObjectNode();
        request.set("originator", originator);

        originator.put("originatorType", "SYSTEM");
        originator.put("originatingSystemId", originatingSystemId);
        originator.put("startType", "AUTOMATIC");

        if (templateCode != null) {
            request.put("templateCode", templateCode);
        }

        dataListArray = request.putArray("dataList");
    }

    private ObjectNode createObjectNodeForParameter(ObjectMapper mapper, String paramName, String paramValue) {
        ObjectNode objectParam = mapper.createObjectNode();
        objectParam.put("typeId", paramName);
        objectParam.put("value", paramValue);
        return objectParam;
    }

    private ObjectNode createObjectNodeForProduct(ObjectMapper mapper, ProductParam product) {
        ObjectNode objectParam = mapper.createObjectNode();
        objectParam.put("obligationSrcId", product.getObligationSrcId());
        objectParam.put("obligationKey", product.getObligationKey().toString());
        objectParam.put("productType", product.getProductType());
        objectParam.put("srcSystemId", product.getSrcSystemId());
        objectParam.put("obligationAmount", product.getObligationAmount().toString());

        if (product.getCardNumber() != null) {
            objectParam.put("cardNumber", product.getCardNumber());
        }
        if (product.getContractNumber() != null) {
            objectParam.put("contractNumber", product.getContractNumber());
        }
        if (product.getAccountNumber() != null) {
            objectParam.put("accountNumber", product.getAccountNumber());
        }
        return objectParam;
    }
}
