package cz.rb.tif.cus_00041_ent_party;

import cz.equa.camapp.model.update_party.additional_info.AdditionalInfoDTO;
import cz.equa.camapp.model.update_party.basic_info.BasicInfoDTO;
import cz.equa.camapp.rest.model.cus.party.AddressDto;
import cz.equa.camapp.rest.model.cus.party.ContactsDto;
import cz.equa.camapp.service.ServiceException;
import cz.rb.services.party._1.EntPartyRequest;
import cz.rb.services.party.update_1_2._1.OpUpdate12;
import cz.rb.tif.TifClient;
import cz.rb.tif.TifServiceDescription;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
public class Cus00041EntParty {

    private static final TifServiceDescription UPDATE = new TifServiceDescription("opUpdate_1_2", "CUS_EntParty-Update_1_2");

    @Autowired
    private TifClient tifClient;

    public void update(String partyId, BasicInfoDTO basicInfo, AdditionalInfoDTO additionalInfo, ContactsDto contactsDto) throws ServiceException {
        EntPartyRequest request = createEntPartyRequest(UPDATE);
        OpUpdate12 updatePartyRequest = new OpUpdate12();
        updatePartyRequest.setPartyId(partyId);
        request.getEntityService().setOpUpdate12(updatePartyRequest);
        updatePartyRequest.setBasicInfo(PartyUpdateMapper.INSTANCE.dtoToTifBasicInfo(basicInfo));
        if (additionalInfo != null) {
            updatePartyRequest.setAdditionalInfo(PartyUpdateMapper.INSTANCE.dtoToTifAdditionalInfo(additionalInfo));
            updatePartyRequest.getAdditionalInfo().setEsa95(addEsa95(additionalInfo, contactsDto));
        }
        tifClient.call(UPDATE.getServiceName(), 100, request);
    }

    private String addEsa95(AdditionalInfoDTO additionalInfo, ContactsDto contactsDto) {
        if (additionalInfo.getEsa95() != null) {
            return additionalInfo.getEsa95();
        }
        if ((contactsDto == null) || (contactsDto.getAddress() == null)) {
            return null;
        }
        Optional<AddressDto> addressDto = contactsDto.getAddress().stream().filter(AddressDto::getIsResidency).findFirst();
        if (addressDto.isEmpty()) {
            return null;
        }
        if ("CZ".equals(addressDto.get().getCountry())) {
            return "1430000";
        } else {
            return "2004300";
        }
    }

    private EntPartyRequest.EntityService createEntityService(String operation) {
        EntPartyRequest.EntityService entityService = new EntPartyRequest.EntityService();
        entityService.setEntity("Party");
        entityService.setOperation(operation);
        return entityService;
    }

    private EntPartyRequest createEntPartyRequest(TifServiceDescription tifServiceDescription) {
        EntPartyRequest request = new EntPartyRequest();
        request.setEntityService(createEntityService(tifServiceDescription.getOperationName()));
        return request;
    }

}
