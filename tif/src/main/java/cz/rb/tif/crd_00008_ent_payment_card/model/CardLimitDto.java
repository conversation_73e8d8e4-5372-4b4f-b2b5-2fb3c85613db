package cz.rb.tif.crd_00008_ent_payment_card.model;

import java.math.BigDecimal;

public class CardLimitDto {
    private String limitType;
    private MaxAmount maxAmount;
    private String period;

    public CardLimitDto() {
    }

    public CardLimitDto(String limitType, MaxAmount maxAmount, String period) {
        this.limitType = limitType;
        this.maxAmount = maxAmount;
        this.period = period;
    }

    public String getLimitType() {
        return limitType;
    }

    public void setLimitType(String limitType) {
        this.limitType = limitType;
    }

    public MaxAmount getMaxAmount() {
        return maxAmount;
    }

    public void setMaxAmount(MaxAmount maxAmount) {
        this.maxAmount = maxAmount;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }


    public static class MaxAmount {
        private BigDecimal amount;
        private String currencyCode;

        public MaxAmount() {
        }

        public MaxAmount(BigDecimal amount, String currencyCode) {
            this.amount = amount;
            this.currencyCode = currencyCode;
        }

        public BigDecimal getAmount() {
            return amount;
        }

        public void setAmount(BigDecimal amount) {
            this.amount = amount;
        }

        public String getCurrencyCode() {
            return currencyCode;
        }

        public void setCurrencyCode(String currencyCode) {
            this.currencyCode = currencyCode;
        }
    }
}
