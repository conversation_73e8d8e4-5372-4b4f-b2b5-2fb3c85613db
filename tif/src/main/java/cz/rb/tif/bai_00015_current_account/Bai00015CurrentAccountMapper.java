package cz.rb.tif.bai_00015_current_account;

import cz.equa.camapp.model.CreateAppDTO;
import cz.equa.camapp.model.account_number.AccountNumberDto;
import cz.rb.services.currentaccount.createaccountnumber_1_0._1.OpCreateAccountNumber10;
import cz.rb.services.currentaccount.createaccountnumber_1_0._1.OpCreateAccountNumber10Res;
import cz.rb.services.currentaccount.createapp_1_2.OpCreateApp12Res;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface Bai00015CurrentAccountMapper {
    Bai00015CurrentAccountMapper INSTANCE = Mappers.getMapper(Bai00015CurrentAccountMapper.class);

    // create account number
    OpCreateAccountNumber10.AccountNumber accountNumberDtoToEsb(AccountNumberDto dto);
    AccountNumberDto accountNumberEsbToDto(OpCreateAccountNumber10Res.AccountNumber accountNumber);

    // create app (create account)
    CreateAppDTO createAppEsbToDto(OpCreateApp12Res.Application.Identification identification);
}
