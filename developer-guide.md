# Developer guide

## Table of contents
   
<!-- TOC -->
* [Developer guide](#developer-guide)
  * [Table of contents](#table-of-contents)
* [How to add TIF connector](#how-to-add-tif-connector)
  * [Add .xml files to project](#add-xml-files-to-project)
  * [Add .xsd files to project](#add-xsd-files-to-project)
  * [Build generated sources](#build-generated-sources)
  * [Besim data](#besim-data)
    * [Ask for Besim test data](#ask-for-besim-test-data)
    * [Add Besim test data to the project](#add-besim-test-data-to-the-project)
      * [Create dir in the resources](#create-dir-in-the-resources)
      * [Copy wrapper xml](#copy-wrapper-xml)
      * [Insert response into wrapper](#insert-response-into-wrapper)
      * [Change property `fceId`](#change-property-fceid)
      * [Change property `name`](#change-property-name)
    * [Multiple Besim responses for one request](#multiple-besim-responses-for-one-request)
  * [Create service class](#create-service-class)
    * [TifServiceDescription constants](#tifservicedescription-constants)
    * [Create request and fill required fields](#create-request-and-fill-required-fields)
  * [Create output DTO](#create-output-dto)
  * [Create output DTO Mapper](#create-output-dto-mapper)
    * [List mapping](#list-mapping)
  * [Create mapper test class](#create-mapper-test-class)
  * [Create service test class](#create-service-test-class)
* [How to add or update REST service](#how-to-add-or-update-rest-service)
* [Add process variable to ProcessConfiguration.properties](#add-process-variable-to-processconfigurationproperties)
* [Bugs](#bugs)
  * [NoSuchMethodError: `java.time.LocalDate.<init>()`](#nosuchmethoderror-javatimelocaldateinit)
    * [Solution](#solution)
* [Deployment](#deployment)
  * [Build in Jenkins](#build-in-jenkins)
  * [Create DA package in Jira](#create-da-package-in-jira)
* [Run On Localhost](#run-on-localhost)
  * [Allow default user](#allow-default-user)
  * [Persist H2 Database on disk](#persist-h2-database-on-disk)
  * [Call Rest API with Postman on localhost](#call-rest-api-with-postman-on-localhost)
<!-- TOC -->

# How to add TIF connector

You receive a zip file with interface files. With the files from the zip file, do the following: 

## Add .xml files to project
Add .xml files from zip dir `api/*` to [tif/src/main/resources/defs](tif/src/main/resources/defs).
Directory structure must pre preserved.
    
    zip structure (example):

      [1.0]
        [api]
      ->| [models]
        |   [RBCZ]
        |     [ES]
        |       ...
        |   WMB_BAI_ConsumerLoan-GetOffers_1_0_100.xml
        |   CMN_BAI_ConsumerLoan-GetOffers_1_0_100_Dapi.xml
        |   ...
          
    copy to:

      [tif]
        [src]
          [main]
            [resources]
              [defs]
      ->|       ...
        |       ...

Xml with name `CMN_*_Dapi.xml` is required. If not included, supplier of the package should be contacted to include 
`CMN_*_Dapi.xml`. 

Workaround: try to copy from other `_Dapi.xml` if included in the package,

    example: copy APN_*_Dapi.xml -> CMN_*_Dapi.xml 

## Add .xsd files to project
add .xsd files to [tif/src/main/resources/defs/xsd](tif/src/main/resources/defs/xsd), 

    example: BAI_00029_ConsumerLoan-GetOffers_1_0_1.0.xsd

## Build generated sources
Run build to create generated sources (output is in 
[tif/target/generated-sources/xsd/cz/rb/services](tif/target/generated-sources/xsd/cz/rb/services)).
Generated classes are used in service implementation bellow.  
 
## Besim data
Besim is part of TIF connector. It is an emulator of remote backend for development purposes. 
It returns responses (stored locally in xml files) for every type of request. 

### Ask for Besim test data
We need to obtain example of the response from the remove system. 
Write email to `<EMAIL>` with following text (replace service and operation name):

    Dobrý den,
    prosím o zaslání response z ent. služby "BAI_00029_ConsumerLoan", operace "GetOffers_1_0".
       
While you wait for the data, continue to chapter [Create service class](#create-service-class). 

### Add Besim test data to the project
When you receive test data, it should be in the following format:

    <?xml version="1.0" encoding="UTF-8" standalone="no"?>
    <message version="3">
       <head>
           <RES_CODE>0</RES_CODE>
           <ERR_CODE/>
           <ERR_DESC/>
           ...
       </head>
       <body>
           <res xmlns="http://rb.cz/services/consumerLoan/GetOffers_1_0/1.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://rb.cz/services/consumerLoan/GetOffers_1_0/1.0 BAI_00029_ConsumerLoan-GetOffers_1_0_1.0.xsd">
           ...
           </res>
       </body>
    </message>

#### Create dir in the resources
Create new dir in the resources with the name of the new functionality 
under [tif/src/main/resources/defs/besim](tif/src/main/resources/defs/besim).

    example: mkdir tif/src/main/resources/defs/besim/BAI_00029_ConsumerLoan

#### Copy wrapper xml
Copy xml from any other directory under [tif/src/main/resources/defs/besim](tif/src/main/resources/defs/besim) 
and rename it to fit the operation name

    example: copy besim/BAI_00003_CashAccount/getList_1_2.xml -> besim/BAI_00029_ConsumerLoan/getOffers_1_0.xml

#### Insert response into wrapper
Open the xml and replace content of the tag `<void property="actionParameter">`. Inserted xml test data must be escaped. 

      <void property="actionParameter">
        <string>
          INSERT XML TEST DATA (ESCAPED) FROM EMAIL 
        </string>
      </void>

#### Change property `fceId`
Change the content of the tag `<void property="fceId">`:

      <void property="fceId">
        <string>BAI_ConsumerLoan-GetOffers_1_0</string>
      </void>

#### Change property `name`
Change the content of the tag `<void property="name">`:

      <void property="name">
        <string>BAI_ConsumerLoan-GetOffers_1_0</string>
      </void>

### Multiple Besim responses for one request
If you need more multiple responses for one request, use following:

In the java test, set the `TifClient.origSysId` property to a unique value:

    @Test
	public void questionnaireExistsSuccess() throws Exception {
        tifClient.setOrigSysId("success");
        ...
    }

In the Besim xml, set the `feSysId` property to the same value:

    <void property="feSysId">
      <string>success</string>
    </void>
            
The same result can be achieved also with property `tifClient.origAppId` and it's counterpart `feApplId` in the Besim xml. 

## Create service class
Create new package and service class in it in [tif/src/main/java/cz/rb/tif](tif/src/main/java/cz/rb/tif). 
Copy from another service in the package and implement required functionality.
    
    @Component
    public class Bai00029ConsumerLoan {

      private static final TifServiceDescription GET_OFFERS = new TifServiceDescription("opGetOffers_1_0", "BAI_ConsumerLoan-GetOffers_1_0");
    
      @Autowired
      private TifClient tifClient;

      public List<...> getOffers(partyId) {
        ...  
      }
    }

### TifServiceDescription constants
For each operation (function) in the service class, there should be one constant.

    private static final TifServiceDescription GET_OFFERS = new TifServiceDescription("opGetOffers_1_0", "BAI_ConsumerLoan-GetOffers_1_0");

* Name of the constant should be same as the operation name
* Second parameter is the service and operation name. 
  Can be found in `_Dapi.xml` in xml tag `<name>BAI_ConsumerLoan-GetOffers_1_0</name>`
* First parameter is the operation name. Usually it is the second part behind `-` from second parameter, with prefix `op`, 
  like `opGetOffers_1_0` 
        
### Create request and fill required fields
Before each operation, request object must be created. Request and response classes can be found in generated sources 
in [tif/target/generated-sources/xsd/cz/rb/services](tif/target/generated-sources/xsd/cz/rb/services). 

Each request have required and optional fields. Required field are marked as `@XmlElement(required = true)` 
in the request class. All the field must be filled. 

## Create output DTO
If the service returns any data, they should be mapped to DTO. 

Create one or more DTOs under `model` subdirectory in the service package, 

    example: `tif/src/main/java/cz/rb/tif/bai_00029_consumer_loan/model` 

## Create output DTO Mapper
Service result needs to be mapped to DTO. We use `org.mapstruct` mapper. 
Create mapper interface in the service package, create new or copy it from an existing mapper.

Create mapper interface in the service package under [tif/src/main/java/cz/rb/tif](tif/src/main/java/cz/rb/tif).
             
Example:
    
    package cz.rb.tif.bai_00029_consumer_loan;

    @Mapper(uses = {LovFactory.class})
    public interface ConsumerLoanMapper { //NOSONAR
    Logger logger = LoggerFactory.getLogger(ConsumerLoanMapper.class);
    ConsumerLoanMapper INSTANCE = Mappers.getMapper(ConsumerLoanMapper.class);
    
        @Mappings({@Mapping(source = "entity.listOfRepayments.repayment", target = "repayments")})
        OfferDTO tifToDto(Res.Offer entity);
    
        @InheritInverseConfiguration
        Res.Offer dtoToTif(OfferDTO entity);
    }

At the beginning, define top level mapping method and reverse method (if needed) such as 

    OfferDTO tifToDto(Res.Offer entity);

    @InheritInverseConfiguration
    Res.Offer dtoToTif(OfferDTO entity);

If source and destination field structure is 1:1, there is no annotation required. If there is a difference, 
use mapping annotations such as `@Mappings' and '@Mapping`.

### List mapping
If you need map sourceObject.innerObject.sourceList -> targetObject.targetList, use mapping like this:

    @Mappings({@Mapping(source = "sourceObject.innerObject.sourceList", target = "targetList")})
    TargetDTO tifToDto(SourceDTO sourceObject);
                     
Real example from `cz.rb.tif.bai_00029_consumer_loan.ConsumerLoanMapper`: 

    @Mappings({@Mapping(source = "entity.listOfRepayments.repayment", target = "repayments")})
    OfferDTO tifToDto(Res.Offer entity);

## Create mapper test class
Create mapper test class in [tif/src/test/java/cz/rb/tif](tif/src/test/java/cz/rb/tif). Copy from an existing class.

## Create service test class
Create test class in [tif/src/test/java/cz/rb/tif](tif/src/test/java/cz/rb/tif). Copy from an existing class.
   
              
# How to add or update REST service
new REST path add to `rest/download-yaml.groovy`

run `groovy rest/download-yaml.groovy` OR
in IntelliJ Idea activate maven profile groovy-generate and run download-yaml.groovy
- updated or new yaml are in [rest/src/main/resources/rest](rest/src/main/resources/rest) folder

Then `mvn install` should generate classes

# Add process variable to ProcessConfiguration.properties
- add to [webapp/etc/processConfiguration.properties](webapp/etc/processConfiguration.properties)
- add to test [webapp/src/test/resources/processConfiguration.properties](webapp/src/test/resources/processConfiguration.properties)

variable will be loaded in [cz/equa/camapp/config/ProcessConfiguration.java](ProcessConfiguration.java)
in form NameOfProcess.NameOfVariable

its needed to define variable for deployment camapp-cm.yaml and proper vars and kubernetes files

# Bugs

## NoSuchMethodError: `java.time.LocalDate.<init>()`
    ERROR org.camunda.commons.logging.BaseLogger - ENGINE-16004 Exception while closing command context: java.time.LocalDate.<init>()
    java.lang.NoSuchMethodError: java.time.LocalDate.<init>()
    at com.sun.xml.bind.v2.ClassFactory.tryGetDeclaredConstructor(ClassFactory.java:115) ~[jaxb-runtime-2.3.5.jar!/:2.3.5]
    at com.sun.xml.bind.v2.ClassFactory.create0(ClassFactory.java:76) ~[jaxb-runtime-2.3.5.jar!/:2.3.5]
    at com.sun.xml.bind.v2.runtime.ClassBeanInfoImpl.createInstance(ClassBeanInfoImpl.java:254) ~[jaxb-runtime-2.3.5.jar!/:2.3.5]
    ...
    Caused by: java.lang.NoSuchMethodException: java.time.LocalDate.<init>()
    at java.lang.Class.getConstructor0(Class.java:3427) ~[?:?]
    at java.lang.Class.getDeclaredConstructor(Class.java:2631) ~[?:?]
    at com.sun.xml.bind.v2.ClassFactory.tryGetDeclaredConstructor(ClassFactory.java:107) ~[jaxb-runtime-2.3.5.jar!/:2.3.5]
    ... 132 more  

### Solution
Forgotten `@XmlJavaTypeAdapter(DateAdapter.class)` on a date field in a DTO. Must manually search in all DTOs.

    @XmlJavaTypeAdapter(DateAdapter.class)
    protected LocalDate date;


# Deployment

Deployment process consists of these steps:

- [Build in Jenkins](#build-in-jenkins)
- [Create DA package in Jira](#create-da-package-in-jira)

## Build in Jenkins

Login to Jenkins and go to Camunda RB project page:

http://jenkins.dev1.equabank.loc:8080/view/WFL/job/Camunda%20RB/build

In left menu, Build with Parameters:

- Branch: origin/feature/camunda-tif
- Deploy: false
- Release: TRUE
- Sonar: false

## Create DA package in Jira

- Filter Jira: `type = "DA Package" AND component = CAMUNDA`
- Clone latest 'Balík CAMUNDA verze RBCL xx.x.xx' with settings:
  - Clone side tasks: false
  - Clone connections: false
- Change `DA package version` to generated to version by Jenkins build
- Change `DA package stored` to generated to version by Jenkins build
- Change `Description`
- Link fixed Jira issues with `decomposes to` relation type
- Change state to "Deliver"

# Run On Localhost

- [Allow default user](#allow-default-user)
- [Persist H2 Database on disk](#persist-h2-database-on-disk)
- [Call rest API with Postman on localhost](#call-rest-api-with-postman-on-localhost)

## Allow default user
You can allow default user for localhost - not needed to set up new user every time camunda app is run.

just uncomment the example in `webapp/etc/application.yaml`:

    admin-user:
      id: demo
      password: demo


## Persist H2 Database on disk

When you don't want to always start new Process, when run instance on localhost, you can change application.yaml,
set datasource to H2 file. Next time you run the application, the same persisted DB state will be connected. Always
you can reset the state to initial by deleting the location of DB (`root/tmp-database/camundaLocalhostDB.mv.db`). 

just uncomment the example in `webapp/etc/application.yaml`:

    datasource:
    url: jdbc:h2:./tmp-database/camundaLocalhostDB2;MODE=Oracle;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE

## Call Rest API with Postman on localhost

There is way to call GET,POST, PUT, etc... Rest API request on Camunda via Postman application. Authentication must be
configured, instead of Auth configuration, the response is 401 Unauthorized.

1. Install `Postman` application from https://www.postman.com/.
2. Install extension `Postman Interceptor` into your web browser. For Chrome browser: https://chromewebstore.google.com/detail/postman-interceptor/aicmkgpgakddgnaphhhpliifpcfhicfo
3. In extension - Start Caption session cookie and sync cookie for `localhost` and `localhost:9001`
4. Prepare request in Postman - POST,GET, etc, 
   
   for example
   with URL: `http://localhost:9001/rest/process-service/business-keys` 
   
   with Authorization: No Auth

   with Body payload:

        {
        "firstResult": "0",
        "maxResults": "10",
        "sortOrder": "desc",
        "sortBy": "date",
        "tablePage": "0",
        "businessKeyLike": "",
        "withStates": null,
        "processDefinitionIds": [],
        "applicationStates": null,
        "withIncidentsOnly": "",
        "searchInProcessVars": "false",
        "tcherDateTimeFrom": "",
        "tcherDateTimeTo": "",
        "processStartDatetimeFrom": "",
        "processStartDatetimeTo": "",
        "productIds": []
        }
5. On the right top corner of window open `cookies` and use `sync cookies` and connect it with your web browser.
6. Login into Camunda Cockpit with user in web browser. The session cookies have been made now.
7. You can send request via Postman app now. Authenticated by synced cookies from web browser.